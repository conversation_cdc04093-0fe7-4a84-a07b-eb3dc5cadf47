# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Essential Commands

### Development Workflow
```bash
# Install dependencies
pnpm install

# Start development server (API only)
cd apps/api && pnpm dev

# Start all apps in development mode (API, Web, MinIO)
pnpm dev

# Run API tests
cd apps/api && pnpm test

# Run single test file
cd apps/api && pnpm test src/__tests__/routes/organizations.test.ts

# Watch tests during development
cd apps/api && pnpm test:watch

# Type checking
pnpm typecheck

# Lint code
pnpm lint

# Format code
pnpm format

# Build all apps
pnpm build

# Database operations
cd apps/api && pnpm db:generate  # Generate migrations
cd apps/api && pnpm db:migrate   # Run migrations
cd apps/api && pnpm db:seed      # Seed database
```

### Testing Infrastructure
- Uses **Vitest** with **TestContainers** for isolated PostgreSQL testing
- Tests automatically spawn containerized databases for complete isolation
- All organization CRUD tests exist: `organizations.test.ts` (comprehensive) and `organizations-simple.test.ts` (basic)
- Run `pnpm test` to execute full test suite
- Test files use `.test.ts` extension and follow AAA pattern

## Architecture Overview

### Monorepo Structure
This is a **Turborepo** monorepo with:
- `apps/api/` - Fastify TypeScript API server
- `apps/web/` - React SPA with TanStack Router
- `packages/` - Shared packages:
  - `@repo/db` - Drizzle ORM database operations
  - `@repo/ui` - Shared UI component library
  - `@repo/typescript-config` - Shared TypeScript configurations
  - `@repo/eslint-config` - Shared linting rules
- Uses **pnpm workspaces** and **Turbo** for build orchestration

### API Architecture (apps/api/)

#### Core Stack
- **Fastify 5.x** with TypeScript and Zod validation
- **Drizzle ORM** with PostgreSQL database
- **Generic CRUD Router** pattern for rapid API development
- **Service Layer Architecture** for business logic separation
- **MinIO** for S3-compatible file storage
- **Firebase Admin SDK** for push notifications
- **Nodemailer** for email services

#### Configuration Management
- **Centralized config** in `src/config/index.ts` using `src/env.ts`
- **NEVER use `process.env`** directly - always import from `env.ts` or `config`
- Environment validation with Zod schemas
- Feature flags and environment-specific settings

#### Error Handling System
- **Standardized ApiError classes** in `src/errors/ApiError.ts`
- **Global error handler** in `src/errors/errorHandler.ts`
- **Database error translator** for user-friendly messages
- Consistent error response format with error codes
- Proper error logging with correlation IDs

#### Middleware Stack
- **Logging middleware** (`src/middleware/logging.ts`) - adds correlation IDs and structured logging
- **Performance middleware** (`src/middleware/performance.ts`) - tracks request metrics and timing
- **Auth middleware** (`src/middleware/auth.ts`) - JWT verification and user context
- **Response timing** headers automatically added
- All middleware integrated at server level in `app.ts`

#### Generic CRUD Pattern
The `createCrudRouter` in `src/helpers/createCrudRouter.ts` automatically generates:
- GET `/resource` (list with pagination, filtering, sorting)
- GET `/resource/:id` (single item)
- POST `/resource` (create)
- PATCH `/resource/:id` (update)
- DELETE `/resource/:id` (delete)

**Usage pattern:**
```typescript
const router = createCrudRouter({
  name: "organization",
  selectSchema: selectOrganizationSchema,
  insertSchema: insertOrganizationSchema, 
  patchSchema: patchOrganizationSchema,
  table: organization,
  queryApi: db.query.organization,
  service: organizationService, // Optional business logic layer
  requireAuth: true, // Enable authentication
  // ... other config
});
```

#### Service Layer
- **BaseService** class provides common CRUD operations with validation hooks
- **Lifecycle hooks**: `beforeCreate`, `afterCreate`, `beforeUpdate`, `afterUpdate`, `beforeDelete`, `afterDelete`
- **Validation hooks**: `validateCreate`, `validateUpdate`, `validateDelete`
- Services integrate with CRUD router via `service` config option
- Built-in audit trail support (createdBy, updatedBy)

#### Database Layer
- **Drizzle ORM** with typed queries and relations
- Database connection via `@repo/db` package
- Schema definitions in `@repo/db/schema`
- Relations defined in separate files for clarity
- Automatic database cleanup in tests using TRUNCATE CASCADE
- Migration system with version tracking

#### Authentication & Authorization
- **JWT-based authentication** with refresh tokens
- **Argon2** for password hashing
- User context available via `request.user`
- Organization-based access control
- API key support for service accounts

#### File Storage
- **MinIO integration** for S3-compatible storage
- Presigned URLs for secure uploads/downloads
- Automatic file validation and virus scanning hooks
- Support for multiple storage buckets

#### Request Flow
1. **Middleware** (logging, performance tracking, auth)
2. **Route Handler** (createCrudRouter generated or custom)
3. **Service Layer** (business validation if configured)
4. **Database Operations** (Drizzle ORM)
5. **Response Formatting** (Zod serialization)
6. **Error Handling** (global error handler)

### Frontend Architecture (apps/web/)

#### Core Stack
- **React 19** with TypeScript
- **TanStack Router** for type-safe routing
- **TanStack Query** for server state management
- **Zustand** for client state management
- **Tailwind CSS** with semantic theme variables
- **Radix UI** primitives for accessible components
- **React Hook Form** with Zod validation

#### Key Patterns
- **File-based routing** with TanStack Router
- **API client** with automatic error handling
- **Theme system** supporting light/dark modes
- **Component library** in `@repo/ui` package
- **Type-safe API integration** with shared Zod schemas

### Key Patterns to Follow

#### Route Organization
- Routes auto-registered in `src/routes/` with `autoPrefix` exports
- Use `createCrudRouter` for standard CRUD operations
- Integrate services for business logic validation
- Custom routes follow RESTful conventions

#### Logging and Observability
- All requests get correlation IDs for debugging
- Structured logging with context (operation, user, timing)
- Performance metrics automatically collected
- Use `request.log.info/warn/error` for logging
- OpenTelemetry support for APM integration

#### Type Safety
- Zod schemas for all API input/output validation
- TypeScript strict mode enabled
- Use inferred types from Drizzle schema: `InferSelectModel<typeof table>`
- Shared schemas between frontend and backend

#### Standardized Validation Helpers
- **validation-utils.ts** provides reusable validation patterns for common UI input scenarios:
  - `textField(maxLength?)` - Converts empty strings to null, optional max length
  - `emailField(maxLength?)` - Email validation with empty string to null conversion
  - `datetimeField()` - Accepts ISO datetime strings, converts to Date objects
  - `requiredTextField(minLength, maxLength?)` - Required text with length validation  
  - `jsonField()` - JSON object validation
- **Usage**: Import helpers instead of writing complex Zod unions manually
- **Consistency**: Handles UI input patterns (empty strings, datetime strings) seamlessly

### Environment Configuration
- Development: auto-loads `.env` files
- Production: expects environment variables to be set
- Required vars: `DATABASE_URL`, `JWT_SECRET`, `MINIO_*` credentials
- Optional vars: `LOG_LEVEL`, `PORT`, `CORS_ORIGINS`
- Validation via Zod schemas in `env.ts`

### Testing Approach
- **Isolated testing** with TestContainers PostgreSQL
- **Full integration tests** against real database
- **Comprehensive CRUD testing** patterns established
- Database cleanup between tests with foreign key cascade
- Test both success and error scenarios
- Mock external services (email, storage) in tests

### Database Schema Patterns
- **Audit fields**: All tables include `createdAt`, `updatedAt`, `createdBy`, `updatedBy`
- **Soft deletes**: Use `deletedAt` for logical deletion
- **UUIDs**: Primary keys use UUIDs for better distribution
- **Indexes**: Add for foreign keys and commonly queried fields
- **Relations**: Defined explicitly in Drizzle for type safety

### API Documentation
- **Swagger/OpenAPI** auto-generated from Zod schemas
- Available at `/docs` in development
- Includes authentication requirements
- Shows request/response examples
- Supports "Try it out" functionality

## Code Quality Instructions for Claude

### Working Style
- **Be meticulous and detail-oriented**: Every line, file structure, and naming convention should be carefully considered
- **Treat code as craft**: Code should be expressive, intentional, and elegant
- **Refactor relentlessly**: Zero tolerance for "temporary hacks"
- **Architecture with intent**: Code structure should tell the story
- **Context-driven development**: Always understand the broader design goals

### Code Aesthetics & Standards

#### File Naming Conventions
- **Use kebab-case** for all file names: `user-service.ts`, `api-error.ts`
- **Test files** follow pattern: `[module-name].test.ts`
- **Index files** should only re-export, never contain logic
- **Schema files** in database: `[entity-name].schema.ts`

#### Import Organization
Always organize imports in this order with blank lines between groups:
```typescript
// 1. Node.js built-ins
import { readFile } from 'node:fs/promises';

// 2. External dependencies
import { FastifyInstance } from 'fastify';
import { z } from 'zod';

// 3. Internal aliases/packages
import { db } from '@repo/db';

// 4. Relative imports (furthest to nearest)
import { ApiError } from '../../errors/api-error';
import type { UserProfile } from './types';
```

#### TypeScript Best Practices
- **Prefer interfaces** over types for object shapes
- **Explicit return types** on all exported functions
- **No `any`** - use `unknown` and narrow types properly
- **Strict mode** always enabled
- **Use generics** when reusability is needed

### Frontend Specific Guidelines

#### Tailwind Color System
- **NO HARDCODED COLORS**: Never use `bg-white`, `text-black`, etc.
- **Theme Variables Only**: Always use semantic variables that adapt to light/dark
- **Color Variables**:
  - Backgrounds: `bg-background`, `bg-foreground`
  - Text: `text-foreground`, `text-muted-foreground`
  - Brand: `bg-primary`, `text-primary`, `border-primary`
  - Interactive: `bg-accent`, `text-accent`
  - States: `bg-destructive`, `bg-success`, `bg-warning`
- **Opacity**: Use `/` syntax: `bg-primary/10`

#### Typography Standards
- **System Fonts**: Via Tailwind defaults (no custom fonts)
- **Sizes**: `text-2xl` (main), `text-xl` (section), `text-base` (body)
- **Weights**: `font-semibold` (headings), `font-medium` (subheadings)
- **Avoid**: Excessive sizes like `text-5xl` except for marketing

### Security Best Practices
- **Never log sensitive data**: passwords, tokens, PII
- **Validate all inputs**: Use Zod schemas
- **Parameterize queries**: Never concatenate SQL
- **Rate limit endpoints**: Especially auth endpoints
- **Environment variables**: Never hardcode secrets

### Performance Considerations
- **Avoid N+1 queries**: Use proper joins
- **Index database queries**: For frequently queried columns
- **Paginate large datasets**: Never return unlimited results
- **Use streaming**: For large file operations
- **Cache expensive operations**: Redis or in-memory

### Git Workflow
- **Conventional commits**: `feat:`, `fix:`, `docs:`, `refactor:`, `test:`
- **Branch naming**: `feature/description`, `fix/issue-number`
- **PR requirements**: Tests pass, types check, linter clean

# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.