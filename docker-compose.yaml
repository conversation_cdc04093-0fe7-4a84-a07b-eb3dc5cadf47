version: "3.8"

services:
  web:
    container_name: spark-web
    build:
      context: .
      dockerfile: apps/web/Dockerfile
    ports:
      - "80:80"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=production
    depends_on:
      - api
    # command: ["pnpm", "run", "dev", "--filter=./apps/web"]

  api:
    container_name: spark-api-server
    build:
      context: .
      dockerfile: apps/api/Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - DB_HOST=db
      - DB_USER=spark
      - DB_PASSWORD=spark
      - DB_NAME=spark
      - DB_PORT=5432
      - NODE_ENV=production
      - DATABASE_URL=******************************/spark
      - MINIO_ENDPOINT=minio
      - MINIO_PORT=9000
      - MINIO_USE_SSL=false
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - MINIO_BUCKET_NAME=spark-attachments
    depends_on:
      - db
      - minio
    command: ./apps/api/migrate.sh

  db:
    container_name: spark-postgres
    image: postgres:16
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=spark
      - POSTGRES_PASSWORD=spark
      - POSTGRES_DB=spark
    volumes:
      - postgres_data:/var/lib/postgresql/data

  minio:
    container_name: spark-minio
    image: minio/minio:latest
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

volumes:
  postgres_data:
  minio_data:
