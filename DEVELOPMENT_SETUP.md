# Development Setup with MinIO

This guide explains how to run the SPARK platform with MinIO for file storage during development.

## Quick Start

### Option 1: Automatic MinIO Start (Recommended)

From the root directory:

```bash
# This will automatically start MinIO and all services
pnpm dev
```

This command will:
1. Start MinIO container automatically
2. Wait for <PERSON><PERSON> to be ready
3. Start all development servers (API and Web)
4. Show colored output for each service

### Option 2: Manual MinIO Management

If you prefer to manage MinIO separately:

```bash
# Start MinIO manually
pnpm minio:start

# Then run development without auto-starting MinIO
pnpm dev:no-minio

# Stop MinIO when done
pnpm minio:stop
```

### Option 3: Using Docker Compose

For a complete environment with PostgreSQL:

```bash
# Start all services (PostgreSQL, MinIO, API, Web)
docker-compose up -d

# Or start only the dependencies
docker-compose up -d db minio

# Then run development
pnpm dev:no-minio
```

## Available Scripts

### Root Directory Scripts

- `pnpm dev` - Start MinIO automatically and run all services
- `pnpm dev:api` - Run only the API service
- `pnpm dev:web` - Run only the Web service
- `pnpm dev:no-minio` - Run services without starting MinIO
- `pnpm minio:start` - Start MinIO container
- `pnpm minio:stop` - Stop and remove MinIO container

### API Directory Scripts

When working in the `apps/api` directory:

- `pnpm dev` - Checks for dependencies (PostgreSQL, MinIO) before starting
- `pnpm dev:direct` - Start API without dependency checks

## MinIO Access

When MinIO is running:

- **API Endpoint**: http://localhost:9000
- **Admin Console**: http://localhost:9001
- **Default Credentials**: 
  - Username: `minioadmin`
  - Password: `minioadmin`

## Troubleshooting

### MinIO won't start

1. Check if Docker is running:
   ```bash
   docker info
   ```

2. Check if ports are already in use:
   ```bash
   # Check port 9000
   lsof -i :9000
   
   # Check port 9001
   lsof -i :9001
   ```

3. Remove existing container:
   ```bash
   docker rm -f spark-minio-dev
   ```

### "Docker is not running" error

Make sure Docker Desktop is running on your system.

### Port conflicts

If you have another MinIO instance or service using ports 9000/9001:

1. Stop the conflicting service, or
2. Modify the ports in `scripts/start-minio.js` and update your `.env` file

### API can't connect to MinIO

1. Ensure MinIO is running:
   ```bash
   curl http://localhost:9000/minio/health/live
   ```

2. Check your `.env` file has correct MinIO settings:
   ```env
   MINIO_ENDPOINT=localhost
   MINIO_PORT=9000
   MINIO_USE_SSL=false
   ```

## Development Workflow

### Typical Development Session

1. Start development environment:
   ```bash
   pnpm dev
   ```

2. You'll see output like:
   ```
   [minio] ✅ MinIO is ready!
   [minio]   📁 API: http://localhost:9000
   [minio]   🖥️  Console: http://localhost:9001
   [minio]   🔑 Credentials: minioadmin / minioadmin
   [app] API running at http://localhost:3000
   [app] Web running at http://localhost:5173
   ```

3. When done, press `Ctrl+C` to stop all services

### Working on API Only

If you're only working on the API:

```bash
cd apps/api
pnpm dev  # This will check for dependencies
```

The API dev script will warn you if MinIO or PostgreSQL aren't running and give you options to start them.

## Production Considerations

The automatic MinIO setup is for development only. For production:

1. Use managed object storage (AWS S3, Google Cloud Storage, etc.)
2. Or deploy MinIO in production mode with proper:
   - Authentication credentials
   - SSL/TLS encryption
   - Backup strategies
   - High availability setup

See `MINIO_SETUP.md` for more details on MinIO configuration and usage.