{"name": "@repo/eslint-config", "version": "0.0.0", "type": "module", "private": true, "exports": {".": "./base.js", "./base": "./base.js", "./next-js": "./next.js", "./react-internal": "./react-internal.js"}, "devDependencies": {"@eslint/js": "^9.23.0", "@next/eslint-plugin-next": "^15.2.1", "@typescript-eslint/eslint-plugin": "^8.24.1", "@typescript-eslint/parser": "^8.24.1", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-turbo": "^2.4.4", "globals": "^16.0.0", "typescript": "^5.8.2", "typescript-eslint": "^8.27.0"}}