{"name": "@repo/db", "type": "module", "version": "0.0.0", "private": true, "exports": {".": "./src/index.ts", "./orm": "./src/orm.ts", "./schema": "./src/schema/index.ts", "./migrations": "./src/migrations/index.ts", "./seed": "./src/seed.ts"}, "scripts": {"lint": "eslint src/", "check-types": "tsc --noEmit", "build": "tsc", "db:generate": "drizzle-kit generate", "db:migrate": "tsx src/migrate.ts", "db:seed": "tsx src/seed.ts", "db:reset": "tsx src/reset.ts", "db:reset-and-seed": "pnpm db:reset && pnpm db:seed"}, "dependencies": {"@types/bcrypt": "^5.0.2", "argon2": "^0.41.1", "bcrypt": "^6.0.0", "dotenv": "^16.3.1", "dotenv-expand": "^12.0.1", "drizzle-kit": "^0.30.5", "drizzle-orm": "0.41.0", "drizzle-zod": "^0.5.1", "postgres": "^3.4.3", "zod": "^3.22.4"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.13.14", "drizzle-kit": "^0.20.13", "tsx": "^4.7.0", "typescript": "^5.2.2"}}