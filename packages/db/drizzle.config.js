Object.defineProperty(exports, '__esModule', { value: true });
var dotenv = require('dotenv');
var drizzle_kit_1 = require('drizzle-kit');
if (process.env.NODE_ENV !== 'production') {
  dotenv.config();
} else {
}
exports.default = (0, drizzle_kit_1.defineConfig)({
  schema: './src/schema/index.ts',
  out: './src/migrations',
  dialect: 'postgresql',
  dbCredentials: {
    url: process.env.DATABASE_URL || '',
  },
  verbose: true,
  strict: true,
});
