{"extends": "@repo/typescript-config/hono.json", "compilerOptions": {"target": "ESNext", "jsx": "react-jsx", "jsxImportSource": "hono/jsx", "module": "ESNext", "moduleResolution": "Node", "paths": {"@/*": ["./src/*"], "$/*": ["./*"], "@repo/*": ["../../packages/*/src"], "@repo/db/*": ["./src/*"]}, "resolveJsonModule": true, "types": ["node"], "strict": true, "strictNullChecks": true, "noImplicitAny": true, "outDir": "./dist", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "verbatimModuleSyntax": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true}, "include": ["src/**/*.ts"], "exclude": ["node_modules"], "tsc-alias": {"resolveFullPaths": true, "verbose": false}}