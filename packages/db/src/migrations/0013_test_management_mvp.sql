-- Add new fields to test_suite table
ALTER TABLE "test_suite" ADD COLUMN IF NOT EXISTS "parent_suite_id" uuid;
ALTER TABLE "test_suite" ADD COLUMN IF NOT EXISTS "copied_from_id" uuid;

-- Add foreign key constraints for new test_suite fields
ALTER TABLE "test_suite" ADD CONSTRAINT "test_suite_parent_suite_id_test_suite_id_fk" 
  FOREIGN KEY ("parent_suite_id") REFERENCES "public"."test_suite"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "test_suite" ADD CONSTRAINT "test_suite_copied_from_id_test_suite_id_fk" 
  FOREIGN KEY ("copied_from_id") REFERENCES "public"."test_suite"("id") ON DELETE no action ON UPDATE no action;

-- Create test_case_comment table
CREATE TABLE IF NOT EXISTS "test_case_comment" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "test_case_id" uuid NOT NULL,
  "content" text NOT NULL,
  "author_id" uuid NOT NULL,
  "parent_id" uuid,
  "is_edited" boolean DEFAULT false NOT NULL,
  "is_deleted" boolean DEFAULT false NOT NULL,
  "created_at" timestamp DEFAULT now() NOT NULL,
  "updated_at" timestamp DEFAULT now() NOT NULL
);

-- Add foreign key constraints for test_case_comment
ALTER TABLE "test_case_comment" ADD CONSTRAINT "test_case_comment_test_case_id_test_case_id_fk" 
  FOREIGN KEY ("test_case_id") REFERENCES "public"."test_case"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "test_case_comment" ADD CONSTRAINT "test_case_comment_author_id_user_id_fk" 
  FOREIGN KEY ("author_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "test_case_comment" ADD CONSTRAINT "test_case_comment_parent_id_test_case_comment_id_fk" 
  FOREIGN KEY ("parent_id") REFERENCES "public"."test_case_comment"("id") ON DELETE no action ON UPDATE no action;

-- Create test_suite_comment table
CREATE TABLE IF NOT EXISTS "test_suite_comment" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "test_suite_id" uuid NOT NULL,
  "content" text NOT NULL,
  "author_id" uuid NOT NULL,
  "parent_id" uuid,
  "is_edited" boolean DEFAULT false NOT NULL,
  "is_deleted" boolean DEFAULT false NOT NULL,
  "created_at" timestamp DEFAULT now() NOT NULL,
  "updated_at" timestamp DEFAULT now() NOT NULL
);

-- Add foreign key constraints for test_suite_comment
ALTER TABLE "test_suite_comment" ADD CONSTRAINT "test_suite_comment_test_suite_id_test_suite_id_fk" 
  FOREIGN KEY ("test_suite_id") REFERENCES "public"."test_suite"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "test_suite_comment" ADD CONSTRAINT "test_suite_comment_author_id_user_id_fk" 
  FOREIGN KEY ("author_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "test_suite_comment" ADD CONSTRAINT "test_suite_comment_parent_id_test_suite_comment_id_fk" 
  FOREIGN KEY ("parent_id") REFERENCES "public"."test_suite_comment"("id") ON DELETE no action ON UPDATE no action;

-- Create test_plan_comment table
CREATE TABLE IF NOT EXISTS "test_plan_comment" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "test_plan_id" uuid NOT NULL,
  "content" text NOT NULL,
  "author_id" uuid NOT NULL,
  "parent_id" uuid,
  "is_edited" boolean DEFAULT false NOT NULL,
  "is_deleted" boolean DEFAULT false NOT NULL,
  "created_at" timestamp DEFAULT now() NOT NULL,
  "updated_at" timestamp DEFAULT now() NOT NULL
);

-- Add foreign key constraints for test_plan_comment
ALTER TABLE "test_plan_comment" ADD CONSTRAINT "test_plan_comment_test_plan_id_test_plan_id_fk" 
  FOREIGN KEY ("test_plan_id") REFERENCES "public"."test_plan"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "test_plan_comment" ADD CONSTRAINT "test_plan_comment_author_id_user_id_fk" 
  FOREIGN KEY ("author_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "test_plan_comment" ADD CONSTRAINT "test_plan_comment_parent_id_test_plan_comment_id_fk" 
  FOREIGN KEY ("parent_id") REFERENCES "public"."test_plan_comment"("id") ON DELETE no action ON UPDATE no action;

-- Create enum for test case work item link types
CREATE TYPE "public"."test_case_work_item_link_type" AS ENUM('tests', 'tested_by', 'validates', 'verifies', 'relates_to');

-- Create test_case_work_item_link table
CREATE TABLE IF NOT EXISTS "test_case_work_item_link" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "test_case_id" uuid NOT NULL,
  "work_item_id" uuid NOT NULL,
  "link_type" "test_case_work_item_link_type" NOT NULL,
  "created_by_id" uuid NOT NULL,
  "created_at" timestamp DEFAULT now() NOT NULL,
  CONSTRAINT "test_case_work_item_link_test_case_id_work_item_id_link_type_unique" UNIQUE("test_case_id","work_item_id","link_type")
);

-- Add foreign key constraints for test_case_work_item_link
ALTER TABLE "test_case_work_item_link" ADD CONSTRAINT "test_case_work_item_link_test_case_id_test_case_id_fk" 
  FOREIGN KEY ("test_case_id") REFERENCES "public"."test_case"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "test_case_work_item_link" ADD CONSTRAINT "test_case_work_item_link_work_item_id_work_item_id_fk" 
  FOREIGN KEY ("work_item_id") REFERENCES "public"."work_item"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "test_case_work_item_link" ADD CONSTRAINT "test_case_work_item_link_created_by_id_user_id_fk" 
  FOREIGN KEY ("created_by_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;