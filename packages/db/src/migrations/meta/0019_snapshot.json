{"id": "8f3caf28-0b6c-4650-bd0e-71af22d6ec76", "prevId": "fdc9b0ef-9ad1-4752-b8ec-7fb9d5fb7bcf", "version": "7", "dialect": "postgresql", "tables": {"public.attachment": {"name": "attachment", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "entity_type": {"name": "entity_type", "type": "attachment_entity_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "entity_id": {"name": "entity_id", "type": "uuid", "primaryKey": false, "notNull": true}, "file_name": {"name": "file_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "file_type": {"name": "file_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "size": {"name": "size", "type": "bigint", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "uploaded_by": {"name": "uploaded_by", "type": "uuid", "primaryKey": false, "notNull": false}, "uploaded_at": {"name": "uploaded_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"attachment_uploaded_by_user_id_fk": {"name": "attachment_uploaded_by_user_id_fk", "tableFrom": "attachment", "tableTo": "user", "columnsFrom": ["uploaded_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.audit_log": {"name": "audit_log", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "entity_type": {"name": "entity_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "entity_id": {"name": "entity_id", "type": "uuid", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "audit_action", "typeSchema": "public", "primaryKey": false, "notNull": true}, "changed_by": {"name": "changed_by", "type": "uuid", "primaryKey": false, "notNull": false}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "details": {"name": "details", "type": "jsonb", "primaryKey": false, "notNull": true}, "related_entity_type": {"name": "related_entity_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "related_entity_id": {"name": "related_entity_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"audit_log_changed_by_user_id_fk": {"name": "audit_log_changed_by_user_id_fk", "tableFrom": "audit_log", "tableTo": "user", "columnsFrom": ["changed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invitation": {"name": "invitation", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": true}, "invited_by": {"name": "invited_by", "type": "uuid", "primaryKey": false, "notNull": true}, "role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": true}, "scope_type": {"name": "scope_type", "type": "invitation_scope", "typeSchema": "public", "primaryKey": false, "notNull": true}, "scope_id": {"name": "scope_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "invitation_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "accepted_at": {"name": "accepted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"unique_pending_invitation": {"name": "unique_pending_invitation", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "scope_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "scope_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "where": "status = 'pending'", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"invitation_invited_by_user_id_fk": {"name": "invitation_invited_by_user_id_fk", "tableFrom": "invitation", "tableTo": "user", "columnsFrom": ["invited_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "invitation_role_id_role_id_fk": {"name": "invitation_role_id_role_id_fk", "tableFrom": "invitation", "tableTo": "role", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"invitation_token_unique": {"name": "invitation_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.notification": {"name": "notification", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_read": {"name": "is_read", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "read_at": {"name": "read_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"notification_user_id_user_id_fk": {"name": "notification_user_id_user_id_fk", "tableFrom": "notification", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organization": {"name": "organization", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "logo_url": {"name": "logo_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": true}, "domains": {"name": "domains", "type": "jsonb", "primaryKey": false, "notNull": true}, "billing_email": {"name": "billing_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "billing_address": {"name": "billing_address", "type": "text", "primaryKey": false, "notNull": false}, "billing_plan": {"name": "billing_plan", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "plan_expires_at": {"name": "plan_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"organization_created_by_user_id_fk": {"name": "organization_created_by_user_id_fk", "tableFrom": "organization", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"organization_name_unique": {"name": "organization_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "organization_slug_unique": {"name": "organization_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}, "name_idx": {"name": "name_idx", "nullsNotDistinct": false, "columns": ["name"]}, "slug_idx": {"name": "slug_idx", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.permission": {"name": "permission", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "identifier": {"name": "identifier", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "scope_type": {"name": "scope_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"permission_identifier_unique": {"name": "permission_identifier_unique", "nullsNotDistinct": false, "columns": ["identifier"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.priority": {"name": "priority", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "level": {"name": "level", "type": "integer", "primaryKey": false, "notNull": true}, "is_system": {"name": "is_system", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>(7)", "primaryKey": false, "notNull": false}, "icon": {"name": "icon", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"priority_organization_id_organization_id_fk": {"name": "priority_organization_id_organization_id_fk", "tableFrom": "priority", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project": {"name": "project", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "workspace_id": {"name": "workspace_id", "type": "uuid", "primaryKey": false, "notNull": true}, "workflow_id": {"name": "workflow_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "icon": {"name": "icon", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>(7)", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": false}, "target_date": {"name": "target_date", "type": "date", "primaryKey": false, "notNull": false}, "actual_end_date": {"name": "actual_end_date", "type": "date", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "project_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'planning'"}, "visibility": {"name": "visibility", "type": "project_visibility", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'private'"}, "default_assignee_id": {"name": "default_assignee_id", "type": "uuid", "primaryKey": false, "notNull": false}, "last_ticket_number": {"name": "last_ticket_number", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"project_workspace_id_workspace_id_fk": {"name": "project_workspace_id_workspace_id_fk", "tableFrom": "project", "tableTo": "workspace", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "project_workflow_id_workflow_id_fk": {"name": "project_workflow_id_workflow_id_fk", "tableFrom": "project", "tableTo": "workflow", "columnsFrom": ["workflow_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "project_default_assignee_id_user_id_fk": {"name": "project_default_assignee_id_user_id_fk", "tableFrom": "project", "tableTo": "user", "columnsFrom": ["default_assignee_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "project_created_by_user_id_fk": {"name": "project_created_by_user_id_fk", "tableFrom": "project", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project_workflow": {"name": "project_workflow", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "workflow_id": {"name": "workflow_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"project_workflow_project_id_project_id_fk": {"name": "project_workflow_project_id_project_id_fk", "tableFrom": "project_workflow", "tableTo": "project", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "project_workflow_workflow_id_workflow_id_fk": {"name": "project_workflow_workflow_id_workflow_id_fk", "tableFrom": "project_workflow", "tableTo": "workflow", "columnsFrom": ["workflow_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.resolution": {"name": "resolution", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "is_system": {"name": "is_system", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>(7)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"resolution_organization_id_organization_id_fk": {"name": "resolution_organization_id_organization_id_fk", "tableFrom": "resolution", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.role": {"name": "role", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "identifier": {"name": "identifier", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "level": {"name": "level", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "is_system": {"name": "is_system", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"role_created_by_user_id_fk": {"name": "role_created_by_user_id_fk", "tableFrom": "role", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"role_identifier_unique": {"name": "role_identifier_unique", "nullsNotDistinct": false, "columns": ["identifier"]}, "identifier_idx": {"name": "identifier_idx", "nullsNotDistinct": false, "columns": ["identifier"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.role_assignment": {"name": "role_assignment", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": true}, "scope_type": {"name": "scope_type", "type": "role_assignment_scope", "typeSchema": "public", "primaryKey": false, "notNull": true}, "scope_id": {"name": "scope_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"unique_active_role": {"name": "unique_active_role", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "scope_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "scope_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "where": "is_active = true", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"role_assignment_user_id_user_id_fk": {"name": "role_assignment_user_id_user_id_fk", "tableFrom": "role_assignment", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "role_assignment_role_id_role_id_fk": {"name": "role_assignment_role_id_role_id_fk", "tableFrom": "role_assignment", "tableTo": "role", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.role_permission": {"name": "role_permission", "schema": "", "columns": {"role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": true}, "permission_id": {"name": "permission_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"role_permission_role_id_role_id_fk": {"name": "role_permission_role_id_role_id_fk", "tableFrom": "role_permission", "tableTo": "role", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "role_permission_permission_id_permission_id_fk": {"name": "role_permission_permission_id_permission_id_fk", "tableFrom": "role_permission", "tableTo": "permission", "columnsFrom": ["permission_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"role_permission_role_id_permission_id_pk": {"name": "role_permission_role_id_permission_id_pk", "columns": ["role_id", "permission_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sprint": {"name": "sprint", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "goal": {"name": "goal", "type": "text", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": false}, "capacity": {"name": "capacity", "type": "integer", "primaryKey": false, "notNull": false}, "velocity": {"name": "velocity", "type": "integer", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "sprint_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'draft'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"sprint_project_id_project_id_fk": {"name": "sprint_project_id_project_id_fk", "tableFrom": "sprint", "tableTo": "project", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "sprint_created_by_user_id_fk": {"name": "sprint_created_by_user_id_fk", "tableFrom": "sprint", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.status": {"name": "status", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "status_type": {"name": "status_type", "type": "status_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>(7)", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "status_category_id": {"name": "status_category_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"status_organization_id_organization_id_fk": {"name": "status_organization_id_organization_id_fk", "tableFrom": "status", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "status_status_category_id_status_category_id_fk": {"name": "status_status_category_id_status_category_id_fk", "tableFrom": "status", "tableTo": "status_category", "columnsFrom": ["status_category_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.status_category": {"name": "status_category", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>(7)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"status_category_name_unique": {"name": "status_category_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.test_case": {"name": "test_case", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "test_suite_id": {"name": "test_suite_id", "type": "uuid", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status_id": {"name": "status_id", "type": "uuid", "primaryKey": false, "notNull": false}, "priority_id": {"name": "priority_id", "type": "uuid", "primaryKey": false, "notNull": false}, "assignee_id": {"name": "assignee_id", "type": "uuid", "primaryKey": false, "notNull": false}, "reporter_id": {"name": "reporter_id", "type": "uuid", "primaryKey": false, "notNull": false}, "work_item_id": {"name": "work_item_id", "type": "uuid", "primaryKey": false, "notNull": false}, "copied_from_id": {"name": "copied_from_id", "type": "uuid", "primaryKey": false, "notNull": false}, "preconditions": {"name": "preconditions", "type": "text", "primaryKey": false, "notNull": false}, "steps": {"name": "steps", "type": "jsonb", "primaryKey": false, "notNull": true}, "tags": {"name": "tags", "type": "jsonb", "primaryKey": false, "notNull": true}, "estimate": {"name": "estimate", "type": "jsonb", "primaryKey": false, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"test_case_test_suite_id_test_suite_id_fk": {"name": "test_case_test_suite_id_test_suite_id_fk", "tableFrom": "test_case", "tableTo": "test_suite", "columnsFrom": ["test_suite_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "test_case_project_id_project_id_fk": {"name": "test_case_project_id_project_id_fk", "tableFrom": "test_case", "tableTo": "project", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "test_case_status_id_status_id_fk": {"name": "test_case_status_id_status_id_fk", "tableFrom": "test_case", "tableTo": "status", "columnsFrom": ["status_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "test_case_priority_id_priority_id_fk": {"name": "test_case_priority_id_priority_id_fk", "tableFrom": "test_case", "tableTo": "priority", "columnsFrom": ["priority_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "test_case_assignee_id_user_id_fk": {"name": "test_case_assignee_id_user_id_fk", "tableFrom": "test_case", "tableTo": "user", "columnsFrom": ["assignee_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "test_case_reporter_id_user_id_fk": {"name": "test_case_reporter_id_user_id_fk", "tableFrom": "test_case", "tableTo": "user", "columnsFrom": ["reporter_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "test_case_work_item_id_work_item_id_fk": {"name": "test_case_work_item_id_work_item_id_fk", "tableFrom": "test_case", "tableTo": "work_item", "columnsFrom": ["work_item_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "test_case_copied_from_id_test_case_id_fk": {"name": "test_case_copied_from_id_test_case_id_fk", "tableFrom": "test_case", "tableTo": "test_case", "columnsFrom": ["copied_from_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "test_case_created_by_user_id_fk": {"name": "test_case_created_by_user_id_fk", "tableFrom": "test_case", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.test_case_comment": {"name": "test_case_comment", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "test_case_id": {"name": "test_case_id", "type": "uuid", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "author_id": {"name": "author_id", "type": "uuid", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "is_edited": {"name": "is_edited", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"test_case_comment_test_case_id_test_case_id_fk": {"name": "test_case_comment_test_case_id_test_case_id_fk", "tableFrom": "test_case_comment", "tableTo": "test_case", "columnsFrom": ["test_case_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "test_case_comment_author_id_user_id_fk": {"name": "test_case_comment_author_id_user_id_fk", "tableFrom": "test_case_comment", "tableTo": "user", "columnsFrom": ["author_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "test_case_comment_parent_id_test_case_comment_id_fk": {"name": "test_case_comment_parent_id_test_case_comment_id_fk", "tableFrom": "test_case_comment", "tableTo": "test_case_comment", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.test_case_history": {"name": "test_case_history", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "test_case_id": {"name": "test_case_id", "type": "uuid", "primaryKey": false, "notNull": true}, "change_type": {"name": "change_type", "type": "test_case_change_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "changed_by": {"name": "changed_by", "type": "uuid", "primaryKey": false, "notNull": true}, "changed_fields": {"name": "changed_fields", "type": "jsonb", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "summary": {"name": "summary", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"test_case_history_test_case_id_test_case_id_fk": {"name": "test_case_history_test_case_id_test_case_id_fk", "tableFrom": "test_case_history", "tableTo": "test_case", "columnsFrom": ["test_case_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "test_case_history_changed_by_user_id_fk": {"name": "test_case_history_changed_by_user_id_fk", "tableFrom": "test_case_history", "tableTo": "user", "columnsFrom": ["changed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.test_case_work_item_link": {"name": "test_case_work_item_link", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "test_case_id": {"name": "test_case_id", "type": "uuid", "primaryKey": false, "notNull": true}, "work_item_id": {"name": "work_item_id", "type": "uuid", "primaryKey": false, "notNull": true}, "link_type": {"name": "link_type", "type": "test_case_work_item_link_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "created_by_id": {"name": "created_by_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"test_case_work_item_link_test_case_id_test_case_id_fk": {"name": "test_case_work_item_link_test_case_id_test_case_id_fk", "tableFrom": "test_case_work_item_link", "tableTo": "test_case", "columnsFrom": ["test_case_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "test_case_work_item_link_work_item_id_work_item_id_fk": {"name": "test_case_work_item_link_work_item_id_work_item_id_fk", "tableFrom": "test_case_work_item_link", "tableTo": "work_item", "columnsFrom": ["work_item_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "test_case_work_item_link_created_by_id_user_id_fk": {"name": "test_case_work_item_link_created_by_id_user_id_fk", "tableFrom": "test_case_work_item_link", "tableTo": "user", "columnsFrom": ["created_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"test_case_work_item_link_test_case_id_work_item_id_link_type_unique": {"name": "test_case_work_item_link_test_case_id_work_item_id_link_type_unique", "nullsNotDistinct": false, "columns": ["test_case_id", "work_item_id", "link_type"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.test_plan": {"name": "test_plan", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": false}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "test_plan_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'draft'"}, "created_by_id": {"name": "created_by_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"test_plan_project_id_project_id_fk": {"name": "test_plan_project_id_project_id_fk", "tableFrom": "test_plan", "tableTo": "project", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "test_plan_created_by_id_user_id_fk": {"name": "test_plan_created_by_id_user_id_fk", "tableFrom": "test_plan", "tableTo": "user", "columnsFrom": ["created_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.test_plan_comment": {"name": "test_plan_comment", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "test_plan_id": {"name": "test_plan_id", "type": "uuid", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "author_id": {"name": "author_id", "type": "uuid", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "is_edited": {"name": "is_edited", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"test_plan_comment_test_plan_id_test_plan_id_fk": {"name": "test_plan_comment_test_plan_id_test_plan_id_fk", "tableFrom": "test_plan_comment", "tableTo": "test_plan", "columnsFrom": ["test_plan_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "test_plan_comment_author_id_user_id_fk": {"name": "test_plan_comment_author_id_user_id_fk", "tableFrom": "test_plan_comment", "tableTo": "user", "columnsFrom": ["author_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "test_plan_comment_parent_id_test_plan_comment_id_fk": {"name": "test_plan_comment_parent_id_test_plan_comment_id_fk", "tableFrom": "test_plan_comment", "tableTo": "test_plan_comment", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.test_suite": {"name": "test_suite", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": false}, "test_plan_id": {"name": "test_plan_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "test_suite_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'static'"}, "requirement_query": {"name": "requirement_query", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": false}, "parent_suite_id": {"name": "parent_suite_id", "type": "uuid", "primaryKey": false, "notNull": false}, "copied_from_id": {"name": "copied_from_id", "type": "uuid", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"test_suite_test_plan_id_test_plan_id_fk": {"name": "test_suite_test_plan_id_test_plan_id_fk", "tableFrom": "test_suite", "tableTo": "test_plan", "columnsFrom": ["test_plan_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "test_suite_parent_suite_id_test_suite_id_fk": {"name": "test_suite_parent_suite_id_test_suite_id_fk", "tableFrom": "test_suite", "tableTo": "test_suite", "columnsFrom": ["parent_suite_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "test_suite_copied_from_id_test_suite_id_fk": {"name": "test_suite_copied_from_id_test_suite_id_fk", "tableFrom": "test_suite", "tableTo": "test_suite", "columnsFrom": ["copied_from_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.test_suite_comment": {"name": "test_suite_comment", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "test_suite_id": {"name": "test_suite_id", "type": "uuid", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "author_id": {"name": "author_id", "type": "uuid", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "is_edited": {"name": "is_edited", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"test_suite_comment_test_suite_id_test_suite_id_fk": {"name": "test_suite_comment_test_suite_id_test_suite_id_fk", "tableFrom": "test_suite_comment", "tableTo": "test_suite", "columnsFrom": ["test_suite_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "test_suite_comment_author_id_user_id_fk": {"name": "test_suite_comment_author_id_user_id_fk", "tableFrom": "test_suite_comment", "tableTo": "user", "columnsFrom": ["author_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "test_suite_comment_parent_id_test_suite_comment_id_fk": {"name": "test_suite_comment_parent_id_test_suite_comment_id_fk", "tableFrom": "test_suite_comment", "tableTo": "test_suite_comment", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "display_name": {"name": "display_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_staff": {"name": "is_staff", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_email_verified": {"name": "is_email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "last_login": {"name": "last_login", "type": "timestamp", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "job_title": {"name": "job_title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "department": {"name": "department", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "timezone": {"name": "timezone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'UTC'"}, "language": {"name": "language", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "default": "'en'"}, "date_format": {"name": "date_format", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'MM/DD/YYYY'"}, "time_format": {"name": "time_format", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "default": "'12h'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "password_reset_token": {"name": "password_reset_token", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}, "password_reset_expires": {"name": "password_reset_expires", "type": "timestamp", "primaryKey": false, "notNull": false}, "email_verification_token": {"name": "email_verification_token", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}, "login_attempts": {"name": "login_attempts", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "locked_until": {"name": "locked_until", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "email_idx": {"name": "email_idx", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_fcm_token": {"name": "user_fcm_token", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "fcm_token": {"name": "fcm_token", "type": "text", "primaryKey": false, "notNull": true}, "device_type": {"name": "device_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "device_model": {"name": "device_model", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "os": {"name": "os", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "browser": {"name": "browser", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "last_active": {"name": "last_active", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"user_fcm_token_user_id_fcm_token_unique": {"name": "user_fcm_token_user_id_fcm_token_unique", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "fcm_token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_fcm_token_user_id_user_id_fk": {"name": "user_fcm_token_user_id_user_id_fk", "tableFrom": "user_fcm_token", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.work_item": {"name": "work_item", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type_id": {"name": "type_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "assignee_id": {"name": "assignee_id", "type": "uuid", "primaryKey": false, "notNull": false}, "reporter_id": {"name": "reporter_id", "type": "uuid", "primaryKey": false, "notNull": false}, "sprint_id": {"name": "sprint_id", "type": "uuid", "primaryKey": false, "notNull": false}, "initial_sprint_id": {"name": "initial_sprint_id", "type": "uuid", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "status_id": {"name": "status_id", "type": "uuid", "primaryKey": false, "notNull": true}, "priority_id": {"name": "priority_id", "type": "uuid", "primaryKey": false, "notNull": true}, "ticket_number": {"name": "ticket_number", "type": "integer", "primaryKey": false, "notNull": true}, "ticket_id": {"name": "ticket_id", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "tags": {"name": "tags", "type": "jsonb", "primaryKey": false, "notNull": true}, "estimate": {"name": "estimate", "type": "jsonb", "primaryKey": false, "notNull": true}, "dates": {"name": "dates", "type": "jsonb", "primaryKey": false, "notNull": true}, "links": {"name": "links", "type": "jsonb", "primaryKey": false, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"work_item_project_id_project_id_fk": {"name": "work_item_project_id_project_id_fk", "tableFrom": "work_item", "tableTo": "project", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "work_item_type_id_work_item_type_id_fk": {"name": "work_item_type_id_work_item_type_id_fk", "tableFrom": "work_item", "tableTo": "work_item_type", "columnsFrom": ["type_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "work_item_assignee_id_user_id_fk": {"name": "work_item_assignee_id_user_id_fk", "tableFrom": "work_item", "tableTo": "user", "columnsFrom": ["assignee_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "work_item_reporter_id_user_id_fk": {"name": "work_item_reporter_id_user_id_fk", "tableFrom": "work_item", "tableTo": "user", "columnsFrom": ["reporter_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "work_item_sprint_id_sprint_id_fk": {"name": "work_item_sprint_id_sprint_id_fk", "tableFrom": "work_item", "tableTo": "sprint", "columnsFrom": ["sprint_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "work_item_initial_sprint_id_sprint_id_fk": {"name": "work_item_initial_sprint_id_sprint_id_fk", "tableFrom": "work_item", "tableTo": "sprint", "columnsFrom": ["initial_sprint_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "work_item_parent_id_work_item_id_fk": {"name": "work_item_parent_id_work_item_id_fk", "tableFrom": "work_item", "tableTo": "work_item", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "work_item_status_id_status_id_fk": {"name": "work_item_status_id_status_id_fk", "tableFrom": "work_item", "tableTo": "status", "columnsFrom": ["status_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "work_item_priority_id_priority_id_fk": {"name": "work_item_priority_id_priority_id_fk", "tableFrom": "work_item", "tableTo": "priority", "columnsFrom": ["priority_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "work_item_created_by_user_id_fk": {"name": "work_item_created_by_user_id_fk", "tableFrom": "work_item", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_project_ticket_number": {"name": "unique_project_ticket_number", "nullsNotDistinct": false, "columns": ["project_id", "ticket_number"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.work_item_comment": {"name": "work_item_comment", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "work_item_id": {"name": "work_item_id", "type": "uuid", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "author_id": {"name": "author_id", "type": "uuid", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "is_edited": {"name": "is_edited", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"work_item_comment_work_item_id_work_item_id_fk": {"name": "work_item_comment_work_item_id_work_item_id_fk", "tableFrom": "work_item_comment", "tableTo": "work_item", "columnsFrom": ["work_item_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "work_item_comment_author_id_user_id_fk": {"name": "work_item_comment_author_id_user_id_fk", "tableFrom": "work_item_comment", "tableTo": "user", "columnsFrom": ["author_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "work_item_comment_parent_id_work_item_comment_id_fk": {"name": "work_item_comment_parent_id_work_item_comment_id_fk", "tableFrom": "work_item_comment", "tableTo": "work_item_comment", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.work_item_field": {"name": "work_item_field", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "field_type": {"name": "field_type", "type": "field_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "is_required": {"name": "is_required", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "default_value": {"name": "default_value", "type": "jsonb", "primaryKey": false, "notNull": false}, "options": {"name": "options", "type": "jsonb", "primaryKey": false, "notNull": true}, "work_item_type_id": {"name": "work_item_type_id", "type": "uuid", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "is_filterable": {"name": "is_filterable", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_displayed_in_grid": {"name": "is_displayed_in_grid", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"work_item_field_work_item_type_id_work_item_type_id_fk": {"name": "work_item_field_work_item_type_id_work_item_type_id_fk", "tableFrom": "work_item_field", "tableTo": "work_item_type", "columnsFrom": ["work_item_type_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "work_item_field_organization_id_organization_id_fk": {"name": "work_item_field_organization_id_organization_id_fk", "tableFrom": "work_item_field", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.work_item_history": {"name": "work_item_history", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "work_item_id": {"name": "work_item_id", "type": "uuid", "primaryKey": false, "notNull": true}, "change_type": {"name": "change_type", "type": "work_item_change_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "changed_by": {"name": "changed_by", "type": "uuid", "primaryKey": false, "notNull": true}, "changed_fields": {"name": "changed_fields", "type": "jsonb", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "summary": {"name": "summary", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"work_item_history_work_item_id_work_item_id_fk": {"name": "work_item_history_work_item_id_work_item_id_fk", "tableFrom": "work_item_history", "tableTo": "work_item", "columnsFrom": ["work_item_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "work_item_history_changed_by_user_id_fk": {"name": "work_item_history_changed_by_user_id_fk", "tableFrom": "work_item_history", "tableTo": "user", "columnsFrom": ["changed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.work_item_link": {"name": "work_item_link", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "source_work_item_id": {"name": "source_work_item_id", "type": "uuid", "primaryKey": false, "notNull": true}, "target_work_item_id": {"name": "target_work_item_id", "type": "uuid", "primaryKey": false, "notNull": true}, "link_type": {"name": "link_type", "type": "work_item_link_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_by_id": {"name": "created_by_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"work_item_link_created_by_id_user_id_fk": {"name": "work_item_link_created_by_id_user_id_fk", "tableFrom": "work_item_link", "tableTo": "user", "columnsFrom": ["created_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"work_item_link_source_work_item_id_target_work_item_id_link_type_unique": {"name": "work_item_link_source_work_item_id_target_work_item_id_link_type_unique", "nullsNotDistinct": false, "columns": ["source_work_item_id", "target_work_item_id", "link_type"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.work_item_type": {"name": "work_item_type", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "is_system": {"name": "is_system", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "icon": {"name": "icon", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>(7)", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"work_item_type_organization_id_organization_id_fk": {"name": "work_item_type_organization_id_organization_id_fk", "tableFrom": "work_item_type", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workflow": {"name": "workflow", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_system": {"name": "is_system", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "initial_status_id": {"name": "initial_status_id", "type": "uuid", "primaryKey": false, "notNull": false}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "workflow_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'draft'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"workflow_organization_id_organization_id_fk": {"name": "workflow_organization_id_organization_id_fk", "tableFrom": "workflow", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "workflow_initial_status_id_status_id_fk": {"name": "workflow_initial_status_id_status_id_fk", "tableFrom": "workflow", "tableTo": "status", "columnsFrom": ["initial_status_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "workflow_created_by_user_id_fk": {"name": "workflow_created_by_user_id_fk", "tableFrom": "workflow", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workflow_status": {"name": "workflow_status", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "workflow_id": {"name": "workflow_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status_id": {"name": "status_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_initial": {"name": "is_initial", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_final": {"name": "is_final", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "position_x": {"name": "position_x", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "position_y": {"name": "position_y", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "properties": {"name": "properties", "type": "jsonb", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"workflow_status_workflow_id_workflow_id_fk": {"name": "workflow_status_workflow_id_workflow_id_fk", "tableFrom": "workflow_status", "tableTo": "workflow", "columnsFrom": ["workflow_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "workflow_status_status_id_status_id_fk": {"name": "workflow_status_status_id_status_id_fk", "tableFrom": "workflow_status", "tableTo": "status", "columnsFrom": ["status_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workflow_transition": {"name": "workflow_transition", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "workflow_id": {"name": "workflow_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "from_status_id": {"name": "from_status_id", "type": "uuid", "primaryKey": false, "notNull": false}, "to_status_id": {"name": "to_status_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_initial": {"name": "is_initial", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_global": {"name": "is_global", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "button_text": {"name": "button_text", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "confirmation_message": {"name": "confirmation_message", "type": "text", "primaryKey": false, "notNull": false}, "screen_id": {"name": "screen_id", "type": "uuid", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"workflow_transition_workflow_id_workflow_id_fk": {"name": "workflow_transition_workflow_id_workflow_id_fk", "tableFrom": "workflow_transition", "tableTo": "workflow", "columnsFrom": ["workflow_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "workflow_transition_from_status_id_workflow_status_id_fk": {"name": "workflow_transition_from_status_id_workflow_status_id_fk", "tableFrom": "workflow_transition", "tableTo": "workflow_status", "columnsFrom": ["from_status_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "workflow_transition_to_status_id_workflow_status_id_fk": {"name": "workflow_transition_to_status_id_workflow_status_id_fk", "tableFrom": "workflow_transition", "tableTo": "workflow_status", "columnsFrom": ["to_status_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workspace": {"name": "workspace", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "icon": {"name": "icon", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>(7)", "primaryKey": false, "notNull": false}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": true}, "default_project_id": {"name": "default_project_id", "type": "uuid", "primaryKey": false, "notNull": false}, "visibility": {"name": "visibility", "type": "workspace_visibility", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'private'"}, "status": {"name": "status", "type": "workspace_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"workspace_organization_id_organization_id_fk": {"name": "workspace_organization_id_organization_id_fk", "tableFrom": "workspace", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "workspace_created_by_user_id_fk": {"name": "workspace_created_by_user_id_fk", "tableFrom": "workspace", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"org_slug_idx": {"name": "org_slug_idx", "nullsNotDistinct": false, "columns": ["organization_id", "slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.attachment_entity_type": {"name": "attachment_entity_type", "schema": "public", "values": ["work_item", "project", "workspace", "organization", "comment", "test_case", "test_plan", "test_suite", "test_run", "user"]}, "public.audit_action": {"name": "audit_action", "schema": "public", "values": ["CREATE", "UPDATE", "DELETE", "ARCHIVE", "RESTORE", "LOGIN", "LOGOUT", "PASSWORD_CHANGE", "PROFILE_UPDATE", "ROLE_ASSIGNED", "ROLE_REVOKED", "PERMISSION_UPDATED", "STATUS_CHANGE", "SPRINT_TRANSITION", "COMMENT_ADDED", "ATTACHMENT_UPLOADED", "NOTIFICATION_SENT", "SYSTEM_START", "SYSTEM_ERROR", "SCHEDULED_TASK_RUN", "DATA_IMPORT", "DATA_EXPORT"]}, "public.field_type": {"name": "field_type", "schema": "public", "values": ["text", "number", "date", "boolean", "select", "multi_select", "user", "json"]}, "public.invitation_scope": {"name": "invitation_scope", "schema": "public", "values": ["organization", "workspace", "project"]}, "public.invitation_status": {"name": "invitation_status", "schema": "public", "values": ["pending", "accepted", "rejected", "expired", "revoked"]}, "public.project_status": {"name": "project_status", "schema": "public", "values": ["planning", "active", "completed", "archived"]}, "public.project_visibility": {"name": "project_visibility", "schema": "public", "values": ["public", "private"]}, "public.role_assignment_scope": {"name": "role_assignment_scope", "schema": "public", "values": ["organization", "workspace", "project"]}, "public.sprint_status": {"name": "sprint_status", "schema": "public", "values": ["draft", "active", "completed", "archived"]}, "public.status_type": {"name": "status_type", "schema": "public", "values": ["todo", "in_progress", "done"]}, "public.test_case_change_type": {"name": "test_case_change_type", "schema": "public", "values": ["CREATE", "UPDATE", "DELETE", "RESTORE", "COMMENT_ADDED", "COMMENT_UPDATED", "COMMENT_DELETED", "ATTACHMENT_ADDED", "ATTACHMENT_DELETED", "EXECUTION_ADDED", "EXECUTION_UPDATED"]}, "public.test_case_work_item_link_type": {"name": "test_case_work_item_link_type", "schema": "public", "values": ["tests", "tested_by", "validates", "verifies", "relates_to"]}, "public.test_plan_status": {"name": "test_plan_status", "schema": "public", "values": ["draft", "active", "archived"]}, "public.test_suite_type": {"name": "test_suite_type", "schema": "public", "values": ["static", "requirement", "query"]}, "public.work_item_change_type": {"name": "work_item_change_type", "schema": "public", "values": ["CREATE", "UPDATE", "DELETE", "RESTORE", "COMMENT_ADDED", "COMMENT_UPDATED", "COMMENT_DELETED", "ATTACHMENT_ADDED", "ATTACHMENT_DELETED"]}, "public.work_item_link_type": {"name": "work_item_link_type", "schema": "public", "values": ["is_caused_by", "blocked_by", "blocks", "relates_to", "is_child_of", "is_duplicated_by", "duplicates"]}, "public.workflow_status_enum": {"name": "workflow_status_enum", "schema": "public", "values": ["draft", "active", "archived"]}, "public.workspace_status": {"name": "workspace_status", "schema": "public", "values": ["active", "archived"]}, "public.workspace_visibility": {"name": "workspace_visibility", "schema": "public", "values": ["public", "private"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}