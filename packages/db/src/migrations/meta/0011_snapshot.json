{"version": "7", "dialect": "postgresql", "id": "0011_fix_test_case_history", "prevId": "0010_little_sugar_man", "tables": {}, "schemas": {}, "enums": {"public.test_case_change_type": {"name": "test_case_change_type", "schema": "public", "values": ["CREATE", "UPDATE", "DELETE", "RESTORE", "COMMENT_ADDED", "COMMENT_UPDATED", "COMMENT_DELETED", "ATTACHMENT_ADDED", "ATTACHMENT_DELETED", "EXECUTION_ADDED", "EXECUTION_UPDATED"]}}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}