-- Add new attachment entity types
ALTER TYPE "public"."attachment_entity_type" ADD VALUE 'test_plan';
ALTER TYPE "public"."attachment_entity_type" ADD VALUE 'test_suite';

-- Create test plan status enum
CREATE TYPE "public"."test_plan_status" AS ENUM('draft', 'active', 'archived');

-- Create test suite type enum  
CREATE TYPE "public"."test_suite_type" AS ENUM('static', 'requirement', 'query');

-- Create test_plan table
CREATE TABLE IF NOT EXISTS "test_plan" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" varchar(1000),
	"project_id" uuid NOT NULL,
	"status" "test_plan_status" DEFAULT 'draft' NOT NULL,
	"created_by_id" uuid NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create test_suite table
CREATE TABLE IF NOT EXISTS "test_suite" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" varchar(1000),
	"test_plan_id" uuid NOT NULL,
	"type" "test_suite_type" DEFAULT 'static' NOT NULL,
	"requirement_query" varchar(1000),
	"order" integer DEFAULT 0 NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create test_case table
CREATE TABLE IF NOT EXISTS "test_case" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"title" varchar(255) NOT NULL,
	"description" varchar(2000),
	"test_suite_id" uuid NOT NULL,
	"project_id" uuid NOT NULL,
	"priority" varchar(50),
	"assigned_to_id" uuid,
	"work_item_id" uuid,
	"preconditions" varchar(2000),
	"steps" json,
	"expected_results" varchar(2000),
	"order" integer DEFAULT 0 NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create test_case_history table
CREATE TABLE IF NOT EXISTS "test_case_history" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"test_case_id" uuid NOT NULL,
	"changed_fields" json,
	"previous_value" json,
	"new_value" json,
	"changed_by" uuid NOT NULL,
	"changed_at" timestamp DEFAULT now() NOT NULL,
	"comment" varchar(1000)
);

-- Add foreign key constraints for test_plan
DO $$ BEGIN
 ALTER TABLE "test_plan" ADD CONSTRAINT "test_plan_project_id_project_id_fk" FOREIGN KEY ("project_id") REFERENCES "public"."project"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "test_plan" ADD CONSTRAINT "test_plan_created_by_id_user_id_fk" FOREIGN KEY ("created_by_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Add foreign key constraints for test_suite
DO $$ BEGIN
 ALTER TABLE "test_suite" ADD CONSTRAINT "test_suite_test_plan_id_test_plan_id_fk" FOREIGN KEY ("test_plan_id") REFERENCES "public"."test_plan"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Add foreign key constraints for test_case
DO $$ BEGIN
 ALTER TABLE "test_case" ADD CONSTRAINT "test_case_test_suite_id_test_suite_id_fk" FOREIGN KEY ("test_suite_id") REFERENCES "public"."test_suite"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "test_case" ADD CONSTRAINT "test_case_project_id_project_id_fk" FOREIGN KEY ("project_id") REFERENCES "public"."project"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "test_case" ADD CONSTRAINT "test_case_assigned_to_id_user_id_fk" FOREIGN KEY ("assigned_to_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "test_case" ADD CONSTRAINT "test_case_work_item_id_work_item_id_fk" FOREIGN KEY ("work_item_id") REFERENCES "public"."work_item"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Add foreign key constraints for test_case_history
DO $$ BEGIN
 ALTER TABLE "test_case_history" ADD CONSTRAINT "test_case_history_test_case_id_test_case_id_fk" FOREIGN KEY ("test_case_id") REFERENCES "public"."test_case"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "test_case_history" ADD CONSTRAINT "test_case_history_changed_by_user_id_fk" FOREIGN KEY ("changed_by") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;