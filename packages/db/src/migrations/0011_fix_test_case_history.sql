-- Drop the existing test_case_history table
DROP TABLE IF EXISTS "test_case_history";

-- Create test_case_change_type enum
CREATE TYPE "public"."test_case_change_type" AS ENUM('CREATE', 'UPDATE', 'DELETE', 'RESTORE', 'COMMENT_ADDED', 'COMMENT_UPDATED', 'COMMENT_DELETED', 'ATTACHMENT_ADDED', 'ATTACHMENT_DELETED', 'EXECUTION_ADDED', 'EXECUTION_UPDATED');

-- Recreate test_case_history table with correct schema
CREATE TABLE IF NOT EXISTS "test_case_history" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"test_case_id" uuid NOT NULL,
	"change_type" "test_case_change_type" NOT NULL,
	"changed_by" uuid NOT NULL,
	"changed_fields" jsonb,
	"metadata" jsonb,
	"summary" varchar(500),
	"created_at" timestamp DEFAULT now() NOT NULL
);

-- Add foreign key constraints
DO $$ BEGIN
 ALTER TABLE "test_case_history" ADD CONSTRAINT "test_case_history_test_case_id_test_case_id_fk" FOREIGN KEY ("test_case_id") REFERENCES "public"."test_case"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "test_case_history" ADD CONSTRAINT "test_case_history_changed_by_user_id_fk" FOREIGN KEY ("changed_by") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;