-- Create work item link type enum
CREATE TYPE "public"."work_item_link_type" AS ENUM('is_caused_by', 'blocked_by', 'blocks', 'relates_to', 'is_child_of', 'is_duplicated_by', 'duplicates');--> statement-breakpoint

-- Create work item link table
CREATE TABLE IF NOT EXISTS "work_item_link" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"source_work_item_id" uuid NOT NULL,
	"target_work_item_id" uuid NOT NULL,
	"link_type" "work_item_link_type" NOT NULL,
	"title" varchar(500),
	"description" text,
	"created_by_id" uuid NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "work_item_link_source_work_item_id_target_work_item_id_link_type_unique" UNIQUE("source_work_item_id","target_work_item_id","link_type")
);--> statement-breakpoint

-- Add foreign key constraints
DO $$ BEGIN
 ALTER TABLE "work_item_link" ADD CONSTRAINT "work_item_link_source_work_item_id_work_item_id_fk" FOREIGN KEY ("source_work_item_id") REFERENCES "public"."work_item"("id") ON DELETE cascade ON UPDATE cascade;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;--> statement-breakpoint

DO $$ BEGIN
 ALTER TABLE "work_item_link" ADD CONSTRAINT "work_item_link_target_work_item_id_work_item_id_fk" FOREIGN KEY ("target_work_item_id") REFERENCES "public"."work_item"("id") ON DELETE cascade ON UPDATE cascade;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;--> statement-breakpoint

DO $$ BEGIN
 ALTER TABLE "work_item_link" ADD CONSTRAINT "work_item_link_created_by_id_user_id_fk" FOREIGN KEY ("created_by_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;