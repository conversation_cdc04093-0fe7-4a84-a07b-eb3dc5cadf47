CREATE TABLE "test_suite_test_case" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"test_suite_id" uuid NOT NULL,
	"test_case_id" uuid NOT NULL,
	"status_id" uuid,
	"priority_id" uuid,
	"order" integer DEFAULT 0 NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_by" uuid,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "unique_test_suite_test_case" UNIQUE("test_suite_id","test_case_id")
);
--> statement-breakpoint
-- Migrate existing test case associations to the junction table
INSERT INTO "test_suite_test_case" ("test_suite_id", "test_case_id", "status_id", "priority_id", "order", "created_by", "created_at", "updated_at")
SELECT 
    "test_suite_id",
    "id" as "test_case_id",
    "status_id",
    "priority_id",
    "order",
    "created_by",
    "created_at",
    "updated_at"
FROM "test_case"
WHERE "test_suite_id" IS NOT NULL;
--> statement-breakpoint
ALTER TABLE "test_case" DROP CONSTRAINT "test_case_test_suite_id_test_suite_id_fk";
--> statement-breakpoint
ALTER TABLE "test_case" DROP CONSTRAINT "test_case_status_id_status_id_fk";
--> statement-breakpoint
ALTER TABLE "test_case" DROP CONSTRAINT "test_case_priority_id_priority_id_fk";
--> statement-breakpoint
ALTER TABLE "test_suite_test_case" ADD CONSTRAINT "test_suite_test_case_test_suite_id_test_suite_id_fk" FOREIGN KEY ("test_suite_id") REFERENCES "public"."test_suite"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "test_suite_test_case" ADD CONSTRAINT "test_suite_test_case_test_case_id_test_case_id_fk" FOREIGN KEY ("test_case_id") REFERENCES "public"."test_case"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "test_suite_test_case" ADD CONSTRAINT "test_suite_test_case_status_id_status_id_fk" FOREIGN KEY ("status_id") REFERENCES "public"."status"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "test_suite_test_case" ADD CONSTRAINT "test_suite_test_case_priority_id_priority_id_fk" FOREIGN KEY ("priority_id") REFERENCES "public"."priority"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "test_suite_test_case" ADD CONSTRAINT "test_suite_test_case_created_by_user_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "test_case" DROP COLUMN "test_suite_id";--> statement-breakpoint
ALTER TABLE "test_case" DROP COLUMN "status_id";--> statement-breakpoint
ALTER TABLE "test_case" DROP COLUMN "priority_id";