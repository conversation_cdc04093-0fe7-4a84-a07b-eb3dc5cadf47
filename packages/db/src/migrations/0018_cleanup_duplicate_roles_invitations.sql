-- Clean up duplicate role assignments
-- Keep only the most recent active role assignment per user per scope

-- First, identify and deactivate duplicate role assignments
WITH ranked_assignments AS (
  SELECT 
    id,
    user_id,
    scope_type,
    scope_id,
    is_active,
    created_at,
    ROW_NUMBER() OVER (
      PARTITION BY user_id, scope_type, scope_id 
      ORDER BY created_at DESC
    ) as rn
  FROM role_assignment
  WHERE is_active = true
)
UPDATE role_assignment
SET is_active = false, updated_at = NOW()
WHERE id IN (
  SELECT id FROM ranked_assignments WHERE rn > 1
);

-- Clean up duplicate pending invitations
-- Keep only the most recent pending invitation per email per scope

-- First, revoke duplicate pending invitations
WITH ranked_invitations AS (
  SELECT 
    id,
    email,
    scope_type,
    scope_id,
    status,
    created_at,
    ROW_NUMBER() OVER (
      PARTITION BY email, scope_type, scope_id 
      ORDER BY created_at DESC
    ) as rn
  FROM invitation
  WHERE status = 'pending'
)
UPDATE invitation
SET status = 'revoked', updated_at = NOW()
WHERE id IN (
  SELECT id FROM ranked_invitations WHERE rn > 1
);

-- Clean up invitations for users who already have active roles
-- Mark as accepted any pending invitations where user already has an active role

UPDATE invitation i
SET status = 'accepted', accepted_at = NOW(), updated_at = NOW()
WHERE i.status = 'pending'
AND EXISTS (
  SELECT 1 
  FROM "user" u
  INNER JOIN role_assignment ra ON u.id = ra.user_id
  WHERE u.email = i.email
  AND ra.scope_type = i.scope_type
  AND ra.scope_id = i.scope_id
  AND ra.is_active = true
);