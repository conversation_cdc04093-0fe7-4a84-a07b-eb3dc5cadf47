-- Add new columns to work_item_history table first
ALTER TABLE "work_item_history" ADD COLUMN "changed_fields" jsonb;--> statement-breakpoint
ALTER TABLE "work_item_history" ADD COLUMN "metadata" jsonb;--> statement-breakpoint
ALTER TABLE "work_item_history" ADD COLUMN "summary" varchar(500);--> statement-breakpoint

-- Migrate existing data to new structure before changing enum
UPDATE "work_item_history" SET 
  "changed_fields" = CASE 
    WHEN "field_name" IS NOT NULL THEN jsonb_build_object("field_name", jsonb_build_object('oldValue', "old_value", 'newValue', "new_value"))
    ELSE NULL
  END,
  "summary" = CASE 
    WHEN "change_type"::text = 'CREATE' THEN 'Created work item'
    WHEN "field_name" IS NOT NULL THEN 'Updated ' || "field_name"
    ELSE 'Updated work item'
  END;--> statement-breakpoint

-- Create new enum type
CREATE TYPE "public"."work_item_change_type_new" AS ENUM('CREATE', 'UPDATE', 'DELETE', 'RESTORE', 'COMMENT_ADDED', 'COMMENT_UPDATED', 'COMMENT_DELETED', 'ATTACHMENT_ADDED', 'ATTACHMENT_DELETED');--> statement-breakpoint

-- Add temporary column with new type
ALTER TABLE "work_item_history" ADD COLUMN "change_type_new" "work_item_change_type_new";--> statement-breakpoint

-- Update the new column with mapped values
UPDATE "work_item_history" SET 
  "change_type_new" = CASE 
    WHEN "change_type"::text IN ('STATUS_CHANGE', 'PRIORITY_CHANGE', 'ASSIGNMENT_CHANGE', 'SPRINT_CHANGE', 'TITLE_CHANGE', 'DESCRIPTION_CHANGE', 'TYPE_CHANGE', 'PARENT_CHANGE', 'ESTIMATE_CHANGE', 'TAG_CHANGE', 'LINK_CHANGE') THEN 'UPDATE'::work_item_change_type_new
    WHEN "change_type"::text = 'CREATE' THEN 'CREATE'::work_item_change_type_new
    WHEN "change_type"::text = 'DELETE' THEN 'DELETE'::work_item_change_type_new
    WHEN "change_type"::text = 'RESTORE' THEN 'RESTORE'::work_item_change_type_new
    ELSE 'UPDATE'::work_item_change_type_new
  END;--> statement-breakpoint

-- Drop the old column and rename the new one
ALTER TABLE "work_item_history" DROP COLUMN "change_type";--> statement-breakpoint
ALTER TABLE "work_item_history" RENAME COLUMN "change_type_new" TO "change_type";--> statement-breakpoint

-- Make the new column NOT NULL
ALTER TABLE "work_item_history" ALTER COLUMN "change_type" SET NOT NULL;--> statement-breakpoint

-- Drop old columns
ALTER TABLE "work_item_history" DROP COLUMN "field_name";--> statement-breakpoint
ALTER TABLE "work_item_history" DROP COLUMN "old_value";--> statement-breakpoint
ALTER TABLE "work_item_history" DROP COLUMN "new_value";--> statement-breakpoint

-- Drop old enum type and rename new one
DROP TYPE "public"."work_item_change_type";--> statement-breakpoint
ALTER TYPE "public"."work_item_change_type_new" RENAME TO "work_item_change_type";