-- First, we need to populate ticket_number and ticket_id for existing work items
-- This migration will assign sequential numbers within each project

-- Step 1: Add columns as nullable first
ALTER TABLE "work_item" ADD COLUMN IF NOT EXISTS "ticket_number" integer;
ALTER TABLE "work_item" ADD COLUMN IF NOT EXISTS "ticket_id" varchar(20);

-- Step 2: Populate ticket numbers for existing work items
WITH numbered_work_items AS (
  SELECT 
    id,
    project_id,
    ROW_NUMBER() OVER (PARTITION BY project_id ORDER BY created_at) as new_ticket_number
  FROM work_item
  WHERE ticket_number IS NULL
)
UPDATE work_item wi
SET 
  ticket_number = nwi.new_ticket_number,
  ticket_id = (SELECT key FROM project WHERE id = wi.project_id) || '-' || nwi.new_ticket_number
FROM numbered_work_items nwi
WHERE wi.id = nwi.id;

-- Step 3: Update project last_ticket_number based on existing work items
UPDATE project p
SET last_ticket_number = COALESCE((
  SELECT MAX(ticket_number) 
  FROM work_item 
  WHERE project_id = p.id
), 0);

-- Step 4: Now make the columns NOT NULL
ALTER TABLE "work_item" ALTER COLUMN "ticket_number" SET NOT NULL;
ALTER TABLE "work_item" ALTER COLUMN "ticket_id" SET NOT NULL;

-- Step 5: Add the unique constraint
ALTER TABLE "work_item" ADD CONSTRAINT "unique_project_ticket_number" UNIQUE("project_id","ticket_number");