CREATE TYPE "public"."work_item_change_type" AS ENUM('CREATE', 'UPDATE', 'STATUS_CHANGE', 'PRIORITY_CHANGE', 'ASSIGNMENT_CHANGE', 'SPRINT_CHANGE', 'TITLE_CHANGE', '<PERSON>SC<PERSON><PERSON><PERSON>ON_CHANGE', 'TYPE_CHANGE', 'PARENT_CHANGE', 'ESTIMATE_CHANGE', 'TAG_CHANGE', 'LINK_CHANGE', 'DELE<PERSON>', 'RESTORE');--> statement-breakpoint
CREATE TABLE "work_item_history" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"work_item_id" uuid NOT NULL,
	"field_name" varchar(100) NOT NULL,
	"old_value" jsonb,
	"new_value" jsonb,
	"changed_by" uuid NOT NULL,
	"change_type" "work_item_change_type" NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "work_item_history" ADD CONSTRAINT "work_item_history_work_item_id_work_item_id_fk" FOREIGN KEY ("work_item_id") REFERENCES "public"."work_item"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "work_item_history" ADD CONSTRAINT "work_item_history_changed_by_user_id_fk" FOREIGN KEY ("changed_by") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;