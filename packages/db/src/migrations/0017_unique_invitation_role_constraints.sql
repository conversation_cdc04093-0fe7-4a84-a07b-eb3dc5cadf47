-- Add unique constraint for pending invitations
-- Only one pending invitation per email per scope
CREATE UNIQUE INDEX unique_pending_invitation ON invitation (email, scope_type, scope_id, status) 
WHERE status = 'pending';

-- Add unique constraint for active role assignments
-- Only one active role per user per scope
CREATE UNIQUE INDEX unique_active_role ON role_assignment (user_id, scope_type, scope_id, is_active)
WHERE is_active = true;