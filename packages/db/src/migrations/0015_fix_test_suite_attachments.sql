-- Create a trigger function to handle attachment cleanup when entities are deleted
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION delete_entity_attachments()
RETURNS TRIGGER AS $$
BEGIN
  -- Delete all attachments related to the deleted entity
  DELETE FROM attachment 
  WHERE entity_id = OLD.id 
    AND entity_type = TG_ARGV[0]::attachment_entity_type;
  
  RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for each entity type that can have attachments
-- Test Suite trigger
DROP TRIGGER IF EXISTS delete_test_suite_attachments ON test_suite;
CREATE TRIGGER delete_test_suite_attachments
BEFORE DELETE ON test_suite
FOR EACH ROW
EXECUTE FUNCTION delete_entity_attachments('test_suite');

-- Test Case trigger (for consistency)
DROP TRIGGER IF EXISTS delete_test_case_attachments ON test_case;
CREATE TRIGGER delete_test_case_attachments
BEFORE DELETE ON test_case
FOR EACH ROW
EXECUTE FUNCTION delete_entity_attachments('test_case');

-- Test Plan trigger (for consistency)
DROP TRIGGER IF EXISTS delete_test_plan_attachments ON test_plan;
CREATE TRIGGER delete_test_plan_attachments
BEFORE DELETE ON test_plan
FOR EACH ROW
EXECUTE FUNCTION delete_entity_attachments('test_plan');

-- Work Item trigger (for consistency)
DROP TRIGGER IF EXISTS delete_work_item_attachments ON work_item;
CREATE TRIGGER delete_work_item_attachments
BEFORE DELETE ON work_item
FOR EACH ROW
EXECUTE FUNCTION delete_entity_attachments('work_item');

-- Project trigger (for consistency)
DROP TRIGGER IF EXISTS delete_project_attachments ON project;
CREATE TRIGGER delete_project_attachments
BEFORE DELETE ON project
FOR EACH ROW
EXECUTE FUNCTION delete_entity_attachments('project');

-- Workspace trigger (for consistency)
DROP TRIGGER IF EXISTS delete_workspace_attachments ON workspace;
CREATE TRIGGER delete_workspace_attachments
BEFORE DELETE ON workspace
FOR EACH ROW
EXECUTE FUNCTION delete_entity_attachments('workspace');

-- Organization trigger (for consistency)
DROP TRIGGER IF EXISTS delete_organization_attachments ON organization;
CREATE TRIGGER delete_organization_attachments
BEFORE DELETE ON organization
FOR EACH ROW
EXECUTE FUNCTION delete_entity_attachments('organization');

-- Also fix the test_case_history table to have cascade delete
ALTER TABLE "test_case_history" DROP CONSTRAINT IF EXISTS "test_case_history_test_case_id_test_case_id_fk";
ALTER TABLE "test_case_history" ADD CONSTRAINT "test_case_history_test_case_id_test_case_id_fk" 
  FOREIGN KEY ("test_case_id") REFERENCES "public"."test_case"("id") ON DELETE CASCADE ON UPDATE NO ACTION;