-- Drop existing foreign key constraints for test_suite table
ALTER TABLE "test_suite" DROP CONSTRAINT IF EXISTS "test_suite_test_plan_id_test_plan_id_fk";
ALTER TABLE "test_suite" DROP CONSTRAINT IF EXISTS "test_suite_parent_suite_id_test_suite_id_fk";
ALTER TABLE "test_suite" DROP CONSTRAINT IF EXISTS "test_suite_copied_from_id_test_suite_id_fk";

-- Drop existing foreign key constraints for test_case table
ALTER TABLE "test_case" DROP CONSTRAINT IF EXISTS "test_case_test_suite_id_test_suite_id_fk";

-- Drop existing foreign key constraints for test_suite_comment table
ALTER TABLE "test_suite_comment" DROP CONSTRAINT IF EXISTS "test_suite_comment_test_suite_id_test_suite_id_fk";

-- Drop existing foreign key constraints for test_case_comment table
ALTER TABLE "test_case_comment" DROP CONSTRAINT IF EXISTS "test_case_comment_test_case_id_test_case_id_fk";

-- Drop existing foreign key constraints for test_plan table
ALTER TABLE "test_plan" DROP CONSTRAINT IF EXISTS "test_plan_project_id_project_id_fk";

-- Drop existing foreign key constraints for test_plan_comment table
ALTER TABLE "test_plan_comment" DROP CONSTRAINT IF EXISTS "test_plan_comment_test_plan_id_test_plan_id_fk";

-- Drop existing foreign key constraints for test_case_work_item_link table
ALTER TABLE "test_case_work_item_link" DROP CONSTRAINT IF EXISTS "test_case_work_item_link_test_case_id_test_case_id_fk";

-- Re-add foreign key constraints with CASCADE DELETE for test_suite table
ALTER TABLE "test_suite" ADD CONSTRAINT "test_suite_test_plan_id_test_plan_id_fk" 
  FOREIGN KEY ("test_plan_id") REFERENCES "public"."test_plan"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "test_suite" ADD CONSTRAINT "test_suite_parent_suite_id_test_suite_id_fk" 
  FOREIGN KEY ("parent_suite_id") REFERENCES "public"."test_suite"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "test_suite" ADD CONSTRAINT "test_suite_copied_from_id_test_suite_id_fk" 
  FOREIGN KEY ("copied_from_id") REFERENCES "public"."test_suite"("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- Re-add foreign key constraints with CASCADE DELETE for test_case table
ALTER TABLE "test_case" ADD CONSTRAINT "test_case_test_suite_id_test_suite_id_fk" 
  FOREIGN KEY ("test_suite_id") REFERENCES "public"."test_suite"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- Re-add foreign key constraints with CASCADE DELETE for test_suite_comment table
ALTER TABLE "test_suite_comment" ADD CONSTRAINT "test_suite_comment_test_suite_id_test_suite_id_fk" 
  FOREIGN KEY ("test_suite_id") REFERENCES "public"."test_suite"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- Re-add foreign key constraints with CASCADE DELETE for test_case_comment table
ALTER TABLE "test_case_comment" ADD CONSTRAINT "test_case_comment_test_case_id_test_case_id_fk" 
  FOREIGN KEY ("test_case_id") REFERENCES "public"."test_case"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- Re-add foreign key constraints with CASCADE DELETE for test_plan table
ALTER TABLE "test_plan" ADD CONSTRAINT "test_plan_project_id_project_id_fk" 
  FOREIGN KEY ("project_id") REFERENCES "public"."project"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- Re-add foreign key constraints with CASCADE DELETE for test_plan_comment table
ALTER TABLE "test_plan_comment" ADD CONSTRAINT "test_plan_comment_test_plan_id_test_plan_id_fk" 
  FOREIGN KEY ("test_plan_id") REFERENCES "public"."test_plan"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- Re-add foreign key constraints with CASCADE DELETE for test_case_work_item_link table
ALTER TABLE "test_case_work_item_link" ADD CONSTRAINT "test_case_work_item_link_test_case_id_test_case_id_fk" 
  FOREIGN KEY ("test_case_id") REFERENCES "public"."test_case"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- Also add CASCADE DELETE for comment parent relationships
ALTER TABLE "test_case_comment" DROP CONSTRAINT IF EXISTS "test_case_comment_parent_id_test_case_comment_id_fk";
ALTER TABLE "test_case_comment" ADD CONSTRAINT "test_case_comment_parent_id_test_case_comment_id_fk" 
  FOREIGN KEY ("parent_id") REFERENCES "public"."test_case_comment"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "test_suite_comment" DROP CONSTRAINT IF EXISTS "test_suite_comment_parent_id_test_suite_comment_id_fk";
ALTER TABLE "test_suite_comment" ADD CONSTRAINT "test_suite_comment_parent_id_test_suite_comment_id_fk" 
  FOREIGN KEY ("parent_id") REFERENCES "public"."test_suite_comment"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "test_plan_comment" DROP CONSTRAINT IF EXISTS "test_plan_comment_parent_id_test_plan_comment_id_fk";
ALTER TABLE "test_plan_comment" ADD CONSTRAINT "test_plan_comment_parent_id_test_plan_comment_id_fk" 
  FOREIGN KEY ("parent_id") REFERENCES "public"."test_plan_comment"("id") ON DELETE CASCADE ON UPDATE NO ACTION;