-- Update test_case table to match the TypeScript schema

-- 1. Alter existing columns
ALTER TABLE "test_case" 
  ALTER COLUMN "title" TYPE varchar(500),
  ALTER COLUMN "description" TYPE text,
  ALTER COLUMN "preconditions" TYPE text;

-- 2. Rename columns to match schema
ALTER TABLE "test_case" 
  RENAME COLUMN "assigned_to_id" TO "assignee_id";

-- 3. Add missing columns
ALTER TABLE "test_case"
  ADD COLUMN IF NOT EXISTS "status_id" uuid,
  ADD COLUMN IF NOT EXISTS "priority_id" uuid,
  ADD COLUMN IF NOT EXISTS "reporter_id" uuid,
  ADD COLUMN IF NOT EXISTS "created_by" uuid,
  ADD COLUMN IF NOT EXISTS "tags" jsonb DEFAULT '[]'::jsonb NOT NULL,
  ADD COLUMN IF NOT EXISTS "estimate" jsonb DEFAULT '{}'::jsonb NOT NULL;

-- 4. Drop unused column
ALTER TABLE "test_case" 
  DROP COLUMN IF EXISTS "expected_results";

-- 5. Convert priority varchar to priority_id reference
-- First, remove the old priority column
ALTER TABLE "test_case" 
  DROP COLUMN IF EXISTS "priority";

-- 6. Change steps from json to jsonb and set default
-- First update any null values to empty array
UPDATE "test_case" SET "steps" = '[]'::json WHERE "steps" IS NULL;

ALTER TABLE "test_case" 
  ALTER COLUMN "steps" TYPE jsonb USING steps::jsonb,
  ALTER COLUMN "steps" SET DEFAULT '[]'::jsonb,
  ALTER COLUMN "steps" SET NOT NULL;

-- 7. Add foreign key constraints for new columns
DO $$ BEGIN
 ALTER TABLE "test_case" ADD CONSTRAINT "test_case_status_id_status_id_fk" FOREIGN KEY ("status_id") REFERENCES "public"."status"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "test_case" ADD CONSTRAINT "test_case_priority_id_priority_id_fk" FOREIGN KEY ("priority_id") REFERENCES "public"."priority"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "test_case" ADD CONSTRAINT "test_case_reporter_id_user_id_fk" FOREIGN KEY ("reporter_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "test_case" ADD CONSTRAINT "test_case_created_by_user_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- 8. Update the renamed foreign key constraint
ALTER TABLE "test_case" DROP CONSTRAINT IF EXISTS "test_case_assigned_to_id_user_id_fk";
DO $$ BEGIN
 ALTER TABLE "test_case" ADD CONSTRAINT "test_case_assignee_id_user_id_fk" FOREIGN KEY ("assignee_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;