import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import env from './env';
import * as schema from './schema';

async function resetDatabase() {
  const databaseUrl = env.DATABASE_URL;
  if (!databaseUrl) {
    throw new Error('DATABASE_URL is not set');
  }

  // Parse the database URL to get connection details
  const url = new URL(databaseUrl);
  const dbName = url.pathname.slice(1); // Remove leading slash

  // Create a connection to postgres database (not the target database)
  const adminUrl = databaseUrl.replace(`/${dbName}`, '/postgres');
  const adminSql = postgres(adminUrl, { max: 1 });

  try {
    await adminSql`
      SELECT pg_terminate_backend(pid)
      FROM pg_stat_activity
      WHERE datname = ${dbName} 
      AND pid <> pg_backend_pid()
    `;
    await adminSql`DROP DATABASE IF EXISTS ${adminSql(dbName)}`;
    await adminSql`CREATE DATABASE ${adminSql(dbName)}`;
  } finally {
    await adminSql.end();
  }
  const migrationSql = postgres(databaseUrl, { max: 1 });

  try {
    const db = drizzle(migrationSql, { schema });

    // Import drizzle migrate function and config
    const { migrate } = await import('drizzle-orm/postgres-js/migrator');
    const config = await import('$/drizzle.config');

    await migrate(db, { migrationsFolder: config.default.out! });
  } finally {
    await migrationSql.end();
  }
}

// Run the reset
resetDatabase().catch((_error) => {
  process.exit(1);
});
