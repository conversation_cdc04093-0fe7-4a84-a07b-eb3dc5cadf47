import config from '$/drizzle.config';
import { drizzle } from 'drizzle-orm/postgres-js';
import { migrate } from 'drizzle-orm/postgres-js/migrator';
import postgres from 'postgres';

const connectionString = process.env.DATABASE_URL || 'fallback_strinng';

const MAX_RETRIES = 5;
const RETRY_DELAY = 5000;

async function connectWithRetry(retries = MAX_RETRIES) {
  try {
    const pg = postgres(connectionString);
    const db = drizzle(pg);
    await migrate(db, { migrationsFolder: config.out! });

    await pg.end();
  } catch (error) {
    if (retries > 0) {
      await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY));
      await connectWithRetry(retries - 1);
    } else {
      throw error;
    }
  }
}

connectWithRetry().catch((_error) => {
  process.exit(1);
});
