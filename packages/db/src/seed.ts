import { db, pg } from '@repo/db';
import * as schema from '@repo/db/schema';
import type { Table } from 'drizzle-orm';
import { getTableName, sql } from 'drizzle-orm';
import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import {
  permission,
  priority,
  role,
  rolePermission,
  status,
  statusCategory,
  user,
  workItemType,
} from './seeds';

async function resetTable(db: PostgresJsDatabase<typeof schema>, table: Table) {
  return db.execute(sql.raw(`TRUNCATE TABLE "${getTableName(table)}" RESTART IDENTITY CASCADE`));
}

// Main seeding function
async function runSeeds() {
  // Reset tables in correct order (considering foreign key constraints)
  for (const table of [
    schema.organization,
    schema.user,
    schema.role,
    schema.permission,
    schema.rolePermission,
    schema.statusCategory,
    schema.priority,
    schema.workItemType,
    schema.status,
  ]) {
    await resetTable(db, table);
  }

  // Seed in dependency order
  await role(db);
  await permission(db);
  await rolePermission(db);
  await user(db);
  await statusCategory(db);
  await priority(db);
  await workItemType(db);
  await status(db);

  // Close database connection
  await pg.end();
}

// Execute seeding
runSeeds().catch((_err) => {
  pg.end();
  process.exit(1);
});
