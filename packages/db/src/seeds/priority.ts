import { randomUUID } from 'node:crypto';
import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import * as schema from '../schema';
import prioritySeeds from './data/priorities.json' with { type: 'json' };

export default async function seedPriorities(db: PostgresJsDatabase<typeof schema>) {
  // Add UUIDs to the seed data
  const prioritiesWithIds = prioritySeeds.map((priority) => ({
    id: randomUUID(),
    ...priority,
  }));

  // Insert priorities in a single batch
  const insertedPriorities = await db.insert(schema.priority).values(prioritiesWithIds).returning();
  return insertedPriorities;
}
