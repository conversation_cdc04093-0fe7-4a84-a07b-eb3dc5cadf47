import { randomUUID } from 'node:crypto';
import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import * as schema from '../schema';
import statusSeeds from './data/statuses.json' with { type: 'json' };

export default async function seedStatuses(db: PostgresJsDatabase<typeof schema>) {
  // Get all status categories to map names to IDs
  const statusCategories = await db.select().from(schema.statusCategory);
  const statusCategoryMap = new Map(statusCategories.map((sc) => [sc.name, sc.id]));

  // Add UUIDs and map status category names to IDs
  const statusesWithIds = statusSeeds.map((status) => {
    const statusCategoryId = statusCategoryMap.get(status.statusCategoryName);
    if (!statusCategoryId) {
      throw new Error(`Status category '${status.statusCategoryName}' not found`);
    }

    const { statusCategoryName, ...statusData } = status;
    console.log(statusCategoryName);
    return {
      id: randomUUID(),
      ...statusData,
      statusType: statusData.statusType as 'todo' | 'in_progress' | 'done',
      statusCategoryId,
    };
  });

  // Insert statuses in a single batch
  const insertedStatuses = await db.insert(schema.status).values(statusesWithIds).returning();
  return insertedStatuses;
}
