import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import * as schema from '../schema';
import rolePermissionData from './data/role-permissions.json' with { type: 'json' };

export default async function seedRolePermissions(db: PostgresJsDatabase<typeof schema>) {
  // First, get all roles
  const roles = await db.select().from(schema.role);
  const roleMap = new Map(roles.map((r) => [r.identifier, r.id]));

  // Get all permissions
  const permissions = await db.select().from(schema.permission);
  const permissionMap = new Map(permissions.map((p) => [p.identifier, p.id]));

  // Prepare role-permission mappings
  const rolePermissions: Array<{ roleId: string; permissionId: string }> = [];

  for (const [roleIdentifier, permissionIdentifiers] of Object.entries(rolePermissionData)) {
    const roleId = roleMap.get(roleIdentifier);
    if (!roleId) {
      continue;
    }

    for (const permissionIdentifier of permissionIdentifiers) {
      const permissionId = permissionMap.get(permissionIdentifier);
      if (!permissionId) {
        continue;
      }

      rolePermissions.push({ roleId, permissionId });
    }
  }

  // Insert role-permission mappings in batches
  const batchSize = 100;
  let insertedCount = 0;

  for (let i = 0; i < rolePermissions.length; i += batchSize) {
    const batch = rolePermissions.slice(i, i + batchSize);
    await db.insert(schema.rolePermission).values(batch);
    insertedCount += batch.length;
  }
  return insertedCount;
}
