import bcrypt from 'bcrypt';
import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import * as schema from '../schema';
import usersData from './data/users.json' with { type: 'json' };

export default async function seedUsers(db: PostgresJsDatabase<typeof schema>) {
  const insertedUsers = [];

  for (const userData of usersData) {
    // Hash the password
    const hashedPassword = await bcrypt.hash(userData.password, 10);

    // Insert user
    const [user] = await db
      .insert(schema.user)
      .values({
        email: userData.email,
        password: hashedPassword,
        firstName: userData.firstName,
        lastName: userData.lastName,
        displayName: userData.displayName,
        isActive: true,
        isStaff: true,
        isEmailVerified: true,
      })
      .returning();

    if (!user) {
      throw new Error(`Failed to seed user: ${userData.email}`);
    }

    insertedUsers.push(user);
  }

  return insertedUsers;
}
