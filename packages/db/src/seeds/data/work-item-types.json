[{"name": "Epic", "description": "Large body of work that can be broken down into smaller stories", "isSystem": true, "icon": "rocket", "color": "#7c3aed", "data": {"hierarchy": "parent", "defaultFields": ["description", "acceptance_criteria", "business_value"]}}, {"name": "User Story", "description": "Feature or requirement from the user's perspective", "isSystem": true, "icon": "clipboard", "color": "#2563eb", "data": {"hierarchy": "standard", "defaultFields": ["description", "acceptance_criteria", "story_points"]}}, {"name": "Task", "description": "Individual piece of work", "isSystem": true, "icon": "check-circle", "color": "#059669", "data": {"hierarchy": "standard", "defaultFields": ["description", "estimated_hours"]}}, {"name": "Bug", "description": "Problem or error that needs to be fixed", "isSystem": true, "icon": "bug", "color": "#dc2626", "data": {"hierarchy": "standard", "defaultFields": ["description", "steps_to_reproduce", "expected_behavior", "actual_behavior", "severity"]}}, {"name": "Sub-task", "description": "Smaller unit of work that belongs to another work item", "isSystem": true, "icon": "tag", "color": "#6b7280", "data": {"hierarchy": "child", "defaultFields": ["description"]}}]