{"org_owner": ["org:manage_settings", "org:update_profile", "org:delete_organization", "org:transfer_ownership", "org:manage_billing", "org:view_billing", "org:change_plan", "org:manage_payment_methods", "org:download_invoices", "org:create_workspaces", "org:delete_any_workspace", "org:archive_any_workspace", "org:access_all_workspaces", "org:manage_all_workspaces", "org:view_all_workspaces", "org:invite_members", "org:remove_members", "org:manage_member_roles", "org:view_all_members", "org:suspend_members", "org:manage_guest_access", "org:manage_security_settings", "org:configure_sso", "org:manage_2fa_policy", "org:view_audit_logs", "org:export_audit_logs", "org:manage_ip_whitelist", "org:manage_api_keys", "org:export_all_data", "org:import_data", "org:manage_data_retention", "org:manage_backups", "org:manage_integrations", "org:configure_webhooks", "org:manage_oauth_apps"], "org_admin": ["org:manage_settings", "org:update_profile", "org:view_billing", "org:download_invoices", "org:create_workspaces", "org:view_all_workspaces", "org:archive_workspaces", "org:invite_members", "org:remove_members", "org:manage_member_roles", "org:view_all_members", "org:manage_guest_access", "org:view_security_settings", "org:manage_2fa_policy", "org:view_audit_logs", "org:export_audit_logs", "org:export_all_data", "org:manage_data_retention", "org:manage_integrations", "org:configure_webhooks"], "org_member": ["org:view_profile", "org:view_members", "org:view_available_workspaces", "org:request_workspace_access", "org:manage_own_profile", "org:manage_own_notifications", "org:manage_own_api_tokens", "org:use_org_integrations", "org:view_org_announcements"], "org_billing": ["org:manage_billing", "org:view_billing", "org:change_plan", "org:manage_payment_methods", "org:download_invoices", "org:manage_billing_contacts", "org:view_usage_analytics", "org:set_spending_limits", "org:view_profile", "org:view_members", "org:view_all_workspaces", "org:generate_cost_reports", "org:export_billing_data"], "org_guest": ["org:view_basic_profile", "org:view_assigned_workspaces"], "ws_admin": ["ws:manage_settings", "ws:update_details", "ws:delete_workspace", "ws:archive_workspace", "ws:manage_workspace_templates", "ws:invite_members", "ws:remove_members", "ws:manage_member_roles", "ws:view_all_members", "ws:manage_guest_access", "ws:create_projects", "ws:delete_any_project", "ws:archive_any_project", "ws:manage_all_projects", "ws:view_all_projects", "ws:move_projects", "ws:manage_project_templates", "ws:manage_project_visibility", "ws:override_project_permissions", "ws:manage_workspace_permissions", "ws:delete_any_work_item", "ws:bulk_edit_work_items", "ws:manage_work_item_types", "ws:manage_custom_fields", "ws:manage_workflows", "ws:create_automation_rules", "ws:manage_labels_and_tags", "ws:manage_priorities", "ws:manage_statuses", "ws:export_workspace_data", "ws:view_workspace_analytics", "ws:create_workspace_reports", "ws:manage_dashboards", "ws:view_audit_logs", "ws:manage_workspace_integrations", "ws:configure_webhooks", "ws:manage_api_access"], "ws_manager": ["ws:create_projects", "ws:manage_owned_projects", "ws:archive_projects", "ws:view_all_projects", "ws:manage_project_members", "ws:invite_members", "ws:assign_project_roles", "ws:manage_team_capacity", "ws:create_work_items", "ws:edit_any_work_item", "ws:delete_work_items", "ws:bulk_assign_work_items", "ws:manage_sprints", "ws:manage_milestones", "ws:manage_releases", "ws:create_roadmaps", "ws:manage_dependencies", "ws:set_due_dates", "ws:estimate_work", "ws:track_time", "ws:view_workspace_analytics", "ws:create_project_reports", "ws:export_project_data", "ws:view_team_performance", "ws:view_velocity_charts", "ws:customize_project_workflow", "ws:create_project_templates", "ws:manage_project_labels"], "ws_member": ["ws:view_workspace", "ws:view_public_projects", "ws:view_assigned_projects", "ws:view_workspace_members", "ws:create_work_items", "ws:edit_own_work_items", "ws:edit_assigned_work_items", "ws:delete_own_work_items", "ws:assign_work_items_to_self", "ws:change_work_item_status", "ws:add_work_item_relations", "ws:create_comments", "ws:edit_own_comments", "ws:delete_own_comments", "ws:mention_members", "ws:create_discussions", "ws:react_to_content", "ws:upload_attachments", "ws:delete_own_attachments", "ws:view_all_attachments", "ws:create_personal_views", "ws:save_filters", "ws:subscribe_to_items", "ws:manage_notifications", "ws:track_own_time", "ws:view_own_activity", "ws:export_own_data", "ws:view_project_timeline"], "ws_contributor": ["ws:view_workspace", "ws:view_assigned_projects", "ws:view_project_members", "ws:create_work_items_in_assigned_projects", "ws:edit_own_work_items", "ws:change_work_item_status", "ws:add_work_estimates", "ws:create_comments", "ws:edit_own_comments", "ws:upload_attachments_to_own_items", "ws:mention_assigned_members"], "ws_viewer": ["ws:view_workspace", "ws:view_public_projects", "ws:view_assigned_projects", "ws:view_work_items", "ws:view_comments", "ws:view_attachments", "ws:view_project_timeline", "ws:view_project_members", "ws:create_comments", "ws:react_to_content", "ws:subscribe_to_items", "ws:export_visible_data", "ws:search_workspace", "ws:use_filters", "ws:save_personal_views"], "ws_guest": ["ws:view_assigned_projects", "ws:view_assigned_work_items", "ws:view_related_comments", "ws:create_comments_on_assigned_items", "ws:view_mentioned_in"]}