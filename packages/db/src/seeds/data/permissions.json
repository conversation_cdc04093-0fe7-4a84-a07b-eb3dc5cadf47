[{"identifier": "org:manage_settings", "name": "Manage Organization Settings", "description": "Can modify organization settings and preferences", "category": "organization", "scopeType": "org"}, {"identifier": "org:update_profile", "name": "Update Organization Profile", "description": "Can update organization profile information", "category": "organization", "scopeType": "org"}, {"identifier": "org:delete_organization", "name": "Delete Organization", "description": "Can permanently delete the organization", "category": "organization", "scopeType": "org"}, {"identifier": "org:transfer_ownership", "name": "Transfer Organization Ownership", "description": "Can transfer ownership to another user", "category": "organization", "scopeType": "org"}, {"identifier": "org:manage_billing", "name": "Manage Billing", "description": "Can manage billing settings and payment methods", "category": "billing", "scopeType": "org"}, {"identifier": "org:view_billing", "name": "View Billing", "description": "Can view billing information and invoices", "category": "billing", "scopeType": "org"}, {"identifier": "org:change_plan", "name": "Change Subscription Plan", "description": "Can change the organization's subscription plan", "category": "billing", "scopeType": "org"}, {"identifier": "org:manage_payment_methods", "name": "Manage Payment Methods", "description": "Can add, remove, or update payment methods", "category": "billing", "scopeType": "org"}, {"identifier": "org:download_invoices", "name": "Download Invoices", "description": "Can download billing invoices", "category": "billing", "scopeType": "org"}, {"identifier": "org:create_workspaces", "name": "Create Workspaces", "description": "Can create new workspaces in the organization", "category": "workspace", "scopeType": "org"}, {"identifier": "org:delete_any_workspace", "name": "Delete Any Workspace", "description": "Can delete any workspace in the organization", "category": "workspace", "scopeType": "org"}, {"identifier": "org:archive_any_workspace", "name": "Archive Any Workspace", "description": "Can archive any workspace in the organization", "category": "workspace", "scopeType": "org"}, {"identifier": "org:access_all_workspaces", "name": "Access All Workspaces", "description": "Automatic access to all workspaces in the organization", "category": "workspace", "scopeType": "org"}, {"identifier": "org:manage_all_workspaces", "name": "Manage All Workspaces", "description": "Can modify settings for any workspace", "category": "workspace", "scopeType": "org"}, {"identifier": "org:view_all_workspaces", "name": "View All Workspaces", "description": "Can see all workspaces in the organization", "category": "workspace", "scopeType": "org"}, {"identifier": "org:invite_members", "name": "Invite Members", "description": "Can invite new members to the organization", "category": "organization", "scopeType": "org"}, {"identifier": "org:remove_members", "name": "Remove Members", "description": "Can remove members from the organization", "category": "organization", "scopeType": "org"}, {"identifier": "org:manage_member_roles", "name": "Manage Member Roles", "description": "Can assign and modify member roles", "category": "organization", "scopeType": "org"}, {"identifier": "org:view_all_members", "name": "View All Members", "description": "Can view all organization members", "category": "organization", "scopeType": "org"}, {"identifier": "org:suspend_members", "name": "Suspend Members", "description": "Can suspend member accounts", "category": "organization", "scopeType": "org"}, {"identifier": "org:manage_guest_access", "name": "Manage Guest Access", "description": "Can manage guest user access", "category": "organization", "scopeType": "org"}, {"identifier": "org:manage_security_settings", "name": "Manage Security Settings", "description": "Can configure security settings", "category": "security", "scopeType": "org"}, {"identifier": "org:configure_sso", "name": "Configure SSO", "description": "Can configure single sign-on settings", "category": "security", "scopeType": "org"}, {"identifier": "org:manage_2fa_policy", "name": "Manage 2FA Policy", "description": "Can enforce two-factor authentication policies", "category": "security", "scopeType": "org"}, {"identifier": "org:view_audit_logs", "name": "View Audit Logs", "description": "Can view audit logs", "category": "security", "scopeType": "org"}, {"identifier": "org:export_audit_logs", "name": "Export Audit Logs", "description": "Can export audit log data", "category": "security", "scopeType": "org"}, {"identifier": "org:manage_ip_whitelist", "name": "Manage IP Whitelist", "description": "Can manage IP access restrictions", "category": "security", "scopeType": "org"}, {"identifier": "org:manage_api_keys", "name": "Manage API Keys", "description": "Can manage organization-wide API keys", "category": "security", "scopeType": "org"}, {"identifier": "org:export_all_data", "name": "Export All Data", "description": "Can export all organization data", "category": "organization", "scopeType": "org"}, {"identifier": "org:import_data", "name": "Import Data", "description": "Can import data into the organization", "category": "organization", "scopeType": "org"}, {"identifier": "org:manage_data_retention", "name": "Manage Data Retention", "description": "Can configure data retention policies", "category": "organization", "scopeType": "org"}, {"identifier": "org:manage_backups", "name": "Manage Backups", "description": "Can manage backup settings", "category": "organization", "scopeType": "org"}, {"identifier": "org:manage_integrations", "name": "Manage Integrations", "description": "Can configure organization integrations", "category": "organization", "scopeType": "org"}, {"identifier": "org:configure_webhooks", "name": "Configure Webhooks", "description": "Can configure organization webhooks", "category": "organization", "scopeType": "org"}, {"identifier": "org:manage_oauth_apps", "name": "Manage OAuth Apps", "description": "Can manage OAuth applications", "category": "organization", "scopeType": "org"}, {"identifier": "org:view_profile", "name": "View Organization Profile", "description": "Can view organization profile", "category": "organization", "scopeType": "org"}, {"identifier": "org:view_members", "name": "View Members", "description": "Can view member directory", "category": "organization", "scopeType": "org"}, {"identifier": "org:view_available_workspaces", "name": "View Available Workspaces", "description": "Can see workspaces they can join", "category": "workspace", "scopeType": "org"}, {"identifier": "org:request_workspace_access", "name": "Request Workspace Access", "description": "Can request to join workspaces", "category": "workspace", "scopeType": "org"}, {"identifier": "org:manage_own_profile", "name": "Manage Own Profile", "description": "Can manage personal profile", "category": "organization", "scopeType": "org"}, {"identifier": "org:manage_own_notifications", "name": "Manage Own Notifications", "description": "Can manage notification preferences", "category": "organization", "scopeType": "org"}, {"identifier": "org:manage_own_api_tokens", "name": "Manage Own API Tokens", "description": "Can manage personal API tokens", "category": "organization", "scopeType": "org"}, {"identifier": "org:use_org_integrations", "name": "Use Organization Integrations", "description": "Can use configured integrations", "category": "organization", "scopeType": "org"}, {"identifier": "org:view_org_announcements", "name": "View Organization Announcements", "description": "Can view organization announcements", "category": "organization", "scopeType": "org"}, {"identifier": "org:view_security_settings", "name": "View Security Settings", "description": "Can view security settings", "category": "security", "scopeType": "org"}, {"identifier": "org:archive_workspaces", "name": "Archive Workspaces", "description": "Can archive workspaces", "category": "workspace", "scopeType": "org"}, {"identifier": "org:manage_billing_contacts", "name": "Manage Billing Contacts", "description": "Can manage billing contact information", "category": "billing", "scopeType": "org"}, {"identifier": "org:view_usage_analytics", "name": "View Usage Analytics", "description": "Can view usage analytics and metrics", "category": "billing", "scopeType": "org"}, {"identifier": "org:set_spending_limits", "name": "Set Spending Limits", "description": "Can set spending limits and alerts", "category": "billing", "scopeType": "org"}, {"identifier": "org:generate_cost_reports", "name": "Generate Cost Reports", "description": "Can generate cost analysis reports", "category": "billing", "scopeType": "org"}, {"identifier": "org:export_billing_data", "name": "Export Billing Data", "description": "Can export billing data", "category": "billing", "scopeType": "org"}, {"identifier": "org:view_basic_profile", "name": "View Basic Profile", "description": "Can view basic organization information", "category": "organization", "scopeType": "org"}, {"identifier": "org:view_assigned_workspaces", "name": "View Assigned Workspaces", "description": "Can view workspaces they're invited to", "category": "workspace", "scopeType": "org"}, {"identifier": "ws:manage_settings", "name": "Manage Workspace Settings", "description": "Can modify workspace settings", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:update_details", "name": "Update Workspace Details", "description": "Can update workspace information", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:delete_workspace", "name": "Delete Workspace", "description": "Can delete the workspace", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:archive_workspace", "name": "Archive Workspace", "description": "Can archive the workspace", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:manage_workspace_templates", "name": "Manage Workspace Templates", "description": "Can create and manage workspace templates", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:invite_members", "name": "Invite Workspace Members", "description": "Can invite members to the workspace", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:remove_members", "name": "Remove Workspace Members", "description": "Can remove members from the workspace", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:manage_member_roles", "name": "Manage Workspace Member Roles", "description": "Can assign and modify workspace member roles", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:view_all_members", "name": "View All Workspace Members", "description": "Can view all workspace members", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:manage_guest_access", "name": "Manage Workspace Guest Access", "description": "Can manage guest access to workspace", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:create_projects", "name": "Create Projects", "description": "Can create new projects in the workspace", "category": "project", "scopeType": "ws"}, {"identifier": "ws:delete_any_project", "name": "Delete Any Project", "description": "Can delete any project in the workspace", "category": "project", "scopeType": "ws"}, {"identifier": "ws:archive_any_project", "name": "Archive Any Project", "description": "Can archive any project in the workspace", "category": "project", "scopeType": "ws"}, {"identifier": "ws:manage_all_projects", "name": "Manage All Projects", "description": "Can edit any project in the workspace", "category": "project", "scopeType": "ws"}, {"identifier": "ws:view_all_projects", "name": "View All Projects", "description": "Can view all projects including private ones", "category": "project", "scopeType": "ws"}, {"identifier": "ws:move_projects", "name": "Move Projects", "description": "Can move projects between workspaces", "category": "project", "scopeType": "ws"}, {"identifier": "ws:manage_project_templates", "name": "Manage Project Templates", "description": "Can create and manage project templates", "category": "project", "scopeType": "ws"}, {"identifier": "ws:manage_project_visibility", "name": "Manage Project Visibility", "description": "Can change project visibility settings", "category": "project", "scopeType": "ws"}, {"identifier": "ws:override_project_permissions", "name": "Override Project Permissions", "description": "Can override project-level permissions", "category": "project", "scopeType": "ws"}, {"identifier": "ws:manage_workspace_permissions", "name": "Manage Workspace Permissions", "description": "Can manage workspace-level permissions", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:delete_any_work_item", "name": "Delete Any Work Item", "description": "Can delete any work item in the workspace", "category": "work_item", "scopeType": "ws"}, {"identifier": "ws:bulk_edit_work_items", "name": "Bulk Edit Work Items", "description": "Can perform bulk operations on work items", "category": "work_item", "scopeType": "ws"}, {"identifier": "ws:manage_work_item_types", "name": "Manage Work Item Types", "description": "Can create and modify work item types", "category": "work_item", "scopeType": "ws"}, {"identifier": "ws:manage_custom_fields", "name": "Manage Custom Fields", "description": "Can create and manage custom fields", "category": "work_item", "scopeType": "ws"}, {"identifier": "ws:manage_workflows", "name": "Manage Workflows", "description": "Can create and modify workflows", "category": "workflow", "scopeType": "ws"}, {"identifier": "ws:create_automation_rules", "name": "Create Automation Rules", "description": "Can create workflow automation rules", "category": "workflow", "scopeType": "ws"}, {"identifier": "ws:manage_labels_and_tags", "name": "Manage Labels and Tags", "description": "Can create and manage labels and tags", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:manage_priorities", "name": "Manage Priorities", "description": "Can create and modify priority levels", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:manage_statuses", "name": "Manage Statuses", "description": "Can create and modify status options", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:export_workspace_data", "name": "Export Workspace Data", "description": "Can export all workspace data", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:view_workspace_analytics", "name": "View Workspace Analytics", "description": "Can view workspace analytics and metrics", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:create_workspace_reports", "name": "Create Workspace Reports", "description": "Can create custom workspace reports", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:manage_dashboards", "name": "Manage Dashboards", "description": "Can create and manage dashboards", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:view_audit_logs", "name": "View Workspace Audit Logs", "description": "Can view workspace audit logs", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:manage_workspace_integrations", "name": "Manage Workspace Integrations", "description": "Can configure workspace integrations", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:configure_webhooks", "name": "Configure Workspace Webhooks", "description": "Can configure workspace webhooks", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:manage_api_access", "name": "Manage API Access", "description": "Can manage workspace API access", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:manage_owned_projects", "name": "Manage Owned Projects", "description": "Can manage projects they create or lead", "category": "project", "scopeType": "ws"}, {"identifier": "ws:archive_projects", "name": "Archive Projects", "description": "Can archive projects", "category": "project", "scopeType": "ws"}, {"identifier": "ws:manage_project_members", "name": "Manage Project Members", "description": "Can manage project team members", "category": "project", "scopeType": "ws"}, {"identifier": "ws:assign_project_roles", "name": "Assign Project Roles", "description": "Can assign roles within projects", "category": "project", "scopeType": "ws"}, {"identifier": "ws:manage_team_capacity", "name": "Manage Team Capacity", "description": "Can manage team capacity planning", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:create_work_items", "name": "Create Work Items", "description": "Can create new work items", "category": "work_item", "scopeType": "ws"}, {"identifier": "ws:edit_any_work_item", "name": "Edit Any Work Item", "description": "Can edit any work item", "category": "work_item", "scopeType": "ws"}, {"identifier": "ws:delete_work_items", "name": "Delete Work Items", "description": "Can delete work items", "category": "work_item", "scopeType": "ws"}, {"identifier": "ws:bulk_assign_work_items", "name": "Bulk Assign Work Items", "description": "Can bulk assign work items", "category": "work_item", "scopeType": "ws"}, {"identifier": "ws:manage_sprints", "name": "Manage Sprints", "description": "Can create and manage sprints", "category": "project", "scopeType": "ws"}, {"identifier": "ws:manage_milestones", "name": "Manage Milestones", "description": "Can create and manage milestones", "category": "project", "scopeType": "ws"}, {"identifier": "ws:manage_releases", "name": "Manage Releases", "description": "Can create and manage releases", "category": "project", "scopeType": "ws"}, {"identifier": "ws:create_roadmaps", "name": "Create Roadmaps", "description": "Can create project roadmaps", "category": "project", "scopeType": "ws"}, {"identifier": "ws:manage_dependencies", "name": "Manage Dependencies", "description": "Can manage work item dependencies", "category": "work_item", "scopeType": "ws"}, {"identifier": "ws:set_due_dates", "name": "Set Due Dates", "description": "Can set and modify due dates", "category": "work_item", "scopeType": "ws"}, {"identifier": "ws:estimate_work", "name": "Estimate Work", "description": "Can provide work estimates", "category": "work_item", "scopeType": "ws"}, {"identifier": "ws:track_time", "name": "Track Time", "description": "Can track time on work items", "category": "work_item", "scopeType": "ws"}, {"identifier": "ws:create_project_reports", "name": "Create Project Reports", "description": "Can create project-level reports", "category": "project", "scopeType": "ws"}, {"identifier": "ws:export_project_data", "name": "Export Project Data", "description": "Can export project data", "category": "project", "scopeType": "ws"}, {"identifier": "ws:view_team_performance", "name": "View Team Performance", "description": "Can view team performance metrics", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:view_velocity_charts", "name": "View Velocity Charts", "description": "Can view velocity and burndown charts", "category": "project", "scopeType": "ws"}, {"identifier": "ws:customize_project_workflow", "name": "Customize Project Workflow", "description": "Can customize project-specific workflows", "category": "workflow", "scopeType": "ws"}, {"identifier": "ws:create_project_templates", "name": "Create Project Templates", "description": "Can create project templates", "category": "project", "scopeType": "ws"}, {"identifier": "ws:manage_project_labels", "name": "Manage Project Labels", "description": "Can manage project-specific labels", "category": "project", "scopeType": "ws"}, {"identifier": "ws:view_workspace", "name": "View Workspace", "description": "Can view workspace information", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:view_public_projects", "name": "View Public Projects", "description": "Can view public projects", "category": "project", "scopeType": "ws"}, {"identifier": "ws:view_assigned_projects", "name": "View Assigned Projects", "description": "Can view projects they're assigned to", "category": "project", "scopeType": "ws"}, {"identifier": "ws:view_workspace_members", "name": "View Workspace Members", "description": "Can view workspace member list", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:edit_own_work_items", "name": "Edit Own Work Items", "description": "Can edit work items they created", "category": "work_item", "scopeType": "ws"}, {"identifier": "ws:edit_assigned_work_items", "name": "Edit Assigned Work Items", "description": "Can edit work items assigned to them", "category": "work_item", "scopeType": "ws"}, {"identifier": "ws:delete_own_work_items", "name": "Delete Own Work Items", "description": "Can delete work items they created", "category": "work_item", "scopeType": "ws"}, {"identifier": "ws:assign_work_items_to_self", "name": "Assign Work Items to Self", "description": "Can assign work items to themselves", "category": "work_item", "scopeType": "ws"}, {"identifier": "ws:change_work_item_status", "name": "Change Work Item Status", "description": "Can change work item status", "category": "work_item", "scopeType": "ws"}, {"identifier": "ws:add_work_item_relations", "name": "Add Work Item Relations", "description": "Can add relationships between work items", "category": "work_item", "scopeType": "ws"}, {"identifier": "ws:create_comments", "name": "Create Comments", "description": "Can create comments on work items", "category": "collaboration", "scopeType": "ws"}, {"identifier": "ws:edit_own_comments", "name": "Edit Own Comments", "description": "Can edit their own comments", "category": "collaboration", "scopeType": "ws"}, {"identifier": "ws:delete_own_comments", "name": "Delete Own Comments", "description": "Can delete their own comments", "category": "collaboration", "scopeType": "ws"}, {"identifier": "ws:mention_members", "name": "Mention Members", "description": "Can mention other members", "category": "collaboration", "scopeType": "ws"}, {"identifier": "ws:create_discussions", "name": "Create Discussions", "description": "Can create discussion threads", "category": "collaboration", "scopeType": "ws"}, {"identifier": "ws:react_to_content", "name": "React to Content", "description": "Can add emoji reactions", "category": "collaboration", "scopeType": "ws"}, {"identifier": "ws:upload_attachments", "name": "Upload Attachments", "description": "Can upload file attachments", "category": "collaboration", "scopeType": "ws"}, {"identifier": "ws:delete_own_attachments", "name": "Delete Own Attachments", "description": "Can delete attachments they uploaded", "category": "collaboration", "scopeType": "ws"}, {"identifier": "ws:view_all_attachments", "name": "View All Attachments", "description": "Can view all attachments", "category": "collaboration", "scopeType": "ws"}, {"identifier": "ws:create_personal_views", "name": "Create Personal Views", "description": "Can create personal saved views", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:save_filters", "name": "Save Filters", "description": "Can save search filters", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:subscribe_to_items", "name": "Subscribe to Items", "description": "Can subscribe to work items", "category": "collaboration", "scopeType": "ws"}, {"identifier": "ws:manage_notifications", "name": "Manage Notifications", "description": "Can manage notification preferences", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:track_own_time", "name": "Track Own Time", "description": "Can track their own time", "category": "work_item", "scopeType": "ws"}, {"identifier": "ws:view_own_activity", "name": "View Own Activity", "description": "Can view their own activity log", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:export_own_data", "name": "Export Own Data", "description": "Can export their own data", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:view_project_timeline", "name": "View Project Timeline", "description": "Can view project timelines", "category": "project", "scopeType": "ws"}, {"identifier": "ws:create_work_items_in_assigned_projects", "name": "Create Work Items in Assigned Projects", "description": "Can create work items in assigned projects", "category": "work_item", "scopeType": "ws"}, {"identifier": "ws:view_project_members", "name": "View Project Members", "description": "Can view project team members", "category": "project", "scopeType": "ws"}, {"identifier": "ws:add_work_estimates", "name": "Add Work Estimates", "description": "Can add work estimates", "category": "work_item", "scopeType": "ws"}, {"identifier": "ws:upload_attachments_to_own_items", "name": "Upload Attachments to Own Items", "description": "Can upload attachments to their own items", "category": "collaboration", "scopeType": "ws"}, {"identifier": "ws:mention_assigned_members", "name": "Mention Assigned Members", "description": "Can mention members in context", "category": "collaboration", "scopeType": "ws"}, {"identifier": "ws:view_work_items", "name": "View Work Items", "description": "Can view work items", "category": "work_item", "scopeType": "ws"}, {"identifier": "ws:view_comments", "name": "View Comments", "description": "Can view comments", "category": "collaboration", "scopeType": "ws"}, {"identifier": "ws:view_attachments", "name": "View Attachments", "description": "Can view attachments", "category": "collaboration", "scopeType": "ws"}, {"identifier": "ws:export_visible_data", "name": "Export Visible Data", "description": "Can export data they can see", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:search_workspace", "name": "Search Workspace", "description": "Can search within workspace", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:use_filters", "name": "Use Filters", "description": "Can use search filters", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:save_personal_views", "name": "Save Personal Views", "description": "Can save personal view configurations", "category": "workspace", "scopeType": "ws"}, {"identifier": "ws:view_assigned_work_items", "name": "View Assigned Work Items", "description": "Can view specific assigned work items", "category": "work_item", "scopeType": "ws"}, {"identifier": "ws:view_related_comments", "name": "View Related Comments", "description": "Can view comments on visible items", "category": "collaboration", "scopeType": "ws"}, {"identifier": "ws:create_comments_on_assigned_items", "name": "Create Comments on Assigned Items", "description": "Can comment on assigned items", "category": "collaboration", "scopeType": "ws"}, {"identifier": "ws:view_mentioned_in", "name": "View Mentioned In", "description": "Can view items where they're mentioned", "category": "collaboration", "scopeType": "ws"}]