[{"name": "Super Admin", "identifier": "super_admin", "level": "app", "description": "Super administrator with full access to all features"}, {"name": "Organization Owner", "identifier": "org_owner", "level": "organization", "description": "Full control over the organization"}, {"name": "Organization Admin", "identifier": "org_admin", "level": "organization", "description": "Administrative access without billing control"}, {"name": "Organization Member", "identifier": "org_member", "level": "organization", "description": "Standard organization member"}, {"name": "Billing Manager", "identifier": "org_billing", "level": "organization", "description": "Dedicated billing and financial access"}, {"name": "Guest", "identifier": "org_guest", "level": "organization", "description": "External user with minimal access"}, {"name": "Workspace Admin", "identifier": "ws_admin", "level": "workspace", "description": "Full control over the workspace"}, {"name": "Project Manager", "identifier": "ws_manager", "level": "workspace", "description": "Can manage projects and coordinate teams"}, {"name": "Team Member", "identifier": "ws_member", "level": "workspace", "description": "Standard team member access"}, {"name": "Contributor", "identifier": "ws_contributor", "level": "workspace", "description": "Limited contributor access"}, {"name": "Viewer", "identifier": "ws_viewer", "level": "workspace", "description": "Read-only access"}, {"name": "Guest", "identifier": "ws_guest", "level": "workspace", "description": "Minimal guest access"}, {"identifier": "proj_lead", "name": "Project Lead", "level": "project", "description": "Leads the project with full access"}, {"identifier": "proj_member", "name": "Project Member", "level": "project", "description": "Standard member of the project"}, {"identifier": "proj_guest", "name": "Project Guest", "level": "project", "description": "External user with limited project access"}]