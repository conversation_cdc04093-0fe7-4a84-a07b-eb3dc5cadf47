import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import * as schema from '../schema';
import permissionSeeds from './data/permissions.json' with { type: 'json' };

export default async function seedPermissions(db: PostgresJsDatabase<typeof schema>) {
  // Insert permissions in a single batch
  const insertedPermissions = await db
    .insert(schema.permission)
    .values(permissionSeeds)
    .returning();
  return insertedPermissions;
}
