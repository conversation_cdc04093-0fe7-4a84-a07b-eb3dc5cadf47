import { randomUUID } from 'node:crypto';
import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import * as schema from '../schema';
import statusCategorySeeds from './data/status-categories.json' with { type: 'json' };

export default async function seedStatusCategories(db: PostgresJsDatabase<typeof schema>) {
  // Add UUIDs to the seed data
  const statusCategoriesWithIds = statusCategorySeeds.map((statusCategory) => ({
    id: randomUUID(),
    ...statusCategory,
  }));

  // Insert status categories in a single batch
  const insertedStatusCategories = await db
    .insert(schema.statusCategory)
    .values(statusCategoriesWithIds)
    .returning();
  return insertedStatusCategories;
}
