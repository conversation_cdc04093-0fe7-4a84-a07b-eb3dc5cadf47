import { relations } from 'drizzle-orm';
import { boolean, integer, pgTable, text, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { requiredTextField, textField } from './validation-utils';
import workflow, { selectWorkflowSchema } from './workflow';
import workflowStatus, { selectWorkflowStatusSchema } from './workflow-status';

const workflowTransition = pgTable('workflow_transition', {
  id: uuid('id').primaryKey().defaultRandom(),
  workflowId: uuid('workflow_id')
    .references(() => workflow.id)
    .notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  fromStatusId: uuid('from_status_id').references(() => workflowStatus.id),
  toStatusId: uuid('to_status_id')
    .references(() => workflowStatus.id)
    .notNull(),
  isInitial: boolean('is_initial').default(false).notNull(),
  isGlobal: boolean('is_global').default(false).notNull(),
  order: integer('order').default(0).notNull(),
  buttonText: varchar('button_text', { length: 100 }),
  confirmationMessage: text('confirmation_message'),
  screenId: uuid('screen_id'),
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const workflowTransitionRelations = relations(workflowTransition, ({ one }) => ({
  workflow: one(workflow, {
    fields: [workflowTransition.workflowId],
    references: [workflow.id],
  }),
  fromStatus: one(workflowStatus, {
    fields: [workflowTransition.fromStatusId],
    references: [workflowStatus.id],
  }),
  toStatus: one(workflowStatus, {
    fields: [workflowTransition.toStatusId],
    references: [workflowStatus.id],
  }),
}));

export default workflowTransition;

// Zod schemas
export const selectWorkflowTransitionSchema = createSelectSchema(workflowTransition).extend({
  workflow: selectWorkflowSchema.partial().nullable(),
  fromStatus: selectWorkflowStatusSchema.partial().nullable(),
  toStatus: selectWorkflowStatusSchema.partial().nullable(),
});

export const insertWorkflowTransitionSchema = createInsertSchema(workflowTransition, {
  name: () => requiredTextField(1, 255),
  description: () => textField(),
  buttonText: () => textField(100),
  confirmationMessage: () => textField(),
})
  .required({
    workflowId: true,
    name: true,
    toStatusId: true,
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchWorkflowTransitionSchema = insertWorkflowTransitionSchema.partial();

// Types
export type WorkflowTransition = typeof workflowTransition.$inferSelect;
export type WorkflowTransitionInsert = typeof insertWorkflowTransitionSchema._type;
export type WorkflowTransitionPatch = typeof patchWorkflowTransitionSchema._type;
export type WorkflowTransitionSelect = typeof selectWorkflowTransitionSchema._type;
