import { relations } from 'drizzle-orm';
import type { AnyPgColumn } from 'drizzle-orm/pg-core';
import {
  boolean,
  integer,
  jsonb,
  pgTable,
  text,
  timestamp,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { z } from 'zod';
import priority from './priority';
import project, { selectProjectSchema } from './project';
import status from './status';
import user, { selectUserSchema } from './user';
import { jsonField, requiredTextField, textField } from './validation-utils';
import workItem, { selectWorkItemSchema } from './work-item';

const testCase = pgTable('test_case', {
  id: uuid('id').primaryKey().defaultRandom(),
  title: varchar('title', { length: 500 }).notNull(),
  description: text('description'),
  projectId: uuid('project_id')
    .references(() => project.id)
    .notNull(),
  assigneeId: uuid('assignee_id').references(() => user.id),
  reporterId: uuid('reporter_id').references(() => user.id),
  workItemId: uuid('work_item_id').references(() => workItem.id),
  copiedFromId: uuid('copied_from_id').references((): AnyPgColumn => testCase.id, {
    onDelete: 'set null',
  }),
  preconditions: text('preconditions'),
  steps: jsonb('steps')
    .$type<Array<{ step: string; expectedResult: string }>>()
    .$default(() => [])
    .notNull(),
  tags: jsonb('tags')
    .$type<string[]>()
    .$default(() => [])
    .notNull(),
  estimate: jsonb('estimate')
    .$type<object>()
    .$default(() => ({}))
    .notNull(),
  order: integer('order').default(0).notNull(),
  isActive: boolean('is_active').default(true).notNull(),
  createdBy: uuid('created_by').references(() => user.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const testCaseRelations = relations(testCase, ({ one }) => ({
  project: one(project, {
    fields: [testCase.projectId],
    references: [project.id],
  }),
  assignee: one(user, {
    fields: [testCase.assigneeId],
    references: [user.id],
    relationName: 'assignedTestCases',
  }),
  reporter: one(user, {
    fields: [testCase.reporterId],
    references: [user.id],
    relationName: 'reportedTestCases',
  }),
  creator: one(user, {
    fields: [testCase.createdBy],
    references: [user.id],
    relationName: 'createdTestCases',
  }),
  workItem: one(workItem, {
    fields: [testCase.workItemId],
    references: [workItem.id],
  }),
  copiedFrom: one(testCase, {
    fields: [testCase.copiedFromId],
    references: [testCase.id],
    relationName: 'copiedFrom',
  }),
  // comments: many(testCaseComment), // Commented out to avoid circular dependency
  // workItemLinks: many(testCaseWorkItemLink), // Commented out to avoid circular dependency
  // history: many(testCaseHistory), // Commented out to avoid circular dependency
  // testSuites: many(testSuiteTestCase), // Commented out to avoid circular dependency
}));

export default testCase;

// Zod schemas
export const selectTestCaseSchema = createSelectSchema(testCase, {
  steps: () =>
    z
      .array(
        z.object({
          step: z.string(),
          expectedResult: z.string(),
        }),
      )
      .default([]),
  tags: () => z.array(z.string()).default([]),
  estimate: () => z.object({}).passthrough().default({}),
}).extend({
  project: selectProjectSchema.partial().nullable().optional(),
  assignee: selectUserSchema.partial().nullable().optional(),
  reporter: selectUserSchema.partial().nullable().optional(),
  creator: selectUserSchema.partial().nullable().optional(),
  workItem: selectWorkItemSchema.partial().nullable().optional(),
});

export const insertTestCaseSchema = createInsertSchema(testCase, {
  title: () => requiredTextField(1, 500),
  description: () => textField(),
  preconditions: () => textField(),
  steps: () =>
    z
      .array(
        z.object({
          step: z.string(),
          expectedResult: z.string(),
        }),
      )
      .default([]),
  tags: () => z.array(z.string()).default([]),
  estimate: jsonField,
})
  .extend({
    priorityId: z.string().uuid().nullable().optional(),
    statusId: z.string().uuid().nullable().optional(),
  })
  .required({
    title: true,
    projectId: true,
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchTestCaseSchema = insertTestCaseSchema.partial();

// Types
export type TestCase = typeof testCase.$inferSelect;
export type TestCaseInsert = typeof insertTestCaseSchema._type;
export type TestCasePatch = typeof patchTestCaseSchema._type;
export type TestCaseSelect = typeof selectTestCaseSchema._type;
