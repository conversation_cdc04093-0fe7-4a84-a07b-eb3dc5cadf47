import { relations } from 'drizzle-orm';
import {
  boolean,
  pgEnum,
  pgTable,
  text,
  timestamp,
  unique,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import user, { selectUserSchema } from './user';
import { datetimeField, textField } from './validation-utils';
import workItem, { selectWorkItemSchema } from './work-item';

// Define link type enum
export const workItemLinkTypeEnum = pgEnum('work_item_link_type', [
  'is_caused_by',
  'blocked_by',
  'blocks',
  'relates_to',
  'is_child_of',
  'is_duplicated_by',
  'duplicates',
]);

const workItemLink = pgTable(
  'work_item_link',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    sourceWorkItemId: uuid('source_work_item_id').notNull(),
    targetWorkItemId: uuid('target_work_item_id').notNull(),
    linkType: workItemLinkTypeEnum('link_type').notNull(),
    title: varchar('title', { length: 500 }),
    description: text('description'),
    createdById: uuid('created_by_id')
      .references(() => user.id)
      .notNull(),
    isActive: boolean('is_active').default(true).notNull(),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (table) => ({
    // Prevent duplicate links between same work items with same type
    uniqueLink: unique().on(table.sourceWorkItemId, table.targetWorkItemId, table.linkType),
  }),
);

export const workItemLinkRelations = relations(workItemLink, ({ one }) => ({
  sourceWorkItem: one(workItem, {
    fields: [workItemLink.sourceWorkItemId],
    references: [workItem.id],
    relationName: 'sourceLinks',
  }),
  targetWorkItem: one(workItem, {
    fields: [workItemLink.targetWorkItemId],
    references: [workItem.id],
    relationName: 'targetLinks',
  }),
  createdBy: one(user, {
    fields: [workItemLink.createdById],
    references: [user.id],
  }),
}));

export default workItemLink;

// Zod schemas
export const selectWorkItemLinkSchema = createSelectSchema(workItemLink).extend({
  createdBy: selectUserSchema.partial().nullable(),
  sourceWorkItem: selectWorkItemSchema.partial().nullable(),
  targetWorkItem: selectWorkItemSchema.partial().nullable(),
});

export const insertWorkItemLinkSchema = createInsertSchema(workItemLink, {
  title: () => textField(500),
  description: () => textField(),
  createdAt: datetimeField(),
  updatedAt: datetimeField(),
})
  .required({
    sourceWorkItemId: true,
    targetWorkItemId: true,
    linkType: true,
    createdById: true,
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchWorkItemLinkSchema = insertWorkItemLinkSchema.partial();

// Types
export type WorkItemLink = typeof workItemLink.$inferSelect;
export type WorkItemLinkInsert = typeof insertWorkItemLinkSchema._type;
export type WorkItemLinkPatch = typeof patchWorkItemLinkSchema._type;
export type WorkItemLinkSelect = typeof selectWorkItemLinkSchema._type;
