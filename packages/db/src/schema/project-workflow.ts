import { relations } from 'drizzle-orm';
import { boolean, pgTable, timestamp, uuid } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import project, { selectProjectSchema } from './project';
import workflow, { selectWorkflowSchema } from './workflow';

const projectWorkflow = pgTable('project_workflow', {
  id: uuid('id').primaryKey().defaultRandom(),
  projectId: uuid('project_id')
    .references(() => project.id)
    .notNull(),
  workflowId: uuid('workflow_id')
    .references(() => workflow.id)
    .notNull(),
  isActive: boolean('is_active').default(true).notNull(),
  isDefault: boolean('is_default').default(false).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const projectWorkflowRelations = relations(projectWorkflow, ({ one }) => ({
  project: one(project, {
    fields: [projectWorkflow.projectId],
    references: [project.id],
  }),
  workflow: one(workflow, {
    fields: [projectWorkflow.workflowId],
    references: [workflow.id],
  }),
}));

export default projectWorkflow;

// Zod schemas
export const selectProjectWorkflowSchema = createSelectSchema(projectWorkflow).extend({
  project: selectProjectSchema.partial().nullable(),
  workflow: selectWorkflowSchema.partial().nullable(),
});

export const insertProjectWorkflowSchema = createInsertSchema(projectWorkflow)
  .required({
    projectId: true,
    workflowId: true,
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchProjectWorkflowSchema = insertProjectWorkflowSchema.partial();

// Types
export type ProjectWorkflow = typeof projectWorkflow.$inferSelect;
export type ProjectWorkflowInsert = typeof insertProjectWorkflowSchema._type;
export type ProjectWorkflowPatch = typeof patchProjectWorkflowSchema._type;
export type ProjectWorkflowSelect = typeof selectProjectWorkflowSchema._type;
