import { relations } from 'drizzle-orm';
import {
  boolean,
  integer,
  pgTable,
  text,
  timestamp,
  unique,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { z } from 'zod';

const user = pgTable(
  'user',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    email: varchar('email', { length: 255 }).notNull().unique(),
    firstName: varchar('first_name', { length: 255 }).notNull(),
    lastName: varchar('last_name', { length: 255 }).notNull(),
    displayName: varchar('display_name', { length: 255 }).notNull(),
    password: varchar('password', { length: 255 }).notNull(),
    isActive: boolean('is_active').default(true).notNull(),
    isStaff: boolean('is_staff').default(false).notNull(),
    isEmailVerified: boolean('is_email_verified').default(false).notNull(),
    lastLogin: timestamp('last_login'),
    avatarUrl: varchar('avatar_url', { length: 255 }),
    bio: text('bio'),
    jobTitle: varchar('job_title', { length: 255 }),
    department: varchar('department', { length: 255 }),
    phone: varchar('phone', { length: 50 }),
    timezone: varchar('timezone', { length: 50 }).default('UTC').notNull(),
    language: varchar('language', { length: 10 }).default('en').notNull(),
    dateFormat: varchar('date_format', { length: 20 }).default('MM/DD/YYYY').notNull(),
    timeFormat: varchar('time_format', { length: 10 }).default('12h').notNull(),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
    passwordResetToken: varchar('password_reset_token', { length: 512 }),
    passwordResetExpires: timestamp('password_reset_expires'),
    emailVerificationToken: varchar('email_verification_token', {
      length: 512,
    }),
    loginAttempts: integer('login_attempts').default(0).notNull(),
    lockedUntil: timestamp('locked_until'),
  },
  (table) => [unique('email_idx').on(table.email)],
);

// Relations can be added here when we have the theme schema
export const userRelations = relations(user, ({}) => ({
  // remved "one" from params to avoide unsed error
  // theme: one(theme, {
  //   fields: [user.themeId],
  //   references: [theme.id],
  // }),
}));

export default user;

// Zod schemas
export const selectUserSchema = createSelectSchema(user);

export const insertUserSchema = createInsertSchema(user, {
  email: (schema) => schema.email.email(),
  firstName: (schema) => schema.firstName.min(1).max(255),
  lastName: (schema) => schema.lastName.min(1).max(255),
  displayName: (schema) => schema.displayName.min(1).max(255),
  password: (schema) => schema.password.min(8).max(255),
  phone: () => z.string().min(10).max(50).optional(),
})
  .required({
    email: true,
    firstName: true,
    lastName: true,
    displayName: true,
    password: true,
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchUserSchema = insertUserSchema.partial();

// Types
export type User = typeof user.$inferSelect;
export type UserInsert = typeof insertUserSchema._type;
export type UserPatch = typeof patchUserSchema._type;
export type UserSelect = typeof selectUserSchema._type;
