import { relations } from 'drizzle-orm';
import { boolean, pgTable, text, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { requiredTextField, textField } from './validation-utils';

const statusCategory = pgTable('status_category', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 100 }).notNull().unique(),
  description: text('description'),
  color: varchar('color', { length: 7 }),
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const statusCategoryRelations = relations(statusCategory, ({}) => ({}));

export default statusCategory;

// Zod schemas
export const selectStatusCategorySchema = createSelectSchema(statusCategory);

export const insertStatusCategorySchema = createInsertSchema(statusCategory, {
  name: () => requiredTextField(1, 100),
  description: () => textField(),
  color: () => textField(7),
})
  .required({
    name: true,
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchStatusCategorySchema = insertStatusCategorySchema.partial();

// Types
export type StatusCategory = typeof statusCategory.$inferSelect;
export type StatusCategoryInsert = typeof insertStatusCategorySchema._type;
export type StatusCategoryPatch = typeof patchStatusCategorySchema._type;
export type StatusCategorySelect = typeof selectStatusCategorySchema._type;
