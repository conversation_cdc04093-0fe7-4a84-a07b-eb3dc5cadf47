import { relations } from 'drizzle-orm';
import {
  boolean,
  date,
  integer,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import project, { selectProjectSchema } from './project';
import user, { selectUserSchema } from './user';
import { dateField, requiredTextField, textField } from './validation-utils';

export const sprintStatusEnum = pgEnum('sprint_status', [
  'draft',
  'active',
  'completed',
  'archived',
]);

const sprint = pgTable('sprint', {
  id: uuid('id').primaryKey().defaultRandom(),
  projectId: uuid('project_id')
    .references(() => project.id)
    .notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  goal: text('goal'),
  startDate: date('start_date'),
  endDate: date('end_date'),
  capacity: integer('capacity'),
  velocity: integer('velocity'),
  status: sprintStatusEnum('status').default('draft').notNull(),
  isActive: boolean('is_active').default(true).notNull(),
  createdBy: uuid('created_by').references(() => user.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const sprintRelations = relations(sprint, ({ one }) => ({
  project: one(project, {
    fields: [sprint.projectId],
    references: [project.id],
  }),
  creator: one(user, {
    fields: [sprint.createdBy],
    references: [user.id],
  }),
}));

export default sprint;

// Zod schemas
export const selectSprintSchema = createSelectSchema(sprint, {
  startDate: dateField,
  endDate: dateField,
}).extend({
  project: selectProjectSchema.partial().nullable(),
  creator: selectUserSchema.partial().nullable(),
});

export const insertSprintSchema = createInsertSchema(sprint, {
  name: () => requiredTextField(1, 255),
  goal: () => textField(),
  startDate: dateField,
  endDate: dateField,
})
  .required({
    projectId: true,
    name: true,
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchSprintSchema = insertSprintSchema.partial();

// Types
export type Sprint = typeof sprint.$inferSelect;
export type SprintInsert = typeof insertSprintSchema._type;
export type SprintPatch = typeof patchSprintSchema._type;
export type SprintSelect = typeof selectSprintSchema._type;
