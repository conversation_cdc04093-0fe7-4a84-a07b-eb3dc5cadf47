import { relations } from 'drizzle-orm';
import { boolean, pgTable, text, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import organization, { selectOrganizationSchema } from './organization';
import { requiredTextField, textField } from './validation-utils';

const resolution = pgTable('resolution', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 100 }).notNull(),
  description: text('description'),
  organizationId: uuid('organization_id').references(() => organization.id),
  isSystem: boolean('is_system').default(false).notNull(),
  color: varchar('color', { length: 7 }),
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const resolutionRelations = relations(resolution, ({ one }) => ({
  organization: one(organization, {
    fields: [resolution.organizationId],
    references: [organization.id],
  }),
}));

export default resolution;

// Zod schemas
export const selectResolutionSchema = createSelectSchema(resolution).extend({
  organization: selectOrganizationSchema.partial().nullable(),
});

export const insertResolutionSchema = createInsertSchema(resolution, {
  name: () => requiredTextField(1, 100),
  description: () => textField(),
  color: () => textField(7),
})
  .required({
    name: true,
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchResolutionSchema = insertResolutionSchema.partial();

// Types
export type Resolution = typeof resolution.$inferSelect;
export type ResolutionInsert = typeof insertResolutionSchema._type;
export type ResolutionPatch = typeof patchResolutionSchema._type;
export type ResolutionSelect = typeof selectResolutionSchema._type;
