import { relations } from 'drizzle-orm';
import type { AnyPgColumn } from 'drizzle-orm/pg-core';
import { boolean, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import testPlan from './test-plan';
import user, { selectUserSchema } from './user';
import { requiredTextField } from './validation-utils';

const testPlanComment = pgTable('test_plan_comment', {
  id: uuid('id').primaryKey().defaultRandom(),
  testPlanId: uuid('test_plan_id')
    .references(() => testPlan.id, { onDelete: 'cascade' })
    .notNull(),
  content: text('content').notNull(),
  authorId: uuid('author_id')
    .references(() => user.id)
    .notNull(),
  parentId: uuid('parent_id').references((): AnyPgColumn => testPlanComment.id, {
    onDelete: 'cascade',
  }),
  isEdited: boolean('is_edited').default(false).notNull(),
  isDeleted: boolean('is_deleted').default(false).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const testPlanCommentRelations = relations(testPlanComment, ({ one, many }) => ({
  testPlan: one(testPlan, {
    fields: [testPlanComment.testPlanId],
    references: [testPlan.id],
  }),
  author: one(user, {
    fields: [testPlanComment.authorId],
    references: [user.id],
  }),
  parent: one(testPlanComment, {
    fields: [testPlanComment.parentId],
    references: [testPlanComment.id],
    relationName: 'parentReply',
  }),
  replies: many(testPlanComment, {
    relationName: 'parentReply',
  }),
}));

export default testPlanComment;

// Zod schemas
export const selectTestPlanCommentSchema = createSelectSchema(testPlanComment).extend({
  author: selectUserSchema.partial().nullable(),
});

export const insertTestPlanCommentSchema = createInsertSchema(testPlanComment, {
  content: () => requiredTextField(1),
})
  .required({
    testPlanId: true,
    content: true,
    authorId: true,
  })
  .omit({
    id: true,
    isDeleted: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchTestPlanCommentSchema = insertTestPlanCommentSchema
  .partial()
  .pick({ content: true, isEdited: true });

// Types
export type TestPlanComment = typeof testPlanComment.$inferSelect;
export type TestPlanCommentInsert = typeof insertTestPlanCommentSchema._type;
export type TestPlanCommentPatch = typeof patchTestPlanCommentSchema._type;
export type TestPlanCommentSelect = typeof selectTestPlanCommentSchema._type;
