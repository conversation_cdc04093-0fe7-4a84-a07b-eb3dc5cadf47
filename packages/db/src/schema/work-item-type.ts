import { relations } from 'drizzle-orm';
import { boolean, jsonb, pgTable, text, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import organization, { selectOrganizationSchema } from './organization';
import { jsonField, requiredTextField, textField } from './validation-utils';

const workItemType = pgTable('work_item_type', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 100 }).notNull(),
  description: text('description'),
  organizationId: uuid('organization_id').references(() => organization.id),
  isSystem: boolean('is_system').default(false).notNull(),
  icon: varchar('icon', { length: 255 }),
  color: varchar('color', { length: 7 }),
  data: jsonb('data')
    .$type<object>()
    .$default(() => ({}))
    .notNull(),
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const workItemTypeRelations = relations(workItemType, ({ one }) => ({
  organization: one(organization, {
    fields: [workItemType.organizationId],
    references: [organization.id],
  }),
}));

export default workItemType;

// Zod schemas
export const selectWorkItemTypeSchema = createSelectSchema(workItemType, {
  data: jsonField,
}).extend({
  organization: selectOrganizationSchema.partial().nullable(),
});

export const insertWorkItemTypeSchema = createInsertSchema(workItemType, {
  name: () => requiredTextField(1, 100),
  description: () => textField(),
  icon: () => textField(255),
  color: () => textField(7),
  data: jsonField,
})
  .required({
    name: true,
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchWorkItemTypeSchema = insertWorkItemTypeSchema.partial();

// Types
export type WorkItemType = typeof workItemType.$inferSelect;
export type WorkItemTypeInsert = typeof insertWorkItemTypeSchema._type;
export type WorkItemTypePatch = typeof patchWorkItemTypeSchema._type;
export type WorkItemTypeSelect = typeof selectWorkItemTypeSchema._type;
