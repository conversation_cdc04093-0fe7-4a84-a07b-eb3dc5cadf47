import { relations } from 'drizzle-orm';
import { bigint, boolean, pgEnum, pgTable, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import type { z } from 'zod';
import { selectOrganizationSchema } from './organization';
import { selectProjectSchema } from './project';
import user, { selectUserSchema } from './user';
import { requiredTextField, textField } from './validation-utils';
import { selectWorkItemSchema } from './work-item';
import { selectWorkspaceSchema } from './workspace';

export const attachmentEntityTypeEnum = pgEnum('attachment_entity_type', [
  'work_item',
  'project',
  'workspace',
  'organization',
  'comment',
  'test_case',
  'test_plan',
  'test_suite',
  'test_run',
  'user',
]);

const attachment = pgTable('attachment', {
  id: uuid('id').primaryKey().defaultRandom(),
  entityType: attachmentEntityTypeEnum('entity_type').notNull(),
  entityId: uuid('entity_id').notNull(),
  fileName: varchar('file_name', { length: 255 }).notNull(),
  url: varchar('url', { length: 500 }).notNull(),
  fileType: varchar('file_type', { length: 100 }),
  size: bigint('size', { mode: 'number' }),
  isActive: boolean('is_active').default(true).notNull(),
  uploadedBy: uuid('uploaded_by').references(() => user.id),
  uploadedAt: timestamp('uploaded_at').defaultNow().notNull(),
});

export const attachmentRelations = relations(attachment, ({ one }) => ({
  uploader: one(user, {
    fields: [attachment.uploadedBy],
    references: [user.id],
  }),
}));

export default attachment;

// Zod schemas
export const selectAttachmentSchema = createSelectSchema(attachment).extend({
  uploader: selectUserSchema.partial().nullable(),
  organization: selectOrganizationSchema.partial().nullable(),
  workspace: selectWorkspaceSchema.partial().nullable(),
  project: selectProjectSchema.partial().nullable(),
  workItem: selectWorkItemSchema.partial().nullable(),
});

export const insertAttachmentSchema = createInsertSchema(attachment, {
  fileName: () => requiredTextField(1, 255),
  url: () => requiredTextField(1, 500),
  fileType: () => textField(100),
})
  .required({
    entityType: true,
    entityId: true,
    fileName: true,
    url: true,
  })
  .omit({
    id: true,
    uploadedAt: true,
  });

export const patchAttachmentSchema = insertAttachmentSchema.partial();

// Types
export type Attachment = typeof attachment.$inferSelect;
export type AttachmentInsert = z.infer<typeof insertAttachmentSchema>;
export type AttachmentPatch = z.infer<typeof patchAttachmentSchema>;
export type AttachmentSelect = z.infer<typeof selectAttachmentSchema>;
