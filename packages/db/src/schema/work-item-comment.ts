import { relations } from 'drizzle-orm';
import type { AnyPgColumn } from 'drizzle-orm/pg-core';
import { boolean, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import user, { selectUserSchema } from './user';
import { requiredTextField } from './validation-utils';
import workItem from './work-item';

const workItemComment = pgTable('work_item_comment', {
  id: uuid('id').primaryKey().defaultRandom(),
  workItemId: uuid('work_item_id')
    .references(() => workItem.id)
    .notNull(),
  content: text('content').notNull(),
  authorId: uuid('author_id')
    .references(() => user.id)
    .notNull(),
  parentId: uuid('parent_id').references((): AnyPgColumn => workItemComment.id),
  isEdited: boolean('is_edited').default(false).notNull(),
  isDeleted: boolean('is_deleted').default(false).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const workItemCommentRelations = relations(workItemComment, ({ one, many }) => ({
  workItem: one(workItem, {
    fields: [workItemComment.workItemId],
    references: [workItem.id],
  }),
  author: one(user, {
    fields: [workItemComment.authorId],
    references: [user.id],
  }),
  parent: one(workItemComment, {
    fields: [workItemComment.parentId],
    references: [workItemComment.id],
    relationName: 'parentReply',
  }),
  replies: many(workItemComment, {
    relationName: 'parentReply',
  }),
}));

export default workItemComment;

// Zod schemas
export const selectWorkItemCommentSchema = createSelectSchema(workItemComment).extend({
  author: selectUserSchema.partial().nullable(),
});

export const insertWorkItemCommentSchema = createInsertSchema(workItemComment, {
  content: () => requiredTextField(1),
})
  .required({
    workItemId: true,
    content: true,
    authorId: true,
  })
  .omit({
    id: true,
    isDeleted: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchWorkItemCommentSchema = insertWorkItemCommentSchema
  .partial()
  .pick({ content: true, isEdited: true });

// Types
export type WorkItemComment = typeof workItemComment.$inferSelect;
export type WorkItemCommentInsert = typeof insertWorkItemCommentSchema._type;
export type WorkItemCommentPatch = typeof patchWorkItemCommentSchema._type;
export type WorkItemCommentSelect = typeof selectWorkItemCommentSchema._type;
