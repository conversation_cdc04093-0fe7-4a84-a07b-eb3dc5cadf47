import { z } from 'zod';
import { insertOrganizationSchema } from './organization';
import { insertUserSchema } from './user';

// Supporting Models
export {
  type AttachmentInsert,
  attachmentEntityTypeEnum,
  attachmentRelations,
  default as attachment,
  insertAttachmentSchema,
  patchAttachmentSchema,
  selectAttachmentSchema,
} from './attachment';
export {
  auditActionEnum,
  auditLogRelations,
  default as auditLog,
  insertAuditLogSchema,
  patchAuditLogSchema,
  selectAuditLogSchema,
} from './audit-log';
export {
  default as invitation,
  insertInvitationSchema,
  invitationRelations,
  invitationScopeEnum,
  invitationStatusEnum,
  patchInvitationSchema,
  selectInvitationSchema,
} from './invitation';
export {
  type CreateNotification,
  createNotificationSchema,
  default as notification,
  insertNotificationSchema,
  type Notification,
  type PatchNotification,
  patchNotificationSchema,
  selectNotificationSchema,
} from './notification';
// Core Entities
export {
  default as organization,
  insertOrganizationSchema,
  type OrganizationInsert,
  organizationRelations,
  patchOrganizationSchema,
  selectOrganizationSchema,
} from './organization';
export { default as permission } from './permission';
export {
  default as priority,
  insertPrioritySchema,
  patchPrioritySchema,
  priorityRelations,
  selectPrioritySchema,
} from './priority';
export {
  default as project,
  insertProjectSchema,
  type ProjectInsert,
  patchProjectSchema,
  projectRelations,
  projectStatusEnum,
  projectVisibilityEnum,
  selectProjectSchema,
} from './project';
// Agile Work Management
export {
  default as projectWorkflow,
  insertProjectWorkflowSchema,
  patchProjectWorkflowSchema,
  projectWorkflowRelations,
  selectProjectWorkflowSchema,
} from './project-workflow';
export {
  default as resolution,
  insertResolutionSchema,
  patchResolutionSchema,
  resolutionRelations,
  selectResolutionSchema,
} from './resolution';
export {
  default as role,
  insertRoleSchema,
  patchRoleSchema,
  type RoleInsert,
  roleRelations,
  selectRoleSchema,
} from './role';
// Access Control
export {
  default as roleAssignment,
  insertRoleAssignmentSchema,
  patchRoleAssignmentSchema,
  roleAssignmentRelations,
  roleAssignmentScopeEnum,
  selectRoleAssignmentSchema,
} from './role-assignment';
export { default as rolePermission } from './role-permission';
export {
  default as sprint,
  insertSprintSchema,
  patchSprintSchema,
  selectSprintSchema,
  sprintRelations,
  sprintStatusEnum,
} from './sprint';
export {
  default as status,
  insertStatusSchema,
  patchStatusSchema,
  selectStatusSchema,
  statusRelations,
  statusTypeEnum,
} from './status';
// System Configuration
export {
  default as statusCategory,
  insertStatusCategorySchema,
  patchStatusCategorySchema,
  selectStatusCategorySchema,
  statusCategoryRelations,
} from './status-category';
export {
  default as testCase,
  insertTestCaseSchema,
  patchTestCaseSchema,
  selectTestCaseSchema,
  type TestCase,
  type TestCaseInsert,
  type TestCasePatch,
  type TestCaseSelect,
  testCaseRelations,
} from './test-case';
export {
  default as testCaseComment,
  insertTestCaseCommentSchema,
  patchTestCaseCommentSchema,
  selectTestCaseCommentSchema,
  type TestCaseComment,
  type TestCaseCommentInsert,
  type TestCaseCommentPatch,
  type TestCaseCommentSelect,
  testCaseCommentRelations,
} from './test-case-comment';
export {
  default as testCaseHistory,
  insertTestCaseHistorySchema,
  patchTestCaseHistorySchema,
  selectTestCaseHistorySchema,
  type TestCaseHistory,
  type TestCaseHistoryInsert,
  type TestCaseHistoryPatch,
  type TestCaseHistorySelect,
  testCaseChangeTypeEnum,
  testCaseHistoryRelations,
} from './test-case-history';
export {
  default as testCaseWorkItemLink,
  insertTestCaseWorkItemLinkSchema,
  patchTestCaseWorkItemLinkSchema,
  selectTestCaseWorkItemLinkSchema,
  type TestCaseWorkItemLink,
  type TestCaseWorkItemLinkInsert,
  type TestCaseWorkItemLinkSelect,
  testCaseWorkItemLinkRelations,
  testCaseWorkItemLinkTypeEnum,
} from './test-case-work-item-link';
// Test Management
export {
  default as testPlan,
  insertTestPlanSchema,
  patchTestPlanSchema,
  selectTestPlanSchema,
  type TestPlan,
  type TestPlanInsert,
  type TestPlanPatch,
  type TestPlanSelect,
  testPlanRelations,
  testPlanStatusEnum,
} from './test-plan';
export {
  default as testPlanComment,
  insertTestPlanCommentSchema,
  patchTestPlanCommentSchema,
  selectTestPlanCommentSchema,
  type TestPlanComment,
  type TestPlanCommentInsert,
  type TestPlanCommentPatch,
  type TestPlanCommentSelect,
  testPlanCommentRelations,
} from './test-plan-comment';
export {
  default as testSuite,
  insertTestSuiteSchema,
  patchTestSuiteSchema,
  selectTestSuiteSchema,
  type TestSuite,
  type TestSuiteInsert,
  type TestSuitePatch,
  type TestSuiteSelect,
  testSuiteRelations,
  testSuiteTypeEnum,
} from './test-suite';
export {
  default as testSuiteComment,
  insertTestSuiteCommentSchema,
  patchTestSuiteCommentSchema,
  selectTestSuiteCommentSchema,
  type TestSuiteComment,
  type TestSuiteCommentInsert,
  type TestSuiteCommentPatch,
  type TestSuiteCommentSelect,
  testSuiteCommentRelations,
} from './test-suite-comment';
export {
  default as testSuiteTestCase,
  insertTestSuiteTestCaseSchema,
  patchTestSuiteTestCaseSchema,
  selectTestSuiteTestCaseSchema,
  type TestSuiteTestCase,
  type TestSuiteTestCaseInsert,
  type TestSuiteTestCasePatch,
  type TestSuiteTestCaseSelect,
  testSuiteTestCaseRelations,
} from './test-suite-test-case';
export {
  default as user,
  insertUserSchema,
  patchUserSchema,
  selectUserSchema,
  userRelations,
} from './user';
// FCM & Notification schemas
export {
  type CreateUserFcmToken,
  createUserFcmTokenSchema,
  default as userFcmToken,
  insertUserFcmTokenSchema,
  type PatchUserFcmToken,
  patchUserFcmTokenSchema,
  selectUserFcmTokenSchema,
  type UserFcmToken,
} from './user-fcm-token';
export {
  default as workItem,
  insertWorkItemSchema,
  patchWorkItemSchema,
  selectWorkItemSchema,
  type WorkItem,
  type WorkItemInsert,
  type WorkItemPatch,
  type WorkItemSelect,
  workItemRelations,
} from './work-item';
export {
  default as workItemComment,
  insertWorkItemCommentSchema,
  patchWorkItemCommentSchema,
  selectWorkItemCommentSchema,
  workItemCommentRelations,
} from './work-item-comment';
export {
  default as workItemField,
  fieldTypeEnum,
  insertWorkItemFieldSchema,
  patchWorkItemFieldSchema,
  selectWorkItemFieldSchema,
  workItemFieldRelations,
} from './work-item-field';
export {
  default as workItemHistory,
  insertWorkItemHistorySchema,
  patchWorkItemHistorySchema,
  selectWorkItemHistorySchema,
  workItemChangeTypeEnum,
  workItemHistoryRelations,
} from './work-item-history';
export {
  default as workItemLink,
  insertWorkItemLinkSchema,
  patchWorkItemLinkSchema,
  selectWorkItemLinkSchema,
  type WorkItemLinkInsert,
  workItemLinkRelations,
  workItemLinkTypeEnum,
} from './work-item-link';
export {
  default as workItemType,
  insertWorkItemTypeSchema,
  patchWorkItemTypeSchema,
  selectWorkItemTypeSchema,
  workItemTypeRelations,
} from './work-item-type';
// Workflow
export {
  default as workflow,
  insertWorkflowSchema,
  patchWorkflowSchema,
  selectWorkflowSchema,
  workflowRelations,
  workflowStatusEnum,
} from './workflow';
export {
  default as workflowStatus,
  insertWorkflowStatusSchema,
  patchWorkflowStatusSchema,
  selectWorkflowStatusSchema,
  workflowStatusRelations,
} from './workflow-status';
export {
  default as workflowTransition,
  insertWorkflowTransitionSchema,
  patchWorkflowTransitionSchema,
  selectWorkflowTransitionSchema,
  workflowTransitionRelations,
} from './workflow-transition';
export {
  default as workspace,
  insertWorkspaceSchema,
  patchWorkspaceSchema,
  selectWorkspaceSchema,
  type WorkspaceInsert,
  workspaceRelations,
  workspaceStatusEnum,
  workspaceVisibilityEnum,
} from './workspace';

// Auth schemas
export const registerSchema = z.object({
  user: insertUserSchema,
  organization: insertOrganizationSchema.optional(),
});

export const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8).max(32),
});
