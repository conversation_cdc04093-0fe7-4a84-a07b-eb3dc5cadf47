import { relations } from 'drizzle-orm';
import { jsonb, pgEnum, pgTable, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { z } from 'zod';
import user, { selectUserSchema } from './user';
import workItem, { selectWorkItemSchema } from './work-item';

export const workItemChangeTypeEnum = pgEnum('work_item_change_type', [
  'CREATE',
  'UPDATE',
  'DELETE',
  'RESTORE',
  'COMMENT_ADDED',
  'COMMENT_UPDATED',
  'COMMENT_DELETED',
  'ATTACHMENT_ADDED',
  'ATTACHMENT_DELETED',
]);

const workItemHistory = pgTable('work_item_history', {
  id: uuid('id').primaryKey().defaultRandom(),
  workItemId: uuid('work_item_id')
    .references(() => workItem.id)
    .notNull(),
  changeType: workItemChangeTypeEnum('change_type').notNull(),
  changedBy: uuid('changed_by')
    .references(() => user.id)
    .notNull(),
  // Generic storage for field changes: { fieldName: { oldValue, newValue } }
  changedFields: jsonb('changed_fields'),
  // Additional metadata for comments, attachments, etc.
  metadata: jsonb('metadata'),
  // Summary description of what changed (optional, for easier readability)
  summary: varchar('summary', { length: 500 }),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

export const workItemHistoryRelations = relations(workItemHistory, ({ one }) => ({
  workItem: one(workItem, {
    fields: [workItemHistory.workItemId],
    references: [workItem.id],
  }),
  user: one(user, {
    fields: [workItemHistory.changedBy],
    references: [user.id],
  }),
}));

export default workItemHistory;

// Zod schemas
export const selectWorkItemHistorySchema = createSelectSchema(workItemHistory, {
  changedFields: () =>
    z
      .record(
        z.object({
          oldValue: z.union([z.string(), z.number(), z.boolean(), z.null()]),
          newValue: z.union([z.string(), z.number(), z.boolean(), z.null()]),
        }),
      )
      .nullable(),
  metadata: () =>
    z
      .object({
        comment: z
          .object({
            id: z.string(),
            content: z.string(),
          })
          .optional(),
        attachment: z
          .object({
            id: z.string(),
            filename: z.string(),
            size: z.number().optional(),
            mimeType: z.string().optional(),
          })
          .optional(),
      })
      .passthrough()
      .nullable(),
}).extend({
  user: selectUserSchema.partial().nullable(),
  workItem: selectWorkItemSchema.partial().nullable(),
});

export const insertWorkItemHistorySchema = createInsertSchema(workItemHistory, {
  changedFields: () =>
    z
      .record(
        z.object({
          oldValue: z.union([z.string(), z.number(), z.boolean(), z.null()]),
          newValue: z.union([z.string(), z.number(), z.boolean(), z.null()]),
        }),
      )
      .nullable(),
  metadata: () =>
    z
      .object({
        comment: z
          .object({
            id: z.string(),
            content: z.string(),
          })
          .optional(),
        attachment: z
          .object({
            id: z.string(),
            filename: z.string(),
            size: z.number().optional(),
            mimeType: z.string().optional(),
          })
          .optional(),
      })
      .passthrough()
      .nullable(),
})
  .required({
    workItemId: true,
    changedBy: true,
    changeType: true,
  })
  .omit({
    id: true,
    createdAt: true,
  });

export const patchWorkItemHistorySchema = insertWorkItemHistorySchema.partial();

// Types
export type WorkItemHistory = typeof workItemHistory.$inferSelect;
export type WorkItemHistoryInsert = typeof insertWorkItemHistorySchema._type;
export type WorkItemHistoryPatch = typeof patchWorkItemHistorySchema._type;
export type WorkItemHistorySelect = typeof selectWorkItemHistorySchema._type;

// Metadata type definition for better type safety
export type WorkItemHistoryMetadata = {
  // For comment-related changes
  comment?: {
    id: string;
    content: string;
  };
  // For attachment-related changes
  attachment?: {
    id: string;
    filename: string;
    size?: number;
    mimeType?: string;
  };
  // Additional metadata can be added here
  [key: string]: unknown;
};
