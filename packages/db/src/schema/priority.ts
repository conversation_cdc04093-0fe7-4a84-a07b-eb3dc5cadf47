import { relations } from 'drizzle-orm';
import { boolean, integer, pgTable, text, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import organization, { selectOrganizationSchema } from './organization';
import { requiredTextField, textField } from './validation-utils';

const priority = pgTable('priority', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 100 }).notNull(),
  description: text('description'),
  organizationId: uuid('organization_id').references(() => organization.id),
  level: integer('level').notNull(),
  isSystem: boolean('is_system').default(false).notNull(),
  color: varchar('color', { length: 7 }),
  icon: varchar('icon', { length: 255 }),
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const priorityRelations = relations(priority, ({ one }) => ({
  organization: one(organization, {
    fields: [priority.organizationId],
    references: [organization.id],
  }),
}));

export default priority;

// Zod schemas
export const selectPrioritySchema = createSelectSchema(priority).extend({
  organization: selectOrganizationSchema.partial().nullable(),
});

export const insertPrioritySchema = createInsertSchema(priority, {
  name: () => requiredTextField(1, 100),
  description: () => textField(),
  color: () => textField(7),
  icon: () => textField(255),
})
  .required({
    name: true,
    level: true,
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchPrioritySchema = insertPrioritySchema.partial();

// Types
export type Priority = typeof priority.$inferSelect;
export type PriorityInsert = typeof insertPrioritySchema._type;
export type PriorityPatch = typeof patchPrioritySchema._type;
export type PrioritySelect = typeof selectPrioritySchema._type;
