import { relations } from 'drizzle-orm';
import { jsonb, pgEnum, pgTable, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { selectOrganizationSchema } from './organization';
import { selectPrioritySchema } from './priority';
import { selectProjectSchema } from './project';
import { selectRoleSchema } from './role';
import { selectSprintSchema } from './sprint';
import { selectStatusSchema } from './status';
import user, { selectUserSchema } from './user';
import { jsonField } from './validation-utils';
import { selectWorkItemSchema } from './work-item';
import { selectWorkflowSchema } from './workflow';
import { selectWorkspaceSchema } from './workspace';

export const auditActionEnum = pgEnum('audit_action', [
  'CREATE',
  'UPDATE',
  'DELETE',
  'ARCHIVE',
  'RESTORE',
  'LOGIN',
  'LOGOUT',
  'PASSWORD_CHANGE',
  'PROFILE_UPDATE',
  'ROLE_ASSIGNED',
  'ROLE_REVOKED',
  'PERMISSION_UPDATED',
  'STATUS_CHANGE',
  'SPRINT_TRANSITION',
  'COMMENT_ADDED',
  'ATTACHMENT_UPLOADED',
  'NOTIFICATION_SENT',
  'SYSTEM_START',
  'SYSTEM_ERROR',
  'SCHEDULED_TASK_RUN',
  'DATA_IMPORT',
  'DATA_EXPORT',
]);

const auditLog = pgTable('audit_log', {
  id: uuid('id').primaryKey().defaultRandom(),
  entityType: varchar('entity_type', { length: 100 }).notNull(),
  entityId: uuid('entity_id').notNull(),
  action: auditActionEnum('action').notNull(),
  changedBy: uuid('changed_by').references(() => user.id),
  timestamp: timestamp('timestamp').defaultNow().notNull(),
  details: jsonb('details')
    .$type<object>()
    .$default(() => ({}))
    .notNull(),
  relatedEntityType: varchar('related_entity_type', { length: 100 }),
  relatedEntityId: uuid('related_entity_id'),
});

export const auditLogRelations = relations(auditLog, ({ one }) => ({
  user: one(user, {
    fields: [auditLog.changedBy],
    references: [user.id],
  }),
}));

export default auditLog;

// Zod schemas
export const selectAuditLogSchema = createSelectSchema(auditLog, {
  details: jsonField,
}).extend({
  user: selectUserSchema.partial().nullable(),
  organization: selectOrganizationSchema.partial().nullable(),
  workspace: selectWorkspaceSchema.partial().nullable(),
  project: selectProjectSchema.partial().nullable(),
  workItem: selectWorkItemSchema.partial().nullable(),
  role: selectRoleSchema.partial().nullable(),
  sprint: selectSprintSchema.partial().nullable(),
  workflow: selectWorkflowSchema.partial().nullable(),
  status: selectStatusSchema.partial().nullable(),
  priority: selectPrioritySchema.partial().nullable(),
});

export const insertAuditLogSchema = createInsertSchema(auditLog, {
  details: jsonField,
})
  .required({
    entityType: true,
    entityId: true,
    action: true,
  })
  .omit({
    id: true,
    timestamp: true,
  });

export const patchAuditLogSchema = insertAuditLogSchema.partial();

// Types
export type AuditLog = typeof auditLog.$inferSelect;
export type AuditLogInsert = typeof insertAuditLogSchema._type;
export type AuditLogPatch = typeof patchAuditLogSchema._type;
export type AuditLogSelect = typeof selectAuditLogSchema._type;
