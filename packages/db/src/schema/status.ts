import { relations } from 'drizzle-orm';
import {
  boolean,
  integer,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import organization, { selectOrganizationSchema } from './organization';
import statusCategory, { selectStatusCategorySchema } from './status-category';
import { requiredTextField, textField } from './validation-utils';

export const statusTypeEnum = pgEnum('status_type', ['todo', 'in_progress', 'done']);

const status = pgTable('status', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 100 }).notNull(),
  description: text('description'),
  organizationId: uuid('organization_id').references(() => organization.id),
  statusType: statusTypeEnum('status_type').notNull(),
  color: varchar('color', { length: 7 }),
  order: integer('order').default(0).notNull(),
  statusCategoryId: uuid('status_category_id')
    .references(() => statusCategory.id)
    .notNull(),
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const statusRelations = relations(status, ({ one }) => ({
  organization: one(organization, {
    fields: [status.organizationId],
    references: [organization.id],
  }),
  statusCategory: one(statusCategory, {
    fields: [status.statusCategoryId],
    references: [statusCategory.id],
  }),
}));

export default status;

// Zod schemas
export const selectStatusSchema = createSelectSchema(status).extend({
  organization: selectOrganizationSchema.partial().nullable(),
  statusCategory: selectStatusCategorySchema.partial().nullable(),
});

export const insertStatusSchema = createInsertSchema(status, {
  name: () => requiredTextField(1, 100),
  description: () => textField(),
  color: () => textField(7),
})
  .required({
    name: true,
    statusType: true,
    statusCategoryId: true,
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchStatusSchema = insertStatusSchema.partial();

// Types
export type Status = typeof status.$inferSelect;
export type StatusInsert = typeof insertStatusSchema._type;
export type StatusPatch = typeof patchStatusSchema._type;
export type StatusSelect = typeof selectStatusSchema._type;
