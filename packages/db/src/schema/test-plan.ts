import { relations } from 'drizzle-orm';
import { boolean, pgEnum, pgTable, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import project, { selectProjectSchema } from './project';
import user, { selectUserSchema } from './user';
import { requiredTextField, textField } from './validation-utils';

export const testPlanStatusEnum = pgEnum('test_plan_status', ['draft', 'active', 'archived']);

const testPlan = pgTable('test_plan', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  description: varchar('description', { length: 1000 }),
  projectId: uuid('project_id')
    .references(() => project.id, { onDelete: 'cascade' })
    .notNull(),
  status: testPlanStatusEnum('status').default('draft').notNull(),
  createdById: uuid('created_by_id')
    .references(() => user.id)
    .notNull(),
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const testPlanRelations = relations(testPlan, ({ one }) => ({
  project: one(project, {
    fields: [testPlan.projectId],
    references: [project.id],
  }),
  createdBy: one(user, {
    fields: [testPlan.createdById],
    references: [user.id],
  }),
  // testSuites: many(testSuite), // Commented out to avoid circular dependency
  // comments: many(testPlanComment), // Commented out to avoid circular dependency
}));

export default testPlan;

// Zod schemas
export const selectTestPlanSchema = createSelectSchema(testPlan).extend({
  project: selectProjectSchema.partial().nullable(),
  createdBy: selectUserSchema.partial().nullable(),
});

export const insertTestPlanSchema = createInsertSchema(testPlan, {
  name: () => requiredTextField(1, 255),
  description: () => textField(1000),
})
  .required({
    name: true,
    projectId: true,
    createdById: true,
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchTestPlanSchema = insertTestPlanSchema.partial();

// Types
export type TestPlan = typeof testPlan.$inferSelect;
export type TestPlanInsert = typeof insertTestPlanSchema._type;
export type TestPlanPatch = typeof patchTestPlanSchema._type;
export type TestPlanSelect = typeof selectTestPlanSchema._type;
