import { relations } from 'drizzle-orm';
import {
  boolean,
  jsonb,
  pgEnum,
  pgTable,
  text,
  timestamp,
  unique,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import organization, { selectOrganizationSchema } from './organization';
import user, { selectUserSchema } from './user';
import { jsonField, requiredTextField, textField } from './validation-utils';

export const workspaceVisibilityEnum = pgEnum('workspace_visibility', ['public', 'private']);
export const workspaceStatusEnum = pgEnum('workspace_status', ['active', 'archived']);

const workspace = pgTable(
  'workspace',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    organizationId: uuid('organization_id')
      .references(() => organization.id)
      .notNull(),
    name: varchar('name', { length: 255 }).notNull(),
    slug: varchar('slug', { length: 255 }).notNull(),
    description: text('description'),
    icon: varchar('icon', { length: 500 }),
    color: varchar('color', { length: 7 }),
    settings: jsonb('settings')
      .$type<object>()
      .$default(() => ({}))
      .notNull(),
    defaultProjectId: uuid('default_project_id'),
    visibility: workspaceVisibilityEnum('visibility').default('private').notNull(),
    status: workspaceStatusEnum('status').default('active').notNull(),
    isActive: boolean('is_active').default(true).notNull(),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
    createdBy: uuid('created_by').references(() => user.id),
  },
  (table) => [unique('org_slug_idx').on(table.organizationId, table.slug)],
);

export const workspaceRelations = relations(workspace, ({ one }) => ({
  organization: one(organization, {
    fields: [workspace.organizationId],
    references: [organization.id],
  }),
  creator: one(user, {
    fields: [workspace.createdBy],
    references: [user.id],
  }),
}));

export default workspace;

// Zod schemas
export const selectWorkspaceSchema = createSelectSchema(workspace, {
  settings: jsonField,
}).extend({
  organization: selectOrganizationSchema.partial().nullable(),
  creator: selectUserSchema.partial().nullable(),
});

export const insertWorkspaceSchema = createInsertSchema(workspace, {
  name: () => requiredTextField(1, 255),
  slug: () => requiredTextField(1, 255),
  description: () => textField(),
  icon: () => textField(),
  color: () => textField(7),
  settings: jsonField,
})
  .required({
    organizationId: true,
    name: true,
    slug: true,
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchWorkspaceSchema = insertWorkspaceSchema.partial();

// Types
export type Workspace = typeof workspace.$inferSelect;
export type WorkspaceInsert = typeof insertWorkspaceSchema._type;
export type WorkspacePatch = typeof patchWorkspaceSchema._type;
export type WorkspaceSelect = typeof selectWorkspaceSchema._type;
