import { relations } from 'drizzle-orm';
import { boolean, integer, pgTable, timestamp, unique, uuid } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import priority, { selectPrioritySchema } from './priority';
import status, { selectStatusSchema } from './status';
import testCase, { selectTestCaseSchema } from './test-case';
import testSuite, { selectTestSuiteSchema } from './test-suite';
import user, { selectUserSchema } from './user';

const testSuiteTestCase = pgTable(
  'test_suite_test_case',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    testSuiteId: uuid('test_suite_id')
      .references(() => testSuite.id, { onDelete: 'cascade' })
      .notNull(),
    testCaseId: uuid('test_case_id')
      .references(() => testCase.id, { onDelete: 'cascade' })
      .notNull(),
    statusId: uuid('status_id').references(() => status.id),
    priorityId: uuid('priority_id').references(() => priority.id),
    order: integer('order').default(0).notNull(),
    isActive: boolean('is_active').default(true).notNull(),
    createdBy: uuid('created_by').references(() => user.id),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (table) => ({
    // Ensure a test case can only be linked once to a test suite
    uniqueTestSuiteTestCase: unique('unique_test_suite_test_case').on(
      table.testSuiteId,
      table.testCaseId,
    ),
  }),
);

export const testSuiteTestCaseRelations = relations(testSuiteTestCase, ({ one }) => ({
  testSuite: one(testSuite, {
    fields: [testSuiteTestCase.testSuiteId],
    references: [testSuite.id],
  }),
  testCase: one(testCase, {
    fields: [testSuiteTestCase.testCaseId],
    references: [testCase.id],
  }),
  status: one(status, {
    fields: [testSuiteTestCase.statusId],
    references: [status.id],
  }),
  priority: one(priority, {
    fields: [testSuiteTestCase.priorityId],
    references: [priority.id],
  }),
  creator: one(user, {
    fields: [testSuiteTestCase.createdBy],
    references: [user.id],
  }),
}));

export default testSuiteTestCase;

// Zod schemas
export const selectTestSuiteTestCaseSchema = createSelectSchema(testSuiteTestCase).extend({
  testSuite: selectTestSuiteSchema.partial().nullable().optional(),
  testCase: selectTestCaseSchema.partial().nullable().optional(),
  status: selectStatusSchema.partial().nullable().optional(),
  priority: selectPrioritySchema.partial().nullable().optional(),
  creator: selectUserSchema.partial().nullable().optional(),
});

export const insertTestSuiteTestCaseSchema = createInsertSchema(testSuiteTestCase)
  .required({
    testSuiteId: true,
    testCaseId: true,
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchTestSuiteTestCaseSchema = insertTestSuiteTestCaseSchema.partial();

// Types
export type TestSuiteTestCase = typeof testSuiteTestCase.$inferSelect;
export type TestSuiteTestCaseInsert = typeof insertTestSuiteTestCaseSchema._type;
export type TestSuiteTestCasePatch = typeof patchTestSuiteTestCaseSchema._type;
export type TestSuiteTestCaseSelect = typeof selectTestSuiteTestCaseSchema._type;
