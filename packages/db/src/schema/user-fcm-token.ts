import { pgTable, text, timestamp, uniqueIndex, uuid, varchar } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { z } from 'zod';
import user from './user';

export const userFcmToken = pgTable(
  'user_fcm_token',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    userId: uuid('user_id')
      .notNull()
      .references(() => user.id, { onDelete: 'cascade' }),
    fcmToken: text('fcm_token').notNull(),
    deviceType: varchar('device_type', { length: 50 }),
    deviceModel: varchar('device_model', { length: 255 }),
    os: varchar('os', { length: 50 }),
    browser: varchar('browser', { length: 100 }),
    lastActive: timestamp('last_active').defaultNow().notNull(),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (table) => ({
    userTokenUnique: uniqueIndex('user_fcm_token_user_id_fcm_token_unique').on(
      table.userId,
      table.fcmToken,
    ),
  }),
);

// Base schemas
export const insertUserFcmTokenSchema = createInsertSchema(userFcmToken);
export const selectUserFcmTokenSchema = createSelectSchema(userFcmToken);

// Custom schemas for API operations
export const createUserFcmTokenSchema = z.object({
  fcmToken: z.string().min(1),
  deviceType: z.string().optional(),
  deviceModel: z.string().optional(),
  os: z.string().optional(),
  browser: z.string().optional(),
});

export const patchUserFcmTokenSchema = z.object({
  deviceType: z.string().optional(),
  deviceModel: z.string().optional(),
  os: z.string().optional(),
  browser: z.string().optional(),
});

// Type exports
export type UserFcmToken = z.infer<typeof selectUserFcmTokenSchema>;
export type CreateUserFcmToken = z.infer<typeof createUserFcmTokenSchema>;
export type PatchUserFcmToken = z.infer<typeof patchUserFcmTokenSchema>;

export default userFcmToken;
