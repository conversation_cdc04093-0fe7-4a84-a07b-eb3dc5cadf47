import { relations } from 'drizzle-orm';
import {
  boolean,
  jsonb,
  pgTable,
  text,
  timestamp,
  unique,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import user, { selectUserSchema } from './user';
import {
  datetimeField,
  emailField,
  jsonField,
  requiredTextField,
  textField,
} from './validation-utils';

const organization = pgTable(
  'organization',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    name: varchar('name', { length: 255 }).notNull().unique(),
    slug: varchar('slug', { length: 255 }).notNull().unique(),
    description: text('description'),
    website: varchar('website', { length: 255 }),
    logoUrl: varchar('logo_url', { length: 500 }),
    isActive: boolean('is_active').default(true).notNull(),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
    createdBy: uuid('created_by').references(() => user.id),
    settings: jsonb('settings')
      .$type<object>()
      .$default(() => ({}))
      .notNull(),
    domains: jsonb('domains')
      .$type<object>()
      .$default(() => ({}))
      .notNull(),
    billingEmail: varchar('billing_email', { length: 255 }),
    billingAddress: text('billing_address'),
    billingPlan: varchar('billing_plan', { length: 50 }),
    planExpiresAt: timestamp('plan_expires_at'),
  },
  (table) => [unique('name_idx').on(table.name), unique('slug_idx').on(table.slug)],
);

export const organizationRelations = relations(organization, ({ one }) => ({
  creator: one(user, {
    fields: [organization.createdBy],
    references: [user.id],
  }),
}));

export default organization;

// Zod schemas
export const selectOrganizationSchema = createSelectSchema(organization, {
  domains: jsonField,
  settings: jsonField,
  planExpiresAt: datetimeField,
}).extend({
  creator: selectUserSchema.partial().nullable(),
});

export const insertOrganizationSchema = createInsertSchema(organization, {
  settings: jsonField,
  domains: jsonField,
  name: () => requiredTextField(1, 255),
  slug: () => requiredTextField(1, 255),
  planExpiresAt: datetimeField,
  description: () => textField(),
  website: () => textField(255),
  logoUrl: () => textField(),
  billingEmail: () => emailField(255),
  billingAddress: () => textField(),
  billingPlan: () => textField(50),
})
  .required({
    name: true,
    slug: true,
    createdBy: true,
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchOrganizationSchema = insertOrganizationSchema.partial();

// Types
export type Organization = typeof organization.$inferSelect;
export type OrganizationInsert = typeof insertOrganizationSchema._type;
export type OrganizationPatch = typeof patchOrganizationSchema._type;
export type OrganizationSelect = typeof selectOrganizationSchema._type;
