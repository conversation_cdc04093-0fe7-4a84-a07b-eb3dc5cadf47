import { relations } from 'drizzle-orm';
import type { AnyPgColumn } from 'drizzle-orm/pg-core';
import { boolean, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import testSuite from './test-suite';
import user, { selectUserSchema } from './user';
import { requiredTextField } from './validation-utils';

const testSuiteComment = pgTable('test_suite_comment', {
  id: uuid('id').primaryKey().defaultRandom(),
  testSuiteId: uuid('test_suite_id')
    .references(() => testSuite.id, { onDelete: 'cascade' })
    .notNull(),
  content: text('content').notNull(),
  authorId: uuid('author_id')
    .references(() => user.id)
    .notNull(),
  parentId: uuid('parent_id').references((): AnyPgColumn => testSuiteComment.id, {
    onDelete: 'cascade',
  }),
  isEdited: boolean('is_edited').default(false).notNull(),
  isDeleted: boolean('is_deleted').default(false).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const testSuiteCommentRelations = relations(testSuiteComment, ({ one, many }) => ({
  testSuite: one(testSuite, {
    fields: [testSuiteComment.testSuiteId],
    references: [testSuite.id],
  }),
  author: one(user, {
    fields: [testSuiteComment.authorId],
    references: [user.id],
  }),
  parent: one(testSuiteComment, {
    fields: [testSuiteComment.parentId],
    references: [testSuiteComment.id],
    relationName: 'parentReply',
  }),
  replies: many(testSuiteComment, {
    relationName: 'parentReply',
  }),
}));

export default testSuiteComment;

// Zod schemas
export const selectTestSuiteCommentSchema = createSelectSchema(testSuiteComment).extend({
  author: selectUserSchema.partial().nullable(),
});

export const insertTestSuiteCommentSchema = createInsertSchema(testSuiteComment, {
  content: () => requiredTextField(1),
})
  .required({
    testSuiteId: true,
    content: true,
    authorId: true,
  })
  .omit({
    id: true,
    isDeleted: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchTestSuiteCommentSchema = insertTestSuiteCommentSchema
  .partial()
  .pick({ content: true, isEdited: true });

// Types
export type TestSuiteComment = typeof testSuiteComment.$inferSelect;
export type TestSuiteCommentInsert = typeof insertTestSuiteCommentSchema._type;
export type TestSuiteCommentPatch = typeof patchTestSuiteCommentSchema._type;
export type TestSuiteCommentSelect = typeof selectTestSuiteCommentSchema._type;
