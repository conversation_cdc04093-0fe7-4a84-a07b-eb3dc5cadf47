import { relations } from 'drizzle-orm';
import { pgEnum, pgTable, timestamp, unique, uuid } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import testCase from './test-case';
import user, { selectUserSchema } from './user';
import workItem from './work-item';

// Define link type enum for test case to work item relationships
export const testCaseWorkItemLinkTypeEnum = pgEnum('test_case_work_item_link_type', [
  'tests',
  'tested_by',
  'validates',
  'verifies',
  'relates_to',
]);

const testCaseWorkItemLink = pgTable(
  'test_case_work_item_link',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    testCaseId: uuid('test_case_id')
      .references(() => testCase.id, { onDelete: 'cascade' })
      .notNull(),
    workItemId: uuid('work_item_id')
      .references(() => workItem.id)
      .notNull(),
    linkType: testCaseWorkItemLinkTypeEnum('link_type').notNull(),
    createdById: uuid('created_by_id')
      .references(() => user.id)
      .notNull(),
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (table) => ({
    // Prevent duplicate links between same test case and work item with same type
    uniqueLink: unique().on(table.testCaseId, table.workItemId, table.linkType),
  }),
);

export const testCaseWorkItemLinkRelations = relations(testCaseWorkItemLink, ({ one }) => ({
  testCase: one(testCase, {
    fields: [testCaseWorkItemLink.testCaseId],
    references: [testCase.id],
  }),
  workItem: one(workItem, {
    fields: [testCaseWorkItemLink.workItemId],
    references: [workItem.id],
  }),
  createdBy: one(user, {
    fields: [testCaseWorkItemLink.createdById],
    references: [user.id],
  }),
}));

export default testCaseWorkItemLink;

// Zod schemas
export const selectTestCaseWorkItemLinkSchema = createSelectSchema(testCaseWorkItemLink).extend({
  createdBy: selectUserSchema.partial().nullable(),
});

export const insertTestCaseWorkItemLinkSchema = createInsertSchema(testCaseWorkItemLink)
  .required({
    testCaseId: true,
    workItemId: true,
    linkType: true,
    createdById: true,
  })
  .omit({
    id: true,
    createdAt: true,
  });

export const patchTestCaseWorkItemLinkSchema = insertTestCaseWorkItemLinkSchema.partial();

// Types
export type TestCaseWorkItemLink = typeof testCaseWorkItemLink.$inferSelect;
export type TestCaseWorkItemLinkInsert = typeof insertTestCaseWorkItemLinkSchema._type;
export type TestCaseWorkItemLinkSelect = typeof selectTestCaseWorkItemLinkSchema._type;
