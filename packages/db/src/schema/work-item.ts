import { relations } from 'drizzle-orm';
import {
  boolean,
  integer,
  jsonb,
  pgTable,
  text,
  timestamp,
  unique,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import priority, { selectPrioritySchema } from './priority';
import project, { selectProjectSchema } from './project';
import sprint, { selectSprintSchema } from './sprint';
import status, { selectStatusSchema } from './status';
import user, { selectUserSchema } from './user';
import { jsonField, requiredTextField, textField } from './validation-utils';
import workItemType, { selectWorkItemTypeSchema } from './work-item-type';

const workItem = pgTable(
  'work_item',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    projectId: uuid('project_id')
      .references(() => project.id)
      .notNull(),
    typeId: uuid('type_id')
      .references(() => workItemType.id)
      .notNull(),
    title: varchar('title', { length: 500 }).notNull(),
    description: text('description'),
    assigneeId: uuid('assignee_id').references(() => user.id),
    reporterId: uuid('reporter_id').references(() => user.id),
    sprintId: uuid('sprint_id').references(() => sprint.id),
    initialSprintId: uuid('initial_sprint_id').references(() => sprint.id),
    parentId: uuid('parent_id').references((): any => workItem.id),
    statusId: uuid('status_id')
      .references(() => status.id)
      .notNull(),
    priorityId: uuid('priority_id')
      .references(() => priority.id)
      .notNull(),
    ticketNumber: integer('ticket_number').notNull(),
    ticketId: varchar('ticket_id', { length: 20 }).notNull(),
    tags: jsonb('tags')
      .$type<string[]>()
      .$default(() => [])
      .notNull(),
    estimate: jsonb('estimate')
      .$type<object>()
      .$default(() => ({}))
      .notNull(),
    dates: jsonb('dates')
      .$type<object>()
      .$default(() => ({}))
      .notNull(),
    links: jsonb('links')
      .$type<object>()
      .$default(() => ({}))
      .notNull(),
    order: integer('order').default(0).notNull(),
    isActive: boolean('is_active').default(true).notNull(),
    createdBy: uuid('created_by').references(() => user.id),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
    updatedBy: uuid('updated_by').references(() => user.id),
  },
  (table) => [unique('unique_project_ticket_number').on(table.projectId, table.ticketNumber)],
);

export const workItemRelations = relations(workItem, ({ one, many }) => ({
  project: one(project, {
    fields: [workItem.projectId],
    references: [project.id],
  }),
  type: one(workItemType, {
    fields: [workItem.typeId],
    references: [workItemType.id],
  }),
  assignee: one(user, {
    fields: [workItem.assigneeId],
    references: [user.id],
  }),
  reporter: one(user, {
    fields: [workItem.reporterId],
    references: [user.id],
  }),
  sprint: one(sprint, {
    fields: [workItem.sprintId],
    references: [sprint.id],
  }),
  initialSprint: one(sprint, {
    fields: [workItem.initialSprintId],
    references: [sprint.id],
  }),
  parent: one(workItem, {
    fields: [workItem.parentId],
    references: [workItem.id],
    relationName: 'parentChild',
  }),
  status: one(status, {
    fields: [workItem.statusId],
    references: [status.id],
  }),
  priority: one(priority, {
    fields: [workItem.priorityId],
    references: [priority.id],
  }),
  creator: one(user, {
    fields: [workItem.createdBy],
    references: [user.id],
  }),
  children: many(workItem, {
    relationName: 'parentChild',
  }),
  updater: one(user, {
    fields: [workItem.updatedBy],
    references: [user.id],
  }),
  // sourceLinks: many(workItemLink, {
  //   relationName: "sourceLinks",
  // }),
  // targetLinks: many(workItemLink, {
  //   relationName: "targetLinks",
  // }),
}));

export default workItem;

// Zod schemas
export const selectWorkItemSchema = createSelectSchema(workItem, {
  tags: jsonField,
  estimate: jsonField,
  dates: jsonField,
  links: jsonField,
}).extend({
  project: selectProjectSchema.partial().nullable(),
  type: selectWorkItemTypeSchema.partial().nullable(),
  assignee: selectUserSchema.partial().nullable(),
  reporter: selectUserSchema.partial().nullable(),
  sprint: selectSprintSchema.partial().nullable(),
  initialSprint: selectSprintSchema.partial().nullable(),
  status: selectStatusSchema.partial().nullable(),
  priority: selectPrioritySchema.partial().nullable(),
  creator: selectUserSchema.partial().nullable(),
  updater: selectUserSchema.partial().nullable(),
});

export const insertWorkItemSchema = createInsertSchema(workItem, {
  title: () => requiredTextField(1, 500),
  description: () => textField(),
  tags: jsonField,
  estimate: jsonField,
  dates: jsonField,
  links: jsonField,
})
  .required({
    projectId: true,
    typeId: true,
    title: true,
    statusId: true,
    priorityId: true,
  })
  .omit({
    id: true,
    ticketNumber: true,
    ticketId: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchWorkItemSchema = insertWorkItemSchema.partial();

// Types
export type WorkItem = typeof workItem.$inferSelect;
export type WorkItemInsert = typeof insertWorkItemSchema._type;
export type WorkItemPatch = typeof patchWorkItemSchema._type;
export type WorkItemSelect = typeof selectWorkItemSchema._type;
