import { relations } from 'drizzle-orm';
import type { AnyPgColumn } from 'drizzle-orm/pg-core';
import { boolean, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import testCase from './test-case';
import user, { selectUserSchema } from './user';
import { requiredTextField } from './validation-utils';

const testCaseComment = pgTable('test_case_comment', {
  id: uuid('id').primaryKey().defaultRandom(),
  testCaseId: uuid('test_case_id')
    .references(() => testCase.id, { onDelete: 'cascade' })
    .notNull(),
  content: text('content').notNull(),
  authorId: uuid('author_id')
    .references(() => user.id)
    .notNull(),
  parentId: uuid('parent_id').references((): AnyPgColumn => testCaseComment.id, {
    onDelete: 'cascade',
  }),
  isEdited: boolean('is_edited').default(false).notNull(),
  isDeleted: boolean('is_deleted').default(false).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const testCaseCommentRelations = relations(testCaseComment, ({ one, many }) => ({
  testCase: one(testCase, {
    fields: [testCaseComment.testCaseId],
    references: [testCase.id],
  }),
  author: one(user, {
    fields: [testCaseComment.authorId],
    references: [user.id],
  }),
  parent: one(testCaseComment, {
    fields: [testCaseComment.parentId],
    references: [testCaseComment.id],
    relationName: 'parentReply',
  }),
  replies: many(testCaseComment, {
    relationName: 'parentReply',
  }),
}));

export default testCaseComment;

// Zod schemas
export const selectTestCaseCommentSchema = createSelectSchema(testCaseComment).extend({
  author: selectUserSchema.partial().nullable(),
});

export const insertTestCaseCommentSchema = createInsertSchema(testCaseComment, {
  content: () => requiredTextField(1),
})
  .required({
    testCaseId: true,
    content: true,
    authorId: true,
  })
  .omit({
    id: true,
    isDeleted: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchTestCaseCommentSchema = insertTestCaseCommentSchema
  .partial()
  .pick({ content: true, isEdited: true });

// Types
export type TestCaseComment = typeof testCaseComment.$inferSelect;
export type TestCaseCommentInsert = typeof insertTestCaseCommentSchema._type;
export type TestCaseCommentPatch = typeof patchTestCaseCommentSchema._type;
export type TestCaseCommentSelect = typeof selectTestCaseCommentSchema._type;
