import { relations } from 'drizzle-orm';
import { boolean, integer, jsonb, pgTable, timestamp, uuid } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import status, { selectStatusSchema } from './status';
import { jsonField } from './validation-utils';
import workflow, { selectWorkflowSchema } from './workflow';

const workflowStatus = pgTable('workflow_status', {
  id: uuid('id').primaryKey().defaultRandom(),
  workflowId: uuid('workflow_id')
    .references(() => workflow.id)
    .notNull(),
  statusId: uuid('status_id')
    .references(() => status.id)
    .notNull(),
  isInitial: boolean('is_initial').default(false).notNull(),
  isFinal: boolean('is_final').default(false).notNull(),
  positionX: integer('position_x').default(0).notNull(),
  positionY: integer('position_y').default(0).notNull(),
  properties: jsonb('properties')
    .$type<object>()
    .$default(() => ({}))
    .notNull(),
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const workflowStatusRelations = relations(workflowStatus, ({ one }) => ({
  workflow: one(workflow, {
    fields: [workflowStatus.workflowId],
    references: [workflow.id],
  }),
  status: one(status, {
    fields: [workflowStatus.statusId],
    references: [status.id],
  }),
}));

export default workflowStatus;

// Zod schemas
export const selectWorkflowStatusSchema = createSelectSchema(workflowStatus, {
  properties: jsonField,
}).extend({
  workflow: selectWorkflowSchema.partial().nullable(),
  status: selectStatusSchema.partial().nullable(),
});

export const insertWorkflowStatusSchema = createInsertSchema(workflowStatus, {
  properties: jsonField,
})
  .required({
    workflowId: true,
    statusId: true,
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchWorkflowStatusSchema = insertWorkflowStatusSchema.partial();

// Types
export type WorkflowStatus = typeof workflowStatus.$inferSelect;
export type WorkflowStatusInsert = typeof insertWorkflowStatusSchema._type;
export type WorkflowStatusPatch = typeof patchWorkflowStatusSchema._type;
export type WorkflowStatusSelect = typeof selectWorkflowStatusSchema._type;
