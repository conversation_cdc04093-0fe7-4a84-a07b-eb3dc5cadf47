import { z } from 'zod';

/**
 * Standard validation helpers for common UI input patterns
 * These handle the most common cases where UI sends different formats than DB expects
 */

/**
 * Text field that converts empty strings to null
 * Usage: textField() or textField(100) for max length
 */
export const textField = (maxLength?: number) => {
  let schema = z
    .string()
    .trim()
    .transform((val) => (val === '' ? null : val))
    .nullable()
    .optional();
  if (maxLength) {
    schema = z
      .string()
      .trim()
      .max(maxLength)
      .transform((val) => (val === '' ? null : val))
      .nullable()
      .optional();
  }
  return schema;
};

/**
 * Email field that converts empty strings to null and validates email format
 */
export const emailField = (maxLength = 255) =>
  z
    .union([
      z.string().trim().email().max(maxLength),
      z.literal('').transform(() => null),
      z.null(),
      z.undefined(),
    ])
    .optional();

/**
 * Datetime field that accepts ISO strings and converts to Date objects
 */
export const datetimeField = () =>
  z
    .union([
      z
        .string()
        .datetime()
        .transform((val) => new Date(val)),
      z.date(),
      z.null(),
      z.undefined(),
    ])
    .optional();

/**
 * Required text field with min/max length validation
 */
export const requiredTextField = (minLength = 1, maxLength?: number) => {
  let schema = z.string().trim().min(minLength);
  if (maxLength) {
    schema = schema.max(maxLength);
  }
  return schema;
};

/**
 * JSON field for objects (settings, domains, etc.)
 */
export const jsonField = () => z.record(z.any()).optional();

/**
 * Date field that accepts date strings (YYYY-MM-DD) or Date objects
 * This is for date-only fields without time component
 * Returns strings in YYYY-MM-DD format for Drizzle date columns
 */
export const dateField = () =>
  z
    .union([
      z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
      z.date().transform((val) => {
        const year = val.getFullYear();
        const month = String(val.getMonth() + 1).padStart(2, '0');
        const day = String(val.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      }),
      z.null(),
      z.undefined(),
    ])
    .nullable()
    .optional();
