import { relations } from 'drizzle-orm';
import { jsonb, pgEnum, pgTable, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { z } from 'zod';
import testCase, { selectTestCaseSchema } from './test-case';
import user, { selectUserSchema } from './user';

export const testCaseChangeTypeEnum = pgEnum('test_case_change_type', [
  'CREATE',
  'UPDATE',
  'DELETE',
  'RESTORE',
  'COMMENT_ADDED',
  'COMMENT_UPDATED',
  'COMMENT_DELETED',
  'ATTACHMENT_ADDED',
  'ATTACHMENT_DELETED',
  'EXECUTION_ADDED',
  'EXECUTION_UPDATED',
]);

const testCaseHistory = pgTable('test_case_history', {
  id: uuid('id').primaryKey().defaultRandom(),
  testCaseId: uuid('test_case_id')
    .references(() => testCase.id, { onDelete: 'cascade' })
    .notNull(),
  changeType: testCaseChangeTypeEnum('change_type').notNull(),
  changedBy: uuid('changed_by')
    .references(() => user.id)
    .notNull(),
  // Generic storage for field changes: { fieldName: { oldValue, newValue } }
  changedFields: jsonb('changed_fields'),
  // Additional metadata for comments, attachments, executions, etc.
  metadata: jsonb('metadata'),
  // Summary description of what changed (optional, for easier readability)
  summary: varchar('summary', { length: 500 }),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

export const testCaseHistoryRelations = relations(testCaseHistory, ({ one }) => ({
  testCase: one(testCase, {
    fields: [testCaseHistory.testCaseId],
    references: [testCase.id],
  }),
  user: one(user, {
    fields: [testCaseHistory.changedBy],
    references: [user.id],
  }),
}));

export default testCaseHistory;

// Zod schemas
export const selectTestCaseHistorySchema = createSelectSchema(testCaseHistory, {
  changedFields: () =>
    z
      .record(
        z.object({
          oldValue: z.union([z.string(), z.number(), z.boolean(), z.array(z.string()), z.null()]),
          newValue: z.union([z.string(), z.number(), z.boolean(), z.array(z.string()), z.null()]),
        }),
      )
      .nullable(),
  metadata: () =>
    z
      .object({
        comment: z
          .object({
            id: z.string(),
            content: z.string(),
          })
          .optional(),
        attachment: z
          .object({
            id: z.string(),
            filename: z.string(),
            size: z.number().optional(),
            mimeType: z.string().optional(),
          })
          .optional(),
        execution: z
          .object({
            id: z.string(),
            status: z.string(),
            result: z.string().optional(),
          })
          .optional(),
      })
      .passthrough()
      .nullable(),
}).extend({
  user: selectUserSchema.partial().nullable(),
  testCase: selectTestCaseSchema.partial().nullable(),
});

export const insertTestCaseHistorySchema = createInsertSchema(testCaseHistory, {
  changedFields: () =>
    z
      .record(
        z.object({
          oldValue: z.union([z.string(), z.number(), z.boolean(), z.array(z.string()), z.null()]),
          newValue: z.union([z.string(), z.number(), z.boolean(), z.array(z.string()), z.null()]),
        }),
      )
      .nullable(),
  metadata: () =>
    z
      .object({
        comment: z
          .object({
            id: z.string(),
            content: z.string(),
          })
          .optional(),
        attachment: z
          .object({
            id: z.string(),
            filename: z.string(),
            size: z.number().optional(),
            mimeType: z.string().optional(),
          })
          .optional(),
        execution: z
          .object({
            id: z.string(),
            status: z.string(),
            result: z.string().optional(),
          })
          .optional(),
      })
      .passthrough()
      .nullable(),
})
  .required({
    testCaseId: true,
    changedBy: true,
    changeType: true,
  })
  .omit({
    id: true,
    createdAt: true,
  });

export const patchTestCaseHistorySchema = insertTestCaseHistorySchema.partial();

// Types
export type TestCaseHistory = typeof testCaseHistory.$inferSelect;
export type TestCaseHistoryInsert = typeof insertTestCaseHistorySchema._type;
export type TestCaseHistoryPatch = typeof patchTestCaseHistorySchema._type;
export type TestCaseHistorySelect = typeof selectTestCaseHistorySchema._type;

// Metadata type definition for better type safety
export type TestCaseHistoryMetadata = {
  // For comment-related changes
  comment?: {
    id: string;
    content: string;
  };
  // For attachment-related changes
  attachment?: {
    id: string;
    filename: string;
    size?: number;
    mimeType?: string;
  };
  // For test execution-related changes
  execution?: {
    id: string;
    status: string;
    result?: string;
  };
  // Additional metadata can be added here
  [key: string]: unknown;
};
