import { relations } from 'drizzle-orm';
import {
  boolean,
  integer,
  jsonb,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import organization, { selectOrganizationSchema } from './organization';
import { jsonField, requiredTextField, textField } from './validation-utils';
import workItemType, { selectWorkItemTypeSchema } from './work-item-type';

export const fieldTypeEnum = pgEnum('field_type', [
  'text',
  'number',
  'date',
  'boolean',
  'select',
  'multi_select',
  'user',
  'json',
]);

const workItemField = pgTable('work_item_field', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 100 }).notNull(),
  description: text('description'),
  fieldType: fieldTypeEnum('field_type').notNull(),
  isRequired: boolean('is_required').default(false).notNull(),
  defaultValue: jsonb('default_value'),
  options: jsonb('options')
    .$type<object>()
    .$default(() => ({}))
    .notNull(),
  workItemTypeId: uuid('work_item_type_id')
    .references(() => workItemType.id)
    .notNull(),
  organizationId: uuid('organization_id').references(() => organization.id),
  order: integer('order').default(0).notNull(),
  isFilterable: boolean('is_filterable').default(true).notNull(),
  isDisplayedInGrid: boolean('is_displayed_in_grid').default(true).notNull(),
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const workItemFieldRelations = relations(workItemField, ({ one }) => ({
  workItemType: one(workItemType, {
    fields: [workItemField.workItemTypeId],
    references: [workItemType.id],
  }),
  organization: one(organization, {
    fields: [workItemField.organizationId],
    references: [organization.id],
  }),
}));

export default workItemField;

// Zod schemas
export const selectWorkItemFieldSchema = createSelectSchema(workItemField, {
  defaultValue: jsonField,
  options: jsonField,
}).extend({
  workItemType: selectWorkItemTypeSchema.partial().nullable(),
  organization: selectOrganizationSchema.partial().nullable(),
});

export const insertWorkItemFieldSchema = createInsertSchema(workItemField, {
  name: () => requiredTextField(1, 100),
  description: () => textField(),
  defaultValue: jsonField,
  options: jsonField,
})
  .required({
    name: true,
    fieldType: true,
    workItemTypeId: true,
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchWorkItemFieldSchema = insertWorkItemFieldSchema.partial();

// Types
export type WorkItemField = typeof workItemField.$inferSelect;
export type WorkItemFieldInsert = typeof insertWorkItemFieldSchema._type;
export type WorkItemFieldPatch = typeof patchWorkItemFieldSchema._type;
export type WorkItemFieldSelect = typeof selectWorkItemFieldSchema._type;
