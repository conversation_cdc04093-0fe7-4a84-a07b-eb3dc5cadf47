import { relations } from 'drizzle-orm';
import {
  boolean,
  date,
  integer,
  jsonb,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import user, { selectUserSchema } from './user';
import {
  dateField,
  datetimeField,
  jsonField,
  requiredTextField,
  textField,
} from './validation-utils';
import workflow, { selectWorkflowSchema } from './workflow';
import workspace, { selectWorkspaceSchema } from './workspace';

export const projectStatusEnum = pgEnum('project_status', [
  'planning',
  'active',
  'completed',
  'archived',
]);
export const projectVisibilityEnum = pgEnum('project_visibility', ['public', 'private']);

const project = pgTable('project', {
  id: uuid('id').primaryKey().defaultRandom(),
  workspaceId: uuid('workspace_id')
    .references(() => workspace.id)
    .notNull(),
  workflowId: uuid('workflow_id').references(() => workflow.id),
  name: varchar('name', { length: 255 }).notNull(),
  key: varchar('key', { length: 10 }).notNull(),
  description: text('description'),
  icon: varchar('icon', { length: 255 }),
  color: varchar('color', { length: 7 }),
  startDate: date('start_date'),
  targetDate: date('target_date'),
  actualEndDate: date('actual_end_date'),
  status: projectStatusEnum('status').default('planning').notNull(),
  visibility: projectVisibilityEnum('visibility').default('private').notNull(),
  defaultAssigneeId: uuid('default_assignee_id').references(() => user.id),
  lastTicketNumber: integer('last_ticket_number').default(0).notNull(),
  settings: jsonb('settings')
    .$type<object>()
    .$default(() => ({}))
    .notNull(),
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  createdBy: uuid('created_by').references(() => user.id),
});

export const projectRelations = relations(project, ({ one }) => ({
  workspace: one(workspace, {
    fields: [project.workspaceId],
    references: [workspace.id],
  }),
  workflow: one(workflow, {
    fields: [project.workflowId],
    references: [workflow.id],
  }),
  creator: one(user, {
    fields: [project.createdBy],
    references: [user.id],
  }),
  defaultAssignee: one(user, {
    fields: [project.defaultAssigneeId],
    references: [user.id],
  }),
}));

export default project;

// Zod schemas
export const selectProjectSchema = createSelectSchema(project, {
  settings: jsonField(),
  startDate: dateField(),
  targetDate: dateField(),
  actualEndDate: dateField(),
  createdAt: datetimeField(),
  updatedAt: datetimeField(),
}).extend({
  workspace: selectWorkspaceSchema.partial().nullable(),
  workflow: selectWorkflowSchema.partial().nullable(),
  creator: selectUserSchema.partial().nullable(),
  defaultAssignee: selectUserSchema.partial().nullable(),
});

export const insertProjectSchema = createInsertSchema(project, {
  name: () => requiredTextField(1, 255),
  key: () => requiredTextField(1, 10),
  description: () => textField(),
  icon: () => textField(),
  color: () => textField(7),
  settings: jsonField(),
  startDate: dateField(),
  targetDate: dateField(),
  actualEndDate: dateField(),
})
  .required({
    workspaceId: true,
    name: true,
    key: true,
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchProjectSchema = insertProjectSchema.partial();

// Types
export type Project = typeof project.$inferSelect;
export type ProjectInsert = typeof insertProjectSchema._type;
export type ProjectPatch = typeof patchProjectSchema._type;
export type ProjectSelect = typeof selectProjectSchema._type;
