import { relations } from 'drizzle-orm';
import { boolean, pgTable, text, timestamp, unique, uuid, varchar } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import user from './user';

const role = pgTable(
  'role',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    name: varchar('name', { length: 255 }).notNull(),
    description: text('description'),
    identifier: varchar('identifier', { length: 255 }).notNull().unique(),
    level: varchar('level', { length: 50 }).notNull(),
    isSystem: boolean('is_system').default(false).notNull(),
    isActive: boolean('is_active').default(true).notNull(),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
    createdBy: uuid('created_by').references(() => user.id),
  },
  (table) => [unique('identifier_idx').on(table.identifier)],
);

export const roleRelations = relations(role, ({ one }) => ({
  creator: one(user, {
    fields: [role.createdBy],
    references: [user.id],
  }),
}));

export default role;

// Zod schemas
export const selectRoleSchema = createSelectSchema(role);

export const insertRoleSchema = createInsertSchema(role, {
  name: (schema) => schema.name.min(1).max(255),
  identifier: (schema) => schema.identifier.min(1).max(255),
  level: (schema) => schema.level.min(1).max(50),
})
  .required({
    name: true,
    identifier: true,
    level: true,
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchRoleSchema = insertRoleSchema.partial();

// Types
export type Role = typeof role.$inferSelect;
export type RoleInsert = typeof insertRoleSchema._type;
export type RolePatch = typeof patchRoleSchema._type;
export type RoleSelect = typeof selectRoleSchema._type;
