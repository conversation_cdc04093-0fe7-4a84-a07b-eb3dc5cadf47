import { relations, sql } from 'drizzle-orm';
import { boolean, pgEnum, pgTable, timestamp, uniqueIndex, uuid } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { selectOrganizationSchema } from './organization';
import { selectProjectSchema } from './project';
import role, { selectRoleSchema } from './role';
import user, { selectUserSchema } from './user';
import { selectWorkspaceSchema } from './workspace';

export const roleAssignmentScopeEnum = pgEnum('role_assignment_scope', [
  'organization',
  'workspace',
  'project',
]);

const roleAssignment = pgTable(
  'role_assignment',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    userId: uuid('user_id')
      .references(() => user.id)
      .notNull(),
    roleId: uuid('role_id')
      .references(() => role.id)
      .notNull(),
    scopeType: roleAssignmentScopeEnum('scope_type').notNull(),
    scopeId: uuid('scope_id').notNull(),
    isActive: boolean('is_active').default(true).notNull(),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (table) => {
    return {
      // Unique constraint: only one active role per user per scope
      uniqueActiveRole: uniqueIndex('unique_active_role')
        .on(table.userId, table.scopeType, table.scopeId, table.isActive)
        .where(sql`is_active = true`),
    };
  },
);

export const roleAssignmentRelations = relations(roleAssignment, ({ one }) => ({
  user: one(user, {
    fields: [roleAssignment.userId],
    references: [user.id],
  }),
  role: one(role, {
    fields: [roleAssignment.roleId],
    references: [role.id],
  }),
}));

export default roleAssignment;

// Zod schemas
export const selectRoleAssignmentSchema = createSelectSchema(roleAssignment).extend({
  user: selectUserSchema.partial(),
  role: selectRoleSchema.partial(),
  organization: selectOrganizationSchema.partial().nullable(),
  workspace: selectWorkspaceSchema.partial().nullable(),
  project: selectProjectSchema.partial().nullable(),
});

export const insertRoleAssignmentSchema = createInsertSchema(roleAssignment)
  .required({
    userId: true,
    roleId: true,
    scopeType: true,
    scopeId: true,
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchRoleAssignmentSchema = insertRoleAssignmentSchema.partial();

// Types
export type RoleAssignment = typeof roleAssignment.$inferSelect;
export type RoleAssignmentInsert = typeof insertRoleAssignmentSchema._type;
export type RoleAssignmentPatch = typeof patchRoleAssignmentSchema._type;
export type RoleAssignmentSelect = typeof selectRoleAssignmentSchema._type;
