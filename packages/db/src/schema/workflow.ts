import { relations } from 'drizzle-orm';
import {
  boolean,
  integer,
  jsonb,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import organization, { selectOrganizationSchema } from './organization';
import status from './status';
import user, { selectUserSchema } from './user';
import { jsonField, requiredTextField, textField } from './validation-utils';

export const workflowStatusEnum = pgEnum('workflow_status_enum', ['draft', 'active', 'archived']);

const workflow = pgTable('workflow', {
  id: uuid('id').primaryKey().defaultRandom(),
  organizationId: uuid('organization_id').references(() => organization.id),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  isSystem: boolean('is_system').default(false).notNull(),
  isDefault: boolean('is_default').default(false).notNull(),
  initialStatusId: uuid('initial_status_id').references(() => status.id),
  version: integer('version').default(1).notNull(),
  settings: jsonb('settings')
    .$type<object>()
    .$default(() => ({}))
    .notNull(),
  status: workflowStatusEnum('status').default('draft').notNull(),
  isActive: boolean('is_active').default(true).notNull(),
  createdBy: uuid('created_by').references(() => user.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const workflowRelations = relations(workflow, ({ one }) => ({
  organization: one(organization, {
    fields: [workflow.organizationId],
    references: [organization.id],
  }),
  creator: one(user, {
    fields: [workflow.createdBy],
    references: [user.id],
  }),
  initialStatus: one(status, {
    fields: [workflow.initialStatusId],
    references: [status.id],
  }),
}));

export default workflow;

// Zod schemas
export const selectWorkflowSchema = createSelectSchema(workflow, {
  settings: jsonField,
}).extend({
  organization: selectOrganizationSchema.partial().nullable(),
  creator: selectUserSchema.partial().nullable(),
});

export const insertWorkflowSchema = createInsertSchema(workflow, {
  name: () => requiredTextField(1, 255),
  description: () => textField(),
  settings: jsonField,
})
  .required({
    name: true,
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchWorkflowSchema = insertWorkflowSchema.partial();

// Types
export type Workflow = typeof workflow.$inferSelect;
export type WorkflowInsert = typeof insertWorkflowSchema._type;
export type WorkflowPatch = typeof patchWorkflowSchema._type;
export type WorkflowSelect = typeof selectWorkflowSchema._type;
