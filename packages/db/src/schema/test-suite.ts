import { relations } from 'drizzle-orm';
import type { AnyPgColumn } from 'drizzle-orm/pg-core';
import { boolean, integer, pgEnum, pgTable, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { z } from 'zod';
import testPlan, { selectTestPlanSchema } from './test-plan';
import { requiredTextField, textField } from './validation-utils';

export const testSuiteTypeEnum = pgEnum('test_suite_type', ['static', 'requirement', 'query']);

const testSuite = pgTable('test_suite', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  description: varchar('description', { length: 1000 }),
  testPlanId: uuid('test_plan_id')
    .references(() => testPlan.id, { onDelete: 'cascade' })
    .notNull(),
  type: testSuiteTypeEnum('type').default('static').notNull(),
  requirementQuery: varchar('requirement_query', { length: 1000 }),
  parentSuiteId: uuid('parent_suite_id').references((): AnyPgColumn => testSuite.id, {
    onDelete: 'cascade',
  }),
  copiedFromId: uuid('copied_from_id').references((): AnyPgColumn => testSuite.id, {
    onDelete: 'set null',
  }),
  order: integer('order').default(0).notNull(),
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const testSuiteRelations = relations(testSuite, ({ one, many }) => ({
  testPlan: one(testPlan, {
    fields: [testSuite.testPlanId],
    references: [testPlan.id],
  }),
  parentSuite: one(testSuite, {
    fields: [testSuite.parentSuiteId],
    references: [testSuite.id],
    relationName: 'parentChild',
  }),
  childSuites: many(testSuite, {
    relationName: 'parentChild',
  }),
  copiedFrom: one(testSuite, {
    fields: [testSuite.copiedFromId],
    references: [testSuite.id],
    relationName: 'copiedFrom',
  }),
  // testCases: many(testCase), // Commented out to avoid circular dependency (replaced by testSuiteTestCases)
  // testSuiteTestCases: many(testSuiteTestCase), // Commented out to avoid circular dependency
  // comments: many(testSuiteComment), // Commented out to avoid circular dependency
}));

export default testSuite;

// Zod schemas
export const selectTestSuiteSchema = createSelectSchema(testSuite).extend({
  testPlan: selectTestPlanSchema.partial().nullable(),
});

export const insertTestSuiteSchema = createInsertSchema(testSuite, {
  name: () => requiredTextField(1, 255),
  description: () => textField(1000),
  requirementQuery: () => textField(1000),
})
  .required({
    name: true,
    testPlanId: true,
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  })
  .extend({
    includeTestCases: z.boolean().default(true).optional(),
    sourceSuiteId: z.string().uuid().optional(), // For import operation
    selectedTestCaseIds: z.array(z.string().uuid()).optional(), // For selective copy
    linkTestCases: z.boolean().default(false).optional(), // Link vs deep copy
  });

export const patchTestSuiteSchema = insertTestSuiteSchema.partial();

// Types
export type TestSuite = typeof testSuite.$inferSelect;
export type TestSuiteInsert = typeof insertTestSuiteSchema._type;
export type TestSuitePatch = typeof patchTestSuiteSchema._type;
export type TestSuiteSelect = typeof selectTestSuiteSchema._type;
