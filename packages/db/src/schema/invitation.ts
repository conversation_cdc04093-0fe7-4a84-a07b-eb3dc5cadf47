import { relations, sql } from 'drizzle-orm';
import { pgEnum, pgTable, timestamp, uniqueIndex, uuid, varchar } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { selectOrganizationSchema } from './organization';
import { selectProjectSchema } from './project';
import role, { selectRoleSchema } from './role';
import user, { selectUserSchema } from './user';
import { datetimeField, emailField } from './validation-utils';
import { selectWorkspaceSchema } from './workspace';

export const invitationScopeEnum = pgEnum('invitation_scope', [
  'organization',
  'workspace',
  'project',
]);
export const invitationStatusEnum = pgEnum('invitation_status', [
  'pending',
  'accepted',
  'rejected',
  'expired',
  'revoked',
]);

const invitation = pgTable(
  'invitation',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    email: varchar('email', { length: 255 }).notNull(),
    token: varchar('token', { length: 512 }).notNull().unique(),
    invitedBy: uuid('invited_by')
      .references(() => user.id)
      .notNull(),
    roleId: uuid('role_id')
      .references(() => role.id)
      .notNull(),
    scopeType: invitationScopeEnum('scope_type').notNull(),
    scopeId: uuid('scope_id').notNull(),
    status: invitationStatusEnum('status').default('pending').notNull(),
    expiresAt: timestamp('expires_at').notNull(),
    acceptedAt: timestamp('accepted_at'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (table) => {
    return {
      // Unique constraint: only one pending invitation per email per scope
      uniquePendingInvitation: uniqueIndex('unique_pending_invitation')
        .on(table.email, table.scopeType, table.scopeId, table.status)
        .where(sql`status = 'pending'`),
    };
  },
);

export const invitationRelations = relations(invitation, ({ one }) => ({
  inviter: one(user, {
    fields: [invitation.invitedBy],
    references: [user.id],
  }),
  role: one(role, {
    fields: [invitation.roleId],
    references: [role.id],
  }),
}));

export default invitation;

// Zod schemas
export const selectInvitationSchema = createSelectSchema(invitation, {
  expiresAt: datetimeField,
  acceptedAt: datetimeField,
}).extend({
  inviter: selectUserSchema.partial().nullable(),
  role: selectRoleSchema.partial().nullable(),
  organization: selectOrganizationSchema.partial().nullable(),
  workspace: selectWorkspaceSchema.partial().nullable(),
  project: selectProjectSchema.partial().nullable(),
});

export const insertInvitationSchema = createInsertSchema(invitation, {
  email: () => emailField(255),
  expiresAt: datetimeField,
  acceptedAt: datetimeField,
})
  .required({
    email: true,
    invitedBy: true,
    roleId: true,
    scopeType: true,
    scopeId: true,
  })
  .omit({
    id: true,
    expiresAt: true,
    token: true,
    createdAt: true,
    updatedAt: true,
  });

export const patchInvitationSchema = insertInvitationSchema.partial();

// Types
export type Invitation = typeof invitation.$inferSelect;
export type InvitationInsert = typeof insertInvitationSchema._type;
export type InvitationPatch = typeof patchInvitationSchema._type;
export type InvitationSelect = typeof selectInvitationSchema._type;
