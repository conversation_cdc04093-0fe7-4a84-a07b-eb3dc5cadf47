import { boolean, jsonb, pgTable, text, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { z } from 'zod';
import user from './user';

export const notification = pgTable('notification', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id')
    .notNull()
    .references(() => user.id, { onDelete: 'cascade' }),
  title: varchar('title', { length: 255 }).notNull(),
  body: text('body').notNull(),
  type: varchar('type', { length: 50 }).notNull(),
  data: jsonb('data'),
  isRead: boolean('is_read').default(false).notNull(),
  readAt: timestamp('read_at'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Base schemas
export const insertNotificationSchema = createInsertSchema(notification);
export const selectNotificationSchema = createSelectSchema(notification);

// Custom schemas for API operations
export const createNotificationSchema = z.object({
  userId: z.string().uuid(),
  title: z.string().min(1).max(255),
  body: z.string().min(1),
  type: z.string().min(1).max(50),
  data: z.record(z.any()).optional(),
});

export const patchNotificationSchema = z.object({
  isRead: z.boolean().optional(),
  readAt: z.string().datetime().optional(),
});

// Type exports
export type Notification = z.infer<typeof selectNotificationSchema>;
export type CreateNotification = z.infer<typeof createNotificationSchema>;
export type PatchNotification = z.infer<typeof patchNotificationSchema>;

export default notification;
