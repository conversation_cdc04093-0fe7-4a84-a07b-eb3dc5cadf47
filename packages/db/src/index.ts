import * as schema from '@repo/db/schema';
import { drizzle, type PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import env from './env';

let db: PostgresJsDatabase<typeof schema>;
let client: ReturnType<typeof postgres>;

if (process.env.NODE_ENV === 'production') {
  client = postgres(env.DATABASE_URL || 'fallback_string');
  db = drizzle(client, { schema });
} else {
  if (!(global as unknown as { database: PostgresJsDatabase<typeof schema> }).database) {
    client = postgres(env.DATABASE_URL || '');
    (global as unknown as { database: PostgresJsDatabase<typeof schema> }).database = drizzle(
      client,
      { schema },
    );
  }
  db = (global as unknown as { database: PostgresJsDatabase<typeof schema> }).database;
}

// get DB from db
export type DB = typeof db;
export type Client = typeof client;

export { db, client as pg };
