:root {
  /* Color System */
  --editor-text-default: hsl(240 10% 3.9%);
  --editor-text-gray: hsl(240 3.8% 46.1%);
  --editor-text-brown: hsl(25 95% 53%);
  --editor-text-orange: hsl(24 95% 53%);
  --editor-text-yellow: hsl(48 96% 53%);
  --editor-text-green: hsl(142 71% 45%);
  --editor-text-blue: hsl(221 83% 53%);
  --editor-text-purple: hsl(269 97% 85%);
  --editor-text-pink: hsl(336 80% 58%);
  --editor-text-red: hsl(0 84% 60%);

  /* Background Colors */
  --editor-bg-default: hsl(0 0% 100%);
  --editor-bg-subtle: hsl(0 0% 98%);
  --editor-bg-muted: hsl(240 5% 96%);

  /* Highlight Colors */
  --editor-highlight-default: hsl(0 0% 98%);
  --editor-highlight-gray: hsl(240 5% 96%);
  --editor-highlight-brown: hsl(43 96% 96%);
  --editor-highlight-orange: hsl(33 100% 96%);
  --editor-highlight-yellow: hsl(54 100% 96%);
  --editor-highlight-green: hsl(142 71% 96%);
  --editor-highlight-blue: hsl(217 91% 96%);
  --editor-highlight-purple: hsl(269 97% 96%);
  --editor-highlight-pink: hsl(336 80% 96%);
  --editor-highlight-red: hsl(0 84% 96%);

  /* Border Colors */
  --editor-border-default: hsl(240 5% 88%);
  --editor-border-strong: hsl(240 5% 65%);

  /* Spacing System */
  --editor-spacing-1: 0.25rem;
  --editor-spacing-2: 0.5rem;
  --editor-spacing-3: 0.75rem;
  --editor-spacing-4: 1rem;
  --editor-spacing-6: 1.5rem;
  --editor-spacing-8: 2rem;
  --editor-spacing-12: 3rem;
  --editor-spacing-16: 4rem;

  /* Typography */
  --editor-font-sans:
    system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  --editor-font-mono:
    ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;
  --editor-font-serif: Georgia, Cambria, "Times New Roman", Times, serif;

  /* Animation */
  --editor-transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --editor-transition-normal: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --editor-transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);

  /* Shadows */
  --editor-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --editor-shadow-md:
    0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --editor-shadow-lg:
    0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Dark Mode Custom Properties */
.dark {
  --editor-text-default: hsl(0 0% 98%);
  --editor-text-gray: hsl(240 5% 64.9%);
  --editor-text-brown: hsl(25 95% 53%);
  --editor-text-orange: hsl(24 95% 53%);
  --editor-text-yellow: hsl(48 96% 53%);
  --editor-text-green: hsl(142 71% 45%);
  --editor-text-blue: hsl(221 83% 53%);
  --editor-text-purple: hsl(269 97% 85%);
  --editor-text-pink: hsl(336 80% 58%);
  --editor-text-red: hsl(0 84% 60%);

  --editor-bg-default: hsl(240 10% 3.9%);
  --editor-bg-subtle: hsl(240 3.7% 15.9%);
  --editor-bg-muted: hsl(240 5% 26%);

  --editor-highlight-default: hsl(240 3.7% 15.9%);
  --editor-highlight-gray: hsl(240 5% 26%);
  --editor-highlight-brown: hsl(43 96% 10%);
  --editor-highlight-orange: hsl(33 100% 10%);
  --editor-highlight-yellow: hsl(54 100% 10%);
  --editor-highlight-green: hsl(142 71% 10%);
  --editor-highlight-blue: hsl(217 91% 10%);
  --editor-highlight-purple: hsl(269 97% 10%);
  --editor-highlight-pink: hsl(336 80% 10%);
  --editor-highlight-red: hsl(0 84% 10%);

  --editor-border-default: hsl(240 5% 26%);
  --editor-border-strong: hsl(240 5% 64.9%);
}

/* Core Editor Styles */
.ProseMirror {
  caret-color: var(--editor-text-default);
  outline: none;
  padding: var(--editor-spacing-4) var(--editor-spacing-4);
  margin: 0 auto;
  max-width: 90ch;
  font-family: var(--editor-font-sans);
  position: relative;
  /* background-color: var(--editor-bg-default); */
  color: var(--editor-text-default);
  transition: all var(--editor-transition-normal);
  min-height: 100%;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ProseMirror:focus {
  outline: none;
  box-shadow: none;
}

.ProseMirror .selection,
.ProseMirror *::selection {
  background-color: var(--editor-highlight-blue);
  /* color: var(--editor-text-default); */
}

.ProseMirror > .react-renderer {
  margin: var(--editor-spacing-12) 0;
  transition: all var(--editor-transition-normal);
}

.ProseMirror > .react-renderer:first-child {
  margin-top: 0;
}

.ProseMirror > .react-renderer:last-child {
  margin-bottom: 0;
}

/* Typography Styles */
.ProseMirror p {
  line-height: 0.6;
  margin: var(--editor-spacing-4) 0;
  color: var(--editor-text-default);
  font-size: 1.125rem;
}

.ProseMirror > p {
  margin: var(--editor-spacing-6) 0;
}

.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3,
.ProseMirror h4 {
  font-family: var(--editor-font-sans);
  font-weight: 700;
  letter-spacing: -0.025em;
  color: var(--editor-text-default);
  scroll-margin-top: var(--editor-spacing-16);
  line-height: 1.2;
}

.ProseMirror h1 {
  font-size: 2.5rem;
  margin: var(--editor-spacing-8) 0 var(--editor-spacing-4);
}

.ProseMirror h2 {
  font-size: 2rem;
  margin: var(--editor-spacing-8) 0 var(--editor-spacing-4);
}

.ProseMirror h3 {
  font-size: 1.5rem;
  margin: var(--editor-spacing-6) 0 var(--editor-spacing-3);
}

.ProseMirror h4 {
  font-size: 1.25rem;
  margin: var(--editor-spacing-4) 0 var(--editor-spacing-2);
}

.ProseMirror a {
  color: var(--editor-text-blue);
  cursor: pointer;
  text-decoration: underline;
  text-decoration-thickness: 0.1em;
  text-underline-offset: 0.2em;
  transition: all var(--editor-transition-fast);
}

.ProseMirror a:hover {
  color: var(--editor-text-blue);
  text-decoration-thickness: 0.2em;
}

.ProseMirror code {
  font-family: var(--editor-font-mono);
  font-size: 0.9em;
  background-color: var(--editor-bg-muted);
  padding: 0.2em 0.4em;
  border-radius: 4px;
  color: var(--editor-text-default);
  border: 1px solid var(--editor-border-default);
}

.ProseMirror pre {
  margin: var(--editor-spacing-6) 0;
  padding: var(--editor-spacing-4);
  background-color: var(--editor-bg-subtle);
  border-radius: 8px;
  overflow-x: auto;
  border: 1px solid var(--editor-border-default);
}

.ProseMirror pre code {
  background-color: transparent;
  padding: 0;
  border: none;
  font-size: 0.875rem;
  line-height: 1.7;
  color: var(--editor-text-default);
}

.ProseMirror blockquote {
  margin: var(--editor-spacing-6) 0;
  padding: var(--editor-spacing-4) var(--editor-spacing-6);
  border-left: 4px solid var(--editor-border-strong);
  font-style: italic;
  color: var(--editor-text-gray);
  background-color: var(--editor-bg-subtle);
  border-radius: 0 8px 8px 0;
}

/* Lists */
.ProseMirror ul,
.ProseMirror ol {
  margin: var(--editor-spacing-4) 0;
  padding-left: var(--editor-spacing-6);
}

.ProseMirror li {
  margin: var(--editor-spacing-2) 0;
  padding-left: var(--editor-spacing-2);
}

.ProseMirror ul {
  list-style-type: disc;
}

.ProseMirror ul ul {
  list-style-type: circle;
}

.ProseMirror ul ul ul {
  list-style-type: square;
}

.ProseMirror ol {
  list-style-type: decimal;
}

.ProseMirror ol ol {
  list-style-type: lower-alpha;
}

.ProseMirror ol ol ol {
  list-style-type: lower-roman;
}

/* Tables */
.ProseMirror table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin: var(--editor-spacing-6) 0;
  border: 1px solid var(--editor-border-default);
  border-radius: 8px;
  overflow: hidden;
}

.ProseMirror th {
  background-color: var(--editor-bg-subtle);
  font-weight: 600;
  text-align: left;
  padding: var(--editor-spacing-3) var(--editor-spacing-4);
  border-bottom: 2px solid var(--editor-border-default);
}

.ProseMirror td {
  padding: var(--editor-spacing-3) var(--editor-spacing-4);
  border-bottom: 1px solid var(--editor-border-default);
  transition: background-color var(--editor-transition-fast);
}

.ProseMirror tr:last-child td {
  border-bottom: none;
}

.ProseMirror tr:hover td {
  background-color: var(--editor-bg-subtle);
}

/* Images */
.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  border: 1px solid var(--editor-border-default);
  box-shadow: var(--editor-shadow-sm);
  transition: all var(--editor-transition-normal);
  display: block;
  margin: var(--editor-spacing-1) auto;
}

.ProseMirror img:hover {
  box-shadow: var(--editor-shadow-lg);
  transform: translateY(-2px);
}

/* Horizontal Rule */
.ProseMirror hr {
  margin: var(--editor-spacing-8) 0;
  border: none;
  border-top: 2px solid var(--editor-border-default);
}

/* Floating Menu & Toolbar */
.floating-menu {
  background-color: var(--editor-bg-default);
  border: 1px solid var(--editor-border-default);
  box-shadow: var(--editor-shadow-lg);
  border-radius: 8px;
  padding: var(--editor-spacing-1);
  display: flex;
  gap: var(--editor-spacing-1);
  align-items: center;
  animation: fadeIn var(--editor-transition-normal);
  backdrop-filter: blur(8px);
}

.toolbar-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  height: 2.25rem;
  padding: 0 var(--editor-spacing-3);
  transition: all var(--editor-transition-fast);
  background-color: transparent;
  color: var(--editor-text-default);
  border: 1px solid transparent;
}

.toolbar-button:hover {
  background-color: var(--editor-bg-subtle);
  color: var(--editor-text-default);
}

.toolbar-button:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px var(--editor-border-strong);
}

.toolbar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-button.active {
  background-color: var(--editor-bg-muted);
  color: var(--editor-text-blue);
}

/* Placeholder Styles
.ProseMirror p.is-editor-empty:first-child::before {
  content: "Start writing or press '/' for commands...";
  color: var(--editor-text-gray);
  pointer-events: none;
  float: left;
  height: 0;
} */

/* Mobile Optimizations */
@media (max-width: 640px) {
  .ProseMirror {
    padding: var(--editor-spacing-3) var(--editor-spacing-3);
  }

  .ProseMirror h1 {
    font-size: 2rem;
  }
  .ProseMirror h2 {
    font-size: 1.75rem;
  }
  .ProseMirror h3 {
    font-size: 1.5rem;
  }
  .ProseMirror h4 {
    font-size: 1.25rem;
  }
  .ProseMirror p {
    font-size: 1rem;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Print Styles */
@media print {
  .ProseMirror {
    padding: 0;
    max-width: none;
  }

  .floating-menu,
  .toolbar-button {
    display: none;
  }
}

.is-editor-empty::before {
  color: var(--editor-text-gray);
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}
