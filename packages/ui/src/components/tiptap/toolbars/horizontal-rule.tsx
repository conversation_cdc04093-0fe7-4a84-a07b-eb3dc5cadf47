'use client';

import { Button, type ButtonProps } from '@repo/ui/components/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@repo/ui/components/tooltip';
import { cn } from '@repo/ui/lib/utils';
import { SeparatorHorizontal } from 'lucide-react';
import React from 'react';
import { useToolbar } from './toolbar-provider.js';

const HorizontalRuleToolbar = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, onClick, children, ...props }, ref) => {
    const { editor } = useToolbar();
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            className={cn('h-8 w-8 p-0 sm:h-9 sm:w-9', className)}
            onClick={(e) => {
              editor?.chain().focus().setHorizontalRule().run();
              onClick?.(e);
            }}
            ref={ref}
            size="icon"
            variant="ghost"
            {...props}
          >
            {children ?? <SeparatorHorizontal className="h-4 w-4" />}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <span>Horizontal Rule</span>
        </TooltipContent>
      </Tooltip>
    );
  },
);

HorizontalRuleToolbar.displayName = 'HorizontalRuleToolbar';

export { HorizontalRuleToolbar };
