'use client';

import { Button, type ButtonProps } from '@repo/ui/components/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@repo/ui/components/tooltip';
import { cn } from '@repo/ui/lib/utils';
import { ItalicIcon } from 'lucide-react';
import React from 'react';
import { useToolbar } from './toolbar-provider.js';

const ItalicToolbar = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, onClick, children, ...props }, ref) => {
    const { editor } = useToolbar();
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            className={cn(
              'h-8 w-8 p-0 sm:h-9 sm:w-9',
              editor?.isActive('italic') && 'bg-accent',
              className,
            )}
            disabled={!editor?.can().chain().focus().toggleItalic().run()}
            onClick={(e) => {
              editor?.chain().focus().toggleItalic().run();
              onClick?.(e);
            }}
            ref={ref}
            size="icon"
            variant="ghost"
            {...props}
          >
            {children ?? <ItalicIcon className="h-4 w-4" />}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <span>Italic</span>
          <span className="ml-1 text-gray-11 text-xs">(cmd + i)</span>
        </TooltipContent>
      </Tooltip>
    );
  },
);

ItalicToolbar.displayName = 'ItalicToolbar';

export { ItalicToolbar };
