'use client';

import { But<PERSON> } from '@repo/ui/components/button';
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@repo/ui/components/drawer';
import { cn } from '@repo/ui/lib/utils';
import { ChevronDown } from 'lucide-react';
import React, { useState } from 'react';

interface MobileToolbarGroupProps {
  label: string;
  children: React.ReactNode;
  className?: string;
}

export const MobileToolbarGroup = ({ label, children, className }: MobileToolbarGroupProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const closeDrawer = () => setIsOpen(false);

  return (
    <Drawer onOpenChange={setIsOpen} open={isOpen}>
      <DrawerTrigger asChild>
        <Button
          className={cn('h-8 w-max gap-1 px-3 font-normal', className)}
          size="sm"
          variant="ghost"
        >
          {label}
          <ChevronDown className="h-4 w-4" />
        </Button>
      </DrawerTrigger>
      <DrawerContent>
        <DrawerHeader>
          <DrawerTitle className="text-start">{label}</DrawerTitle>
        </DrawerHeader>
        <div className="flex flex-col p-4">
          {React.Children.map(children, (child) =>
            React.isValidElement(child)
              ? React.cloneElement(child, { closeDrawer } as {
                  closeDrawer: () => void;
                })
              : child,
          )}
        </div>
      </DrawerContent>
    </Drawer>
  );
};

export const MobileToolbarItem = ({
  children,
  active,
  onClick,
  closeDrawer,
  ...props
}: React.ButtonHTMLAttributes<HTMLButtonElement> & {
  active?: boolean;
  closeDrawer?: () => void;
  onClick?: React.MouseEventHandler<HTMLButtonElement>;
}) => (
  <button
    className={cn(
      'flex w-full items-center rounded-md px-4 py-2 text-sm transition-colors hover:bg-accent',
      active && 'bg-accent',
    )}
    onClick={(e) => {
      onClick?.(e);
      setTimeout(() => {
        closeDrawer?.();
      }, 100);
    }}
    {...props}
  >
    {children}
  </button>
);
