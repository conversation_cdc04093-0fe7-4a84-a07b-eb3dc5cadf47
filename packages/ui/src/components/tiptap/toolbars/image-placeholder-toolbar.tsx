'use client';

import { Button, type ButtonProps } from '@repo/ui/components/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@repo/ui/components/tooltip';
import { cn } from '@repo/ui/lib/utils';
import { Image } from 'lucide-react';
import React from 'react';
import { useToolbar } from './toolbar-provider.js';

const ImagePlaceholderToolbar = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, onClick, children, ...props }, ref) => {
    const { editor } = useToolbar();
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            className={cn(
              'h-8 w-8 p-0 sm:h-9 sm:w-9',
              editor?.isActive('image-placeholder') && 'bg-accent',
              className,
            )}
            onClick={(e) => {
              editor?.chain().focus().insertImagePlaceholder().run();
              onClick?.(e);
            }}
            ref={ref}
            size="icon"
            variant="ghost"
            {...props}
          >
            {children ?? <Image className="h-4 w-4" />}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <span>Image</span>
        </TooltipContent>
      </Tooltip>
    );
  },
);

ImagePlaceholderToolbar.displayName = 'ImagePlaceholderToolbar';

export { ImagePlaceholderToolbar };
