import { <PERSON>roll<PERSON><PERSON>, <PERSON>rollBar } from '@repo/ui/components/scroll-area';
import { Separator } from '@repo/ui/components/separator';
import { TooltipProvider } from '@repo/ui/components/tooltip';
import type { Editor } from '@tiptap/core';
import { AlignmentTooolbar } from './alignment.js';
import { BlockquoteToolbar } from './blockquote.js';
import { BoldToolbar } from './bold.js';
import { BulletListToolbar } from './bullet-list.js';
import { CodeToolbar } from './code.js';
import { CodeBlockToolbar } from './code-block.js';
import { ColorHighlightToolbar } from './color-and-highlight.js';
import { HeadingsToolbar } from './headings.js';
import { HorizontalRuleToolbar } from './horizontal-rule.js';
import { ImagePlaceholderToolbar } from './image-placeholder-toolbar.js';
import { ItalicToolbar } from './italic.js';
import { LinkToolbar } from './link.js';
import { OrderedListToolbar } from './ordered-list.js';
import { RedoToolbar } from './redo.js';
import { SearchAndReplaceToolbar } from './search-and-replace-toolbar.js';
import { StrikeThroughToolbar } from './strikethrough.js';
import { ToolbarProvider } from './toolbar-provider.js';
import { UnderlineToolbar } from './underline.js';
import { UndoToolbar } from './undo.js';

export const EditorToolbar = ({ editor }: { editor: Editor }) => {
  return (
    <div className="sticky top-0 z-20 hidden w-full border-b bg-background sm:block">
      <ToolbarProvider editor={editor}>
        <TooltipProvider>
          <ScrollArea className="h-fit py-0.5">
            <div>
              <div className="flex items-center gap-1 px-2">
                {/* History Group */}
                <UndoToolbar />
                <RedoToolbar />
                <Separator className="mx-1 h-7" orientation="vertical" />

                {/* Text Structure Group */}
                <HeadingsToolbar />
                <BlockquoteToolbar />
                <CodeToolbar />
                <CodeBlockToolbar />
                <Separator className="mx-1 h-7" orientation="vertical" />

                {/* Basic Formatting Group */}
                <BoldToolbar />
                <ItalicToolbar />
                <UnderlineToolbar />
                <StrikeThroughToolbar />
                <LinkToolbar />
                <Separator className="mx-1 h-7" orientation="vertical" />

                {/* Lists & Structure Group */}
                <BulletListToolbar />
                <OrderedListToolbar />
                <HorizontalRuleToolbar />
                <Separator className="mx-1 h-7" orientation="vertical" />

                {/* Alignment Group */}
                <AlignmentTooolbar />
                <Separator className="mx-1 h-7" orientation="vertical" />

                {/* Media & Styling Group */}
                <ImagePlaceholderToolbar />
                <ColorHighlightToolbar />
                <Separator className="mx-1 h-7" orientation="vertical" />

                <div className="flex-1" />

                {/* Utility Group */}
                <SearchAndReplaceToolbar />
              </div>
            </div>
            <ScrollBar className="hidden" orientation="horizontal" />
          </ScrollArea>
        </TooltipProvider>
      </ToolbarProvider>
    </div>
  );
};
