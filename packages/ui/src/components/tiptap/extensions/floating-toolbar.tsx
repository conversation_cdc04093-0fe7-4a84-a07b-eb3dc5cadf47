'use client';

import { <PERSON><PERSON><PERSON><PERSON>, ScrollBar } from '@repo/ui/components/scroll-area';
import { Separator } from '@repo/ui/components/separator';
import { TooltipProvider } from '@repo/ui/components/tooltip';
import { useMediaQuery } from '@repo/ui/hooks/use-media-querry';
import { BubbleMenu, type Editor } from '@tiptap/react';
import { useEffect } from 'react';
import { AlignmentTooolbar } from '../toolbars/alignment.js';
import { BlockquoteToolbar } from '../toolbars/blockquote.js';
import { BoldToolbar } from '../toolbars/bold.js';
import { BulletListToolbar } from '../toolbars/bullet-list.js';
import { ColorHighlightToolbar } from '../toolbars/color-and-highlight.js';
import { HeadingsToolbar } from '../toolbars/headings.js';
import { ImagePlaceholderToolbar } from '../toolbars/image-placeholder-toolbar.js';
import { ItalicToolbar } from '../toolbars/italic.js';
import { LinkToolbar } from '../toolbars/link.js';
import { OrderedListToolbar } from '../toolbars/ordered-list.js';
import { ToolbarProvider } from '../toolbars/toolbar-provider.js';
import { UnderlineToolbar } from '../toolbars/underline.js';

export function FloatingToolbar({ editor }: { editor: Editor | null }) {
  const isMobile = useMediaQuery('(max-width: 640px)');

  // Prevent default context menu on mobile
  useEffect(() => {
    if (!(editor?.options.element && isMobile)) {
      return;
    }

    const handleContextMenu = (e: Event) => {
      e.preventDefault();
    };

    const el = editor.options.element;
    el.addEventListener('contextmenu', handleContextMenu);

    return () => el.removeEventListener('contextmenu', handleContextMenu);
  }, [editor, isMobile]);

  if (!editor) {
    return null;
  }

  if (isMobile) {
    return (
      <TooltipProvider>
        <BubbleMenu
          className="mx-0 w-full min-w-full rounded-sm border bg-background shadow-sm"
          editor={editor}
          shouldShow={() => {
            // Show toolbar when editor is focused and has selection
            return editor.isEditable && editor.isFocused;
          }}
          tippyOptions={{
            duration: 100,
            placement: 'bottom',
            offset: [0, 10],
          }}
        >
          <ToolbarProvider editor={editor}>
            <ScrollArea className="h-fit w-full py-0.5">
              <div className="flex items-center gap-0.5 px-2">
                <div className="flex items-center gap-0.5 p-1">
                  {/* Primary formatting */}
                  <BoldToolbar />
                  <ItalicToolbar />
                  <UnderlineToolbar />
                  <Separator className="mx-1 h-6" orientation="vertical" />

                  {/* Structure controls */}
                  <HeadingsToolbar />
                  <BulletListToolbar />
                  <OrderedListToolbar />
                  <Separator className="mx-1 h-6" orientation="vertical" />

                  {/* Rich formatting */}
                  <ColorHighlightToolbar />
                  <LinkToolbar />
                  <ImagePlaceholderToolbar />
                  <Separator className="mx-1 h-6" orientation="vertical" />

                  {/* Additional controls */}
                  <AlignmentTooolbar />
                  <BlockquoteToolbar />
                </div>
              </div>
              <ScrollBar className="h-0.5" orientation="horizontal" />
            </ScrollArea>
          </ToolbarProvider>
        </BubbleMenu>
      </TooltipProvider>
    );
  }

  return null;
}
