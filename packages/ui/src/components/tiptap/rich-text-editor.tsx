'use client';
import { TipTapFloatingMenu } from '@repo/ui/components/tiptap/extensions/floating-menu';
import { FloatingToolbar } from '@repo/ui/components/tiptap/extensions/floating-toolbar';
import { ImageExtension } from '@repo/ui/components/tiptap/extensions/image';
import { ImagePlaceholder } from '@repo/ui/components/tiptap/extensions/image-placeholder';
import SearchAndReplace from '@repo/ui/components/tiptap/extensions/search-and-replace';
import { content } from '@repo/ui/lib/content';
import { cn } from '@repo/ui/lib/utils';
import { Color } from '@tiptap/extension-color';
import Highlight from '@tiptap/extension-highlight';
import Link from '@tiptap/extension-link';
import Placeholder from '@tiptap/extension-placeholder';
import Subscript from '@tiptap/extension-subscript';
import Superscript from '@tiptap/extension-superscript';
import TextAlign from '@tiptap/extension-text-align';
import TextStyle from '@tiptap/extension-text-style';
import Typography from '@tiptap/extension-typography';
import Underline from '@tiptap/extension-underline';
import { EditorContent, type Extension, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import './tiptap.css';
import { EditorToolbar } from './toolbars/editor-toolbar.js';

const extensions = [
  StarterKit.configure({
    orderedList: {
      HTMLAttributes: {
        class: 'list-decimal',
      },
    },
    bulletList: {
      HTMLAttributes: {
        class: 'list-disc',
      },
    },
    heading: {
      levels: [1, 2, 3, 4],
    },
  }),
  Placeholder.configure({
    emptyNodeClass: 'is-editor-empty',
    placeholder: ({ node }) => {
      switch (node.type.name) {
        case 'heading':
          return `Heading ${node.attrs.level}`;
        case 'detailsSummary':
          return 'Section title';
        case 'codeBlock':
          // never show the placeholder when editing code
          return '';
        default:
          return "Write, type '/' for commands";
      }
    },
    includeChildren: false,
  }),
  TextAlign.configure({
    types: ['heading', 'paragraph'],
  }),
  TextStyle,
  Subscript,
  Superscript,
  Underline,
  Link,
  Color,
  Highlight.configure({
    multicolor: true,
  }),
  ImageExtension,
  ImagePlaceholder,
  SearchAndReplace,
  Typography,
];

export function RichTextEditorDemo({ className }: { className?: string }) {
  const editor = useEditor({
    immediatelyRender: false,
    extensions: extensions as Extension[],
    content,
    editorProps: {
      attributes: {
        class: 'max-w-full focus:outline-none',
      },
    },
    onUpdate: ({ editor }) => {
      const html = editor.getHTML(); // remove if dont work added while fixing formting
      console.log(html);
    },
  });

  if (!editor) {
    return null;
  }

  return (
    <div
      className={cn(
        'relative max-h-full w-full overflow-hidden overflow-y-scroll border bg-card sm:pb-0',
        className,
      )}
    >
      <EditorToolbar editor={editor} />
      <FloatingToolbar editor={editor} />
      <TipTapFloatingMenu editor={editor} />
      <EditorContent
        className=" min-h-[600px] w-full min-w-full cursor-text sm:p-6"
        editor={editor}
      />
    </div>
  );
}
