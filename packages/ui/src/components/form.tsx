'use client';

import type * as LabelPrimitive from '@radix-ui/react-label';
import { Slot } from '@radix-ui/react-slot';
import type { AnyFieldApi } from '@tanstack/react-form';
import * as React from 'react';
import { cn } from '../lib/utils.js';
import { Label } from './label.js';

// FormFieldContext to pass TanStack Form field
type FormFieldContextValue = {
  field: AnyFieldApi;
  required?: boolean;
};

const FormFieldContext = React.createContext<FormFieldContextValue>({} as FormFieldContextValue);

// FormField component for TanStack Form
const FormField = ({
  field,
  required,
  children,
}: {
  field: AnyFieldApi;
  required?: boolean;
  children: React.ReactNode;
}) => {
  return (
    <FormFieldContext.Provider value={{ field, required }}>{children}</FormFieldContext.Provider>
  );
};

// useFormField hook to get field metadata
const useFormField = () => {
  const fieldContext = React.useContext(FormFieldContext);
  const itemContext = React.useContext(FormItemContext);

  if (!fieldContext) {
    throw new Error('useFormField should be used within <FormField>');
  }

  const { field } = fieldContext;
  const { id } = itemContext;

  return {
    id,
    name: field.name,
    required: fieldContext.required,
    error: field.state.meta.errors[0],
    formItemId: `${id}-form-item`,
    formMessageId: `${id}-form-item-message`,
  };
};

// FormItemContext for unique IDs
type FormItemContextValue = {
  id: string;
};

const FormItemContext = React.createContext<FormItemContextValue>({} as FormItemContextValue);

// FormItem component
const FormItem = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => {
    const id = React.useId();

    return (
      <FormItemContext.Provider value={{ id }}>
        <div className={cn('space-y-2', className)} ref={ref} {...props} />
      </FormItemContext.Provider>
    );
  },
);
FormItem.displayName = 'FormItem';

// FormLabel component with asterisk for required fields
const FormLabel = React.forwardRef<
  React.ElementRef<typeof LabelPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> & {
    required?: boolean;
  }
>(({ className, children, ...props }, ref) => {
  const { formItemId, required, error } = useFormField();

  return (
    <Label
      className={cn(
        required && 'after:ml-0.5 after:text-red-500 after:content-["*"]',
        error && 'text-destructive',
        className,
      )}
      htmlFor={formItemId}
      ref={ref}
      {...props}
    >
      {children}
    </Label>
  );
});
FormLabel.displayName = 'FormLabel';

// FormControl component
const FormControl = React.forwardRef<
  React.ElementRef<typeof Slot>,
  React.ComponentPropsWithoutRef<typeof Slot>
>(({ ...props }, ref) => {
  const { error, formItemId, formMessageId } = useFormField();

  return (
    <Slot
      aria-describedby={error ? formMessageId : undefined}
      aria-invalid={!!error}
      id={formItemId}
      ref={ref}
      {...props}
    />
  );
});
FormControl.displayName = 'FormControl';

// FormMessage component for errors
const FormMessage = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, children, ...props }, ref) => {
  const { error, formMessageId } = useFormField();
  const body =
    error && typeof error === 'object' && 'message' in error
      ? String(error.message)
      : error
        ? String(error)
        : children;

  if (!body) {
    return null;
  }

  return (
    <p
      className={cn('font-medium text-[0.8rem] text-destructive capitalize', className)}
      id={formMessageId}
      ref={ref}
      {...props}
    >
      {body}
    </p>
  );
});
FormMessage.displayName = 'FormMessage';

// Form component (basic wrapper)
const Form = ({ children, ...props }: React.ComponentPropsWithoutRef<'form'>) => {
  return (
    <form className="space-y-8" {...props}>
      {children}
    </form>
  );
};

export { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, useFormField };
