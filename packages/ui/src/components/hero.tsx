import { Button } from '@repo/components/ui/button';
import Link from 'next/link';

export function Hero() {
  return (
    <section className="py-24">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-start gap-4">
          <h1 className="font-bold text-4xl tracking-tighter sm:text-5xl md:text-6xl lg:text-7xl">
            Build your rich-text editor
          </h1>
          <p className="text-xl">Framework · Plugins · Components</p>
          <div className="mt-4 flex gap-4">
            <Button asChild size="lg">
              <Link
                href="https://tiptap-shadcn.vercel.app/"
                rel="noopener noreferrer"
                target="_blank"
              >
                Goto website
              </Link>
            </Button>
            <Button asChild size="lg" variant="outline">
              <Link
                href="https://github.com/ehtisham-afzal/tiptap-shadcn"
                rel="noopener noreferrer"
                target="_blank"
              >
                Github
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
