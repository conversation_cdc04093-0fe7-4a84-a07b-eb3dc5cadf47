import { Button } from '@repo/ui/components/button';
import { Checkbox } from '@repo/ui/components/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@repo/ui/components/dropdown-menu';
import { Input } from '@repo/ui/components/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/ui/components/table';
import {
  type CellContext,
  type ColumnDef,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  type NoInfer,
  type Row,
  type RowSelectionState,
  type SortingState,
  type Table as TanstackTable,
  useReactTable,
} from '@tanstack/react-table';
import {
  ArrowDown,
  ArrowUp,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from 'lucide-react';
import React, { useEffect, useMemo, useState } from 'react';

// Type for cell parameters
interface CellParams<TData, TValue> {
  value: NoInfer<TValue>;
  row: { original: TData };
}

// MUI-like column definition
export interface MuiColumn<TData> {
  field: keyof TData;
  headerName?: string;
  width?: number;
  sortable?: boolean;
  filterable?: boolean;
  valueFormatter?: (params: {
    value: NoInfer<TData[keyof TData]>;
  }) => string | number | Date | React.ReactNode;
  renderCell?: (params: CellParams<TData, TData[keyof TData]>) => React.ReactNode;
}

// Button configuration
interface AddButtonConfig {
  variant: 'default' | 'outline' | 'destructive' | 'link' | 'ghost';
  size: 'default' | 'sm' | 'lg' | 'icon' | null | undefined;
  title: string;
  onClick: () => void;
}

// Nested table configuration
interface NestedTableConfig<TData> {
  renderNestedTable: (row: TData) => React.ReactNode; // Returns JSX for nested table
  nestedMaxHeight?: string | number; // Max height for nested table
}

// Props for CustomDataGrid
export interface CustomDataGridProps<TData extends object> {
  rows?: TData[];
  columns?: MuiColumn<TData>[];
  pageSize?: number;
  pageSizeOptions?: number[];
  initialState?: {
    sorting?: SortingState;
    globalFilter?: string;
    rowSelection?: RowSelectionState;
    page?: number;
  };
  onRowSelectionChange?: (rowSelection: RowSelectionState) => void;
  disableSelectionOnClick?: boolean;
  autoHeight?: boolean;
  maxHeight?: string | number;
  loading?: boolean;
  rowHeight?: number;
  checkboxSelection?: boolean;
  disableColumnMenu?: boolean;
  onSortModelChange?: (sorting: SortingState) => void;
  onFilterModelChange?: (filterModel: { globalFilter: string }) => void;
  onPageChange?: (pageIndex: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  addButton?: AddButtonConfig | React.ReactNode;
  totalRows?: number;
  nestedTable?: NestedTableConfig<TData>;
  isNested?: boolean; // Indicates if this is a nested table
}

const CustomDataGrid = <TData extends object>({
  rows = [],
  columns = [],
  pageSize = 10,
  pageSizeOptions = [5, 10, 20, 50],
  initialState = {},
  onRowSelectionChange,
  disableSelectionOnClick = false,
  autoHeight = false,
  maxHeight = '',
  loading = false,
  rowHeight = 52,
  checkboxSelection = false,
  disableColumnMenu = false,
  onSortModelChange,
  onFilterModelChange,
  onPageChange,
  onPageSizeChange,
  addButton,
  totalRows = 0,
  nestedTable,
  isNested = false,
}: CustomDataGridProps<TData>) => {
  const [sorting, setSorting] = useState<SortingState>(initialState.sorting || []);
  const [globalFilter, setGlobalFilter] = useState<string>(initialState.globalFilter || '');
  const [rowSelection, setRowSelection] = useState<RowSelectionState>(
    initialState.rowSelection || {},
  );
  const [pagination, setPagination] = useState({
    pageIndex: initialState.page || 0,
    pageSize,
  });
  const [expandedRows, setExpandedRows] = useState<{ [key: string]: boolean }>({});

  // Calculate total pages based on totalRows and pageSize
  const totalPages = Math.ceil(totalRows / pagination.pageSize);

  // Adjust pageIndex if it exceeds the valid range
  useEffect(() => {
    if (totalRows === 0) {
      setPagination((prev) => ({ ...prev, pageIndex: 0 }));
      return;
    }
    if (pagination.pageIndex >= totalPages && totalPages > 0) {
      const newPageIndex = totalPages - 1;
      setPagination((prev) => ({ ...prev, pageIndex: newPageIndex }));
      onPageChange?.(newPageIndex);
    }
  }, [totalRows, pagination.pageIndex, totalPages, onPageChange]);

  const data = useMemo(() => rows, [rows]);
  const tableColumns = useMemo<ColumnDef<TData, TData[keyof TData]>[]>(() => {
    const transformedColumns = columns.map((col) => ({
      accessorKey: col.field as string,
      header: col.headerName || String(col.field),
      size: col.width,
      enableSorting: col.sortable !== false,
      enableColumnFilter: col.filterable !== false,
      cell: ({ row, getValue }: CellContext<TData, TData[keyof TData]>) => {
        const value = getValue();
        if (typeof col.renderCell === 'function') {
          return col.renderCell({
            value,
            row: { original: row.original },
          });
        }
        if (typeof col.valueFormatter === 'function') {
          return col.valueFormatter({ value });
        }
        return value;
      },
    }));

    if (checkboxSelection) {
      return [
        {
          id: 'select',
          header: ({ table }: { table: TanstackTable<TData> }) => (
            <Checkbox
              aria-label="Select all"
              checked={table.getIsAllRowsSelected()}
              onCheckedChange={(value) => table.toggleAllRowsSelected(!!value)}
            />
          ),
          cell: ({ row }: { row: Row<TData> }) => (
            <Checkbox
              aria-label="Select row"
              checked={row.getIsSelected()}
              disabled={disableSelectionOnClick}
              onCheckedChange={(value) => row.toggleSelected(!!value)}
            />
          ),
          enableSorting: false,
          enableColumnFilter: false,
        },
        ...transformedColumns,
      ];
    }
    return transformedColumns;
  }, [columns, checkboxSelection, disableSelectionOnClick]);

  const table = useReactTable<TData>({
    data,
    columns: tableColumns,
    state: {
      sorting,
      globalFilter,
      rowSelection,
      pagination,
    },
    onSortingChange: (updater) => {
      const newSorting = typeof updater === 'function' ? updater(sorting) : updater;
      setSorting(newSorting);
      onSortModelChange?.(newSorting);
    },
    onGlobalFilterChange: (value) => {
      setGlobalFilter(String(value));
      onFilterModelChange?.({ globalFilter: String(value) });
    },
    onRowSelectionChange: (updater) => {
      const newSelection = typeof updater === 'function' ? updater(rowSelection) : updater;
      setRowSelection(newSelection);
      onRowSelectionChange?.(newSelection);
    },
    onPaginationChange: (updater) => {
      const newPagination = typeof updater === 'function' ? updater(pagination) : updater;
      setPagination(newPagination);
      onPageChange?.(newPagination.pageIndex);
      onPageSizeChange?.(newPagination.pageSize);
    },
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: true,
    rowCount: totalRows,
  });

  const containerStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    height: autoHeight ? 'auto' : '100%',
    maxHeight: typeof maxHeight === 'number' ? `${maxHeight}px` : maxHeight,
    overflow: 'hidden',
  };

  const toggleRowExpansion = (rowId: string) => {
    setExpandedRows((prev) => ({
      ...prev,
      [rowId]: !prev[rowId],
    }));
  };

  return (
    <div className="flex flex-col rounded-sm border" style={containerStyle}>
      {/* Header with conditional search and add button */}
      {!isNested || addButton ? (
        <div className="flex items-center justify-between border-b p-4">
          {!isNested && (
            <Input
              className="max-w-sm"
              disabled={loading}
              onChange={(e) => table.setGlobalFilter(e.target.value)}
              placeholder="Filter all columns..."
              value={globalFilter}
            />
          )}
          {checkboxSelection && (
            <div className="text-muted-foreground text-sm">
              {table.getFilteredSelectedRowModel().rows.length} of{' '}
              {table.getFilteredRowModel().rows.length} row(s) selected
            </div>
          )}
          {addButton && (
            <div className="flex justify-end">
              {React.isValidElement(addButton) ? (
                addButton
              ) : (
                <Button
                  onClick={(addButton as AddButtonConfig).onClick}
                  size={(addButton as AddButtonConfig).size}
                  variant={(addButton as AddButtonConfig).variant}
                >
                  {(addButton as AddButtonConfig).title}
                </Button>
              )}
            </div>
          )}
        </div>
      ) : null}

      <div className="flex flex-1 flex-col overflow-hidden">
        <div className="relative flex-1 overflow-auto">
          <Table>
            <TableHeader className="sticky top-0 z-10 border-b bg-background">
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow className="bg-secondary" key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      <div className="flex items-center space-x-2">
                        {flexRender(header.column.columnDef.header, header.getContext())}
                        {header.column.getCanSort() && !disableColumnMenu && (
                          <Button
                            className="ml-1 gap-0"
                            disabled={loading}
                            onClick={() => header.column.toggleSorting()}
                            size="icon"
                            variant="ghost"
                          >
                            <ArrowUp
                              className={`m-0 h-4 w-4 ${sorting[0]?.id === header.id && !sorting[0]?.desc ? 'bg-primary text-primary-foreground' : ''}`}
                            />
                            <ArrowDown
                              className={`m-0 h-4 w-4 ${sorting[0]?.id === header.id && sorting[0]?.desc ? 'bg-primary text-primary-foreground' : ''}`}
                            />
                          </Button>
                        )}
                      </div>
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell className="h-24 text-center" colSpan={tableColumns.length}>
                    Loading...
                  </TableCell>
                </TableRow>
              ) : table.getRowModel().rows.length ? (
                table.getRowModel().rows.map((row) => (
                  <>
                    <TableRow
                      aria-expanded={nestedTable && expandedRows[row.id] ? 'true' : 'false'}
                      className={nestedTable ? 'cursor-pointer hover:bg-muted' : ''}
                      data-state={row.getIsSelected() && 'selected'}
                      key={row.id}
                      onClick={() => {
                        if (nestedTable && !disableSelectionOnClick) {
                          toggleRowExpansion(row.id);
                        }
                      }}
                      style={{ height: `${rowHeight}px` }}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                    {nestedTable && expandedRows[row.id] && (
                      <TableRow>
                        <TableCell className="p-0" colSpan={tableColumns.length}>
                          <div
                            className="bg-muted p-4"
                            style={{
                              maxHeight:
                                typeof nestedTable.nestedMaxHeight === 'number'
                                  ? `${nestedTable.nestedMaxHeight}px`
                                  : nestedTable.nestedMaxHeight || '300px',
                              overflowY: 'auto',
                            }}
                          >
                            {nestedTable.renderNestedTable(row.original)}
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </>
                ))
              ) : (
                <TableRow>
                  <TableCell className="h-full text-center" colSpan={tableColumns.length}>
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Compact Pagination */}
      <div className="flex shrink-0 items-center justify-between border-t p-2">
        <div className="flex items-center space-x-1">
          <Button
            disabled={!table.getCanPreviousPage() || loading}
            onClick={() => table.setPageIndex(0)}
            size="sm"
            variant="outline"
          >
            <ChevronsLeft className="h-3 w-3" />
          </Button>
          <Button
            disabled={!table.getCanPreviousPage() || loading}
            onClick={() => table.previousPage()}
            size="sm"
            variant="outline"
          >
            <ChevronLeft className="h-3 w-3" />
          </Button>
          <Button
            disabled={!table.getCanNextPage() || loading}
            onClick={() => table.nextPage()}
            size="sm"
            variant="outline"
          >
            <ChevronRight className="h-3 w-3" />
          </Button>
          <Button
            disabled={!table.getCanNextPage() || loading}
            onClick={() => table.setPageIndex(totalPages - 1)}
            size="sm"
            variant="outline"
          >
            <ChevronsRight className="h-3 w-3" />
          </Button>
          <span className="text-xs">
            Page {pagination.pageIndex + 1} of {totalPages}
          </span>
        </div>
        <div className="flex items-center space-x-1">
          <span className="text-xs">Rows:</span>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button disabled={loading} size="sm" variant="outline">
                {table.getState().pagination.pageSize}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              {pageSizeOptions.map((size) => (
                <DropdownMenuItem key={size} onClick={() => table.setPageSize(size)}>
                  {size}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
          <span className="text-xs">
            {totalRows === 0 ? 0 : pagination.pageIndex * pagination.pageSize + 1}-
            {Math.min((pagination.pageIndex + 1) * pagination.pageSize, totalRows)} of {totalRows}
          </span>
        </div>
      </div>
    </div>
  );
};

export default CustomDataGrid;
