import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@repo/ui/components/alert-dialog';
import { Button } from '@repo/ui/components/button';
import { cn } from '@repo/ui/lib/utils';
import type React from 'react';
import { cloneElement, isValidElement, useState } from 'react';

interface ButtonProps {
  name: string;
  onClick?: () => void;
  variant?: 'default' | 'secondary' | 'destructive' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon' | null | undefined;
  className?: string;
  disabled?: boolean;
}

// Define a type for the child element to ensure it can accept onClick
interface TriggerProps {
  onClick?: React.MouseEventHandler<HTMLElement>;
}

interface ComponentProps {
  title: string;
  description: string;
  confirmButton: ButtonProps;
  cancelButton: ButtonProps;
  children: React.ReactElement<TriggerProps>; // Restrict children to elements that can accept onClick
}

const Confirmation: React.FC<ComponentProps> = ({
  title,
  description,
  confirmButton,
  cancelButton,
  children,
}) => {
  const [open, setOpen] = useState(false);

  // Ensure the child is a valid element and clone it to add onClick
  const triggerElement = isValidElement<TriggerProps>(children)
    ? cloneElement(children, {
        onClick: (e: React.MouseEvent<HTMLElement>) => {
          // Call the child's original onClick if it exists
          if (children.props.onClick) {
            children.props.onClick(e);
          }
          setOpen(true); // Open dialog
        },
      })
    : null;

  // Render a reusable button using shadcn Button component
  const renderButton = ({
    name,
    onClick,
    variant = 'default',
    size = 'sm',
    disabled = false,
    className,
  }: ButtonProps) => (
    <Button
      className={cn(className)}
      disabled={disabled}
      onClick={() => {
        // Call the provided onClick if it exists
        if (onClick) {
          onClick();
        }
        // Close the dialog
        setOpen(false);
      }}
      size={size}
      variant={variant}
    >
      {name}
    </Button>
  );

  return (
    <>
      {triggerElement}
      <AlertDialog onOpenChange={setOpen} open={open}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{title}</AlertDialogTitle>
            <AlertDialogDescription>{description}</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex justify-end gap-2">
            {renderButton(cancelButton)}
            {renderButton(confirmButton)}
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export { Confirmation };
