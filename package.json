{"name": "my-turborepo", "private": true, "scripts": {"build": "turbo run build", "dev": "node scripts/dev.js", "dev:api": "turbo run dev --filter=api", "dev:web": "turbo run dev --filter=web", "dev:no-minio": "turbo run dev", "minio:start": "node scripts/start-minio.js", "minio:stop": "node scripts/start-minio.js stop", "lint": "turbo run lint", "lint:biome": "biome check . --linter-enabled=true --formatter-enabled=false", "lint:biome:fix": "biome check . --linter-enabled=true --formatter-enabled=false --write", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "format:biome": "biome format . --write", "check:biome": "biome check .", "check-types": "turbo run check-types"}, "devDependencies": {"@biomejs/biome": "2.0.6", "husky": "^9.1.7", "lint-staged": "^16.1.2", "playwright": "^1.52.0", "prettier": "^3.5.3", "turbo": "^2.4.4", "typescript": "5.8.2", "ultracite": "5.0.32"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}, "dependencies": {"mkdirp": "^3.0.1", "node-fetch": "2"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,jsonc,css,scss,md,mdx}": ["npx ultracite format"]}}