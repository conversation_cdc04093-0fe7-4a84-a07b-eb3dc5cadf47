# Professional Code Review and Commit Command

Please help me commit and push the currently staged changes with the following steps:

1. **Check Staged Changes**: Use `git diff --cached --name-only` to see what files are staged for commit

2. **Code Review Phase**: 
   - Analyze the staged changes using `git diff --cached`
   - **Adopt the Senior Full-Stack Developer persona** from `.claude/commands/persona.md`
   - Perform a thorough code review following the persona's standards:
     - Architecture and design patterns
     - Code quality and conventions
     - TypeScript usage and type safety
     - Performance implications
     - Security considerations
     - Testing coverage
   - Check against the review checklist in persona.md
   - Provide feedback on any issues found

3. **Approval Decision**:
   - If **CRITICAL ISSUES** found: Stop here and provide detailed feedback for fixes
   - If **MINOR IMPROVEMENTS** suggested: Note them but proceed with commit
   - If **APPROVED**: Continue to commit step

4. **Generate Commit Message** (only if approved):
   - Write a concise commit message (maximum 2 lines)
   - Use conventional commit format with appropriate prefixes (feat:, fix:, docs:, refactor:, test:, etc.)
   - Keep it simple and descriptive
   - No references to AI or automated tools in the message

5. **Commit**: Execute `git commit -m "generated message"` with the created message

6. **Push**: Immediately push to the current branch with `git push`

7. **Confirmation**: Show only the commit hash and push status

Additional context: $ARGUMENTS

**Note**: This process includes mandatory code review. If critical issues are found, the commit will be blocked until they are resolved.