# Professional Pull Request Command

**Adopt the Senior Full-Stack Developer persona** from `.claude/commands/persona.md`

Please help me create and raise a pull request with the following steps:

1. **Pre-PR Checks**:
   - Check current branch name
   - Verify clean working directory with `git status`
   - Ensure branch is up to date

2. **Push Branch**: Push current branch to origin with `git push origin <current-branch>`

3. **Generate PR Content**:
   - **Title**: Create a clear, concise title based on branch name and recent commits
   - **Description**: Write a brief, professional PR description (max 5 lines) following the persona's standards:
     - What was changed (technical details)
     - Why it was needed (business/technical reasoning)
     - Any important implementation notes
     - Architecture or performance considerations if relevant
   - Use the persona's professional developer language and tone
   - Write as if you're documenting your own high-quality work
   - No references to AI, automation, or Claude

4. **Create PR**: 
   - Use `gh pr create` if GitHub CLI is available
   - Auto-assign appropriate labels based on changes
   - No confirmation required - create immediately

5. **Output**: Show only the PR URL and status

Additional context: $ARGUMENTS

Execute all steps automatically without asking for confirmation.