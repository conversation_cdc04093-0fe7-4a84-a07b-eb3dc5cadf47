{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(pnpm install:*)", "Bash(pnpm test:*)", "Bash(grep:*)", "mcp__ide__getDiagnostics", "Bash(npm run type-check:*)", "Bash(npm run:*)", "Bash(npx tsc:*)", "Bash(pnpm run test:*)", "Bash(pnpm dev:*)", "Bash(lsof:*)", "Bash(pnpm lint:*)", "Bash(pnpm add:*)", "Bash(node:*)", "Bash(claude mcp add context7 -- npx -y @upstash/context7-mcp)", "<PERSON><PERSON>(timeout:*)", "Bash(ls:*)", "<PERSON><PERSON>(createdb:*)", "Bash(pnpm db:generate:*)", "Bash(pnpm db:migrate:*)", "Bash(pnpm db:seed:*)", "Bash(pnpm -w run check-types)", "Bash(pnpm check-types)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "Bash(pnpm remove:*)", "Bash(pnpm build:*)", "<PERSON><PERSON>(true)", "mcp__context7__get-library-docs", "<PERSON><PERSON>(jq:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(diff:*)", "Bash(tree:*)", "Bash(for file in audit-logs.ts invitations.ts organizations.ts priorities.ts project-workflows.ts projects.ts resolutions.ts sprints.ts status-categories.ts statuses.ts work-item-fields.ts work-item-types.ts work-items.ts workflow-statuses.ts workflow-transitions.ts workflows.ts workspaces.ts)", "Bash(do sed -i '' 's|from \"\"../helpers/create-crud-router\"\"|from \"\"../utils/crud/create-crud-router\"\"|g' \"$file\")", "Bash(done)", "<PERSON><PERSON>(docker exec:*)", "Bash(pnpm run:*)", "Bash(PGPASSWORD=postgres psql -h localhost -p 5432 -U postgres -d spark -c \"\\dT+\")", "Bash(PGPASSWORD=postgres psql:*)", "Bash(npm --version)", "Bash(pnpm:*)", "<PERSON><PERSON>(env)", "<PERSON><PERSON>(sed:*)", "Bash(git checkout:*)", "Bash(git add:*)", "Bash(npx tsx:*)", "Bash(pgrep:*)", "Bash(npm install:*)", "Bash(npx:*)", "Bash(kill:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(echo:*)", "Bash(DATABASE_URL=postgresql://postgres:postgres@localhost:5432/spark_test pnpm --filter @repo/db db:push)", "Bash(DATABASE_URL=postgresql://postgres:postgres@localhost:5432/spark_test pnpm db:migrate)", "<PERSON><PERSON>(touch:*)", "Bash(NODE_ENV=development psql \"$DATABASE_URL\" -c \"\\dt\")", "<PERSON><PERSON>(docker-compose up:*)", "Bash(NODE_ENV=development DATABASE_URL=postgresql://postgres:postgres@localhost:5432/spark cd packages/db)", "Bash(NODE_ENV=development DATABASE_URL=postgresql://postgres:postgres@localhost:5432/spark psql \"$DATABASE_URL\" -c \"SELECT name, description, color FROM status_category ORDER BY name;\")", "Bash(rg:*)", "Bash(/Users/<USER>/.nvm/versions/node/v18.20.2/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"font-|text-[0-9]xl|text-[0-9]lg\" apps/web2/src/components/onboarding/)", "Bash(cp:*)"], "deny": []}, "enableAllProjectMcpServers": false, "hooks": {"Notification": [{"hooks": [{"type": "command", "command": "say -r 200 'Claude Code: user action required! bro'"}]}], "Stop": [{"hooks": [{"type": "command", "command": "say -r 200 'Task completed successfully! bro'"}]}]}}