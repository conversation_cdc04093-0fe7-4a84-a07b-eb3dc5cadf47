apiVersion: v1
kind: Service
metadata:
  namespace: {{ .Release.Namespace }}
  name: {{ include "frontend.fullname" . }}
  labels:
    {{- include "frontend.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.ports.http.port }}
      targetPort: {{ .Values.service.ports.http.targetPort }}
      protocol: TCP
      name: http
    - port: {{ .Values.service.ports.https.port }}
      targetPort: {{ .Values.service.ports.https.targetPort }}
      protocol: TCP
      name: https
      nodePort: 31700
  selector:
    {{- include "frontend.selectorLabels" . | nindent 4 }}