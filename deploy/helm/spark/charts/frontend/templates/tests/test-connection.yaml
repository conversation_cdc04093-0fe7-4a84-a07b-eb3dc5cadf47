apiVersion: v1
kind: Pod
metadata:
  namespace: {{ .Release.Namespace }}
  name: "{{ include "frontend.fullname" . }}-test-connection"
  labels:
    {{- include "frontend.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "frontend.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
