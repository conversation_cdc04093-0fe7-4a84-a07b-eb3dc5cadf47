apiVersion: v1
kind: Service
metadata:
  name: {{ include "minio.fullname" . }}
  labels:
    {{- include "minio.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.apiPort }}
      targetPort: api
      protocol: TCP
      name: api
      nodePort: 31703
    - port: {{ .Values.service.consolePort }}
      targetPort: http
      protocol: TCP
      name: console
      nodePort: 31704
  selector:
    {{- include "minio.selectorLabels" . | nindent 4 }}