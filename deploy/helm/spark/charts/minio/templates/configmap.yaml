apiVersion: v1
kind: ConfigMap
metadata:
  namespace: {{ .Release.Namespace }}
  name: {{ include "minio.fullname" . }}
data:
  MINIO_DEFAULT_BUCKETS: {{ .Values.config.bucketName | quote }}
  # MINIO_FORCE_NEW_KEYS: {{ .Values.config.MINIO_FORCE_NEW_KEYS | quote }}
  # MINIO_STORAGE_ACCESS_KEY: {{ .Values.config.MINIO_STORAGE_ACCESS_KEY | quote }}
  # MINIO_STORAGE_SECRET_KEY: {{ .Values.config.MINIO_STORAGE_SECRET_KEY | quote }}
  

