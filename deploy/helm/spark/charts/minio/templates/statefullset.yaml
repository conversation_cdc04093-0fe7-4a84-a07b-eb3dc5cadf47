apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "minio.fullname" . }}
  labels:
    {{- include "minio.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "minio.selectorLabels" . | nindent 6 }}
  serviceName: "{{ include "minio.fullname" . }}-headless"
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "minio.labels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      securityContext:
        {{- toYaml .Values.securityContext | nindent 8 }}
      restartPolicy: Always
      containers:
        - name: {{ include "minio.fullname" . }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.consolePort }}
              protocol: TCP
            - name: api
              containerPort: {{ .Values.service.apiPort }}
              protocol: TCP
          command: ["/bin/bash"]
          args:
            - -ec
            - |
              echo "Starting MinIO container..."
              id
              echo "Current permissions:"
              ls -la /bitnami/minio
              ls -la /bitnami/minio/data
              echo "Setting permissions..."
              mkdir -p /bitnami/minio/data
              chmod -R 777 /bitnami/minio
              echo "Permissions after change:"
              ls -la /bitnami/minio
              ls -la /bitnami/minio/data
              echo "Running MinIO..."
              exec /opt/bitnami/scripts/minio/entrypoint.sh /opt/bitnami/scripts/minio/run.sh
          env:
            - name: MINIO_DEFAULT_BUCKETS
              valueFrom:
                configMapKeyRef:
                  name: {{ include "minio.fullname" . }}
                  key: MINIO_DEFAULT_BUCKETS
            # - name: MINIO_FORCE_NEW_KEYS 
            #   valueFrom:
            #     configMapKeyRef:
            #       name: {{ include "minio.fullname" . }}
            #       key: MINIO_FORCE_NEW_KEYS
            # - name: MINIO_STORAGE_ACCESS_KEY  
            #   valueFrom:
            #     configMapKeyRef:
            #       name: {{ include "minio.fullname" . }}
            #       key: MINIO_STORAGE_ACCESS_KEY
            # - name: MINIO_STORAGE_SECRET_KEY
            #   valueFrom:
            #     configMapKeyRef:
            #       name: {{ include "minio.fullname" . }}
            #       key: MINIO_STORAGE_SECRET_KEY
          envFrom:
            - secretRef:
                name: {{ include "minio.fullname" . }}
          volumeMounts:
            - name: minio-data
              mountPath: "/bitnami/minio/data"
          securityContext:
            runAsUser: 0
  volumeClaimTemplates:
    - metadata:
        name: minio-data
      spec:
        accessModes: [ "{{ .Values.persistent.accessMode }}" ]
        resources:
          requests:
            storage: {{ .Values.persistent.size }}
        storageClassName: {{ .Values.persistent.storageClassName }}
  {{- with .Values.nodeSelector }}
  nodeSelector:
    {{- toYaml . | nindent 8 }}
  {{- end }}
  {{- with .Values.affinity }}
  affinity:
    {{- toYaml . | nindent 8 }}
  {{- end }}
  {{- with .Values.tolerations }}
  tolerations:
    {{- toYaml . | nindent 8 }}
  {{- end }}
