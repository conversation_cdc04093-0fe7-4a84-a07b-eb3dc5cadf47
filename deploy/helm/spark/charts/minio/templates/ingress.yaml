{{- if .Values.ingress.enabled }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "minio.fullname" . }}-ingress
  annotations:
    {{- toYaml .Values.ingress.annotations | nindent 4 }}
spec:
  ingressClassName: {{ .Values.ingress.nginxClassName | default "nginx" }}
  rules:
    {{- range .Values.ingress.hosts }}
    - host: {{ .host }}
      http:
        paths:
          {{- range .paths }}
          - path: {{ . }}
            pathType: {{ $.Values.ingress.pathType }}
            backend:
              service:
                name: {{ include "minio.fullname" $ }}
                port:
                  number: {{ $.Values.service.port }}
          {{- end }}
    {{- end }}
  {{- with .Values.ingress.tls }}
  tls:
    {{- range . }}
    - secretName: {{ .secretName }}
      hosts:
        {{- range .hosts }}
        - {{ . }}
        {{- end }}
    {{- end }}
  {{- end }}
{{- end }}

