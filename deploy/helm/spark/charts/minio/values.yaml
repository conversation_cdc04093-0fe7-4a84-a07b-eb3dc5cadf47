# Number of replicas for MinIO deployment
replicaCount: 1

# Image configuration
image:
  repository: docker.io/bitnami/minio
  tag: "2023.9.23"
  pullPolicy: IfNotPresent

# Service configuration
service:
  type: NodePort
  apiPort: 9000
  consolePort: 9001


# Pod annotations and labels
podAnnotations: {}
podLabels: {}

# Security context for pods
podSecurityContext: {}

# Container security context
securityContext:
  runAsUser: 0
  fsGroup: 1001

# Service Account configuration
serviceAccount:
  create: false
  name: ""

# Autoscaling configuration
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 10
  targetCPUUtilizationPercentage: 50

config:
  # MINIO_FORCE_NEW_KEYS: ""
  # MINIO_STORAGE_ACCESS_KEY: ""
  # MINIO_STORAGE_SECRET_KEY: ""

# Liveness and readiness probes configuration
livenessProbe: {}
readinessProbe: {}

# Resource requests and limits for the containers
resources: {}

# Volume mounts for the containers
# volumeMounts:
#   - name: minio-storage
#     mountPath: "/bitnami/minio"

# # Volumes configuration
# volumes:
#   - name: minio-storage
#     persistentVolumeClaim:
#       claimName: minio-pvc

# Node selector configuration
nodeSelector: {}

# Affinity configuration
affinity: {}

# Tolerations configuration
tolerations: {}

# PersistentVolume and PersistentVolumeClaim configuration
persistent:
  size: 15Gi
  accessMode: ReadWriteOnce
  # reclaimPolicy: Delete
  storageClassName: local-path
  # hostPath: /data/postgres

# Ingress configuration
ingress:
  enabled: false
  name: ""
  nginxClassName: ""
  annotations: {}
  pathType: ImplementationSpecific
  hosts:
    - host: chart-example.local
      paths:
        - /
  tls: []

