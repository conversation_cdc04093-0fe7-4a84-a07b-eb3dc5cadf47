# Default values for api-server.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
replicaCount: 1
image:
  repository: local-docker-registry:5001/api
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""
  
imagePullSecrets: []

nameOverride: ""
fullnameOverride: ""

config:
  databaseName: ""
  databasePort: ""
  # databasePassword is intentionally left blank to demonstrate default value
  databaseHost: ""
  databaseUser: ""
  databasePasswd: ""
  # databasePort is intentionally left blank to demonstrate default value
podAnnotations: {}

podLabels: {}

podSecurityContext: {}
# fsGroup: 2000
securityContext: {}
# capabilities:
# drop:
# - ALL
# readOnlyRootFilesystem: true
# runAsNonRoot: true  DATABASE_URL=************************************/spms

# runAsUser: 1000
service:
  type: NodePort
  port: 80

# Additional volumes on the output Deployment definition.
volumes: []
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false
# Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true
nodeSelector: {}
tolerations: []
affinity: {}

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80