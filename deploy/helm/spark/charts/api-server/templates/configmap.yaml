apiVersion: v1
kind: ConfigMap
metadata:
  namespace: {{ .Release.Namespace }}
  name: {{ include "api-server.fullname" . }}
data:
  {{ if .Values.config.databaseHost -}}
  DB_HOST: {{ .Values.config.databaseHost | quote }} 
  {{- else -}}
  DB_HOST: "{{ .Release.Name }}-postgres.{{ .Release.Namespace }}.svc.cluster.local"
  {{ end -}}
  DB_PORT: {{ .Values.config.databasePort | quote }}
  DB_NAME: {{ .Values.config.databaseName | quote }} 
  NODE_ENV: {{ .Values.config.nodeEnv | quote }}
  DB_USER: {{ .Values.config.databaseUser | quote }}
  DB_PASSWORD: {{ .Values.config.databasePasswd | quote }}
  # DATABASE_URL: postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}
  DATABASE_URL: postgresql://{{ .Values.config.databaseUser }}:{{ .Values.config.databasePasswd }}@{{ .Release.Name }}-postgres.{{ .Release.Namespace }}.svc.cluster.local:{{ .Values.config.databasePort}}/{{ .Values.config.databaseName}}
  FRONT_END_URL: {{ .Values.config.FRONT_END_URL | quote }}
  # {{ if .Values.config.FRONT_END_URL -}}
  # FRONT_END_URL: {{ .Values.config.FRONT_END_URL | quote }}
  # {{- else -}} 
  # FRONT_END_URL: "https://{{ .Release.Name }}-frontend.{{ .Release.Namespace }}.svc.cluster.local"
  # {{- end -}}
  # EMAIL_USER: {{ .Values.config.EMAIL_USER | quote }}
  # EMAIL_PASS: {{ .Values.config.EMAIL_PASS | quote }}
  MINIO_ACCESS_KEY: {{ .Values.config.MINIO_ACCESS_KEY | quote }}
  MINIO_SECRET_KEY: {{ .Values.config.MINIO_SECRET_KEY | quote }}
  MINIO_BUCKET_NAME: {{ .Values.config.MINIO_BUCKET_NAME | quote }}
  {{ if .Values.config.MINIO_ENDPOINT -}}
  MINIO_ENDPOINT: {{ .Values.config.MINIO_ENDPOINT | quote }} 
  {{- else -}}
  MINIO_ENDPOINT: "{{ .Release.Name }}-minio.{{ .Release.Namespace }}.svc.cluster.local"
  {{ end -}}
  MINIO_PORT: {{ .Values.config.MINIO_PORT | quote }}
  MINIO_USE_SSL: {{ .Values.config.MINIO_USE_SSL | quote }}
  SMTP_HOST: {{ .Values.config.SMTP_HOST | quote }}
  SMTP_PORT: {{ .Values.config.SMTP_PORT | quote }}
  SMTP_USER: {{ .Values.config.SMTP_USER | quote }}
  SMTP_PASS: {{ .Values.config.SMTP_PASS | quote }}
  SMTP_SECURE: {{ .Values.config.SMTP_SECURE | quote }}
  SMTP_FROM: {{ .Values.config.SMTP_FROM | quote }}