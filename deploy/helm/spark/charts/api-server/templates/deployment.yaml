apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: {{ .Release.Namespace }}
  name: {{ include "api-server.fullname" . }}
  labels:
    {{- include "api-server.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "api-server.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "api-server.labels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      initContainers:
      - name: wait-for-postgres
        image: ghcr.io/groundnuty/k8s-wait-for:v1.6
        imagePullPolicy: Always
        args:
          - "pod"
          - "-lapp.kubernetes.io/name=postgres"
          - "--namespace"
          - "{{ .Release.Namespace }}"
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      restartPolicy: Always
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          env:
          #   - name: DATABASE_NAME
          #     valueFrom:
          #       configMapKeyRef:
          #         name: {{ include "api-server.fullname" . }}-config
          #         key: DATABASE_NAME
          #   - name: DATABASE_HOST
          #     valueFrom:
          #       configMapKeyRef:
          #         name: {{ include "api-server.fullname" . }}-config
          #         key: DATABASE_HOST
          #   - name: DATABASE_PORT
          #     valueFrom:
          #       configMapKeyRef:
          #         name: {{ include "api-server.fullname" . }}-config
          #         key: DATABASE_PORT
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: {{ include "api-server.fullname" . }}
                  key: DB_USER
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "api-server.fullname" . }}
                  key: DB_PASSWORD
          envFrom:
            # - secretRef:
            #     name: {{ include "api-server.fullname" . }}-secret
            - configMapRef:
                name: {{ include "api-server.fullname" . }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          {{- with .Values.volumeMounts }}
          volumeMounts:
            {{- toYaml . | nindent 12 }}
          {{- end }}
      {{- with .Values.volumes }}
      volumes:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}