apiVersion: v1
kind: Service
metadata:
  namespace: {{ .Release.Namespace }}
  name: {{ include "api-server.fullname" . }}
  labels:
    {{- include "api-server.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      # targetPort: http
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
      nodePort: 31702
  selector:
    {{- include "api-server.selectorLabels" . | nindent 4 }}
