# Default values for postgres.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
replicaCount: 1

image:
  repository: postgres
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "16"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

podAnnotations: {}
podLabels: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: NodePort
  port: 5432
  targetPort: 5432
  clusterIP: None


config:
  postgresDb: ""

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 3
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80


ingress:
  enabled: false
  className: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

persistent:
  size: 30Gi
  accessMode: ReadWriteOnce
  # reclaimPolicy: Delete
  storageClassName: local-path
  # hostPath: /data/postgresql
  # type: DirectoryOrCreate

# persistentVolumeClaim:
#   size: 5Gi
#   accessMode: ReadWriteOnce
#   storageClassName: local-storage


# volumeMounts:
#   name: postgres-data
#   mountPath: /data

# volumes:
#   name: postgres-data
#   persistentVolumeClaim:
#     claimName: postgres-pvc

# # Additional volumes on the output Deployment definition.
# volumes:
#   - name: postgresdata
#     persistentVolumeClaim:
#       claimName: postgres-pvc

# # Additional volumeMounts on the output Deployment definition.
# volumeMounts:
#   - name: postgresdata
#     mountPath: /data

nodeSelector: {}
tolerations: []
affinity: {}
