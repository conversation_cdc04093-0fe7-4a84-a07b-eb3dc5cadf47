apiVersion: v1
kind: Pod
metadata:
  namespace: {{ .Release.Namespace }}
  name: "{{ include "postgres.fullname" . }}-test-connection"
  labels:
    {{- include "postgres.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "postgres.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
