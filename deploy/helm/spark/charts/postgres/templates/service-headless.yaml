apiVersion: v1
kind: Service
metadata:
  namespace: {{ .Release.Namespace }}
  name: {{ include "postgres.fullname" . }}-headless
  labels:
    {{- include "postgres.labels" . | nindent 4 }}
spec:
  clusterIP: {{ .Values.service.clusterIP }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "postgres.selectorLabels" . | nindent 4 }}



