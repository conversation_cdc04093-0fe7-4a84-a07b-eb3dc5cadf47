postgres:
  replicaCount: 1

  config:
    postgresDb: spark

  image:
    repository: postgres
    tag: "16"
    pullPolicy: IfNotPresent

  service:
    type: NodePort
    port: 5432

api-server:
  replicaCount: 1

  config:
    databaseName: spark
    databasePort: "5432"
    databaseHost: ""
    databaseUser: spark
    databasePasswd: "spark"
    nodeEnv: production
    FRONT_END_URL: https://spms.buildbot.tech:31700
    # FRONT_END_URL: ""
    # EMAIL_USER: <EMAIL>
    # EMAIL_PASS: ucim vgmf saxt pupm
    MINIO_ACCESS_KEY: E6se5QFLENVA6lrRSbMS
    MINIO_ENDPOINT: ""
    MINIO_BUCKET_NAME: spark
    MINIO_SECRET_KEY: RlA5REUbGrd9dxHeWEhD9sGzLpmKL6EIO8BWO2MT
    MINIO_PORT: "9000"
    MINIO_USE_SSL: "false"
    SMTP_PORT: "587"
    SMTP_USER: "<EMAIL>"
    SMTP_HOST: "smtp.gmail.com"
    SMTP_PASS: "ucim vgmf saxt pupm"
    SMTP_SECURE: "false"
    SMTP_FROM: "Spark Team"

  image:
    repository: local-docker-registry:5001/api
    tag: "v1.0.0"
    pullPolicy: Always

  service:
    type: NodePort
    port: 3000
    targetPort: 3000

frontend:
  replicaCount: 1

  config:
    VITE_API_URL: https://localhost
    VITE_VAP_KEY: BDP9mTaaOq598599Psd9NQOqxq9urf1oyGMLQq_iySntnGFXh8qoQNFXOVtIQGIm7HuHRG_prP0g3XXPKpiok9Y

  image:
    repository: local-docker-registry:5001/ui
    tag: "v1.0.0"
    pullPolicy: Always

  service:
    type: NodePort
    ports:
      http:
        port: 80
        targetPort: 80
      https:
        port: 443
        targetPort: 443

minio:
  enabled: true

  replicaCount: 1

  config:
    bucketName: spark

  image:
    repository: docker.io/bitnami/minio
    tag: "2023.9.23"
    pullPolicy: IfNotPresent

  service:
    type: NodePort
    apiPort: 9000
    consolePort: 9001
