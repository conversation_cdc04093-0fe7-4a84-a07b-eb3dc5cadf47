apiVersion: v2
appVersion: v1.0
name: spark
type: application
description: A Helm chart for my application containing API server, frontend, minio and postgres
version: v1.0
dependencies:
  - name: postgres
    version: v1.0
    repository: file://charts/postgres

  - name: api-server
    version: v1.0
    repository: file://charts/api-server
 
  - name: frontend
    version: v1.0
    repository: file://charts/frontend

  - name: minio
    condition: minio.enabled
    version: v1.0
    repository: file://charts/minio