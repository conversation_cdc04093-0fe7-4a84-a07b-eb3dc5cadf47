#!/usr/bin/env node

const fs = require('node:fs');
const path = require('node:path');
const _util = require('node:util');
const { mkdirp } = require('mkdirp');

// Templates directory
const templatesDir = path.join(__dirname, 'templates');

// Check if templates directory exists, if not create it
if (!fs.existsSync(templatesDir)) {
  fs.mkdirSync(templatesDir, { recursive: true });
}

// Template file paths
const templateFiles = {
  defs: path.join(templatesDir, 'resource.defs.template.txt'),
  handlers: path.join(templatesDir, 'resource.handlers.template.txt'),
  route: path.join(templatesDir, 'resource.route.template.txt'),
};

// Function to capitalize first letter
function capitalize(str) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

// Function to pluralize a word (very simplified)
function pluralize(word) {
  if (word.endsWith('y')) {
    return `${word.slice(0, -1)}ies`;
  }
  if (
    word.endsWith('s') ||
    word.endsWith('x') ||
    word.endsWith('z') ||
    word.endsWith('ch') ||
    word.endsWith('sh')
  ) {
    return `${word}es`;
  }
  return `${word}s`;
}
// Function to update the routes/index.ts file
function updateRoutesIndex(resourceName) {
  const indexPath = path.join(__dirname, '..', 'apps', 'api', 'src', 'routes', 'index.ts');

  if (!fs.existsSync(indexPath)) {
    return false;
  }

  let content = fs.readFileSync(indexPath, 'utf8');

  // Add the import statement
  // Look for the last import statement
  const importRegex = /^import .+ from ".+";?$/gm;
  const imports = [...content.matchAll(importRegex)];

  if (imports.length > 0) {
    const lastImport = imports.at(-1);
    const importStatement = `import ${resourceName} from "./${resourceName}/${resourceName}.route";`;
    content =
      content.slice(0, lastImport.index + lastImport[0].length) +
      '\n' +
      importStatement +
      content.slice(lastImport.index + lastImport[0].length);
  } else {
    // If no imports found, add at the beginning
    content = `import ${resourceName} from "./${resourceName}/${resourceName}.route";\n` + content;
  }

  // Update the registerRoutes function
  const routeRegex = /return app\s*\.(route\(".+"\s*,\s*.+\))(\s*;|\s*\.route|\s*$)/;
  const routeMatches = content.match(routeRegex);

  if (routeMatches && routeMatches.length >= 3) {
    // Replace the existing return with the updated one that includes the new route
    const existingRoutes = routeMatches[1];
    const ending = routeMatches[2];
    const newRouteStatement = `.route("/v1", ${resourceName})`;

    // If ending with a semicolon or nothing, maintain that
    if (ending.trim() === ';' || ending.trim() === '') {
      content = content.replace(
        routeRegex,
        `return app.${existingRoutes}${newRouteStatement}${ending}`,
      );
    }
    // If ending with another .route, keep the chain going
    else {
      content = content.replace(
        routeRegex,
        `return app.${existingRoutes}${newRouteStatement}${ending}`,
      );
    }
  } else {
    return false;
  }

  // Write the updated content back to the file
  fs.writeFileSync(indexPath, content);
  return true;
}

function generateResource(resourceName) {
  try {
    if (!resourceName) {
      process.exit(1);
    }

    // Calculate variations of the resource name
    const ResourceName = capitalize(resourceName);
    const resourceNamePlural = pluralize(resourceName);
    const ResourceNamePlural = capitalize(resourceNamePlural);
    const resourcePath = resourceName;
    const resourceTag = resourceName;
    const resourceNameDb = resourceName;

    // Create output directory
    const outputDir = path.join(process.cwd(), 'apps', 'api', 'src', 'routes', resourceName);
    mkdirp.sync(outputDir);

    // Read template files
    const defsTemplate = fs.readFileSync(templateFiles.defs, 'utf8');
    const handlersTemplate = fs.readFileSync(templateFiles.handlers, 'utf8');
    const routeTemplate = fs.readFileSync(templateFiles.route, 'utf8');

    // Replace placeholders
    const replacePlaceholders = (template) => {
      return template
        .replace(/\{\{resourceName\}\}/g, resourceName)
        .replace(/\{\{ResourceName\}\}/g, ResourceName)
        .replace(/\{\{resourceNamePlural\}\}/g, resourceNamePlural)
        .replace(/\{\{ResourceNamePlural\}\}/g, ResourceNamePlural)
        .replace(/\{\{resourcePath\}\}/g, resourcePath)
        .replace(/\{\{resourceTag\}\}/g, resourceTag)
        .replace(/\{\{resourceNameDb\}\}/g, resourceNameDb);
    };

    const defsContent = replacePlaceholders(defsTemplate);
    const handlersContent = replacePlaceholders(handlersTemplate);
    const routeContent = replacePlaceholders(routeTemplate);

    // Write files
    fs.writeFileSync(path.join(outputDir, `${resourceName}.defs.ts`), defsContent);
    fs.writeFileSync(path.join(outputDir, `${resourceName}.handlers.ts`), handlersContent);
    fs.writeFileSync(path.join(outputDir, `${resourceName}.route.ts`), routeContent);
    // Update the routes/index.ts file
    const indexUpdated = updateRoutesIndex(resourceName);
    if (indexUpdated) {
    } else {
    }

    // Also check if schema file exists
    const schemaPath = path.join(
      process.cwd(),
      'packages',
      'db',
      'src',
      'schema',
      `${resourceName}.ts`,
    );
    if (!fs.existsSync(schemaPath)) {
    }
  } catch (_error) {
    process.exit(1);
  }
}

// Get resource name from command line arguments
const resourceName = process.argv[2];
generateResource(resourceName);
