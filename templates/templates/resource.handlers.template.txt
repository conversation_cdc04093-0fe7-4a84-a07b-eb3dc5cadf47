import { ZOD_ERROR_CODES, ZOD_ERROR_MESSAGES } from "@/api/lib/constants";
import type { AppRouteHandler } from "@/api/lib/types";
import {
  applyQueryOptions,
  buildFilterCondition,
} from "@/api/middlewares/query/applyQueryOptions";
import { createPaginatedResponse } from "@/api/middlewares/query/paginationUtils";
import { db } from "@repo/db";
import { {{resourceNameDb}}, select{{ResourceName}}Schema } from "@repo/db/schema";
import { count, eq, inArray } from "drizzle-orm";
import * as HttpStatusCodes from "stoker/http-status-codes";
import { z } from "zod";
import type {
  CreateRoute,
  GetOneRoute,
  ListRoute,
  PatchRoute,
  RemoveRoute,
} from "./{{resourceName}}.defs";

// Define error type using the shape from select{{ResourceName}}Schema but with an error field
type {{ResourceName}}Error = {
  id: string;
  error: { message: string; code?: string };
};

/**
 * Handler to list {{resourceNamePlural}} with pagination, filtering and sorting
 */
export const listHandler: AppRouteHandler<ListRoute> = async (c) => {
  try {
    const queryOptions = c.get("queryOptions") || {};

    // Count total items
    let totalItems = 0;

    // Perform count with direct queries to avoid type issues
    if (queryOptions.filters) {
      const filterCondition = buildFilterCondition({{resourceNameDb}}, queryOptions.filters);
      if (filterCondition) {
        const result = await db
          .select({ count: count() })
          .from({{resourceNameDb}})
          .where(filterCondition);
        totalItems = result[0].count;
      } else {
        const result = await db.select({ count: count() }).from({{resourceNameDb}});
        totalItems = result[0].count;
      }
    } else {
      const result = await db.select({ count: count() }).from({{resourceNameDb}});
      totalItems = result[0].count;
    }

    // Set up the main query
    let query = db.select().from({{resourceNameDb}});

    if (Object.keys(queryOptions).length > 0) {
      query = applyQueryOptions(query, {{resourceNameDb}}, queryOptions);
    }

    // Execute main query
    const items = await query;

    // Always return a paginated response with default values if not provided
    const pagination = queryOptions.pagination || {
      page: 1,
      pageSize: 10,
    };

    const paginatedResponse = createPaginatedResponse(
      c,
      items,
      pagination,
      totalItems
    );

    // Return the 200 OK response with paginated data
    return c.json(paginatedResponse, 200);
  } catch (error) {
    c.get("logger").error(
      { error, route: "{{resourceName}}.list" },
      "Error listing {{resourceNamePlural}}"
    );

    // The success property must be explicitly false, not a boolean
    return c.json(
      {
        success: false as const,
        error: {
          message: "Internal server error",
        },
      },
      HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};

/**
 * Handler to create new {{resourceName}} or {{resourceNamePlural}}
 */
export const createHandler: AppRouteHandler<CreateRoute> = async (c) => {
  try {
    const data = c.req.valid("json");
    const isBulk = Array.isArray(data);

    if (isBulk && data.length === 0) {
      return c.json(
        {
          success: false as const,
          error: {
            message: "No items provided",
          },
        },
        HttpStatusCodes.BAD_REQUEST
      );
    }

    const results = await db.transaction(async (tx) => {
      if (isBulk) {
        const inserted = await tx.insert({{resourceNameDb}}).values(data).returning();
        return inserted;
      } else {
        const [inserted] = await tx.insert({{resourceNameDb}}).values(data).returning();
        return inserted;
      }
    });

    // Always return a 200 OK with the data
    return c.json(results, 200);
  } catch (error: unknown) {
    c.get("logger").error(
      { error, route: "{{resourceName}}.create" },
      "Error creating {{resourceName}}"
    );

    if ((error as any).code === "23505") {
      // Postgres unique constraint violation
      return c.json(
        {
          success: false as const,
          error: {
            message: "A {{resourceName}} with this name already exists",
            code: "DUPLICATE_ENTRY",
          },
        },
        HttpStatusCodes.CONFLICT
      );
    }

    return c.json(
      {
        success: false as const,
        error: {
          message: "Internal server error",
        },
      },
      HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};

/**
 * Handler to get a single {{resourceName}} by ID
 */
export const getOneHandler: AppRouteHandler<GetOneRoute> = async (c) => {
  try {
    const { id } = c.req.valid("param");

    const item = await db
      .select()
      .from({{resourceNameDb}})
      .where(eq({{resourceNameDb}}.id, id))
      .limit(1);

    if (!item.length) {
      return c.json(
        {
          success: false as const,
          error: {
            message: "{{ResourceName}} not found",
          },
        },
        HttpStatusCodes.NOT_FOUND
      );
    }

    // Return 200 OK with the {{resourceName}} data
    return c.json(item[0], 200);
  } catch (error) {
    c.get("logger").error(
      { error, route: "{{resourceName}}.getOne" },
      "Error getting {{resourceName}}"
    );

    return c.json(
      {
        success: false as const,
        error: {
          message: "Internal server error",
        },
      },
      HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};

/**
 * Handler to update existing {{resourceName}} or {{resourceNamePlural}}
 */
export const patchHandler: AppRouteHandler<PatchRoute> = async (c) => {
  try {
    const updates = c.req.valid("json");
    const isBulk = Array.isArray(updates);
    const { id: paramId } = c.req.valid("param");

    if (isBulk && updates.length === 0) {
      return c.json(
        {
          success: false as const,
          error: {
            message: "No updates provided",
          },
        },
        HttpStatusCodes.BAD_REQUEST
      );
    }

    // Handle bulk updates
    if (isBulk) {
      const resultsArray = await db.transaction(async (tx) => {
        const results: Array<z.infer<typeof select{{ResourceName}}Schema> | {{ResourceName}}Error> = [];

        for (const update of updates as any[]) {
          const { id, ...data } = update;

          if (Object.keys(data).length === 0) {
            results.push({
              id,
              error: {
                code: ZOD_ERROR_CODES.INVALID_UPDATES,
                message: ZOD_ERROR_MESSAGES.NO_UPDATES,
              },
            });
            continue;
          }

          const [updated] = await tx
            .update({{resourceNameDb}})
            .set(data)
            .where(eq({{resourceNameDb}}.id, id))
            .returning();

          if (!updated) {
            results.push({
              id,
              error: {
                message: "{{ResourceName}} not found",
              },
            });
            continue;
          }

          results.push(updated);
        }

        return results;
      });

      // Return 200 OK with the results array
      return c.json(resultsArray, 200);
    }
    // Handle single update
    else {

      if (Object.keys(updates).length === 0) {
        return c.json(
          {
            success: false as const,
            error: {
              message: ZOD_ERROR_MESSAGES.NO_UPDATES,
              code: ZOD_ERROR_CODES.INVALID_UPDATES,
            },
          },
          HttpStatusCodes.BAD_REQUEST
        );
      }

      const [updated] = await db
        .update({{resourceNameDb}})
        .set(updates)
        .where(eq({{resourceNameDb}}.id, paramId))
        .returning();

      if (!updated) {
        return c.json(
          {
            success: false as const,
            error: {
              message: "{{ResourceName}} not found",
            },
          },
          HttpStatusCodes.NOT_FOUND
        );
      }

      // Return 200 OK with the updated {{resourceName}}
      return c.json(updated, 200);
    }
  } catch (error) {
    c.get("logger").error(
      { error, route: "{{resourceName}}.patch" },
      "Error updating {{resourceName}}"
    );

    return c.json(
      {
        success: false as const,
        error: {
          message: "Internal server error",
        },
      },
      HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};

/**
 * Handler to remove {{resourceName}} or {{resourceNamePlural}}
 */
export const removeHandler: AppRouteHandler<RemoveRoute> = async (c) => {
  try {
    const params = c.req.valid("param");
    const ids = params.id.toString().split(",");

    const isBulk = ids.length > 1;

    return await db.transaction(async (tx) => {
      if (isBulk) {
        const deleted = await tx
          .delete({{resourceNameDb}})
          .where(inArray({{resourceNameDb}}.id, ids))
          .returning({ id: {{resourceNameDb}}.id });

        const deletedIds = deleted.map((item) => item.id);
        const notFoundIds = ids.filter((id) => !deletedIds.includes(id));

        // For bulk deletes, return 200 with results
        return c.json(
          {
            deleted: deletedIds,
            notFound: notFoundIds,
          },
          200
        );
      } else {
        const id = ids[0];

        const [deleted] = await tx
          .delete({{resourceNameDb}})
          .where(eq({{resourceNameDb}}.id, id))
          .returning();

        if (!deleted) {
          return c.json(
            {
              success: false as const,
              error: {
                message: "{{ResourceName}} not found",
              },
            },
            HttpStatusCodes.NOT_FOUND
          );
        }

        // For single deletes, use a 204 No Content response
        // Use Response constructor directly to avoid type errors with c.json
        return new Response(null, { status: HttpStatusCodes.NO_CONTENT });
      }
    });
  } catch (error) {
    c.get("logger").error(
      { error, route: "{{resourceName}}.remove" },
      "Error removing {{resourceName}}"
    );

    // Foreign key violation
    if ((error as any).code === "23503") {
      return c.json(
        {
          success: false as const,
          error: {
            message: "Cannot delete {{resourceName}} as it's referenced by other records",
            code: "FOREIGN_KEY_VIOLATION",
          },
        },
        HttpStatusCodes.CONFLICT
      );
    }

    return c.json(
      {
        success: false as const,
        error: {
          message: "Internal server error",
        },
      },
      HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};



// Export all handlers as a group
export const {{resourceName}}Handlers = {
  listHandler,
  createHandler,
  getOneHandler,
  patchHandler,
  removeHandler,
};

export default {{resourceName}}Handlers;