import { createRouter } from "@/api/lib/createApp";
import {{resourceName}}Routes from "./{{resourceName}}.defs";
import {{resourceName}}Handlers from "./{{resourceName}}.handlers";

const router = createRouter()
  .openapi({{resourceName}}Routes.list, {{resourceName}}Handlers.listHandler)
  .openapi({{resourceName}}Routes.create, {{resourceName}}Handlers.createHandler)
  .openapi({{resourceName}}Routes.getOne, {{resourceName}}Handlers.getOneHandler)
  .openapi({{resourceName}}Routes.patch, {{resourceName}}Handlers.patchHandler)
  .openapi({{resourceName}}Routes.remove, {{resourceName}}Handlers.removeHandler);

export default router;