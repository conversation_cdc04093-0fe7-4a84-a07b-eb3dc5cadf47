import { jwtAuth } from "@/api/middlewares/jwtAuth";
import { createRoute } from "@hono/zod-openapi";
import { insert{{ResourceName}}Schema, select{{ResourceName}}Schema } from "@repo/db/schema";
import * as HttpStatusCodes from "stoker/http-status-codes";
import { jsonContent, jsonContentRequired } from "stoker/openapi/helpers";
import { z } from "zod";

// Constants for paths
const path = "/{{resourcePath}}";
const paths = {
  base: path,
  byId: `${path}/{id}`,
} as const;

// Tags for API documentation
const tags = ["{{resourceTag}}"];

// Standardize error schema with LITERAL false for success flag
const standardErrorSchema = z.object({
  success: z.literal(false),
  error: z.object({
    message: z.string(),
    code: z.string().optional(),
    path: z.array(z.string()).optional(),
  }),
});

// Improved ID parameter validation with refinement
const IdParamsSchema = z.object({
  id: z.string()
});

// Query parameter schema for list endpoint
const listQuerySchema = z.object({
  filters: z.string().optional(),
  sort: z.string().optional(),
  pagination: z.string().optional(),
  search: z.string().optional(),
});

// Pagination schemas
const paginationMetaSchema = z.object({
  totalItems: z.number(),
  itemCount: z.number(),
  itemsPerPage: z.number(),
  totalPages: z.number(),
  currentPage: z.number(),
});

const paginationLinksSchema = z
  .object({
    first: z.string().optional(),
    previous: z.string().optional(),
    next: z.string().optional(),
    last: z.string().optional(),
  })
  .optional();

// Paginated response schema
const paginatedResponseSchema = z.object({
  items: z.array(select{{ResourceName}}Schema),
  meta: paginationMetaSchema,
  links: paginationLinksSchema,
});

// Patch schemas
const patchSchema = insert{{ResourceName}}Schema.partial();
const bulkPatchSchema = z.array(
  patchSchema.extend({
    id: z.string(),
  })
);

// Error response for a {{resourceName}} update
const {{resourceName}}ErrorResponseSchema = z.object({
  id: z.string(),
  error: z.object({
    message: z.string(),
    code: z.string().optional(),
  }),
});

// Success response for delete endpoint
const deleteSuccessSchema = z.object({
  deleted: z.array(z.number()),
  notFound: z.array(z.number()),
});

// Define route definitions with only the relevant status codes for each endpoint
const {{resourceName}}RouteDef = {
  list: createRoute({
    path: paths.base,
    method: "get",
    security: [{ AuthorizationBearer: [] }],
    tags,
    request: {
      query: listQuerySchema,
    },
    middleware: [jwtAuth()] as const,
    responses: {
      // List endpoint only returns 401, 200 or 500
      [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
        standardErrorSchema,
        "Authentication required"
      ),
      [HttpStatusCodes.OK]: jsonContent(
        paginatedResponseSchema,
        "Paginated list of {{resourceNamePlural}}"
      ),
      [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
        standardErrorSchema,
        "Internal server error"
      ),
    },
  }),

  create: createRoute({
    path: paths.base,
    method: "post",
    security: [{ AuthorizationBearer: [] }],
    tags,
    request: {
      body: jsonContentRequired(
        z.union([insert{{ResourceName}}Schema, z.array(insert{{ResourceName}}Schema)]),
        "Single {{resourceName}} or array of {{resourceNamePlural}} to create"
      ),
    },
    middleware: [jwtAuth()] as const,
    responses: {
      // Create endpoint returns 401, 200, 400, 409, or 500
      [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
        standardErrorSchema,
        "Authentication required"
      ),
      [HttpStatusCodes.OK]: jsonContent(
        z.union([select{{ResourceName}}Schema, z.array(select{{ResourceName}}Schema)]),
        "The created {{resourceName}} or {{resourceNamePlural}}"
      ),
      [HttpStatusCodes.BAD_REQUEST]: jsonContent(
        standardErrorSchema,
        "Empty array or invalid input"
      ),
      [HttpStatusCodes.CONFLICT]: jsonContent(
        standardErrorSchema,
        "{{ResourceName}} with this name already exists"
      ),
      [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
        standardErrorSchema,
        "Internal server error"
      ),
    },
  }),

  getOne: createRoute({
    path: paths.byId,
    method: "get",
    security: [{ AuthorizationBearer: [] }],
    tags,
    request: {
      params: IdParamsSchema,
    },
    middleware: [jwtAuth()] as const,
    responses: {
      // GetOne endpoint returns 401, 200, 400, 404, or 500
      [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
        standardErrorSchema,
        "Authentication required"
      ),
      [HttpStatusCodes.OK]: jsonContent(select{{ResourceName}}Schema, "The requested {{resourceName}}"),
      [HttpStatusCodes.BAD_REQUEST]: jsonContent(
        standardErrorSchema,
        "Invalid ID format"
      ),
      [HttpStatusCodes.NOT_FOUND]: jsonContent(
        standardErrorSchema,
        "{{ResourceName}} not found"
      ),
      [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
        standardErrorSchema,
        "Internal server error"
      ),
    },
  }),

  patch: createRoute({
    path: paths.byId,
    method: "patch",
    security: [{ AuthorizationBearer: [] }],
    tags,
    request: {
      params: IdParamsSchema,
      body: jsonContentRequired(
        z.union([patchSchema, bulkPatchSchema]),
        "Single update or array of updates with IDs"
      ),
    },
    middleware: [jwtAuth()] as const,
    responses: {
      // Patch endpoint returns 401, 200, 400, 404, or 500
      [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
        standardErrorSchema,
        "Authentication required"
      ),
      [HttpStatusCodes.OK]: jsonContent(
        z.union([
          select{{ResourceName}}Schema,
          z.array(z.union([select{{ResourceName}}Schema, {{resourceName}}ErrorResponseSchema])),
        ]),
        "The updated {{resourceName}} or {{resourceNamePlural}}, or error details"
      ),
      [HttpStatusCodes.BAD_REQUEST]: jsonContent(
        standardErrorSchema,
        "Invalid ID format or no updates provided"
      ),
      [HttpStatusCodes.NOT_FOUND]: jsonContent(
        standardErrorSchema,
        "{{ResourceName}} not found"
      ),
      [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
        standardErrorSchema,
        "Internal server error"
      ),
    },
  }),

  remove: createRoute({
    path: paths.byId,
    method: "delete",
    security: [{ AuthorizationBearer: [] }],
    tags,
    request: {
      params: IdParamsSchema,
    },
    middleware: [jwtAuth()] as const,
    responses: {
      // Remove endpoint returns 401, 200, 204, 400, 404, 409, or 500
      [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
        standardErrorSchema,
        "Authentication required"
      ),
      [HttpStatusCodes.OK]: jsonContent(
        deleteSuccessSchema,
        "Bulk delete results"
      ),
      [HttpStatusCodes.NO_CONTENT]: {
        description: "{{ResourceName}} deleted successfully (no content)",
      },
      [HttpStatusCodes.BAD_REQUEST]: jsonContent(
        standardErrorSchema,
        "Invalid ID format"
      ),
      [HttpStatusCodes.NOT_FOUND]: jsonContent(
        standardErrorSchema,
        "{{ResourceName}} not found"
      ),
      [HttpStatusCodes.CONFLICT]: jsonContent(
        standardErrorSchema,
        "Cannot delete due to references"
      ),
      [HttpStatusCodes.INTERNAL_SERVER_ERROR]: jsonContent(
        standardErrorSchema,
        "Internal server error"
      ),
    },
  }),
};

export default {{resourceName}}RouteDef;

// Export route types
export type ListRoute = typeof {{resourceName}}RouteDef.list;
export type CreateRoute = typeof {{resourceName}}RouteDef.create;
export type GetOneRoute = typeof {{resourceName}}RouteDef.getOne;
export type PatchRoute = typeof {{resourceName}}RouteDef.patch;
export type RemoveRoute = typeof {{resourceName}}RouteDef.remove;