import dotenv from 'dotenv';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './packages/db/src/schema/index.ts';

// Load environment variables
dotenv.config();

const connectionString =
  process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/spark';
const pg = postgres(connectionString);
const db = drizzle(pg, { schema });

async function verifySeedData() {
  try {
    // Check status categories
    const statusCategories = await db.select().from(schema.statusCategory);
    statusCategories.forEach((_sc) => {});

    // Check priorities
    const priorities = await db.select().from(schema.priority);
    priorities.forEach((_p) => {});

    // Check work item types
    const workItemTypes = await db.select().from(schema.workItemType);
    workItemTypes.forEach((_wit) => {});

    // Check statuses
    const statuses = await db.select().from(schema.status);
    statuses.forEach((_s) => {});

    // Check roles and users
    const _roles = await db.select().from(schema.role);
    const _users = await db.select().from(schema.user);
  } catch (_error) {
  } finally {
    await pg.end();
  }
}

verifySeedData();
