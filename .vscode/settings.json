{
  "eslint.workingDirectories": [
    {
      "mode": "auto"
    }
  ],
  "editor.codeActionsOnSave": {
    // "source.fixAll.eslint": "always",
    "source.organizeImports": "explicit",
    "source.fixAll.biome": "explicit",
    "source.organizeImports.biome": "explicit"
  },
  "files.exclude": {
    "apps/*/node_modules/": true,
    "apps/*/.turbo/": true,
    "packages/*/.turbo/": true,
    "packages/*/node_modules/": true,
    "packages/eslint-config/": true,
    "packages/typescript-config/": true,
    ".vscode/": true,
    ".husky/": true,
    ".turbo/": true,
    "apps/*/dist/": true,
    "*/build/": true,
    "node_modules/": true
  },
  "editor.formatOnSave": true,
  "eslint.rules.customizations": [
    {
      "rule": "style/*",
      "severity": "off",
      "fixable": true
    },
    {
      "rule": "format/*",
      "severity": "off",
      "fixable": true
    },
    {
      "rule": "*-indent",
      "severity": "off",
      "fixable": true
    },
    {
      "rule": "*-spacing",
      "severity": "off",
      "fixable": true
    },
    {
      "rule": "*-spaces",
      "severity": "off",
      "fixable": true
    },
    {
      "rule": "*-order",
      "severity": "off",
      "fixable": true
    },
    {
      "rule": "*-dangle",
      "severity": "off",
      "fixable": true
    },
    {
      "rule": "*-newline",
      "severity": "off",
      "fixable": true
    },
    {
      "rule": "*quotes",
      "severity": "off",
      "fixable": true
    },
    {
      "rule": "*semi",
      "severity": "off",
      "fixable": true
    }
  ],
  "editor.defaultFormatter": "biomejs.biome",
  "[javascript][typescript][javascriptreact][typescriptreact][json][jsonc][css][graphql]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "typescript.tsdk": "node_modules/typescript/lib",
  "editor.formatOnPaste": true,
  "emmet.showExpandedAbbreviation": "never"
}
