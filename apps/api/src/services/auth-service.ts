import { db } from '@repo/db';
import { user } from '@repo/db/schema';
import bcrypt from 'bcrypt';
import type { InferSelectModel } from 'drizzle-orm';
import { and, eq } from 'drizzle-orm';
import { ApiError } from '../errors/api-error';
import {
  generateEmailVerificationToken,
  generatePasswordResetToken,
  generateTokens,
  type TokenPair,
  verifyEmailToken,
  verifyPasswordResetToken,
  verifyRefreshToken,
} from '../utils/auth/jwt';
import { emailService } from './email-service';

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  displayName?: string;
}

export interface LoginData {
  email: string;
  password: string;
}

type User = InferSelectModel<typeof user>;

export interface AuthResponse {
  user: Omit<
    User,
    | 'password'
    | 'passwordResetToken'
    | 'passwordResetExpires'
    | 'emailVerificationToken'
    | 'loginAttempts'
    | 'lockedUntil'
  >;
  tokens: TokenPair;
}

export class AuthService {
  private readonly SALT_ROUNDS = 10;
  private readonly MAX_LOGIN_ATTEMPTS = 5;
  private readonly LOCK_TIME = 15 * 60 * 1000; // 15 minutes

  async register(data: RegisterData): Promise<AuthResponse> {
    // Check if user already exists
    const existingUser = await db.query.user.findFirst({
      where: eq(user.email, data.email.toLowerCase()),
    });

    if (existingUser) {
      throw new ApiError(409, 'User with this email already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(data.password, this.SALT_ROUNDS);

    // Generate email verification token
    const emailVerificationToken = generateEmailVerificationToken(data.email);

    // Create user
    const [newUser] = await db
      .insert(user)
      .values({
        email: data.email.toLowerCase(),
        password: hashedPassword,
        firstName: data.firstName,
        lastName: data.lastName,
        displayName: data.displayName || `${data.firstName} ${data.lastName}`,
        emailVerificationToken,
        isEmailVerified: false,
      })
      .returning();

    // Send welcome email with verification link
    await emailService.sendEmailVerificationEmail(
      newUser.email,
      newUser.firstName,
      emailVerificationToken,
    );

    // Generate tokens
    const tokens = generateTokens({
      userId: newUser.id,
      email: newUser.email,
    });

    // Return user without sensitive data
    const { password: _, ...userWithoutPassword } = newUser;

    return {
      user: userWithoutPassword,
      tokens,
    };
  }

  async login(data: LoginData): Promise<AuthResponse> {
    // Find user
    const existingUser = await db.query.user.findFirst({
      where: eq(user.email, data.email.toLowerCase()),
    });

    if (!existingUser) {
      throw new ApiError(401, 'Invalid email or password');
    }

    // Check if account is locked
    // if (existingUser.lockedUntil && existingUser.lockedUntil > new Date()) {
    //   const remainingTime = Math.ceil(
    //     (existingUser.lockedUntil.getTime() - Date.now()) / 1000 / 60
    //   );
    //   throw new ApiError(
    //     423,
    //     `Account is locked. Please try again in ${remainingTime} minutes`
    //   );
    // }

    // Verify password
    const isPasswordValid = await bcrypt.compare(data.password, existingUser.password);

    if (!isPasswordValid) {
      // Increment login attempts
      const attempts = existingUser.loginAttempts + 1;
      const updateData: any = { loginAttempts: attempts };

      // Lock account if max attempts reached
      if (attempts >= this.MAX_LOGIN_ATTEMPTS) {
        updateData.lockedUntil = new Date(Date.now() + this.LOCK_TIME);
        updateData.loginAttempts = 0;
      }

      await db.update(user).set(updateData).where(eq(user.id, existingUser.id));

      throw new ApiError(401, 'Invalid email or password');
    }

    // Check if email is verified
    if (!existingUser.isEmailVerified) {
      throw new ApiError(403, 'Please verify your email before logging in');
    }

    // Reset login attempts and update last login
    await db
      .update(user)
      .set({
        loginAttempts: 0,
        lockedUntil: null,
        lastLogin: new Date(),
      })
      .where(eq(user.id, existingUser.id));

    // Generate tokens
    const tokens = generateTokens({
      userId: existingUser.id,
      email: existingUser.email,
    });

    // Return user without sensitive data
    const { password: _, ...userWithoutPassword } = existingUser;

    return {
      user: userWithoutPassword,
      tokens,
    };
  }

  async refreshToken(refreshToken: string): Promise<TokenPair> {
    try {
      // Verify refresh token
      const payload = verifyRefreshToken(refreshToken);

      // Get user
      const existingUser = await db.query.user.findFirst({
        where: eq(user.id, payload.userId),
      });

      if (!existingUser?.isActive) {
        throw new ApiError(401, 'Invalid refresh token');
      }

      // Generate new tokens
      return generateTokens({
        userId: existingUser.id,
        email: existingUser.email,
      });
    } catch (_error) {
      throw new ApiError(401, 'Invalid refresh token');
    }
  }

  async forgotPassword(email: string): Promise<void> {
    // Find user
    const existingUser = await db.query.user.findFirst({
      where: eq(user.email, email.toLowerCase()),
    });
    if (!existingUser) {
      // Don't reveal if user exists
      return;
    }

    // Generate password reset token
    const resetToken = generatePasswordResetToken(existingUser.id, existingUser.email);
    // Save token and expiry to database
    await db
      .update(user)
      .set({
        passwordResetToken: resetToken,
        passwordResetExpires: new Date(Date.now() + 60 * 60 * 1000), // 1 hour
      })
      .where(eq(user.id, existingUser.id));

    // Send password reset email
    await emailService.sendPasswordResetEmail(
      existingUser.email,
      existingUser.firstName,
      resetToken,
    );
  }

  async resetPassword(token: string, newPassword: string): Promise<void> {
    try {
      // Verify token
      const payload = verifyPasswordResetToken(token);

      // Find user with valid token
      const existingUser = await db.query.user.findFirst({
        where: and(eq(user.id, payload.userId), eq(user.passwordResetToken, token)),
      });

      if (!existingUser?.passwordResetExpires || existingUser.passwordResetExpires < new Date()) {
        throw new ApiError(400, 'Invalid or expired reset token');
      }

      // Hash new password
      const hashedPassword = await bcrypt.hash(newPassword, this.SALT_ROUNDS);

      // Update password and clear reset token
      await db
        .update(user)
        .set({
          password: hashedPassword,
          passwordResetToken: null,
          passwordResetExpires: null,
          loginAttempts: 0,
          lockedUntil: null,
        })
        .where(eq(user.id, existingUser.id));
    } catch (_error) {
      throw new ApiError(400, 'Invalid or expired reset token');
    }
  }

  async verifyEmail(token: string): Promise<void> {
    try {
      // Verify token
      const payload = verifyEmailToken(token);

      // Find user with this token
      const existingUser = await db.query.user.findFirst({
        where: and(
          eq(user.email, payload.email.toLowerCase()),
          eq(user.emailVerificationToken, token),
        ),
      });

      if (!existingUser) {
        throw new ApiError(400, 'Invalid verification token');
      }

      // Update user as verified
      await db
        .update(user)
        .set({
          isEmailVerified: true,
          emailVerificationToken: null,
        })
        .where(eq(user.id, existingUser.id));

      // Send welcome email
      await emailService.sendWelcomeEmail(existingUser.email, existingUser.firstName);
    } catch (_error) {
      throw new ApiError(400, 'Invalid verification token');
    }
  }

  async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string,
  ): Promise<void> {
    // Get user
    const existingUser = await db.query.user.findFirst({
      where: eq(user.id, userId),
    });

    if (!existingUser) {
      throw new ApiError(404, 'User not found');
    }

    // Verify current password
    const isPasswordValid = await bcrypt.compare(currentPassword, existingUser.password);

    if (!isPasswordValid) {
      throw new ApiError(401, 'Current password is incorrect');
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, this.SALT_ROUNDS);

    // Update password
    await db.update(user).set({ password: hashedPassword }).where(eq(user.id, userId));
  }

  async resendVerificationEmail(email: string): Promise<void> {
    // Find user
    const existingUser = await db.query.user.findFirst({
      where: eq(user.email, email.toLowerCase()),
    });

    if (!existingUser) {
      // Don't reveal if user exists
      return;
    }

    if (existingUser.isEmailVerified) {
      throw new ApiError(400, 'Email is already verified');
    }

    // Generate new verification token
    const emailVerificationToken = generateEmailVerificationToken(email);

    // Update token in database
    await db.update(user).set({ emailVerificationToken }).where(eq(user.id, existingUser.id));

    // Send verification email
    await emailService.sendEmailVerificationEmail(
      existingUser.email,
      existingUser.firstName,
      emailVerificationToken,
    );
  }
}

export const authService = new AuthService();
