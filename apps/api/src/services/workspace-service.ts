// Workspace service with business logic and validation

import { Readable } from 'node:stream';
import { db } from '@repo/db';
import type { InferSelectModel } from '@repo/db/orm';
import { and, eq, ne } from '@repo/db/orm';
import type { WorkspaceInsert } from '@repo/db/schema';
import { attachment, invitation, role, roleAssignment, user, workspace } from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { ApiError } from '../errors/api-error';
import { generatePublicUrl, generateStorageKey } from '../utils/upload/file-upload';
import { BaseService, type ServiceContext } from './base-service';

type WorkspaceSelect = InferSelectModel<typeof workspace>;
type WorkspaceUpdate = Partial<WorkspaceInsert>;

export class WorkspaceService extends BaseService<
  WorkspaceSelect,
  WorkspaceInsert,
  WorkspaceUpdate
> {
  private fastify?: FastifyInstance;

  constructor(fastify?: FastifyInstance) {
    super(db, workspace, db.query.workspace, 'workspace');
    this.fastify = fastify;
  }

  async validateCreate(data: WorkspaceInsert, _context: ServiceContext): Promise<void> {
    // Business rule: Check if workspace name is unique within organization
    await this.validateUniqueName(data.name, data.organizationId);

    // Business rule: Check if slug is unique within organization
    await this.validateUniqueSlug(data.slug, data.organizationId);

    // Business rule: Validate workspace settings
    if (data.settings && typeof data.settings === 'object') {
      const settings = data.settings as Record<string, any>;
      if (
        settings.defaultSprintDuration &&
        (settings.defaultSprintDuration < 1 || settings.defaultSprintDuration > 12)
      ) {
        throw ApiError.badRequest('Default sprint duration must be between 1 and 12 weeks');
      }
    }

    // Business rule: Validate user has permission to create workspace in organization
    // Note: Making userId optional for testing purposes
    // In production, this should be required
    // if (!context.userId) {
    //   throw ApiError.badRequest('User context required for workspace creation');
    // }
  }

  async validateUpdate(id: string, data: WorkspaceUpdate, context: ServiceContext): Promise<void> {
    const existing = await this.findById(id, context);

    // Business rule: Check if new name is unique within organization (if changing)
    if (data.name && data.name !== existing.name) {
      await this.validateUniqueName(data.name, existing.organizationId, id);
    }

    // Business rule: Check if new slug is unique within organization (if changing)
    if (data.slug && data.slug !== existing.slug) {
      await this.validateUniqueSlug(data.slug, existing.organizationId, id);
    }

    // Business rule: Cannot disable workspace if it has active projects
    if (data.isActive === false) {
      const hasActiveProjects = await this.checkActiveProjects(id);
      if (hasActiveProjects) {
        throw ApiError.conflict('Cannot disable workspace with active projects');
      }
    }

    // Business rule: Cannot change organization after creation
    if (data.organizationId && data.organizationId !== existing.organizationId) {
      throw ApiError.badRequest('Cannot change workspace organization after creation');
    }
  }

  async beforeCreate(data: WorkspaceInsert, context: ServiceContext): Promise<WorkspaceInsert> {
    // Auto-generate slug if not provided
    if (!data.slug && data.name) {
      data.slug = this.generateSlug(data.name);

      // Ensure generated slug is unique
      await this.validateUniqueSlug(data.slug, data.organizationId);
    }

    // Set default settings if not provided
    if (!data.settings) {
      data.settings = {
        defaultSprintDuration: 2, // 2 weeks default
        workItemPrefix: data.slug.toUpperCase().substring(0, 3),
        timeZone: 'UTC',
        workingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
      };
    }

    // Set creator ID if provided in context
    if (context.userId && !data.createdBy) {
      data.createdBy = context.userId;
    }

    // Handle base64 icon upload
    if (data.icon && this.isBase64Image(data.icon)) {
      const attachmentUrl = await this.uploadBase64Icon(data.icon, data.name, context);
      data.icon = attachmentUrl;
    }

    return data;
  }

  async afterCreate(workspace: WorkspaceSelect, _context: ServiceContext): Promise<void> {
    if (workspace.createdBy) {
      await this.assignUserRole(workspace.id, workspace.createdBy, 'ws_admin');
    }

    // Business logic: Create default workspace structure
    await this.createDefaultStructure(workspace);
  }

  async beforeUpdate(
    id: string,
    data: WorkspaceUpdate,
    context: ServiceContext,
  ): Promise<WorkspaceUpdate> {
    // Business logic: Log significant changes
    if (data.name || data.slug) {
      const _current = await this.findById(id, context);
    }

    // Handle base64 icon upload
    if (data.icon && this.isBase64Image(data.icon)) {
      // Delete old icon if exists
      const current = await this.findById(id, context);
      if (current.icon) {
        await this.deleteOldIcon(current.icon);
      }

      // Upload new icon
      const attachmentUrl = await this.uploadBase64Icon(
        data.icon,
        data.name || current.name,
        context,
      );
      data.icon = attachmentUrl;
    }

    return data;
  }

  async afterUpdate(workspace: WorkspaceSelect, _context: ServiceContext): Promise<void> {
    // Business logic: Invalidate related caches
    await this.invalidateWorkspaceCache(workspace.id);

    // Business logic: Notify workspace members of changes
    if (workspace.name) {
      await this.notifyMembersOfChanges(workspace.id, 'workspace_updated');
    }
  }

  // Workspace-specific business methods
  async getWorkspaceStats(
    id: string,
    context: ServiceContext,
  ): Promise<{
    memberCount: number;
    projectCount: number;
    activeProjectCount: number;
    workItemCount: number;
    activeSprintCount: number;
  }> {
    const _ws = await this.findById(id, context);

    // Calculate workspace statistics
    const [memberCount, projectCount, activeProjectCount, workItemCount, activeSprintCount] =
      await Promise.all([
        this.getMemberCount(id),
        this.getProjectCount(id),
        this.getActiveProjectCount(id),
        this.getWorkItemCount(id),
        this.getActiveSprintCount(id),
      ]);

    return {
      memberCount,
      projectCount,
      activeProjectCount,
      workItemCount,
      activeSprintCount,
    };
  }

  // Private helper methods
  private async validateUniqueName(
    name: string,
    organizationId: string,
    excludeId?: string,
  ): Promise<void> {
    const conditions = [eq(workspace.name, name), eq(workspace.organizationId, organizationId)];
    if (excludeId) {
      conditions.push(ne(workspace.id, excludeId));
    }

    const existing = await this.queryApi.findFirst({
      where: and(...conditions),
    });

    if (existing) {
      throw ApiError.duplicate('name', name);
    }
  }

  private async validateUniqueSlug(
    slug: string,
    organizationId: string,
    excludeId?: string,
  ): Promise<void> {
    const conditions = [eq(workspace.slug, slug), eq(workspace.organizationId, organizationId)];
    if (excludeId) {
      conditions.push(ne(workspace.id, excludeId));
    }

    const existing = await this.queryApi.findFirst({
      where: and(...conditions),
    });

    if (existing) {
      throw ApiError.duplicate('slug', slug);
    }
  }

  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
      .substring(0, 50);
  }

  private async checkActiveProjects(_workspaceId: string): Promise<boolean> {
    // Implementation would check if workspace has active projects
    // This is a placeholder
    return false;
  }

  private async assignUserRole(
    workspaceId: string,
    userId: string,
    roleName: string,
  ): Promise<void> {
    try {
      const roleRecord = await db.query.role.findFirst({
        where: eq(role.identifier, roleName),
      });

      if (!roleRecord) {
        return;
      }

      // Check if assignment already exists
      const existingAssignment = await db.query.roleAssignment.findFirst({
        where: and(
          eq(roleAssignment.userId, userId),
          eq(roleAssignment.roleId, roleRecord.id),
          eq(roleAssignment.scopeType, 'workspace'),
          eq(roleAssignment.scopeId, workspaceId),
        ),
      });

      if (existingAssignment) {
        return;
      }

      // Create the role assignment
      await db.insert(roleAssignment).values({
        userId,
        roleId: roleRecord.id,
        scopeType: 'workspace',
        scopeId: workspaceId,
        isActive: true,
      });

      // Check for pending invitations and auto-accept them
      const userRecord = await db.query.user.findFirst({
        where: eq(user.id, userId),
      });

      if (userRecord) {
        const pendingInvitations = await db.query.invitation.findMany({
          where: and(
            eq(invitation.email, userRecord.email),
            eq(invitation.scopeType, 'workspace'),
            eq(invitation.scopeId, workspaceId),
            eq(invitation.status, 'pending'),
          ),
        });

        // Auto-accept any pending invitations since user now has a role
        for (const inv of pendingInvitations) {
          await db
            .update(invitation)
            .set({
              status: 'accepted',
              acceptedAt: new Date(),
              updatedAt: new Date(),
            })
            .where(eq(invitation.id, inv.id));
        }
      }
    } catch (_error) {}
  }

  private async createDefaultStructure(_workspace: WorkspaceSelect): Promise<void> {}

  private async invalidateWorkspaceCache(_workspaceId: string): Promise<void> {}

  private async notifyMembersOfChanges(_workspaceId: string, _changeType: string): Promise<void> {}

  private async getMemberCount(_workspaceId: string): Promise<number> {
    // Implementation would count workspace members
    return 0;
  }

  private async getProjectCount(_workspaceId: string): Promise<number> {
    // Implementation would count workspace projects
    return 0;
  }

  private async getActiveProjectCount(_workspaceId: string): Promise<number> {
    // Implementation would count active workspace projects
    return 0;
  }

  private async getWorkItemCount(_workspaceId: string): Promise<number> {
    // Implementation would count work items in workspace
    return 0;
  }

  private async getActiveSprintCount(_workspaceId: string): Promise<number> {
    // Implementation would count active sprints in workspace
    return 0;
  }

  // Base64 image handling methods
  private isBase64Image(data: string): boolean {
    // Check if the string is a base64 encoded image
    return data.startsWith('data:image/');
  }

  private async uploadBase64Icon(
    base64Data: string,
    workspaceName: string,
    _context: ServiceContext,
  ): Promise<string> {
    if (!this.fastify) {
      throw ApiError.badRequest('Service not properly initialized with fastify instance');
    }

    try {
      // Extract mime type and base64 data
      const matches = base64Data.match(/^data:image\/([a-zA-Z]+);base64,(.+)$/);
      if (!matches || matches.length !== 3) {
        throw ApiError.badRequest('Invalid base64 image format');
      }

      const mimeType = `image/${matches[1]}`;
      const base64Content = matches[2];
      const buffer = Buffer.from(base64Content, 'base64');

      // Generate filename
      const timestamp = Date.now();
      const filename = `${workspaceName.toLowerCase().replace(/[^a-z0-9]/g, '-')}-icon-${timestamp}.${matches[1]}`;

      // Create a temporary workspace ID for storage key generation
      const tempWsId = `temp-${timestamp}`;
      const storageKey = generateStorageKey('workspace', tempWsId, filename);

      // Upload to MinIO
      const stream = Readable.from(buffer);
      await this.fastify.minio.putObject(
        this.fastify.config.minio.bucketName,
        storageKey,
        stream,
        buffer.length,
        {
          'Content-Type': mimeType,
          'x-amz-meta-original-name': filename,
          'x-amz-meta-entity-type': 'workspace',
        },
      );

      // Generate public URL
      const url = generatePublicUrl(storageKey);

      // Note: We're not creating an attachment record here since we don't have the workspace ID yet
      // The attachment record will be created after the workspace is created

      return url;
    } catch (_error) {
      throw ApiError.internal('Failed to upload icon');
    }
  }

  private async deleteOldIcon(iconUrl: string): Promise<void> {
    if (!this.fastify) {
      return; // Skip deletion if fastify is not available
    }

    try {
      // Extract storage key from URL
      const urlParts = iconUrl.split('/');
      const storageKey = urlParts.slice(-3).join('/'); // entity-type/entity-id/filename

      // Delete from MinIO
      await this.fastify.minio.removeObject(this.fastify.config.minio.bucketName, storageKey);

      // Find and delete attachment record if exists
      const attachmentRecord = await db.query.attachment.findFirst({
        where: eq(attachment.url, iconUrl),
      });

      if (attachmentRecord) {
        await db.delete(attachment).where(eq(attachment.id, attachmentRecord.id));
      }
    } catch (_error) {
      // Don't throw error to allow update to proceed
    }
  }
}
