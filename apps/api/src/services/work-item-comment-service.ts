import { db } from '@repo/db';
import type { InferSelectModel } from '@repo/db/orm';
import { eq } from '@repo/db/orm';
import { user, type WorkItemCommentInsert, workItem, workItemComment } from '@repo/db/schema';
import { extractAndResolveMentions } from '../utils/mention-extractor';
import { BaseService, type ServiceContext } from './base-service';
import { fcmService } from './fcm-service';

type WorkItemCommentSelect = InferSelectModel<typeof workItemComment>;
type WorkItemCommentUpdate = Partial<WorkItemCommentInsert>;

export class WorkItemCommentService extends BaseService<
  WorkItemCommentSelect,
  WorkItemCommentInsert,
  WorkItemCommentUpdate
> {
  constructor() {
    super(db, workItemComment, db.query.workItemComment, 'WorkItemComment');
  }

  async validateCreate(data: WorkItemCommentInsert, _context: ServiceContext): Promise<void> {
    // Validate work item exists
    if (data.workItemId) {
      const workItemExists = await db.query.workItem.findFirst({
        where: eq(workItem.id, data.workItemId),
      });
      if (!workItemExists) {
        throw new Error('Work item not found');
      }
    }
  }

  async validateUpdate(
    _id: string,
    _data: WorkItemCommentUpdate,
    _context: ServiceContext,
  ): Promise<void> {
    // Basic validation
  }

  async afterCreate(entity: WorkItemCommentSelect, context: ServiceContext): Promise<void> {
    // Handle mentions in the comment
    if (entity.content) {
      try {
        const { userIds } = await extractAndResolveMentions(entity.content);

        if (userIds.length > 0) {
          // Get work item info
          const workItemData = await db.query.workItem.findFirst({
            where: eq(workItem.id, entity.workItemId),
            with: {
              project: true,
            },
          });

          // Get author info
          let mentionedBy = 'Someone';
          if (context.userId) {
            const author = await db.query.user.findFirst({
              where: eq(user.id, context.userId),
            });
            if (author) {
              mentionedBy = author.displayName || `${author.firstName} ${author.lastName}`;
            }
          }

          // Send notifications to mentioned users
          for (const userId of userIds) {
            // Don't notify the author of their own comment
            if (userId !== context.userId) {
              await fcmService.sendMentionNotification(userId, {
                commentId: entity.id,
                workItemId: entity.workItemId,
                workItemTitle: workItemData?.title || 'Unknown Work Item',
                workItemTicketId: workItemData?.ticketId || 'Unknown Ticket',
                projectId: workItemData?.projectId || '',
                mentionedBy,
                commentText:
                  entity.content.substring(0, 100) + (entity.content.length > 100 ? '...' : ''),
              });
            }
          }
        }
      } catch (_error) {}
    }
  }

  async afterUpdate(entity: WorkItemCommentSelect, context: ServiceContext): Promise<void> {
    // Handle mentions in updated comment
    if (entity.content) {
      try {
        const { userIds } = await extractAndResolveMentions(entity.content);

        if (userIds.length > 0) {
          // Get work item info
          const workItemData = await db.query.workItem.findFirst({
            where: eq(workItem.id, entity.workItemId),
            with: {
              project: true,
            },
          });

          // Get author info
          let mentionedBy = 'Someone';
          if (context.userId) {
            const author = await db.query.user.findFirst({
              where: eq(user.id, context.userId),
            });
            if (author) {
              mentionedBy = author.displayName || `${author.firstName} ${author.lastName}`;
            }
          }

          // Send notifications to mentioned users
          for (const userId of userIds) {
            // Don't notify the author of their own comment
            if (userId !== context.userId) {
              await fcmService.sendMentionNotification(userId, {
                commentId: entity.id,
                workItemId: entity.workItemId,
                workItemTitle: workItemData?.title || 'Unknown Work Item',
                workItemTicketId: workItemData?.ticketId || 'Unknown Ticket',
                projectId: workItemData?.projectId || '',
                mentionedBy,
                commentText:
                  entity.content.substring(0, 100) + (entity.content.length > 100 ? '...' : ''),
              });
            }
          }
        }
      } catch (_error) {}
    }
  }
}

export const workItemCommentService = new WorkItemCommentService();
