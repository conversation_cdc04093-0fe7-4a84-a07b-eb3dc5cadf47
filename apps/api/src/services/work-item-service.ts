import { db } from '@repo/db';
import type { InferSelectModel } from '@repo/db/orm';
import { eq, sql } from '@repo/db/orm';
import { project, user, type WorkItemInsert, workItem } from '@repo/db/schema';
import { ApiError } from '../errors/api-error';
import { BaseService, type ServiceContext } from './base-service';
import { fcmService } from './fcm-service';

type WorkItemSelect = InferSelectModel<typeof workItem>;
type WorkItemUpdate = Partial<WorkItemInsert>;

export class WorkItemService extends BaseService<WorkItemSelect, WorkItemInsert, WorkItemUpdate> {
  constructor() {
    super(db, workItem, db.query.workItem, 'WorkItem');
  }

  // Required abstract methods
  async validateCreate(_data: WorkItemInsert, _context: ServiceContext): Promise<void> {
    // Add any validation logic here if needed
  }

  async validateUpdate(
    _id: string,
    _data: WorkItemUpdate,
    _context: ServiceContext,
  ): Promise<void> {
    // Add any validation logic here if needed
  }

  // Optional methods that can be implemented
  async beforeCreate(data: WorkItemInsert, _context: ServiceContext): Promise<WorkItemInsert> {
    return data;
  }

  async afterCreate(entity: WorkItemSelect, context: ServiceContext): Promise<void> {
    // Send notification if work item is assigned
    if (entity.assigneeId && entity.assigneeId !== context.userId) {
      try {
        // Get project info for notification
        const _projectData = await db.query.project.findFirst({
          where: eq(project.id, entity.projectId),
        });

        // Get actor name
        let actorName = 'Someone';
        if (context.userId) {
          const actor = await db.query.user.findFirst({
            where: eq(user.id, context.userId),
          });
          if (actor) {
            actorName = actor.displayName || `${actor.firstName} ${actor.lastName}`;
          }
        }

        await fcmService.sendWorkItemNotification(entity.assigneeId, {
          workItemId: entity.id,
          workItemTitle: entity.title,
          workItemTicketId: entity.ticketId,
          projectId: entity.projectId,
          action: 'assigned',
          actorName,
        });
      } catch (_error) {}
    }
  }

  async beforeUpdate(
    _id: string,
    data: WorkItemUpdate,
    _context: ServiceContext,
  ): Promise<WorkItemUpdate> {
    return data;
  }

  async afterUpdate(entity: WorkItemSelect, context: ServiceContext): Promise<void> {
    // Send notifications for work item updates
    if (entity.assigneeId && entity.assigneeId !== context.userId) {
      try {
        // Get actor name
        let actorName = 'Someone';
        if (context.userId) {
          const actor = await db.query.user.findFirst({
            where: eq(user.id, context.userId),
          });
          if (actor) {
            actorName = actor.displayName || `${actor.firstName} ${actor.lastName}`;
          }
        }

        // Determine the type of update
        const action: 'updated' | 'status_changed' | 'priority_changed' = 'updated';
        // Note: We'd need to track what changed to determine specific action
        // For now, we'll use generic "updated"

        await fcmService.sendWorkItemNotification(entity.assigneeId, {
          workItemId: entity.id,
          workItemTitle: entity.title,
          workItemTicketId: entity.ticketId,
          projectId: entity.projectId,
          action,
          actorName,
        });
      } catch (_error) {}
    }
  }

  /**
   * Create a new work item with auto-generated ticket ID
   */
  async create(data: WorkItemInsert, context: ServiceContext): Promise<WorkItemSelect> {
    // Validate business rules first
    await this.validateCreate(data, context);

    // Pre-processing hook
    let processedData = data;
    if (this.beforeCreate) {
      processedData = await this.beforeCreate(data, context);
    }

    // Start a transaction to ensure atomicity
    const newWorkItem = await db.transaction(async (tx) => {
      // 1. Get the project and its current ticket counter
      const projectData = await tx.query.project.findFirst({
        where: eq(project.id, processedData.projectId),
      });

      if (!projectData) {
        throw ApiError.notFound('Project', processedData.projectId);
      }

      // 2. Calculate the next ticket number
      const nextTicketNumber = projectData.lastTicketNumber + 1;

      // 3. Generate the ticket ID (e.g., "PROJ-1")
      const ticketId = `${projectData.key}-${nextTicketNumber}`;

      // 4. Update the project's ticket counter
      await tx
        .update(project)
        .set({
          lastTicketNumber: nextTicketNumber,
          updatedAt: new Date(),
        })
        .where(eq(project.id, processedData.projectId));

      // 5. Create the work item with the generated ticket number and ID
      const workItemData: any = {
        ...processedData,
        ticketNumber: nextTicketNumber,
        ticketId,
        createdBy: context.userId || processedData.createdBy,
        createdAt: new Date(),
      };

      const [created] = await tx.insert(workItem).values(workItemData).returning();

      return created;
    });

    // Get the full entity with relations
    const entity = await this.findById(newWorkItem.id, context);

    // Post-processing hook - this will trigger notifications
    if (this.afterCreate) {
      await this.afterCreate(entity, context);
    }

    return entity;
  }

  /**
   * Find work item by ticket ID (e.g., "PROJ-123")
   */
  async findByTicketId(ticketId: string): Promise<WorkItemSelect | null> {
    const result = await db.query.workItem.findFirst({
      where: eq(workItem.ticketId, ticketId),
      with: {
        project: true,
        type: true,
        status: true,
        priority: true,
        assignee: true,
        reporter: true,
        sprint: true,
      },
    });

    return result || null;
  }

  /**
   * Find work item by project ID and ticket number
   */
  async findByProjectAndNumber(
    projectId: string,
    ticketNumber: number,
  ): Promise<WorkItemSelect | null> {
    const result = await db.query.workItem.findFirst({
      where: sql`${workItem.projectId} = ${projectId} AND ${workItem.ticketNumber} = ${ticketNumber}`,
      with: {
        project: true,
        type: true,
        status: true,
        priority: true,
        assignee: true,
        reporter: true,
        sprint: true,
      },
    });

    return result || null;
  }

  /**
   * Override update to exclude ticket fields which are immutable
   */
  async update(id: string, data: WorkItemUpdate, context: ServiceContext): Promise<WorkItemSelect> {
    // Remove ticket fields from update data as they should never change
    const { ticketNumber: _, ticketId: __, ...updateData } = data as any;

    // Call parent's update method with filtered data to ensure lifecycle hooks are called
    return await super.update(id, updateData, context);
  }
}

// Export a singleton instance
export const workItemService = new WorkItemService();
