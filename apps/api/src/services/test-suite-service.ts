import { db } from '@repo/db';
import { and, eq, ilike, inArray, notInArray, or, sql } from '@repo/db/orm';
import type { TestSuite, TestSuiteInsert, TestSuitePatch } from '@repo/db/schema';
import { testCase, testSuite, testSuiteTestCase } from '@repo/db/schema';
import { ApiError } from '../errors/api-error';
import { BaseService, type ServiceContext } from './base-service';
import { testCaseService } from './test-case-service';

export class TestSuiteService extends BaseService<TestSuite, TestSuiteInsert, TestSuitePatch> {
  constructor() {
    super(db, testSuite, db.query.testSuite, 'Test Suite');
  }

  // Required abstract methods from BaseService
  async validateCreate(_data: TestSuiteInsert, _context: ServiceContext): Promise<void> {
    // Add any test suite specific validation here
  }

  async create(data: TestSuiteInsert, context: ServiceContext): Promise<TestSuite> {
    // Check if this is a copy operation
    if (data.copiedFromId) {
      const sourceSuite = await this.db.query.testSuite.findFirst({
        where: eq(testSuite.id, data.copiedFromId),
      });

      if (!sourceSuite) {
        throw ApiError.notFound('Source test suite', data.copiedFromId);
      }

      // Use source suite data if not provided
      const suiteData = {
        ...data,
        description: data.description !== undefined ? data.description : sourceSuite.description,
        type: data.type || sourceSuite.type,
        requirementQuery:
          data.requirementQuery !== undefined
            ? data.requirementQuery
            : sourceSuite.requirementQuery,
        parentSuiteId:
          data.parentSuiteId !== undefined ? data.parentSuiteId : sourceSuite.parentSuiteId,
        order: data.order !== undefined ? data.order : sourceSuite.order,
      };

      // Create the new suite
      const [newSuite] = await this.db.insert(testSuite).values(suiteData).returning();

      // Handle test case copying/linking
      const includeTestCases = (data as any).includeTestCases !== false;
      const selectedTestCaseIds = (data as any).selectedTestCaseIds as string[] | undefined;
      const linkTestCases = (data as any).linkTestCases !== false; // Default to linking (same test case ID)

      if (includeTestCases) {
        // Get test cases from source suite through junction table
        const sourceTestCases = await this.db.query.testSuiteTestCase.findMany({
          where: eq(testSuiteTestCase.testSuiteId, data.copiedFromId),
          with: {
            testCase: true,
            status: true,
            priority: true,
          },
        });

        // Filter test cases if specific ones are selected
        const testCasesToProcess = selectedTestCaseIds
          ? sourceTestCases.filter((tc) => selectedTestCaseIds.includes(tc.testCaseId))
          : sourceTestCases;

        if (testCasesToProcess.length > 0) {
          if (linkTestCases) {
            // Link existing test cases to the new suite (DEFAULT BEHAVIOR)
            // Same test case ID across suites, but different status/priority per suite
            const links = testCasesToProcess.map((tc) => ({
              testSuiteId: newSuite.id,
              testCaseId: tc.testCaseId,
              statusId: tc.statusId,
              priorityId: tc.priorityId,
              order: tc.order,
              createdBy: context.userId,
            }));

            await this.db.insert(testSuiteTestCase).values(links);
          } else {
            // Deep copy test cases (create new test case instances with different IDs)
            const newTestCases = await Promise.all(
              testCasesToProcess.map(async (tc) => {
                const { testCase: sourceTestCase } = tc;
                const [newTestCase] = await this.db
                  .insert(testCase)
                  .values({
                    ...sourceTestCase,
                    id: undefined,
                    copiedFromId: sourceTestCase.id,
                    createdAt: undefined,
                    updatedAt: undefined,
                    createdBy: context.userId,
                  })
                  .returning();

                return { ...tc, testCaseId: newTestCase.id };
              }),
            );

            // Link the new test cases to the suite
            const links = newTestCases.map((tc) => ({
              testSuiteId: newSuite.id,
              testCaseId: tc.testCaseId,
              statusId: tc.statusId,
              priorityId: tc.priorityId,
              order: tc.order,
              createdBy: context.userId,
            }));

            await this.db.insert(testSuiteTestCase).values(links);
          }
        }
      }

      // Return the new suite with relations
      const result = await this.queryApi.findFirst({
        where: eq(testSuite.id, newSuite.id),
        with: context.with,
      });

      if (!result) {
        throw ApiError.notFound(this.entityName, newSuite.id);
      }

      return result;
    }

    // Regular create
    return super.create(data, context);
  }

  async validateUpdate(
    _id: string,
    _data: TestSuitePatch,
    _context: ServiceContext,
  ): Promise<void> {
    // Add any test suite specific validation here
  }

  async update(id: string, data: TestSuitePatch, context: ServiceContext): Promise<TestSuite> {
    // Check if this is an import operation
    const sourceSuiteId = (data as any).sourceSuiteId;
    const selectedTestCaseIds = (data as any).selectedTestCaseIds as string[] | undefined;

    if (sourceSuiteId || selectedTestCaseIds) {
      // This is an import operation - copy test cases from source to target
      const targetSuite = await this.db.query.testSuite.findFirst({
        where: eq(testSuite.id, id),
      });

      if (!targetSuite) {
        throw ApiError.notFound(this.entityName, id);
      }

      let testCasesToImport: any[] = [];

      if (sourceSuiteId) {
        // Import from a specific test suite
        const sourceTestCases = await this.db.query.testSuiteTestCase.findMany({
          where: eq(testSuiteTestCase.testSuiteId, sourceSuiteId),
          with: {
            testCase: true,
            status: true,
            priority: true,
          },
        });

        if (sourceTestCases.length === 0) {
          throw ApiError.badRequest('No test cases found in source suite to import');
        }

        testCasesToImport = selectedTestCaseIds
          ? sourceTestCases.filter((tc) => selectedTestCaseIds.includes(tc.testCaseId))
          : sourceTestCases;
      } else if (selectedTestCaseIds) {
        // Import specific test cases from anywhere
        const testCasesToLink = await this.db.query.testCase.findMany({
          where: and(inArray(testCase.id, selectedTestCaseIds), eq(testCase.isActive, true)),
        });

        if (testCasesToLink.length === 0) {
          throw ApiError.badRequest('No test cases found to import');
        }

        // Transform to match expected structure
        testCasesToImport = testCasesToLink.map((tc) => ({
          testCase: tc,
          testCaseId: tc.id,
          statusId: null,
          priorityId: null,
          order: 0,
        }));
      }

      const linkTestCases = (data as any).linkTestCases !== false; // Default to linking (same test case ID)
      let importedCount = 0;

      if (linkTestCases) {
        // Link existing test cases (DEFAULT BEHAVIOR)
        // Same test case ID across suites, but different status/priority per suite
        const links = testCasesToImport.map((tc) => ({
          testSuiteId: id,
          testCaseId: tc.testCaseId,
          statusId: tc.statusId,
          priorityId: tc.priorityId,
          order: tc.order,
          createdBy: context.userId,
        }));

        // Filter out any test cases that are already linked to this suite
        const existingLinks = await this.db.query.testSuiteTestCase.findMany({
          where: and(
            eq(testSuiteTestCase.testSuiteId, id),
            inArray(
              testSuiteTestCase.testCaseId,
              links.map((l) => l.testCaseId),
            ),
          ),
        });

        const existingTestCaseIds = existingLinks.map((l) => l.testCaseId);
        const newLinks = links.filter((l) => !existingTestCaseIds.includes(l.testCaseId));

        if (newLinks.length > 0) {
          await this.db.insert(testSuiteTestCase).values(newLinks);
        }
        importedCount = newLinks.length;
      } else {
        // Deep copy test cases (create new test case instances with different IDs)
        const newTestCases = await Promise.all(
          testCasesToImport.map(async (tc) => {
            const { testCase: sourceTestCase } = tc;
            const [newTestCase] = await this.db
              .insert(testCase)
              .values({
                ...sourceTestCase,
                id: undefined,
                copiedFromId: sourceTestCase.id,
                createdAt: undefined,
                updatedAt: undefined,
                createdBy: context.userId,
              })
              .returning();

            return { ...tc, testCaseId: newTestCase.id };
          }),
        );

        // Link the new test cases
        const links = newTestCases.map((tc) => ({
          testSuiteId: id,
          testCaseId: tc.testCaseId,
          statusId: tc.statusId,
          priorityId: tc.priorityId,
          order: tc.order,
          createdBy: context.userId,
        }));

        await this.db.insert(testSuiteTestCase).values(links);
        importedCount = links.length;
      }

      // Return a special response for import
      return {
        ...targetSuite,
        imported: importedCount,
      } as any;
    }

    // Regular update
    return super.update(id, data, context);
  }

  async validateDelete(id: string, _context?: ServiceContext): Promise<void> {
    // Check if test suite exists
    const suite = await this.db.query.testSuite.findFirst({
      where: eq(testSuite.id, id),
    });

    if (!suite) {
      throw ApiError.notFound(this.entityName, id);
    }

    // Cascade delete is properly configured in the database schema:
    // - Test cases will be automatically deleted (onDelete: "cascade")
    // - Child test suites will be automatically deleted (onDelete: "cascade")
    // - Comments will be automatically deleted (onDelete: "cascade")
    // - Attachments are handled by database triggers
  }

  protected async checkDeleteAccess(entity: TestSuite, context: ServiceContext): Promise<void> {
    // Call our custom validation
    await this.validateDelete(entity.id, context);
  }

  async delete(id: string, context: ServiceContext): Promise<void> {
    try {
      // Use parent's delete method which handles all the standard checks
      await super.delete(id, context);
    } catch (error: any) {
      // If it's a foreign key constraint error, provide a better message
      if (error.code === '23503') {
        throw ApiError.conflict(
          'Cannot delete test suite because it has related records. Please delete all related records first.',
        );
      }
      throw error;
    }
  }

  /**
   * Get test cases for a specific suite with suite-specific properties
   */
  async getTestCasesForSuite(testSuiteId: string, context: ServiceContext): Promise<any[]> {
    return testCaseService.getTestCasesForSuite(testSuiteId, context);
  }

  /**
   * Add a test case to a test suite
   */
  async addTestCaseToSuite(
    testSuiteId: string,
    testCaseId: string,
    data: { statusId?: string; priorityId?: string; createdBy?: string },
  ): Promise<void> {
    await testCaseService.addToSuite(testCaseId, testSuiteId, data, {
      userId: data.createdBy,
    });
  }

  /**
   * Remove a test case from a test suite
   */
  async removeTestCaseFromSuite(testSuiteId: string, testCaseId: string): Promise<void> {
    await testCaseService.removeFromSuite(testCaseId, testSuiteId);
  }

  /**
   * Update test case properties within a suite
   */
  async updateTestCaseInSuite(
    testSuiteId: string,
    testCaseId: string,
    data: { statusId?: string; priorityId?: string; order?: number },
  ): Promise<void> {
    await testCaseService.updateInSuite(testCaseId, testSuiteId, data);
  }

  /**
   * Get test cases available to add to a suite
   */
  async getAvailableTestCases(testSuiteId: string, projectId?: string): Promise<any[]> {
    // Get all test case IDs already in this suite
    const existingTestCases = await db.query.testSuiteTestCase.findMany({
      where: eq(testSuiteTestCase.testSuiteId, testSuiteId),
      columns: {
        testCaseId: true,
      },
    });

    const existingIds = existingTestCases.map((tc) => tc.testCaseId);

    // Build query conditions
    const conditions: any[] = [];

    // If we have existing test cases, exclude them
    if (existingIds.length > 0) {
      conditions.push(notInArray(testCase.id, existingIds));
    }

    // Filter by project if provided
    if (projectId) {
      conditions.push(eq(testCase.projectId, projectId));
    }

    // Get available test cases
    const availableTestCases = await db.query.testCase.findMany({
      where: conditions.length > 0 ? and(...conditions) : undefined,
      with: {
        project: {
          columns: {
            id: true,
            name: true,
            key: true,
          },
        },
        assignee: {
          columns: {
            id: true,
            email: true,
            displayName: true,
          },
        },
      },
      orderBy: (testCase, { desc }) => [desc(testCase.createdAt)],
    });

    return availableTestCases;
  }

  /**
   * Get all test suites for importing (shows all test suites across all test plans)
   */
  async getAllForImporting(
    options: {
      testPlanId?: string;
      search?: string;
      limit?: number;
      offset?: number;
    },
    context: ServiceContext,
  ): Promise<{ data: any[]; total: number }> {
    const { testPlanId, search, limit = 50, offset = 0 } = options;

    // Build query conditions
    const conditions: any[] = [];

    // Filter by test plan if provided
    if (testPlanId) {
      conditions.push(eq(testSuite.testPlanId, testPlanId));
    }

    // Search filter
    if (search) {
      conditions.push(
        or(ilike(testSuite.name, `%${search}%`), ilike(testSuite.description, `%${search}%`)),
      );
    }

    // Active filter
    conditions.push(eq(testSuite.isActive, true));

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get total count
    const [{ count }] = await db
      .select({ count: sql`count(*)`.mapWith(Number) })
      .from(testSuite)
      .where(whereClause);

    // Get data with test case counts
    const testSuites = await db.query.testSuite.findMany({
      where: whereClause,
      with: {
        testPlan: {
          columns: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: (testSuite, { desc }) => [desc(testSuite.createdAt)],
      limit,
      offset,
    });

    // Get test case counts for each suite
    const testSuitesWithCounts = await Promise.all(
      testSuites.map(async (suite) => {
        const testCaseCount = await db
          .select({ count: sql`count(*)`.mapWith(Number) })
          .from(testSuiteTestCase)
          .where(eq(testSuiteTestCase.testSuiteId, suite.id))
          .then((result) => result[0]?.count || 0);

        return {
          ...suite,
          testCaseCount,
        };
      }),
    );

    return {
      data: testSuitesWithCounts,
      total: count,
    };
  }
}
