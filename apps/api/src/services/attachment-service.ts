import { Readable } from 'node:stream';
import { db } from '@repo/db';
import {
  type AttachmentInsert,
  attachment,
  organization,
  project,
  workItem,
  workspace,
} from '@repo/db/schema';
import { eq } from 'drizzle-orm';
import type { FastifyInstance } from 'fastify';
import { ApiError, ErrorCode } from '../errors/api-error';
import {
  type FileUploadOptions,
  generatePublicUrl,
  generateStorageKey,
  type UploadedFile,
  validateFileUpload,
} from '../utils/upload/file-upload';

export interface FileUploadResult {
  id: string;
  fileName: string;
  url: string;
  size: number;
  fileType: string;
}

export class AttachmentService {
  private fastify: FastifyInstance;

  constructor(fastify: FastifyInstance) {
    this.fastify = fastify;
  }

  /**
   * Upload a file to MinIO and create attachment record
   */
  async uploadFile(
    file: UploadedFile & { data: Buffer | Readable },
    options: FileUploadOptions,
    userId: string,
  ): Promise<FileUploadResult> {
    // Validate file
    const validation = validateFileUpload(file, options);
    if (!validation.valid) {
      throw new ApiError(400, validation.error || 'Invalid file', ErrorCode.VALIDATION_ERROR);
    }

    // Generate storage key
    const storageKey = generateStorageKey(options.entityType, options.entityId, file.filename);

    try {
      // Upload to MinIO
      const stream = file.data instanceof Buffer ? Readable.from(file.data) : file.data;

      await this.fastify.minio.putObject(
        this.fastify.config.minio.bucketName,
        storageKey,
        stream,
        file.size,
        {
          'Content-Type': file.mimetype,
          'x-amz-meta-original-name': file.filename,
          'x-amz-meta-entity-type': options.entityType,
          'x-amz-meta-entity-id': options.entityId,
        },
      );

      // Generate public URL
      const url = generatePublicUrl(storageKey);

      // Create attachment record
      const attachmentData: AttachmentInsert = {
        entityType: options.entityType as any,
        entityId: options.entityId,
        fileName: file.filename,
        url,
        fileType: file.mimetype,
        size: file.size,
        uploadedBy: userId,
      };

      const [createdAttachment] = await db.insert(attachment).values(attachmentData).returning();

      return {
        id: createdAttachment.id,
        fileName: createdAttachment.fileName,
        url: createdAttachment.url,
        size: Number(createdAttachment.size),
        fileType: createdAttachment.fileType || file.mimetype,
      };
    } catch (error) {
      this.fastify.log.error(`Failed to upload file: ${error}`);
      throw new ApiError(500, 'Failed to upload file', ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Delete a file from MinIO and remove attachment record
   */
  async deleteFile(attachmentId: string, _userId?: string): Promise<void> {
    // Get attachment record
    const attachmentRecord = await db.query.attachment.findFirst({
      where: eq(attachment.id, attachmentId),
    });

    if (!attachmentRecord) {
      throw new ApiError(404, 'Attachment not found', ErrorCode.NOT_FOUND);
    }

    // Extract storage key from URL
    const urlParts = attachmentRecord.url.split('/');
    const storageKey = urlParts.slice(-3).join('/'); // entity-type/entity-id/filename

    try {
      // Delete from MinIO
      await this.fastify.minio.removeObject(this.fastify.config.minio.bucketName, storageKey);

      // Delete attachment record
      await db.delete(attachment).where(eq(attachment.id, attachmentId));

      this.fastify.log.info(`Deleted attachment: ${attachmentId}`);
    } catch (error) {
      this.fastify.log.error(`Failed to delete file: ${error}`);
      throw new ApiError(500, 'Failed to delete file', ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get a signed URL for temporary access to a private file
   */
  async getSignedUrl(attachmentId: string, expiry = 3600): Promise<string> {
    const attachmentRecord = await db.query.attachment.findFirst({
      where: eq(attachment.id, attachmentId),
    });

    if (!attachmentRecord) {
      throw new ApiError(404, 'Attachment not found', ErrorCode.NOT_FOUND);
    }

    // Extract storage key from URL
    const urlParts = attachmentRecord.url.split('/');
    const storageKey = urlParts.slice(-3).join('/');

    try {
      const signedUrl = await this.fastify.minio.presignedGetObject(
        this.fastify.config.minio.bucketName,
        storageKey,
        expiry,
      );

      return signedUrl;
    } catch (error) {
      this.fastify.log.error(`Failed to generate signed URL: ${error}`);
      throw new ApiError(500, 'Failed to generate signed URL', ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * List attachments for an entity
   */
  async listAttachments(entityType: string, entityId: string) {
    const attachments = await db.query.attachment.findMany({
      where: (attachment, { and, eq }) =>
        and(
          eq(attachment.entityType, entityType as any),
          eq(attachment.entityId, entityId),
          eq(attachment.isActive, true),
        ),
      with: {
        uploader: {
          columns: {
            id: true,
            email: true,
            displayName: true,
          },
        },
      },
      orderBy: (attachment, { desc }) => [desc(attachment.uploadedAt)],
    });

    // Enrich with entity data
    return await this.enrichMultipleWithEntityObjects(attachments);
  }

  /**
   * Enriches an attachment with its entity object
   */
  async enrichWithEntityObject(attachment: any): Promise<any> {
    let entityObject = null;

    switch (attachment.entityType) {
      case 'organization':
        entityObject = await db.query.organization.findFirst({
          where: eq(organization.id, attachment.entityId),
          columns: {
            id: true,
            name: true,
            slug: true,
            description: true,
            logoUrl: true,
          },
        });
        break;
      case 'workspace':
        entityObject = await db.query.workspace.findFirst({
          where: eq(workspace.id, attachment.entityId),
          columns: {
            id: true,
            name: true,
            slug: true,
            description: true,
            icon: true,
          },
        });
        break;
      case 'project':
        entityObject = await db.query.project.findFirst({
          where: eq(project.id, attachment.entityId),
          columns: {
            id: true,
            name: true,
            key: true,
            description: true,
            icon: true,
          },
        });
        break;
      case 'work_item':
        entityObject = await db.query.workItem.findFirst({
          where: eq(workItem.id, attachment.entityId),
          columns: {
            id: true,
            title: true,
            description: true,
            statusId: true,
          },
        });
        break;
      // Comment, test_case, and test_run tables don't exist yet
      // They can be added when those features are implemented
      case 'comment':
      case 'test_case':
      case 'test_run':
        // These entity types are defined in the enum but tables don't exist yet
        entityObject = null;
        break;
      default:
        // Unknown entity type - log warning but don't fail
        entityObject = null;
        break;
    }

    return {
      ...attachment,
      organization: attachment.entityType === 'organization' ? entityObject : null,
      workspace: attachment.entityType === 'workspace' ? entityObject : null,
      project: attachment.entityType === 'project' ? entityObject : null,
      workItem: attachment.entityType === 'work_item' ? entityObject : null,
    };
  }

  /**
   * Enriches multiple attachments with their entity objects
   */
  async enrichMultipleWithEntityObjects(attachments: any[]): Promise<any[]> {
    // Group attachments by entityType for efficient batch loading
    const groupedByType = attachments.reduce<Record<string, string[]>>((acc, att) => {
      const type = att.entityType as string;
      if (!acc[type]) {
        acc[type] = [];
      }
      acc[type].push(att.entityId as string);
      return acc;
    }, {});

    // Batch load all entity objects
    const entityMaps: Record<string, Map<string, any>> = {};

    await Promise.all(
      Object.entries(groupedByType).map(async ([type, ids]) => {
        const uniqueIds = Array.from(new Set(ids));
        let entities: any[] = [];

        switch (type) {
          case 'organization':
            if (uniqueIds.length > 0) {
              entities = await db.query.organization.findMany({
                where: (org, { inArray }) => inArray(org.id, uniqueIds),
                columns: {
                  id: true,
                  name: true,
                  slug: true,
                  description: true,
                  logoUrl: true,
                },
              });
            }
            break;
          case 'workspace':
            if (uniqueIds.length > 0) {
              entities = await db.query.workspace.findMany({
                where: (ws, { inArray }) => inArray(ws.id, uniqueIds),
                columns: {
                  id: true,
                  name: true,
                  slug: true,
                  description: true,
                  icon: true,
                },
              });
            }
            break;
          case 'project':
            if (uniqueIds.length > 0) {
              entities = await db.query.project.findMany({
                where: (proj, { inArray }) => inArray(proj.id, uniqueIds),
                columns: {
                  id: true,
                  name: true,
                  key: true,
                  description: true,
                  icon: true,
                },
              });
            }
            break;
          case 'work_item':
            if (uniqueIds.length > 0) {
              entities = await db.query.workItem.findMany({
                where: (wi, { inArray }) => inArray(wi.id, uniqueIds),
                columns: {
                  id: true,
                  title: true,
                  description: true,
                  statusId: true,
                },
              });
            }
            break;
          // Comment, test_case, and test_run tables don't exist yet
          case 'comment':
          case 'test_case':
          case 'test_run':
            // These entity types are defined in the enum but tables don't exist yet
            entities = [];
            break;
          default:
            // Unknown entity type - log warning but don't fail
            entities = [];
            break;
        }

        entityMaps[type] = new Map(entities.map((e: any) => [e.id, e]));
      }),
    );

    // Enrich attachments
    return attachments.map((attachment) => {
      const entityMap = entityMaps[attachment.entityType];
      const entityObject = entityMap?.get(attachment.entityId) || null;

      return {
        ...attachment,
        organization: attachment.entityType === 'organization' ? entityObject : null,
        workspace: attachment.entityType === 'workspace' ? entityObject : null,
        project: attachment.entityType === 'project' ? entityObject : null,
        workItem: attachment.entityType === 'work_item' ? entityObject : null,
      };
    });
  }
}
