import { db } from '@repo/db';
import {
  auditLog,
  organization,
  priority,
  project,
  role,
  sprint,
  status,
  user,
  workflow,
  workItem,
  workspace,
} from '@repo/db/schema';
import { eq } from 'drizzle-orm';
import { BaseService } from './base-service';

// Define types based on Drizzle inference
type AuditLog = typeof auditLog.$inferSelect;
type AuditLogInsert = typeof auditLog.$inferInsert;
type AuditLogUpdate = Partial<AuditLogInsert>;

// Define the enriched response type
export interface EnrichedAuditLog extends AuditLog {
  user?: {
    id: string;
    email: string;
    displayName: string | null;
  } | null;
  entity?: any;
  relatedEntity?: any;
}

export class AuditLogService extends BaseService<AuditLog, AuditLogInsert, AuditLogUpdate> {
  constructor() {
    super(db, auditLog, db.query.auditLog, 'AuditLog');
  }

  // Implement required abstract methods
  async validateUpdate(_id: string, _data: AuditLogUpdate, _context: any): Promise<void> {
    // Audit logs should be immutable - no updates allowed
    throw new Error('Audit logs cannot be updated');
  }

  async beforeCreate(data: AuditLogInsert, _context: any): Promise<AuditLogInsert> {
    return data;
  }

  async afterCreate(_entity: AuditLog, _context: any): Promise<void> {
    // Perform any post-creation tasks if needed
  }

  async beforeUpdate(_id: string, _data: AuditLogUpdate, _context: any): Promise<AuditLogUpdate> {
    // Should never reach here due to validateUpdate
    throw new Error('Audit logs cannot be updated');
  }

  async afterUpdate(_entity: AuditLog, _context: any): Promise<void> {
    // Should never reach here
  }

  /**
   * Validates the creation of an audit log
   */
  async validateCreate(_data: AuditLogInsert, _context: any): Promise<void> {
    // Could add validation for entity existence if needed
  }

  /**
   * Get entity data based on entityType and entityId
   */
  private async getEntityData(entityType: string, entityId: string): Promise<any> {
    switch (entityType.toLowerCase()) {
      case 'organization':
        return await db.query.organization.findFirst({
          where: eq(organization.id, entityId),
          columns: {
            id: true,
            name: true,
            slug: true,
            description: true,
            logoUrl: true,
          },
        });
      case 'workspace':
        return await db.query.workspace.findFirst({
          where: eq(workspace.id, entityId),
          columns: {
            id: true,
            name: true,
            slug: true,
            description: true,
            icon: true,
          },
        });
      case 'project':
        return await db.query.project.findFirst({
          where: eq(project.id, entityId),
          columns: {
            id: true,
            name: true,
            key: true,
            description: true,
            icon: true,
          },
        });
      case 'work_item':
      case 'workitem':
        return await db.query.workItem.findFirst({
          where: eq(workItem.id, entityId),
          columns: {
            id: true,
            title: true,
            description: true,
            statusId: true,
          },
        });
      case 'user':
        return await db.query.user.findFirst({
          where: eq(user.id, entityId),
          columns: {
            id: true,
            email: true,
            displayName: true,
          },
        });
      case 'role':
        return await db.query.role.findFirst({
          where: eq(role.id, entityId),
          columns: {
            id: true,
            name: true,
            identifier: true,
            description: true,
          },
        });
      case 'sprint':
        return await db.query.sprint.findFirst({
          where: eq(sprint.id, entityId),
          columns: {
            id: true,
            name: true,
            goal: true,
            startDate: true,
            endDate: true,
          },
        });
      case 'workflow':
        return await db.query.workflow.findFirst({
          where: eq(workflow.id, entityId),
          columns: {
            id: true,
            name: true,
            description: true,
            isDefault: true,
          },
        });
      case 'status':
        return await db.query.status.findFirst({
          where: eq(status.id, entityId),
          columns: {
            id: true,
            name: true,
            statusCategoryId: true,
            color: true,
          },
        });
      case 'priority':
        return await db.query.priority.findFirst({
          where: eq(priority.id, entityId),
          columns: {
            id: true,
            name: true,
            level: true,
            color: true,
          },
        });
      default:
        return null;
    }
  }

  /**
   * Enriches an audit log with its entity objects
   */
  async enrichWithEntityObjects(log: AuditLog): Promise<any> {
    const entityData = await this.getEntityData(log.entityType, log.entityId);

    // Map entity data to the appropriate field based on entityType
    const enrichedLog: any = {
      ...log,
      organization: null,
      workspace: null,
      project: null,
      workItem: null,
      role: null,
      sprint: null,
      workflow: null,
      status: null,
      priority: null,
    };

    // Set the appropriate field based on entityType
    switch (log.entityType.toLowerCase()) {
      case 'organization':
        enrichedLog.organization = entityData;
        break;
      case 'workspace':
        enrichedLog.workspace = entityData;
        break;
      case 'project':
        enrichedLog.project = entityData;
        break;
      case 'work_item':
      case 'workitem':
        enrichedLog.workItem = entityData;
        break;
      case 'user':
        // User is already included via the user relation
        break;
      case 'role':
        enrichedLog.role = entityData;
        break;
      case 'sprint':
        enrichedLog.sprint = entityData;
        break;
      case 'workflow':
        enrichedLog.workflow = entityData;
        break;
      case 'status':
        enrichedLog.status = entityData;
        break;
      case 'priority':
        enrichedLog.priority = entityData;
        break;
      default:
        // Unknown entity type - no enrichment
        break;
    }

    // Handle related entity if present
    if (log.relatedEntityType && log.relatedEntityId) {
      const _relatedData = await this.getEntityData(log.relatedEntityType, log.relatedEntityId);

      // You might want to add related entity fields too, e.g., relatedOrganization, relatedWorkspace, etc.
      // For now, we'll keep it simple and only enrich the main entity
    }

    return enrichedLog;
  }

  /**
   * Enriches multiple audit logs with their entity objects
   */
  async enrichMultipleWithEntityObjects(logs: AuditLog[]): Promise<any[]> {
    // Group logs by entityType for efficient batch loading
    const entityGroups = logs.reduce(
      (acc, log) => {
        const key = `${log.entityType}:${log.entityId}`;
        if (!acc[key]) {
          acc[key] = { type: log.entityType, id: log.entityId };
        }
        return acc;
      },
      {} as Record<string, { type: string; id: string }>,
    );

    // Fetch all entities in parallel
    const entityEntries = await Promise.all(
      Object.entries(entityGroups).map(async ([key, { type, id }]) => {
        const data = await this.getEntityData(type, id);
        return [key, data];
      }),
    );

    // Create lookup map
    const entityMap = new Map(entityEntries as [string, any][]);

    // Enrich logs
    return logs.map((log) => {
      const entityKey = `${log.entityType}:${log.entityId}`;
      const entityData = entityMap.get(entityKey) || null;

      // Create enriched log with all entity fields
      const enrichedLog: any = {
        ...log,
        organization: null,
        workspace: null,
        project: null,
        workItem: null,
        role: null,
        sprint: null,
        workflow: null,
        status: null,
        priority: null,
      };

      // Set the appropriate field based on entityType
      if (entityData) {
        switch (log.entityType.toLowerCase()) {
          case 'organization':
            enrichedLog.organization = entityData;
            break;
          case 'workspace':
            enrichedLog.workspace = entityData;
            break;
          case 'project':
            enrichedLog.project = entityData;
            break;
          case 'work_item':
          case 'workitem':
            enrichedLog.workItem = entityData;
            break;
          case 'role':
            enrichedLog.role = entityData;
            break;
          case 'sprint':
            enrichedLog.sprint = entityData;
            break;
          case 'workflow':
            enrichedLog.workflow = entityData;
            break;
          case 'status':
            enrichedLog.status = entityData;
            break;
          case 'priority':
            enrichedLog.priority = entityData;
            break;
          default:
            // Unknown entity type - no enrichment
            break;
        }
      }

      return enrichedLog;
    });
  }
}

export const auditLogService = new AuditLogService();
