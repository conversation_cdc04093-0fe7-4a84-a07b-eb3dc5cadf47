import { db } from '@repo/db';
import { type CreateNotification, notification, type PatchNotification } from '@repo/db/schema';
import { and, eq } from 'drizzle-orm';
import { ApiError } from '../errors/api-error';
import { BaseService } from './base-service';

export class NotificationService extends BaseService<typeof notification> {
  constructor() {
    super(notification);
  }

  async validateCreate(data: CreateNotification) {
    if (!data.userId) {
      throw new ApiError('User ID is required', 400, 'USER_ID_REQUIRED');
    }

    if (!(data.title && data.body)) {
      throw new ApiError('Title and body are required', 400, 'TITLE_BODY_REQUIRED');
    }

    if (!data.type) {
      throw new ApiError('Notification type is required', 400, 'TYPE_REQUIRED');
    }

    return data;
  }

  async validateUpdate(id: string, data: PatchNotification) {
    // Ensure the notification exists
    const existing = await db.select().from(notification).where(eq(notification.id, id)).limit(1);

    if (existing.length === 0) {
      throw new ApiError('Notification not found', 404, 'NOTIFICATION_NOT_FOUND');
    }

    // If marking as read, set readAt timestamp
    if (data.isRead === true && !data.readAt) {
      data.readAt = new Date().toISOString();
    }

    return data;
  }

  async validateDelete(id: string) {
    // Ensure the notification exists
    const existing = await db.select().from(notification).where(eq(notification.id, id)).limit(1);

    if (existing.length === 0) {
      throw new ApiError('Notification not found', 404, 'NOTIFICATION_NOT_FOUND');
    }

    return true;
  }

  async getUserNotifications(
    userId: string,
    options?: {
      unreadOnly?: boolean;
      limit?: number;
      offset?: number;
    },
  ) {
    let query = db.select().from(notification).where(eq(notification.userId, userId));

    if (options?.unreadOnly) {
      query = query.where(eq(notification.isRead, false));
    }

    if (options?.limit) {
      query = query.limit(options.limit);
    }

    if (options?.offset) {
      query = query.offset(options.offset);
    }

    return await query.orderBy(notification.createdAt);
  }

  async markAsRead(id: string) {
    return await db
      .update(notification)
      .set({
        isRead: true,
        readAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(notification.id, id));
  }

  async markAllAsRead(userId: string) {
    return await db
      .update(notification)
      .set({
        isRead: true,
        readAt: new Date(),
        updatedAt: new Date(),
      })
      .where(and(eq(notification.userId, userId), eq(notification.isRead, false)));
  }

  async getUnreadCount(userId: string): Promise<number> {
    const result = await db
      .select({ count: notification.id })
      .from(notification)
      .where(and(eq(notification.userId, userId), eq(notification.isRead, false)));

    return result.length;
  }
}
