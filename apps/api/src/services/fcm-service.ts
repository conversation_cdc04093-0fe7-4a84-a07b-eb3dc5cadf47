import { db } from '@repo/db';
import { notification, userFcmToken } from '@repo/db/schema';
import { and, eq } from 'drizzle-orm';
import { ApiError } from '../errors/api-error';
import { isFCMConfigured, sendPushNotification } from '../utils/fcm';

export interface NotificationPayload {
  title: string;
  body: string;
  data?: Record<string, string>;
  type: string;
}

export interface WorkItemNotificationData {
  workItemId: string;
  workItemTitle: string;
  workItemTicketId: string;
  projectId: string;
  action: 'assigned' | 'updated' | 'status_changed' | 'priority_changed';
  actorName?: string;
  changes?: Record<string, any>;
}

export interface CommentMentionData {
  commentId: string;
  workItemId: string;
  workItemTitle: string;
  workItemTicketId: string;
  projectId: string;
  mentionedBy: string;
  commentText: string;
}

export class FcmService {
  async sendToUser(userId: string, payload: NotificationPayload) {
    try {
      // Always save notification to database (in-app notification)
      await db.insert(notification).values({
        userId,
        title: payload.title,
        body: payload.body,
        type: payload.type,
        data: payload.data,
        isRead: false,
      });

      // Only send push notification if FCM is configured
      if (!isFCMConfigured()) {
        return;
      }

      // Get user's FCM tokens
      const fcmTokens = await db.select().from(userFcmToken).where(eq(userFcmToken.userId, userId));

      if (fcmTokens.length === 0) {
        return;
      }

      // Send push notification
      const tokens = fcmTokens.map((token) => token.fcmToken);
      await sendPushNotification({
        tokens,
        title: payload.title,
        body: payload.body,
        data: payload.data,
        id: payload.type,
      });

      // Update last active time for tokens
      for (const token of fcmTokens) {
        await db
          .update(userFcmToken)
          .set({ lastActive: new Date() })
          .where(eq(userFcmToken.id, token.id));
      }
    } catch (_error) {
      throw new ApiError(500, 'Failed to send notification');
    }
  }

  async sendWorkItemNotification(userId: string, data: WorkItemNotificationData) {
    let title: string;
    let body: string;
    switch (data.action) {
      case 'assigned':
        title = 'Work Item Assigned';
        body = `You have been assigned to ${data.workItemTicketId}`;
        break;
      case 'status_changed':
        title = 'Work Item Status Changed';
        body = `Status of ${data.workItemTicketId} has been updated`;
        break;
      case 'priority_changed':
        title = 'Work Item Priority Changed';
        body = `Priority of ${data.workItemTicketId} has been updated`;
        break;
      default:
        title = 'Work Item Updated';
        body = `${data.workItemTicketId} has been updated`;
    }

    if (data.actorName) {
      body += ` by ${data.actorName}`;
    }

    await this.sendToUser(userId, {
      title,
      body,
      type: 'work_item_update',
      data: {
        workItemId: data.workItemId,
        projectId: data.projectId,
        action: data.action,
        ...data.changes,
      },
    });
  }

  async sendMentionNotification(userId: string, data: CommentMentionData) {
    const title = 'You were mentioned in a comment';
    const body = `${data.mentionedBy} mentioned you in ${data.workItemTicketId} comment`;

    await this.sendToUser(userId, {
      title,
      body,
      type: 'comment_mention',
      data: {
        commentId: data.commentId,
        workItemId: data.workItemId,
        workItemTicketId: data.workItemTicketId,
        projectId: data.projectId,
        mentionedBy: data.mentionedBy,
      },
    });
  }

  async sendLogoutNotification(fcmToken: string, userId: string) {
    if (!isFCMConfigured()) {
      return;
    }

    try {
      // Send logout notification to specific device
      await sendPushNotification({
        tokens: [fcmToken],
        title: 'Session Ended',
        body: 'You have been logged out from this device',
        data: {
          action: 'logout',
          userId,
        },
        id: 'logout',
      });

      // Remove the FCM token from database
      await db
        .delete(userFcmToken)
        .where(and(eq(userFcmToken.userId, userId), eq(userFcmToken.fcmToken, fcmToken)));
    } catch (_error) {
      // Don't throw error for logout, just log it
    }
  }

  async registerToken(
    userId: string,
    fcmToken: string,
    deviceInfo?: {
      deviceType?: string;
      deviceModel?: string;
      os?: string;
      browser?: string;
    },
  ) {
    try {
      // Check if token already exists
      const existing = await db
        .select()
        .from(userFcmToken)
        .where(and(eq(userFcmToken.userId, userId), eq(userFcmToken.fcmToken, fcmToken)))
        .limit(1);

      if (existing.length > 0) {
        // Update existing token
        await db
          .update(userFcmToken)
          .set({
            ...deviceInfo,
            lastActive: new Date(),
            updatedAt: new Date(),
          })
          .where(eq(userFcmToken.id, existing[0].id));
      } else {
        // Insert new token
        await db.insert(userFcmToken).values({
          userId,
          fcmToken,
          ...deviceInfo,
          lastActive: new Date(),
        });
      }
    } catch (_error) {
      throw new ApiError(500, 'Failed to register FCM token');
    }
  }

  async removeToken(userId: string, fcmToken: string) {
    try {
      await db
        .delete(userFcmToken)
        .where(and(eq(userFcmToken.userId, userId), eq(userFcmToken.fcmToken, fcmToken)));
    } catch (_error) {
      throw new ApiError(500, 'Failed to remove FCM token');
    }
  }

  async removeAllUserTokens(userId: string) {
    try {
      await db.delete(userFcmToken).where(eq(userFcmToken.userId, userId));
    } catch (_error) {
      throw new ApiError(500, 'Failed to remove user FCM tokens');
    }
  }
}

export const fcmService = new FcmService();
