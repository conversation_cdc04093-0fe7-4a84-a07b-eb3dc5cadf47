// Base service class for business logic separation
import { eq } from '@repo/db/orm';
import { ApiError } from '../errors/api-error';

export interface ServiceContext {
  userId?: string;
  organizationId?: string;
  permissions?: string[];
  correlationId?: string;
  with?: Record<string, any>;
}

export interface PaginationOptions {
  page: number;
  limit: number;
  filters?: Record<string, any>;
  sort?: Array<{ field: string; order: 'asc' | 'desc' }>;
}

export interface ServiceResult<T> {
  data: T;
  meta?: {
    totalItems?: number;
    itemCount?: number;
    itemsPerPage?: number;
    totalPages?: number;
    currentPage?: number;
  };
}

export abstract class BaseService<TEntity, TInsert, TUpdate> {
  constructor(
    protected db: any,
    protected table: any,
    protected queryApi: any,
    protected entityName: string,
  ) {}

  // Abstract methods that must be implemented by child services
  abstract validateCreate(data: TInsert, context: ServiceContext): Promise<void>;
  abstract validateUpdate(id: string, data: TUpdate, context: ServiceContext): Promise<void>;

  // Optional lifecycle hooks
  beforeCreate?(data: TInsert, context: ServiceContext): Promise<TInsert>;
  afterCreate?(entity: TEntity, context: ServiceContext): Promise<void>;
  beforeUpdate?(id: string, data: TUpdate, context: ServiceContext): Promise<TUpdate>;
  afterUpdate?(entity: TEntity, context: ServiceContext): Promise<void>;

  // Common CRUD operations with business logic hooks
  async findMany(
    options: PaginationOptions,
    context: ServiceContext,
  ): Promise<ServiceResult<TEntity[]>> {
    const { page, limit, filters = {}, sort = [{ field: 'createdAt', order: 'desc' }] } = options;

    // Apply access control filters
    const accessFilters = await this.applyAccessControl(filters, context);

    const items = await this.queryApi.findMany({
      where: this.buildWhere(accessFilters),
      limit,
      offset: (page - 1) * limit,
      orderBy: this.buildOrderBy(sort),
    });

    const totalItems = await this.countWithFilters(accessFilters);

    return {
      data: items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  async findById(id: string, context: ServiceContext): Promise<TEntity> {
    const entity = await this.queryApi.findFirst({
      where: eq(this.table.id, id),
    });

    if (!entity) {
      throw ApiError.notFound(this.entityName, id);
    }

    // Check access permissions
    await this.checkReadAccess(entity, context);

    return entity;
  }

  async create(data: TInsert, context: ServiceContext): Promise<TEntity> {
    // Validate business rules
    await this.validateCreate(data, context);

    // Pre-processing hook
    let processedData = data;
    if (this.beforeCreate) {
      processedData = await this.beforeCreate(data, context);
    }

    // Add audit fields
    const auditedData = {
      ...processedData,
      // Only set createdBy if not already provided
      createdBy: (processedData as any).createdBy || context.userId,
      createdAt: new Date(),
    } as any;

    try {
      const [created] = await this.db.insert(this.table).values(auditedData).returning();

      const entity = await this.findById(created.id, context);

      // Post-processing hook
      if (this.afterCreate) {
        await this.afterCreate(entity, context);
      }

      return entity;
    } catch (error: any) {
      // If it's already an ApiError, re-throw it as-is
      if (error instanceof ApiError) {
        throw error;
      }
      // Only wrap unknown errors as database errors
      throw this.handleDatabaseError(error, 'create');
    }
  }

  async update(id: string, data: TUpdate, context: ServiceContext): Promise<TEntity> {
    // Check if entity exists and user has access
    const existing = await this.findById(id, context);
    await this.checkWriteAccess(existing, context);

    // Validate business rules
    await this.validateUpdate(id, data, context);

    // Pre-processing hook
    let processedData = data;
    if (this.beforeUpdate) {
      processedData = await this.beforeUpdate(id, data, context);
    }

    // Add audit fields
    const auditedData = {
      ...processedData,
      updatedBy: context.userId,
      updatedAt: new Date(),
    } as any;

    try {
      await this.db.update(this.table).set(auditedData).where(eq(this.table.id, id));

      const entity = await this.findById(id, context);

      // Post-processing hook
      if (this.afterUpdate) {
        await this.afterUpdate(entity, context);
      }

      return entity;
    } catch (error: any) {
      // If it's already an ApiError, re-throw it as-is
      if (error instanceof ApiError) {
        throw error;
      }
      // Only wrap unknown errors as database errors
      throw this.handleDatabaseError(error, 'update');
    }
  }

  async delete(id: string, context: ServiceContext): Promise<void> {
    // Check if entity exists and user has access
    const existing = await this.findById(id, context);
    await this.checkDeleteAccess(existing, context);

    try {
      await this.db.delete(this.table).where(eq(this.table.id, id));
    } catch (error: any) {
      // If it's already an ApiError, re-throw it as-is
      if (error instanceof ApiError) {
        throw error;
      }
      // Only wrap unknown errors as database errors
      throw this.handleDatabaseError(error, 'delete');
    }
  }

  // Access control methods (can be overridden by child services)
  protected async applyAccessControl(
    filters: Record<string, any>,
    context: ServiceContext,
  ): Promise<Record<string, any>> {
    // Default implementation - add organizationId filter if available
    if (context.organizationId && !filters.organizationId) {
      filters.organizationId = context.organizationId;
    }
    return filters;
  }

  protected async checkReadAccess(_entity: TEntity, _context: ServiceContext): Promise<void> {
    // Override in child services for specific access control
  }

  protected async checkWriteAccess(_entity: TEntity, _context: ServiceContext): Promise<void> {
    // Override in child services for specific access control
  }

  protected async checkDeleteAccess(_entity: TEntity, _context: ServiceContext): Promise<void> {
    // Override in child services for specific access control
  }

  // Helper methods
  private buildWhere(_filters: Record<string, any>) {
    // Implementation similar to CRUD router
    // This would be extracted to a shared utility
    return; // Placeholder
  }

  private buildOrderBy(_sort: Array<{ field: string; order: 'asc' | 'desc' }>) {
    // Implementation similar to CRUD router
    // This would be extracted to a shared utility
    return []; // Placeholder
  }

  private async countWithFilters(filters: Record<string, any>): Promise<number> {
    const [result] = await this.db
      .select({ count: 'count(*)' })
      .from(this.table)
      .where(this.buildWhere(filters));
    return result?.count || 0;
  }

  private handleDatabaseError(error: any, operation: string): ApiError {
    // Handle database-specific errors
    switch (error.code) {
      case '23505': {
        // Unique constraint violation
        const match = error.message.match(/Key \(([^)]+)\)=\(([^)]+)\)/);
        if (match) {
          return ApiError.duplicate(match[1], match[2]);
        }
        return ApiError.duplicate('field', 'value');
      }

      case '23503': // Foreign key violation
        return ApiError.badRequest('Referenced resource not found');

      default:
        return ApiError.database(operation, error);
    }
  }
}
