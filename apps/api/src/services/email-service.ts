import fs from 'node:fs/promises';
import path from 'node:path';
import nodemailer from 'nodemailer';
import env from '../config/env';

export interface EmailOptions {
  to: string;
  subject: string;
  html?: string;
  text?: string;
  template?: string;
  variables?: Record<string, string>;
}

export class EmailService {
  private transporter: nodemailer.Transporter;
  private templateCache: Map<string, string> = new Map();

  constructor() {
    this.transporter = nodemailer.createTransport({
      host: env.SMTP_HOST,
      port: env.SMTP_PORT,
      secure: env.SMTP_SECURE,
      auth: {
        user: env.SMTP_USER,
        pass: env.SMTP_PASS,
      },
    });
  }

  async loadTemplate(templateName: string): Promise<string> {
    if (this.templateCache.has(templateName)) {
      return this.templateCache.get(templateName)!;
    }

    const templatePath = path.join(process.cwd(), 'templates/email', `${templateName}.html`);

    const template = await fs.readFile(templatePath, 'utf-8');
    this.templateCache.set(templateName, template);
    return template;
  }

  replaceVariables(template: string, variables: Record<string, string>): string {
    let result = template;
    for (const [key, value] of Object.entries(variables)) {
      result = result.replace(new RegExp(`{{${key}}}`, 'g'), value);
    }
    return result;
  }

  async sendEmail(options: EmailOptions): Promise<void> {
    try {
      let html = options.html;

      if (options.template) {
        const template = await this.loadTemplate(options.template);
        html = this.replaceVariables(template, options.variables || {});
      }

      const mailOptions = {
        from: env.EMAIL_FROM,
        to: options.to,
        subject: options.subject,
        html,
        text: options.text,
      };

      await this.transporter.sendMail(mailOptions);
    } catch (_error) {
      throw new Error('Failed to send email');
    }
  }

  async sendWelcomeEmail(email: string, firstName: string): Promise<void> {
    await this.sendEmail({
      to: email,
      subject: 'Welcome to Spark!',
      template: 'welcome',
      variables: {
        firstName,
        appUrl: env.FRONT_END_URL,
      },
    });
  }

  async sendPasswordResetEmail(
    email: string,
    firstName: string,
    resetToken: string,
  ): Promise<void> {
    const resetUrl = `${env.FRONT_END_URL}/reset/${resetToken}`;

    await this.sendEmail({
      to: email,
      subject: 'Reset Your Password',
      template: 'password-reset',
      variables: {
        firstName,
        resetUrl,
        appUrl: env.FRONT_END_URL,
      },
    });
  }

  async sendEmailVerificationEmail(
    email: string,
    firstName: string,
    verificationToken: string,
  ): Promise<void> {
    const verifyUrl = `${env.FRONT_END_URL}/verifyEmail/${verificationToken}`;

    await this.sendEmail({
      to: email,
      subject: 'Verify Your Email',
      template: 'email-verification',
      variables: {
        firstName,
        verifyUrl,
        appUrl: env.FRONT_END_URL,
      },
    });
  }

  async sendInvitationEmail(
    email: string,
    inviterName: string,
    inviterEmail: string,
    organizationName: string,
    roleName: string,
    invitationToken: string,
    inviteeName?: string,
  ): Promise<void> {
    const invitationUrl = `${env.FRONT_END_URL}/invited/${invitationToken}`;

    await this.sendEmail({
      to: email,
      subject: `You've been invited to join ${organizationName}`,
      template: 'invitation',
      variables: {
        inviteeName: inviteeName || 'there',
        inviterName,
        inviterEmail,
        organizationName,
        user_role: roleName,
        invitationUrl,
        appUrl: env.FRONT_END_URL,
      },
    });
  }

  async verifyConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      return true;
    } catch (_error) {
      return false;
    }
  }
}

export const emailService = new EmailService();
