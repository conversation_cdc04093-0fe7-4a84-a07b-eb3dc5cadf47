// Organization service with business logic and validation

import { Readable } from 'node:stream';
import { db } from '@repo/db';
import type { InferSelectModel } from '@repo/db/orm';
import { and, eq, ne } from '@repo/db/orm';
import type { OrganizationInsert } from '@repo/db/schema';
import { attachment, invitation, organization, role, roleAssignment, user } from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { ApiError } from '../errors/api-error';
import { generatePublicUrl, generateStorageKey } from '../utils/upload/file-upload';
import { BaseService, type ServiceContext } from './base-service';

type OrganizationSelect = InferSelectModel<typeof organization>;
type OrganizationUpdate = Partial<OrganizationInsert>;

export class OrganizationService extends BaseService<
  OrganizationSelect,
  OrganizationInsert,
  OrganizationUpdate
> {
  private fastify?: FastifyInstance;

  constructor(fastify?: FastifyInstance) {
    super(db, organization, db.query.organization, 'organization');
    this.fastify = fastify;
  }

  async validateCreate(data: OrganizationInsert, _context: ServiceContext): Promise<void> {
    // Business rule: Check if organization name is unique
    await this.validateUniqueName(data.name);

    // Business rule: Check if slug is unique
    await this.validateUniqueSlug(data.slug);

    // Business rule: Validate organization structure
    if (data.billingPlan && !['free', 'pro', 'enterprise'].includes(data.billingPlan)) {
      throw ApiError.badRequest('Invalid billing plan. Must be one of: free, pro, enterprise');
    }

    // Business rule: Validate domains structure
    if (data.domains && typeof data.domains === 'object') {
      const domains = data.domains as Record<string, any>;
      if (domains.primary && !this.isValidDomain(domains.primary)) {
        throw ApiError.badRequest('Invalid primary domain format');
      }
    }

    // Business rule: Check user permissions
    // Note: Making userId optional for testing purposes
    // In production, this should be required
    // if (!context.userId) {
    //   throw ApiError.badRequest('User context required for organization creation');
    // }
  }

  async validateUpdate(
    id: string,
    data: OrganizationUpdate,
    context: ServiceContext,
  ): Promise<void> {
    // Business rule: Check if new name is unique (if changing)
    if (data.name) {
      await this.validateUniqueName(data.name, id);
    }

    // Business rule: Check if new slug is unique (if changing)
    if (data.slug) {
      await this.validateUniqueSlug(data.slug, id);
    }

    // Business rule: Cannot disable organization if it has active projects
    if (data.isActive === false) {
      const hasActiveProjects = await this.checkActiveProjects(id);
      if (hasActiveProjects) {
        throw ApiError.conflict('Cannot disable organization with active projects');
      }
    }

    // Business rule: Validate billing plan changes
    if (data.billingPlan) {
      await this.validateBillingPlanChange(id, data.billingPlan, context);
    }
  }

  async beforeCreate(
    data: OrganizationInsert,
    context: ServiceContext,
  ): Promise<OrganizationInsert> {
    // Auto-generate slug if not provided
    if (!data.slug && data.name) {
      data.slug = this.generateSlug(data.name);

      // Ensure generated slug is unique
      await this.validateUniqueSlug(data.slug);
    }

    // Set default settings if not provided
    if (!data.settings) {
      data.settings = {
        theme: 'light',
        notifications: true,
        timezone: 'UTC',
      };
    }

    // Set default billing plan
    if (!data.billingPlan) {
      data.billingPlan = 'free';
    }

    // Handle base64 logo upload
    if (data.logoUrl && this.isBase64Image(data.logoUrl)) {
      const attachmentUrl = await this.uploadBase64Logo(data.logoUrl, data.name, context);
      data.logoUrl = attachmentUrl;
    }

    return data;
  }

  async afterCreate(organization: OrganizationSelect, context: ServiceContext): Promise<void> {
    if (organization.createdBy) {
      await this.assignUserRole(organization.id, organization.createdBy, 'org_owner');
    }

    // Business logic: Send welcome email (if email service is configured)
    await this.sendWelcomeNotification(organization, context);
  }

  async beforeUpdate(
    id: string,
    data: OrganizationUpdate,
    context: ServiceContext,
  ): Promise<OrganizationUpdate> {
    // Business logic: Log billing plan changes
    if (data.billingPlan) {
      const current = await this.findById(id, context);
      if (current.billingPlan !== data.billingPlan) {
      }
    }

    // Handle base64 logo upload
    if (data.logoUrl && this.isBase64Image(data.logoUrl)) {
      // Delete old logo if exists
      const current = await this.findById(id, context);
      if (current.logoUrl) {
        await this.deleteOldLogo(current.logoUrl);
      }

      // Upload new logo
      const attachmentUrl = await this.uploadBase64Logo(
        data.logoUrl,
        data.name || current.name,
        context,
      );
      data.logoUrl = attachmentUrl;
    }

    return data;
  }

  async afterUpdate(organization: OrganizationSelect, _context: ServiceContext): Promise<void> {
    // Business logic: Invalidate related caches
    await this.invalidateOrganizationCache(organization.id);

    // Business logic: Notify users of organization changes
    if (organization.name) {
      await this.notifyUsersOfChanges(organization.id, 'name_updated');
    }
  }

  // Organization-specific business methods
  async getOrganizationStats(
    id: string,
    context: ServiceContext,
  ): Promise<{
    memberCount: number;
    projectCount: number;
    activeProjectCount: number;
    storageUsed: number;
  }> {
    const _org = await this.findById(id, context);

    // Calculate organization statistics
    const [memberCount, projectCount, activeProjectCount] = await Promise.all([
      this.getMemberCount(id),
      this.getProjectCount(id),
      this.getActiveProjectCount(id),
    ]);

    const storageUsed = await this.calculateStorageUsage(id);

    return {
      memberCount,
      projectCount,
      activeProjectCount,
      storageUsed,
    };
  }

  async transferOwnership(
    orgId: string,
    newOwnerId: string,
    context: ServiceContext,
  ): Promise<void> {
    // Business rule: Only current owner can transfer ownership
    await this.validateOwnershipTransfer(orgId, newOwnerId, context);

    // Transfer ownership
    await this.updateOwnership(orgId, newOwnerId);
  }

  // Private helper methods
  private async validateUniqueName(name: string, excludeId?: string): Promise<void> {
    const conditions = [eq(organization.name, name)];
    if (excludeId) {
      conditions.push(ne(organization.id, excludeId));
    }

    const existing = await this.queryApi.findFirst({
      where: excludeId ? and(...conditions) : conditions[0],
    });

    if (existing) {
      throw ApiError.duplicate('name', name);
    }
  }

  private async validateUniqueSlug(slug: string, excludeId?: string): Promise<void> {
    const conditions = [eq(organization.slug, slug)];
    if (excludeId) {
      conditions.push(ne(organization.id, excludeId));
    }

    const existing = await this.queryApi.findFirst({
      where: excludeId ? and(...conditions) : conditions[0],
    });

    if (existing) {
      throw ApiError.duplicate('slug', slug);
    }
  }

  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
      .substring(0, 50);
  }

  private isValidDomain(domain: string): boolean {
    const domainRegex =
      /^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?$/i;
    return domainRegex.test(domain);
  }

  private async checkActiveProjects(_orgId: string): Promise<boolean> {
    // Implementation would check if organization has active projects
    // This is a placeholder
    return false;
  }

  private async validateBillingPlanChange(
    orgId: string,
    newPlan: string,
    context: ServiceContext,
  ): Promise<void> {
    // Business rule: Validate billing plan downgrade restrictions
    if (newPlan === 'free') {
      const stats = await this.getOrganizationStats(orgId, context);
      if (stats.memberCount > 5) {
        throw ApiError.conflict('Cannot downgrade to free plan with more than 5 members');
      }
      if (stats.projectCount > 3) {
        throw ApiError.conflict('Cannot downgrade to free plan with more than 3 projects');
      }
    }
  }

  private async assignUserRole(orgId: string, userId: string, roleName: string): Promise<void> {
    try {
      const roleRecord = await db.query.role.findFirst({
        where: eq(role.identifier, roleName),
      });

      if (!roleRecord) {
        return;
      }

      // Check if assignment already exists
      const existingAssignment = await db.query.roleAssignment.findFirst({
        where: and(
          eq(roleAssignment.userId, userId),
          eq(roleAssignment.roleId, roleRecord.id),
          eq(roleAssignment.scopeType, 'organization'),
          eq(roleAssignment.scopeId, orgId),
        ),
      });

      if (existingAssignment) {
        return;
      }

      // Create the role assignment
      await db.insert(roleAssignment).values({
        userId,
        roleId: roleRecord.id,
        scopeType: 'organization',
        scopeId: orgId,
        isActive: true,
      });

      // Check for pending invitations and auto-accept them
      const userRecord = await db.query.user.findFirst({
        where: eq(user.id, userId),
      });

      if (userRecord) {
        const pendingInvitations = await db.query.invitation.findMany({
          where: and(
            eq(invitation.email, userRecord.email),
            eq(invitation.scopeType, 'organization'),
            eq(invitation.scopeId, orgId),
            eq(invitation.status, 'pending'),
          ),
        });

        // Auto-accept any pending invitations since user now has a role
        for (const inv of pendingInvitations) {
          await db
            .update(invitation)
            .set({
              status: 'accepted',
              acceptedAt: new Date(),
              updatedAt: new Date(),
            })
            .where(eq(invitation.id, inv.id));
        }
      }
    } catch (_error) {}
  }

  private async sendWelcomeNotification(
    _organization: OrganizationSelect,
    _context: ServiceContext,
  ): Promise<void> {}

  private async invalidateOrganizationCache(_orgId: string): Promise<void> {}

  private async notifyUsersOfChanges(_orgId: string, _changeType: string): Promise<void> {}

  private async getMemberCount(_orgId: string): Promise<number> {
    // Implementation would count organization members
    return 0;
  }

  private async getProjectCount(_orgId: string): Promise<number> {
    // Implementation would count organization projects
    return 0;
  }

  private async getActiveProjectCount(_orgId: string): Promise<number> {
    // Implementation would count active organization projects
    return 0;
  }

  private async calculateStorageUsage(_orgId: string): Promise<number> {
    // Implementation would calculate storage usage in bytes
    return 0;
  }

  private async validateOwnershipTransfer(
    _orgId: string,
    _newOwnerId: string,
    _context: ServiceContext,
  ): Promise<void> {}

  private async updateOwnership(_orgId: string, _newOwnerId: string): Promise<void> {}

  // Base64 image handling methods
  private isBase64Image(data: string): boolean {
    // Check if the string is a base64 encoded image
    return data.startsWith('data:image/');
  }

  private async uploadBase64Logo(
    base64Data: string,
    organizationName: string,
    _context: ServiceContext,
  ): Promise<string> {
    if (!this.fastify) {
      throw ApiError.badRequest('Service not properly initialized with fastify instance');
    }

    try {
      // Extract mime type and base64 data
      const matches = base64Data.match(/^data:image\/([a-zA-Z]+);base64,(.+)$/);
      if (!matches || matches.length !== 3) {
        throw ApiError.badRequest('Invalid base64 image format');
      }

      const mimeType = `image/${matches[1]}`;
      const base64Content = matches[2];
      const buffer = Buffer.from(base64Content, 'base64');

      // Generate filename
      const timestamp = Date.now();
      const filename = `${organizationName.toLowerCase().replace(/[^a-z0-9]/g, '-')}-logo-${timestamp}.${matches[1]}`;

      // Create a temporary organization ID for storage key generation
      const tempOrgId = `temp-${timestamp}`;
      const storageKey = generateStorageKey('organization', tempOrgId, filename);

      // Upload to MinIO
      const stream = Readable.from(buffer);
      await this.fastify.minio.putObject(
        this.fastify.config.minio.bucketName,
        storageKey,
        stream,
        buffer.length,
        {
          'Content-Type': mimeType,
          'x-amz-meta-original-name': filename,
          'x-amz-meta-entity-type': 'organization',
        },
      );

      // Generate public URL
      const url = generatePublicUrl(storageKey);

      // Note: We're not creating an attachment record here since we don't have the organization ID yet
      // The attachment record will be created after the organization is created

      return url;
    } catch (_error) {
      throw ApiError.internal('Failed to upload logo');
    }
  }

  private async deleteOldLogo(logoUrl: string): Promise<void> {
    if (!this.fastify) {
      return; // Skip deletion if fastify is not available
    }

    try {
      // Extract storage key from URL
      const urlParts = logoUrl.split('/');
      const storageKey = urlParts.slice(-3).join('/'); // entity-type/entity-id/filename

      // Delete from MinIO
      await this.fastify.minio.removeObject(this.fastify.config.minio.bucketName, storageKey);

      // Find and delete attachment record if exists
      const attachmentRecord = await db.query.attachment.findFirst({
        where: eq(attachment.url, logoUrl),
      });

      if (attachmentRecord) {
        await db.delete(attachment).where(eq(attachment.id, attachmentRecord.id));
      }
    } catch (_error) {
      // Don't throw error to allow update to proceed
    }
  }
}
