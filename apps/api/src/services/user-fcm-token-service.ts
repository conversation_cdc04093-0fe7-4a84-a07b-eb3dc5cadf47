import { db } from '@repo/db';
import { type CreateUserFcmToken, type Patch<PERSON>serFcmToken, userFcmToken } from '@repo/db/schema';
import { and, eq } from 'drizzle-orm';
import { ApiError } from '../errors/api-error';
import { BaseService, type ServiceContext } from './base-service';

export class UserFcmTokenService extends BaseService<
  typeof userFcmToken,
  CreateUserFcmToken,
  PatchUserFcmToken
> {
  constructor() {
    super(db, userFcmToken, db.query.userFcmToken, 'UserFcmToken');
  }

  async validateCreate(data: CreateUserFcmToken, context: ServiceContext): Promise<void> {
    if (!context.userId) {
      throw new ApiError('User ID is required', 400, 'USER_ID_REQUIRED');
    }

    if (!data.fcmToken) {
      throw new ApiError('FCM token is required', 400, 'FCM_TOKEN_REQUIRED');
    }
  }

  async beforeCreate(
    data: CreateUserFcmToken,
    context: ServiceContext,
  ): Promise<CreateUserFcmToken & { userId: string }> {
    if (!context.userId) {
      throw new ApiError('User ID is required', 400, 'USER_ID_REQUIRED');
    }

    // Check if the token already exists for this user and update it
    const existing = await db
      .select()
      .from(userFcmToken)
      .where(and(eq(userFcmToken.userId, context.userId), eq(userFcmToken.fcmToken, data.fcmToken)))
      .limit(1);

    if (existing.length > 0) {
      // Update existing token instead of creating new one
      await db
        .update(userFcmToken)
        .set({
          deviceType: data.deviceType,
          deviceModel: data.deviceModel,
          os: data.os,
          browser: data.browser,
          lastActive: new Date(),
          updatedAt: new Date(),
        })
        .where(eq(userFcmToken.id, existing[0].id));

      // Return the existing record (this will prevent creation of a new one)
      throw new ApiError('Token already exists and has been updated', 200, 'TOKEN_UPDATED');
    }

    // Add userId to the data for creation
    return { ...data, userId: context.userId };
  }

  async validateUpdate(
    id: string,
    data: PatchUserFcmToken,
    _context: ServiceContext,
  ): Promise<void> {
    // Ensure the token exists
    const existing = await db.select().from(userFcmToken).where(eq(userFcmToken.id, id)).limit(1);

    if (existing.length === 0) {
      throw new ApiError('FCM token not found', 404, 'FCM_TOKEN_NOT_FOUND');
    }

    return data;
  }

  async validateDelete(id: string) {
    // Ensure the token exists
    const existing = await db.select().from(userFcmToken).where(eq(userFcmToken.id, id)).limit(1);

    if (existing.length === 0) {
      throw new ApiError('FCM token not found', 404, 'FCM_TOKEN_NOT_FOUND');
    }

    return true;
  }

  async getUserTokens(userId: string) {
    return await db.select().from(userFcmToken).where(eq(userFcmToken.userId, userId));
  }

  async removeUserToken(userId: string, fcmToken: string) {
    return await db
      .delete(userFcmToken)
      .where(and(eq(userFcmToken.userId, userId), eq(userFcmToken.fcmToken, fcmToken)));
  }

  async removeAllUserTokens(userId: string) {
    return await db.delete(userFcmToken).where(eq(userFcmToken.userId, userId));
  }
}
