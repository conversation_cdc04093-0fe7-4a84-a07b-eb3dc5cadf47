import { db } from '@repo/db';
import {
  type TestCase,
  type TestCaseInsert,
  type TestCasePatch,
  type TestSuiteTestCaseInsert,
  testCase,
  testCaseHistory,
  testSuiteTestCase,
} from '@repo/db/schema';
import { and, eq, ilike, notInArray, or, sql } from 'drizzle-orm';
import { BaseService, type ServiceContext } from './base-service';

export class TestCaseService extends BaseService<TestCase, TestCaseInsert, TestCasePatch> {
  async validateCreate(_data: TestCaseInsert, _context: ServiceContext): Promise<void> {
    // Add any validation logic here if needed
    // For now, just return successfully
    return;
  }
  async validateUpdate(_id: string, _data: TestCasePatch, _context: ServiceContext): Promise<void> {
    // Add any validation logic here if needed
    // For now, just return successfully
    return;
  }
  constructor() {
    super(db, testCase, db.query.testCase, 'Test Case');
  }

  async beforeCreate(data: TestCaseInsert, _context: ServiceContext): Promise<TestCaseInsert> {
    // Just return the data as-is
    return data;
  }

  /**
   * Override beforeUpdate to track history
   */
  async beforeUpdate(id: string, data: TestCasePatch, context: any): Promise<TestCasePatch> {
    // Get the current test case before update
    const currentTestCase = await db.query.testCase.findFirst({
      where: eq(testCase.id, id),
    });

    if (!currentTestCase) {
      throw new Error('Test case not found');
    }

    // Store the current state in context for afterUpdate
    context.previousTestCase = currentTestCase;
    context.changedFields = this.getChangedFields(currentTestCase, data);

    return data;
  }

  /**
   * Override afterUpdate to create history entry
   */
  async afterUpdate(entity: TestCase, context: any): Promise<void> {
    if (context.previousTestCase && context.changedFields.length > 0 && context.userId) {
      // Create a history entry
      const changedFieldsObject: Record<string, { oldValue: any; newValue: any }> = {};
      for (const field of context.changedFields) {
        changedFieldsObject[field] = {
          oldValue: context.previousTestCase[field as keyof TestCase],
          newValue: entity[field as keyof TestCase],
        };
      }

      await db.insert(testCaseHistory).values({
        testCaseId: entity.id,
        changeType: 'UPDATE' as const,
        changedBy: context.userId,
        changedFields: changedFieldsObject,
        summary: `Updated ${context.changedFields.join(', ')}`,
      });
    }
  }

  /**
   * Get list of fields that changed
   */
  private getChangedFields(original: Partial<TestCase>, updated: TestCasePatch): string[] {
    const changedFields: string[] = [];

    for (const key in updated) {
      if (
        Object.hasOwn(updated, key) &&
        updated[key as keyof TestCasePatch] !== undefined &&
        key !== 'updatedAt' // Exclude updatedAt from history tracking
      ) {
        const originalValue = original[key as keyof TestCase];
        const newValue = updated[key as keyof TestCasePatch];

        // Compare values (handle JSON fields)
        if (JSON.stringify(originalValue) !== JSON.stringify(newValue)) {
          changedFields.push(key);
        }
      }
    }

    return changedFields;
  }

  /**
   * Extract values for changed fields
   */
  private extractChangedValues(record: Partial<TestCase>, fields: string[]): Record<string, any> {
    const values: Record<string, any> = {};

    for (const field of fields) {
      values[field] = record[field as keyof TestCase];
    }

    return values;
  }

  /**
   * Override create to track initial creation in history
   */
  async afterCreate(entity: TestCase, context: any): Promise<void> {
    // Only create history if we have a valid user
    if (!context.userId) {
      return;
    }

    // Create an initial history entry for creation
    await db.insert(testCaseHistory).values({
      testCaseId: entity.id,
      changeType: 'CREATE' as const,
      changedBy: context.userId,
      changedFields: null,
      summary: 'Test case created',
    });
  }

  /**
   * Override delete to track deletion in history
   */
  async beforeDelete(id: string, context: any): Promise<void> {
    // Only track deletion if we have a valid user
    if (!context.userId) {
      return;
    }

    // Get the test case before deletion
    const testCaseToDelete = await db.query.testCase.findFirst({
      where: eq(testCase.id, id),
    });

    if (testCaseToDelete) {
      // Create a deletion history entry
      await db.insert(testCaseHistory).values({
        testCaseId: id,
        changeType: 'DELETE' as const,
        changedBy: context.userId,
        changedFields: null,
        summary: 'Test case deleted',
      });
    }
  }

  /**
   * Add a test case to a test suite with optional status and priority
   */
  async addToSuite(
    testCaseId: string,
    testSuiteId: string,
    data: Partial<TestSuiteTestCaseInsert>,
    context: ServiceContext,
  ): Promise<void> {
    // Check if the association already exists
    const existing = await db.query.testSuiteTestCase.findFirst({
      where: and(
        eq(testSuiteTestCase.testSuiteId, testSuiteId),
        eq(testSuiteTestCase.testCaseId, testCaseId),
      ),
    });

    if (existing) {
      throw new Error('Test case is already associated with this test suite');
    }

    // Create the association
    await db.insert(testSuiteTestCase).values({
      testSuiteId,
      testCaseId,
      statusId: data.statusId,
      priorityId: data.priorityId,
      order: data.order ?? 0,
      createdBy: context.userId,
    });
  }

  /**
   * Remove a test case from a test suite
   */
  async removeFromSuite(testCaseId: string, testSuiteId: string): Promise<void> {
    await db
      .delete(testSuiteTestCase)
      .where(
        and(
          eq(testSuiteTestCase.testSuiteId, testSuiteId),
          eq(testSuiteTestCase.testCaseId, testCaseId),
        ),
      );
  }

  /**
   * Update test case properties within a specific suite
   */
  async updateInSuite(
    testCaseId: string,
    testSuiteId: string,
    data: { statusId?: string; priorityId?: string; order?: number },
  ): Promise<void> {
    await db
      .update(testSuiteTestCase)
      .set({
        statusId: data.statusId,
        priorityId: data.priorityId,
        order: data.order,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(testSuiteTestCase.testSuiteId, testSuiteId),
          eq(testSuiteTestCase.testCaseId, testCaseId),
        ),
      );
  }

  /**
   * Get test cases for a specific suite with suite-specific properties
   */
  async getTestCasesForSuite(testSuiteId: string, context: ServiceContext): Promise<any[]> {
    const result = await db.query.testSuiteTestCase.findMany({
      where: eq(testSuiteTestCase.testSuiteId, testSuiteId),
      with: {
        testCase: {
          with: context.with || {},
        },
        status: true,
        priority: true,
      },
      orderBy: (testSuiteTestCase, { asc }) => [asc(testSuiteTestCase.order)],
    });

    // Transform the result to include suite-specific properties
    return result.map((item) => ({
      ...item.testCase,
      suiteStatus: item.status,
      suitePriority: item.priority,
      suiteOrder: item.order,
    }));
  }

  /**
   * Get test cases that are available to add to a specific suite
   */
  async getAvailableForSuite(
    testSuiteId: string,
    projectId?: string,
    context?: ServiceContext,
  ): Promise<any[]> {
    // First, get all test case IDs already in this suite
    const existingAssociations = await db.query.testSuiteTestCase.findMany({
      where: eq(testSuiteTestCase.testSuiteId, testSuiteId),
      columns: {
        testCaseId: true,
      },
    });

    const existingTestCaseIds = existingAssociations.map((a) => a.testCaseId);

    // Build the query conditions
    const conditions = [];

    // Exclude test cases already in the suite
    if (existingTestCaseIds.length > 0) {
      conditions.push(notInArray(testCase.id, existingTestCaseIds));
    }

    // Filter by project if specified
    if (projectId) {
      conditions.push(eq(testCase.projectId, projectId));
    }

    // Query available test cases
    const query =
      conditions.length > 0
        ? db.query.testCase.findMany({
            where: and(...conditions),
            with: {
              project: true,
              assignee: true,
            },
          })
        : db.query.testCase.findMany({
            with: {
              project: true,
              assignee: true,
            },
          });

    return await query;
  }

  /**
   * Get all test cases for copying (shows all test cases across all test suites)
   */
  async getAllForCopying(
    options: {
      projectId?: string;
      search?: string;
      limit?: number;
      offset?: number;
    },
    context: ServiceContext,
  ): Promise<{ data: any[]; total: number }> {
    const { projectId, search, limit = 50, offset = 0 } = options;

    // Build query conditions
    const conditions: any[] = [];

    // Filter by project if provided
    if (projectId) {
      conditions.push(eq(testCase.projectId, projectId));
    }

    // Search filter
    if (search) {
      conditions.push(
        or(ilike(testCase.title, `%${search}%`), ilike(testCase.description, `%${search}%`)),
      );
    }

    // Active filter
    conditions.push(eq(testCase.isActive, true));

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get total count
    const [{ count }] = await db
      .select({ count: sql`count(*)`.mapWith(Number) })
      .from(testCase)
      .where(whereClause);

    // Get data with test suite information
    const testCases = await db.query.testCase.findMany({
      where: whereClause,
      with: {
        project: {
          columns: {
            id: true,
            name: true,
            key: true,
          },
        },
        assignee: {
          columns: {
            id: true,
            email: true,
            displayName: true,
          },
        },
      },
      orderBy: (testCase, { desc }) => [desc(testCase.createdAt)],
      limit,
      offset,
    });

    // Get test suite information for each test case
    const testCasesWithSuites = await Promise.all(
      testCases.map(async (tc) => {
        const suiteAssociations = await db.query.testSuiteTestCase.findMany({
          where: eq(testSuiteTestCase.testCaseId, tc.id),
          with: {
            testSuite: {
              columns: {
                id: true,
                name: true,
              },
            },
          },
        });

        return {
          ...tc,
          testSuites: suiteAssociations.map((assoc) => assoc.testSuite),
        };
      }),
    );

    return {
      data: testCasesWithSuites,
      total: count,
    };
  }
}

export const testCaseService = new TestCaseService();
