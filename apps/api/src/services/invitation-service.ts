import { randomBytes } from 'node:crypto';
import { db } from '@repo/db';
import { invitation, organization, project, role, user, workspace } from '@repo/db/schema';
import { eq } from 'drizzle-orm';
import { ApiError, ErrorCode } from '../errors/api-error';
import { BaseService } from './base-service';
import { emailService } from './email-service';
import { roleAssignmentService } from './role-assignment-service';

// Define types based on Drizzle inference
type Invitation = typeof invitation.$inferSelect;
type InvitationInsert = typeof invitation.$inferInsert;
type InvitationUpdate = Partial<InvitationInsert>;

// Define the enriched response type
export interface EnrichedInvitation extends Invitation {
  inviter?: {
    id: string;
    email: string;
    displayName: string | null;
  } | null;
  role?: {
    id: string;
    name: string;
    identifier: string;
  } | null;
  organization?: {
    id: string;
    name: string;
    slug: string;
    description: string | null;
    logoUrl: string | null;
  } | null;
  workspace?: {
    id: string;
    name: string;
    slug: string;
    description: string | null;
    icon: string | null;
  } | null;
  project?: {
    id: string;
    name: string;
    key: string;
    description: string | null;
    icon: string | null;
  } | null;
}

export class InvitationService extends BaseService<Invitation, InvitationInsert, InvitationUpdate> {
  constructor() {
    super(db, invitation, db.query.invitation, 'Invitation');
  }

  // Implement required abstract methods
  async validateUpdate(id: string, data: InvitationUpdate, context: any): Promise<void> {
    // Validate update operations if needed
    if (data.scopeType && data.scopeId) {
      await this.validateScopeExists(data.scopeType, data.scopeId);
    }

    // If status is being updated, validate the user can perform this action
    if (data.status && (data.status === 'accepted' || data.status === 'rejected')) {
      const currentInvitation = await db.query.invitation.findFirst({
        where: eq(invitation.id, id),
      });

      if (!currentInvitation) {
        throw new ApiError(404, 'Invitation not found', ErrorCode.NOT_FOUND);
      }

      // Check if invitation has expired
      if (new Date() > currentInvitation.expiresAt) {
        throw new ApiError(400, 'Invitation has expired', ErrorCode.VALIDATION_ERROR);
      }

      // Check if invitation is still pending
      if (currentInvitation.status !== 'pending') {
        throw new ApiError(
          400,
          `Invitation has already been ${currentInvitation.status}`,
          ErrorCode.VALIDATION_ERROR,
        );
      }

      // If the context has a userId (authenticated request), verify they match the invited email
      if (context.userId) {
        const authenticatedUser = await db.query.user.findFirst({
          where: eq(user.id, context.userId),
        });

        if (authenticatedUser && authenticatedUser.email !== currentInvitation.email) {
          throw new ApiError(
            403,
            'You can only accept or reject invitations sent to your email address',
            ErrorCode.FORBIDDEN,
          );
        }
      }
    }
  }

  async beforeCreate(data: InvitationInsert, _context: any): Promise<InvitationInsert> {
    // Generate a secure random token for the invitation
    const token = randomBytes(32).toString('hex');
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // Set expiration to 7 days from now

    // Add the generated token to the invitation data
    return {
      ...data,
      token,
      expiresAt,
    };
  }

  async afterCreate(entity: Invitation, _context: any): Promise<void> {
    try {
      // Fetch the inviter details
      const inviter = await db.query.user.findFirst({
        where: eq(user.id, entity.invitedBy),
        columns: {
          displayName: true,
          email: true,
        },
      });

      if (!inviter) {
        throw new Error('Inviter not found');
      }

      // Fetch the role details
      const roleDetails = await db.query.role.findFirst({
        where: eq(role.id, entity.roleId),
        columns: {
          name: true,
        },
      });

      if (!roleDetails) {
        throw new Error('Role not found');
      }

      // Fetch the scope name based on scope type
      let scopeName = 'the organization';

      switch (entity.scopeType) {
        case 'organization': {
          const org = await db.query.organization.findFirst({
            where: eq(organization.id, entity.scopeId),
            columns: { name: true },
          });
          scopeName = org?.name || 'the organization';
          break;
        }
        case 'workspace': {
          const ws = await db.query.workspace.findFirst({
            where: eq(workspace.id, entity.scopeId),
            columns: { name: true },
          });
          scopeName = ws?.name || 'the workspace';
          break;
        }
        case 'project': {
          const proj = await db.query.project.findFirst({
            where: eq(project.id, entity.scopeId),
            columns: { name: true },
          });
          scopeName = proj?.name || 'the project';
          break;
        }
        default:
          scopeName = 'the scope';
          break;
      }

      // Try to find existing user to get their name
      const existingUser = await db.query.user.findFirst({
        where: eq(user.email, entity.email),
        columns: {
          displayName: true,
        },
      });

      // Send invitation email
      await emailService.sendInvitationEmail(
        entity.email,
        inviter.displayName || inviter.email,
        inviter.email,
        scopeName,
        roleDetails.name,
        entity.id,
        existingUser?.displayName,
      );
    } catch (_error) {}
  }

  async beforeUpdate(id: string, data: InvitationUpdate, context: any): Promise<InvitationUpdate> {
    // Get the current invitation to check status transition
    const currentInvitation = await db.query.invitation.findFirst({
      where: eq(invitation.id, id),
    });

    if (!currentInvitation) {
      throw new ApiError(404, 'Invitation not found', ErrorCode.NOT_FOUND);
    }

    // Store the previous status in the context for use in afterUpdate
    context.previousStatus = currentInvitation.status;
    context.invitationEmail = currentInvitation.email;

    // If status is being changed from pending to accepted/rejected, add acceptedAt
    if (
      currentInvitation.status === 'pending' &&
      data.status &&
      (data.status === 'accepted' || data.status === 'rejected')
    ) {
      data.acceptedAt = new Date();
    }

    return data;
  }

  async afterUpdate(entity: Invitation, context: any): Promise<void> {
    // Check if status changed from pending to accepted
    if (context.previousStatus === 'pending' && entity.status === 'accepted') {
      // Find the user by email
      const invitedUser = await db.query.user.findFirst({
        where: eq(user.email, entity.email),
      });

      if (!invitedUser) {
        throw new ApiError(
          404,
          `User with email ${entity.email} not found. User must register before accepting invitation.`,
          ErrorCode.NOT_FOUND,
        );
      }

      // Create the role assignment
      try {
        await roleAssignmentService.create(
          {
            userId: invitedUser.id,
            roleId: entity.roleId,
            scopeType: entity.scopeType,
            scopeId: entity.scopeId,
            isActive: true,
          },
          context,
        );
      } catch (error) {
        // If role assignment already exists, it's okay
        if (error instanceof ApiError && error.statusCode === 409) {
        } else {
          throw error;
        }
      }
    }
  }

  /**
   * Validates that the scopeId exists in the appropriate table based on scopeType
   */
  async validateScopeExists(scopeType: string, scopeId: string): Promise<void> {
    let exists = false;

    switch (scopeType) {
      case 'organization': {
        const org = await db.query.organization.findFirst({
          where: eq(organization.id, scopeId),
        });
        exists = !!org;
        break;
      }
      case 'workspace': {
        const ws = await db.query.workspace.findFirst({
          where: eq(workspace.id, scopeId),
        });
        exists = !!ws;
        break;
      }
      case 'project': {
        const proj = await db.query.project.findFirst({
          where: eq(project.id, scopeId),
        });
        exists = !!proj;
        break;
      }
      default:
        throw new ApiError(400, `Invalid scope type: ${scopeType}`);
    }

    if (!exists) {
      throw new ApiError(404, `${scopeType} with id ${scopeId} not found`, ErrorCode.NOT_FOUND);
    }
  }

  /**
   * Validates the creation of an invitation
   */
  async validateCreate(data: InvitationInsert, _context: any): Promise<void> {
    // Validate that the scope exists
    await this.validateScopeExists(data.scopeType, data.scopeId);

    // Check if user already exists with this email
    const existingUser = await db.query.user.findFirst({
      where: eq(user.email, data.email),
    });

    // If user exists, check if they already have an active role assignment for this scope
    if (existingUser) {
      const existingAssignment = await db.query.roleAssignment.findFirst({
        where: (ra, { and, eq }) =>
          and(
            eq(ra.userId, existingUser.id),
            eq(ra.scopeType, data.scopeType),
            eq(ra.scopeId, data.scopeId),
            eq(ra.isActive, true),
          ),
        with: {
          role: {
            columns: {
              name: true,
            },
          },
        },
      });

      if (existingAssignment) {
        throw new ApiError(
          409,
          `User ${data.email} already has an active role (${existingAssignment.role?.name || 'Unknown'}) in this ${data.scopeType}`,
          ErrorCode.DUPLICATE_VALUE,
        );
      }
    }

    // Check for any pending invitation for the same email and scope (regardless of role)
    const existingInvitation = await db.query.invitation.findFirst({
      where: (inv, { and, eq }) =>
        and(
          eq(inv.email, data.email),
          eq(inv.scopeType, data.scopeType),
          eq(inv.scopeId, data.scopeId),
          eq(inv.status, 'pending'),
        ),
      with: {
        role: {
          columns: {
            name: true,
          },
        },
      },
    });

    if (existingInvitation) {
      // Check if it's for the same role
      if (existingInvitation.roleId === data.roleId) {
        throw new ApiError(
          409,
          `A pending invitation already exists for ${data.email} in this ${data.scopeType}`,
          ErrorCode.DUPLICATE_VALUE,
        );
      }
      // Different role - provide more specific message
      throw new ApiError(
        409,
        `A pending invitation already exists for ${data.email} in this ${data.scopeType} with role '${existingInvitation.role?.name || 'Unknown'}'. Please revoke the existing invitation before sending a new one.`,
        ErrorCode.DUPLICATE_VALUE,
      );
    }
  }

  /**
   * Enriches an invitation with its scope object (organization, workspace, or project)
   */
  async enrichWithScopeObject(invitation: Invitation): Promise<any> {
    let scopeObject = null;

    switch (invitation.scopeType) {
      case 'organization':
        scopeObject = await db.query.organization.findFirst({
          where: eq(organization.id, invitation.scopeId),
          columns: {
            id: true,
            name: true,
            slug: true,
            description: true,
            logoUrl: true,
          },
        });
        break;
      case 'workspace':
        scopeObject = await db.query.workspace.findFirst({
          where: eq(workspace.id, invitation.scopeId),
          columns: {
            id: true,
            name: true,
            slug: true,
            description: true,
            icon: true,
          },
        });
        break;
      case 'project':
        scopeObject = await db.query.project.findFirst({
          where: eq(project.id, invitation.scopeId),
          columns: {
            id: true,
            name: true,
            key: true,
            description: true,
            icon: true,
          },
        });
        break;
      default:
        // Unknown scope type - no enrichment
        break;
    }

    return {
      ...invitation,
      organization: invitation.scopeType === 'organization' ? scopeObject : null,
      workspace: invitation.scopeType === 'workspace' ? scopeObject : null,
      project: invitation.scopeType === 'project' ? scopeObject : null,
    };
  }

  /**
   * Enriches multiple invitations with their scope objects
   */
  async enrichMultipleWithScopeObjects(invitations: Invitation[]): Promise<any[]> {
    // Group invitations by scopeType for efficient batch loading
    const orgIds = invitations
      .filter((inv) => inv.scopeType === 'organization')
      .map((inv) => inv.scopeId);
    const wsIds = invitations
      .filter((inv) => inv.scopeType === 'workspace')
      .map((inv) => inv.scopeId);
    const projIds = invitations
      .filter((inv) => inv.scopeType === 'project')
      .map((inv) => inv.scopeId);

    // Batch load all scope objects
    const [orgs, workspaces, projects] = await Promise.all([
      orgIds.length > 0
        ? db.query.organization.findMany({
            where: (org, { inArray }) => inArray(org.id, orgIds),
            columns: {
              id: true,
              name: true,
              slug: true,
              description: true,
              logoUrl: true,
            },
          })
        : [],
      wsIds.length > 0
        ? db.query.workspace.findMany({
            where: (ws, { inArray }) => inArray(ws.id, wsIds),
            columns: {
              id: true,
              name: true,
              slug: true,
              description: true,
              icon: true,
            },
          })
        : [],
      projIds.length > 0
        ? db.query.project.findMany({
            where: (proj, { inArray }) => inArray(proj.id, projIds),
            columns: {
              id: true,
              name: true,
              key: true,
              description: true,
              icon: true,
            },
          })
        : [],
    ]);

    // Create lookup maps
    const orgMap = new Map(orgs.map((o) => [o.id, o]));
    const wsMap = new Map(workspaces.map((w) => [w.id, w]));
    const projMap = new Map(projects.map((p) => [p.id, p]));

    // Enrich invitations
    return invitations.map((invitation) => {
      let scopeObject = null;
      switch (invitation.scopeType) {
        case 'organization':
          scopeObject = orgMap.get(invitation.scopeId) || null;
          break;
        case 'workspace':
          scopeObject = wsMap.get(invitation.scopeId) || null;
          break;
        case 'project':
          scopeObject = projMap.get(invitation.scopeId) || null;
          break;
        default:
          // Unknown scope type - no enrichment
          break;
      }

      return {
        ...invitation,
        organization: invitation.scopeType === 'organization' ? scopeObject : null,
        workspace: invitation.scopeType === 'workspace' ? scopeObject : null,
        project: invitation.scopeType === 'project' ? scopeObject : null,
      };
    });
  }
}

export const invitationService = new InvitationService();
