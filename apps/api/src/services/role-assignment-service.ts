import { db } from '@repo/db';
import { organization, project, roleAssignment, workspace } from '@repo/db/schema';
import { eq } from 'drizzle-orm';
import { ApiError, ErrorCode } from '../errors/api-error';
import { BaseService } from './base-service';

// Define types based on Drizzle inference
type RoleAssignment = typeof roleAssignment.$inferSelect;
type RoleAssignmentInsert = typeof roleAssignment.$inferInsert;
type RoleAssignmentUpdate = Partial<RoleAssignmentInsert>;

// Define the enriched response type
export interface EnrichedRoleAssignment extends RoleAssignment {
  user?: {
    id: string;
    email: string;
    displayName: string | null;
  } | null;
  role?: {
    id: string;
    name: string;
    identifier: string;
  } | null;
  organization?: {
    id: string;
    name: string;
    slug: string;
    description: string | null;
    logoUrl: string | null;
  } | null;
  workspace?: {
    id: string;
    name: string;
    slug: string;
    description: string | null;
    icon: string | null;
  } | null;
  project?: {
    id: string;
    name: string;
    key: string;
    description: string | null;
    icon: string | null;
  } | null;
}

export class RoleAssignmentService extends BaseService<
  RoleAssignment,
  RoleAssignmentInsert,
  RoleAssignmentUpdate
> {
  constructor() {
    super(db, roleAssignment, db.query.roleAssignment, 'RoleAssignment');
  }

  // Implement required abstract methods
  async validateUpdate(_id: string, data: RoleAssignmentUpdate, _context: any): Promise<void> {
    // Validate update operations if needed
    if (data.scopeType && data.scopeId) {
      await this.validateScopeExists(data.scopeType, data.scopeId);
    }
  }

  async beforeCreate(data: RoleAssignmentInsert, _context: any): Promise<RoleAssignmentInsert> {
    return data;
  }

  async afterCreate(_entity: RoleAssignment, _context: any): Promise<void> {
    // Perform any post-creation tasks if needed
  }

  async beforeUpdate(
    _id: string,
    data: RoleAssignmentUpdate,
    _context: any,
  ): Promise<RoleAssignmentUpdate> {
    return data;
  }

  async afterUpdate(_entity: RoleAssignment, _context: any): Promise<void> {
    // Perform any post-update tasks if needed
  }

  /**
   * Validates that the scopeId exists in the appropriate table based on scopeType
   */
  async validateScopeExists(scopeType: string, scopeId: string): Promise<void> {
    let exists = false;

    switch (scopeType) {
      case 'organization': {
        const org = await db.query.organization.findFirst({
          where: eq(organization.id, scopeId),
        });
        exists = !!org;
        break;
      }
      case 'workspace': {
        const ws = await db.query.workspace.findFirst({
          where: eq(workspace.id, scopeId),
        });
        exists = !!ws;
        break;
      }
      case 'project': {
        const proj = await db.query.project.findFirst({
          where: eq(project.id, scopeId),
        });
        exists = !!proj;
        break;
      }
      default:
        throw new ApiError(400, `Invalid scope type: ${scopeType}`);
    }

    if (!exists) {
      throw new ApiError(404, `${scopeType} with id ${scopeId} not found`, ErrorCode.NOT_FOUND);
    }
  }

  /**
   * Validates the creation of a role assignment
   */
  async validateCreate(data: RoleAssignmentInsert, _context: any): Promise<void> {
    // Validate that the scope exists
    await this.validateScopeExists(data.scopeType, data.scopeId);

    // Check for any active role assignment for this user in this scope
    const existingActiveRole = await db.query.roleAssignment.findFirst({
      where: (ra, { and, eq }) =>
        and(
          eq(ra.userId, data.userId),
          eq(ra.scopeType, data.scopeType),
          eq(ra.scopeId, data.scopeId),
          eq(ra.isActive, true),
        ),
      with: {
        role: {
          columns: {
            name: true,
            identifier: true,
          },
        },
      },
    });

    if (existingActiveRole) {
      // Check if it's the exact same role assignment
      if (existingActiveRole.roleId === data.roleId) {
        throw new ApiError(
          409,
          'Role assignment already exists for this user and scope',
          ErrorCode.DUPLICATE_VALUE,
        );
      }
      // Different role - user already has a different active role
      throw new ApiError(
        409,
        `User already has an active role (${existingActiveRole.role?.name || 'Unknown'}) in this ${data.scopeType}. Please deactivate the existing role before assigning a new one.`,
        ErrorCode.DUPLICATE_VALUE,
      );
    }
  }

  /**
   * Enriches a role assignment with its scope object (organization, workspace, or project)
   */
  async enrichWithScopeObject(assignment: RoleAssignment): Promise<any> {
    let scopeObject = null;

    switch (assignment.scopeType) {
      case 'organization':
        scopeObject = await db.query.organization.findFirst({
          where: eq(organization.id, assignment.scopeId),
          columns: {
            id: true,
            name: true,
            slug: true,
            description: true,
            logoUrl: true,
          },
        });
        break;
      case 'workspace':
        scopeObject = await db.query.workspace.findFirst({
          where: eq(workspace.id, assignment.scopeId),
          columns: {
            id: true,
            name: true,
            slug: true,
            description: true,
            icon: true,
            organizationId: true,
          },
        });
        break;
      case 'project':
        scopeObject = await db.query.project.findFirst({
          where: eq(project.id, assignment.scopeId),
          columns: {
            id: true,
            name: true,
            key: true,
            description: true,
            icon: true,
            workspaceId: true,
          },
        });
        break;
      default:
        // Unknown scope type - no enrichment
        break;
    }

    return {
      ...assignment,
      organization: assignment.scopeType === 'organization' ? scopeObject : null,
      workspace: assignment.scopeType === 'workspace' ? scopeObject : null,
      project: assignment.scopeType === 'project' ? scopeObject : null,
    };
  }

  /**
   * Enriches multiple role assignments with their scope objects
   */
  async enrichMultipleWithScopeObjects(assignments: RoleAssignment[]): Promise<any[]> {
    // Group assignments by scopeType for efficient batch loading
    const orgIds = assignments.filter((a) => a.scopeType === 'organization').map((a) => a.scopeId);
    const wsIds = assignments.filter((a) => a.scopeType === 'workspace').map((a) => a.scopeId);
    const projIds = assignments.filter((a) => a.scopeType === 'project').map((a) => a.scopeId);

    // Batch load all scope objects
    const [orgs, workspaces, projects] = await Promise.all([
      orgIds.length > 0
        ? db.query.organization.findMany({
            where: (org, { inArray }) => inArray(org.id, orgIds),
            columns: {
              id: true,
              name: true,
              slug: true,
              description: true,
              logoUrl: true,
            },
          })
        : [],
      wsIds.length > 0
        ? db.query.workspace.findMany({
            where: (ws, { inArray }) => inArray(ws.id, wsIds),
            columns: {
              id: true,
              name: true,
              slug: true,
              description: true,
              icon: true,
              organizationId: true,
            },
          })
        : [],
      projIds.length > 0
        ? db.query.project.findMany({
            where: (proj, { inArray }) => inArray(proj.id, projIds),
            columns: {
              id: true,
              name: true,
              key: true,
              description: true,
              icon: true,
              workspaceId: true,
              workflowId: true,
            },
          })
        : [],
    ]);

    // Create lookup maps
    const orgMap = new Map(orgs.map((o) => [o.id, o]));
    const wsMap = new Map(workspaces.map((w) => [w.id, w]));
    const projMap = new Map(projects.map((p) => [p.id, p]));

    // Enrich assignments
    return assignments.map((assignment) => {
      let scopeObject = null;
      switch (assignment.scopeType) {
        case 'organization':
          scopeObject = orgMap.get(assignment.scopeId) || null;
          break;
        case 'workspace':
          scopeObject = wsMap.get(assignment.scopeId) || null;
          break;
        case 'project':
          scopeObject = projMap.get(assignment.scopeId) || null;
          break;
        default:
          // Unknown scope type - no enrichment
          break;
      }

      return {
        ...assignment,
        organization: assignment.scopeType === 'organization' ? scopeObject : null,
        workspace: assignment.scopeType === 'workspace' ? scopeObject : null,
        project: assignment.scopeType === 'project' ? scopeObject : null,
      };
    });
  }
}

export const roleAssignmentService = new RoleAssignmentService();
