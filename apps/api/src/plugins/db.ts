// plugins/db.ts
import { db, pg } from '@repo/db';
import type { FastifyInstance } from 'fastify';
import fp from 'fastify-plugin';

/**
 * Database connection plugin
 *
 * Attaches the database client and query builder from @repo/db to the Fastify instance
 */
const dbPlugin = async (fastify: FastifyInstance) => {
  try {
    // Attach the db instance to Fastify
    fastify.decorate('db', db);

    // Close database connection when Fastify closes
    fastify.addHook('onClose', async (_) => {
      // Only attempt to close if in production (in dev, connections are reused)
      if (process.env.NODE_ENV === 'production' && pg) {
        await pg.end();
        fastify.log.info('Database connection closed');
      }
    });

    fastify.log.info('Database connection established and attached to Fastify');
  } catch (err) {
    fastify.log.error('Failed to attach database to Fastify:', err);
    throw err;
  }
};

// Use fastify-plugin to make the decorator available to the entire application
export default fp(dbPlugin, {
  name: 'db',
});
