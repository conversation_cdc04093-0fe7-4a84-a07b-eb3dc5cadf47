import type { FastifyPluginAsync } from 'fastify';
import fastifyPlugin from 'fastify-plugin';
import * as Minio from 'minio';
import { config } from '../config';

declare module 'fastify' {
  interface FastifyInstance {
    minio: Minio.Client;
  }
}

const minioPlugin: FastifyPluginAsync = async (fastify) => {
  const minioClient = new Minio.Client({
    endPoint: config.minio.endpoint,
    port: config.minio.port,
    useSSL: config.minio.useSSL,
    accessKey: config.minio.accessKey,
    secretKey: config.minio.secretKey,
  });

  // Check if bucket exists, create if not
  try {
    const bucketExists = await minioClient.bucketExists(config.minio.bucketName);
    if (bucketExists) {
      fastify.log.info(`MinIO bucket exists: ${config.minio.bucketName}`);
    } else {
      await minioClient.makeBucket(config.minio.bucketName, 'us-east-1');
      fastify.log.info(`Created MinIO bucket: ${config.minio.bucketName}`);
    }
  } catch (error) {
    fastify.log.error(`Failed to setup MinIO bucket: ${error}`);
    throw error;
  }

  // Decorate fastify instance with minio client
  fastify.decorate('minio', minioClient);

  fastify.log.info('MinIO client initialized successfully');
};

export default fastifyPlugin(minioPlugin, {
  name: 'minio',
});
