// Common validation schemas and utilities
import { z } from 'zod';

// Common field validations
export const commonSchemas = {
  // UUID with better error message
  uuid: z.string().uuid('Invalid UUID format'),

  // Email with normalization
  email: z
    .string()
    .email('Invalid email format')
    .transform((email) => email.toLowerCase()),

  // URL with validation
  url: z.string().url('Invalid URL format'),

  // Slug format (lowercase, alphanumeric, hyphens)
  slug: z
    .string()
    .regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens')
    .min(3, 'Slug must be at least 3 characters')
    .max(50, 'Slug cannot exceed 50 characters'),

  // Phone number (international format)
  phone: z
    .string()
    .regex(/^\+[1-9]\d{1,14}$/, 'Phone number must be in international format (+1234567890)'),

  // Password with strength requirements
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/\d/, 'Password must contain at least one number')
    .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character'),

  // Date range validation
  dateRange: z
    .object({
      startDate: z.coerce.date(),
      endDate: z.coerce.date(),
    })
    .refine((data) => data.endDate > data.startDate, {
      message: 'End date must be after start date',
      path: ['endDate'],
    }),

  // Pagination parameters with limits
  pagination: z.object({
    page: z.coerce.number().min(1, 'Page must be at least 1').default(1),
    limit: z.coerce
      .number()
      .min(1, 'Limit must be at least 1')
      .max(100, 'Limit cannot exceed 100')
      .default(20),
  }),

  // Search query with sanitization
  search: z
    .string()
    .min(2, 'Search query must be at least 2 characters')
    .max(100, 'Search query cannot exceed 100 characters')
    .transform((query) => query.trim())
    .optional(),

  // Sort field validation
  sortField: z.object({
    field: z.string().min(1, 'Sort field cannot be empty'),
    order: z.enum(['asc', 'desc'], {
      errorMap: () => ({ message: 'Sort order must be asc or desc' }),
    }),
  }),

  // JSON object with size limit
  jsonObject: z.record(z.any()).refine((obj) => JSON.stringify(obj).length <= 10_000, {
    message: 'JSON object is too large (max 10KB)',
  }),
};

// Validation utilities
export const validationUtils = {
  // Custom validator for conditional required fields
  conditionalRequired: <T>(condition: (data: any) => boolean, schema: z.ZodType<T>) => {
    return z.any().superRefine((data, ctx) => {
      if (condition(data)) {
        const result = schema.safeParse(data);
        if (!result.success) {
          result.error.issues.forEach((issue) => ctx.addIssue(issue));
        }
      }
    });
  },

  // Validate that at least one field is provided
  atLeastOneOf: <T extends Record<string, any>>(
    fields: (keyof T)[],
    message = 'At least one field is required',
  ) => {
    return z.any().superRefine((data: T, ctx) => {
      const hasValue = fields.some(
        (field) => data[field] !== undefined && data[field] !== null && data[field] !== '',
      );

      if (!hasValue) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message,
          path: fields as string[],
        });
      }
    });
  },

  // Validate unique array values
  uniqueArray: <T>(itemSchema: z.ZodType<T>, message = 'Array must contain unique values') => {
    return z.array(itemSchema).superRefine((arr, ctx) => {
      const seen = new Set();
      arr.forEach((item, index) => {
        const key = typeof item === 'object' ? JSON.stringify(item) : item;
        if (seen.has(key)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message,
            path: [index],
          });
        }
        seen.add(key);
      });
    });
  },
};

// Error message customization
export const errorMessages = {
  required: 'This field is required',
  invalid_type: 'Invalid data type provided',
  too_small: 'Value is too small',
  too_big: 'Value is too large',
  invalid_string: 'Invalid string format',
  invalid_date: 'Invalid date format',
};

// Custom error map for better user experience
export const customErrorMap: z.ZodErrorMap = (issue, ctx) => {
  switch (issue.code) {
    case z.ZodIssueCode.invalid_type:
      if (issue.expected === 'string' && issue.received === 'undefined') {
        return { message: 'This field is required' };
      }
      return { message: `Expected ${issue.expected}, got ${issue.received}` };

    case z.ZodIssueCode.too_small:
      if (issue.type === 'string') {
        return { message: `Must be at least ${issue.minimum} characters long` };
      }
      return { message: `Must be at least ${issue.minimum}` };

    case z.ZodIssueCode.too_big:
      if (issue.type === 'string') {
        return {
          message: `Must be no more than ${issue.maximum} characters long`,
        };
      }
      return { message: `Must be no more than ${issue.maximum}` };

    default:
      return { message: ctx.defaultError };
  }
};

// Apply custom error map globally
z.setErrorMap(customErrorMap);
