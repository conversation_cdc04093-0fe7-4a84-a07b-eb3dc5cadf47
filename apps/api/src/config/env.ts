/* eslint-disable node/no-process-env */
import { config } from 'dotenv';
import { expand } from 'dotenv-expand';
import { ZodError, z } from 'zod';

// In production, variables should come from ConfigMap/deployment
// In development, load from .env files
if (process.env.NODE_ENV !== 'production') {
  const result = config();
  if (result.parsed) {
    expand(result);
  }
} else {
  // Log critical variables for debugging
  const criticalVars = ['DB_HOST', 'DB_USER', 'DB_NAME', 'DATABASE_URL'];
  criticalVars.forEach((key) => {
    const value = process.env[key];
    if (value) {
    } else {
    }
  });
}

const stringBoolean = z.coerce
  .string()
  .transform((val) => {
    return val === 'true';
  })
  .default('false');

// Modified schema to respect existing NODE_ENV value from environment
const EnvSchema = z.object({
  NODE_ENV: z.string().default('development'),
  PORT: z.coerce.number().default(3000),
  DB_HOST: z.string(),
  DB_USER: z.string(),
  DB_PASSWORD: z.string(),
  DB_NAME: z.string(),
  DB_PORT: z.coerce.number(),
  DATABASE_URL: z.string(),
  DB_MIGRATING: stringBoolean,
  DB_SEEDING: stringBoolean,
  LOG_LEVEL: z.string().default('info'),
  // JWT Config
  JWT_SECRET: z.string().default('your-super-secret-jwt-key-for-development-only'),
  JWT_REFRESH_SECRET: z
    .string()
    .default('your-super-secret-refresh-token-key-for-development-only'),
  JWT_EXPIRES_IN: z.coerce.number().default(86_400), // 24 hours in seconds
  JWT_ISSUER: z.string().default('repo-api'),
  JWT_AUDIENCE: z.string().default('repo-app'),
  // Email Config
  SMTP_HOST: z.string().default('smtp.gmail.com'),
  SMTP_PORT: z.coerce.number().default(587),
  SMTP_SECURE: stringBoolean,
  SMTP_USER: z.string().default(''),
  SMTP_PASS: z.string().default(''),
  EMAIL_FROM: z.string().default('<EMAIL>'),
  FRONT_END_URL: z.string().default('http://localhost:5173'),
  // MinIO Config
  MINIO_ENDPOINT: z.string().default('localhost'),
  MINIO_PORT: z.coerce.number().default(9000),
  MINIO_USE_SSL: stringBoolean,
  MINIO_ACCESS_KEY: z.string().default('minioadmin'),
  MINIO_SECRET_KEY: z.string().default('minioadmin'),
  MINIO_BUCKET_NAME: z.string().default('spark-attachments'),
  // File Upload Config
  MAX_FILE_SIZE: z.coerce.number().default(10_485_760), // 10MB in bytes
  ALLOWED_FILE_TYPES: z
    .string()
    .default('image/*,application/pdf,text/*,.doc,.docx,.xls,.xlsx,.ppt,.pptx'),

  // FCM Config
  FCM_PROJECT_ID: z.string().optional(),
  FCM_PRIVATE_KEY: z.string().optional(),
  FCM_CLIENT_EMAIL: z.string().optional(),
  FCM_SERVICE_ACCOUNT_PATH: z.string().optional(),
});

// eslint-disable-next-line ts/no-redeclare
export type EnvSchema = z.infer<typeof EnvSchema>;

// Parse and validate environment variables
let parsedEnv: EnvSchema;

try {
  parsedEnv = EnvSchema.parse(process.env);
} catch (error) {
  if (error instanceof ZodError) {
    const missingVars: string[] = [];
    const invalidVars: string[] = [];

    error.issues.forEach((issue) => {
      const varName = issue.path[0] as string;
      if (issue.code === 'invalid_type' && issue.received === 'undefined') {
        missingVars.push(varName);
      } else {
        invalidVars.push(`${varName}: ${issue.message}`);
      }
    });

    if (missingVars.length > 0) {
    }

    if (invalidVars.length > 0) {
      invalidVars.forEach((_v) => {});
    }

    // In production, this is critical
    if (process.env.NODE_ENV === 'production') {
    }

    const e = new Error('Environment validation failed. See logs above for details.');
    e.stack = '';
    throw e;
  }
  throw error;
}

export default parsedEnv;
