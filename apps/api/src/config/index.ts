// Centralized configuration management using existing env.ts
import { z } from 'zod';
import env from './env';

// Extended configuration schema that builds on top of env.ts
const extendedConfigSchema = z.object({
  // Server Configuration (using existing env values)
  server: z.object({
    port: z.number(),
    host: z.string().default('0.0.0.0'),
    logLevel: z.string(),
    nodeEnv: z.string(),
  }),

  // Database Configuration (using existing env values)
  database: z.object({
    url: z.string(),
    host: z.string(),
    user: z.string(),
    password: z.string(),
    name: z.string(),
    port: z.number(),
    migrating: z.boolean(),
    seeding: z.boolean(),
  }),

  // Authentication Configuration (using existing env values)
  auth: z.object({
    jwtSecret: z.string(),
    jwtExpiresIn: z.number(),
    jwtIssuer: z.string(),
    jwtAudience: z.string(),
  }),

  // API Configuration (with sensible defaults)
  api: z.object({
    rateLimit: z.object({
      max: z.number().default(100),
      timeWindow: z.string().default('1 minute'),
    }),
    pagination: z.object({
      defaultLimit: z.number().default(20),
      maxLimit: z.number().default(100),
    }),
  }),

  // CORS Configuration
  cors: z.object({
    origins: z.array(z.string()),
    credentials: z.boolean(),
    methods: z.array(z.string()),
  }),

  // Feature Flags (with defaults)
  features: z.object({
    enableSwagger: z.boolean().default(true),
    enableMetrics: z.boolean().default(false),
    enableCaching: z.boolean().default(false),
    enableRateLimit: z.boolean().default(true),
  }),

  // MinIO Configuration (using existing env values)
  minio: z.object({
    endpoint: z.string(),
    port: z.number(),
    useSSL: z.boolean(),
    accessKey: z.string(),
    secretKey: z.string(),
    bucketName: z.string(),
  }),

  // File Upload Configuration
  fileUpload: z.object({
    maxFileSize: z.number(),
    allowedFileTypes: z.string().transform((types) => types.split(',')),
  }),
});

// Build configuration from env.ts
function buildConfig() {
  const baseConfig = {
    server: {
      port: env.PORT,
      host: '0.0.0.0',
      logLevel: env.LOG_LEVEL,
      nodeEnv: env.NODE_ENV,
    },
    database: {
      url: env.DATABASE_URL,
      host: env.DB_HOST,
      user: env.DB_USER,
      password: env.DB_PASSWORD,
      name: env.DB_NAME,
      port: env.DB_PORT,
      migrating: env.DB_MIGRATING,
      seeding: env.DB_SEEDING,
    },
    auth: {
      jwtSecret: env.JWT_SECRET,
      jwtExpiresIn: env.JWT_EXPIRES_IN,
      jwtIssuer: env.JWT_ISSUER,
      jwtAudience: env.JWT_AUDIENCE,
    },
    api: {
      rateLimit: {
        max: 100,
        timeWindow: '1 minute',
      },
      pagination: {
        defaultLimit: 20,
        maxLimit: 100,
      },
    },
    cors: {
      origins: ['http://localhost:3000', 'http://localhost:3001', '*'],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    },
    features: {
      enableSwagger: env.NODE_ENV !== 'production',
      enableMetrics: true,
      enableCaching: false,
      enableRateLimit: true,
    },
    minio: {
      endpoint: env.MINIO_ENDPOINT,
      port: env.MINIO_PORT,
      useSSL: env.MINIO_USE_SSL,
      accessKey: env.MINIO_ACCESS_KEY,
      secretKey: env.MINIO_SECRET_KEY,
      bucketName: env.MINIO_BUCKET_NAME,
    },
    fileUpload: {
      maxFileSize: env.MAX_FILE_SIZE,
      allowedFileTypes: env.ALLOWED_FILE_TYPES,
    },
  };

  try {
    return extendedConfigSchema.parse(baseConfig);
  } catch (error) {
    if (error instanceof z.ZodError) {
      error.errors.forEach((_err) => {});
    }
    throw error;
  }
}

// Export validated configuration
export const config = buildConfig();

// Export types for TypeScript
export type Config = z.infer<typeof extendedConfigSchema>;
