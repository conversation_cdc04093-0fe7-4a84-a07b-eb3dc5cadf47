// server.ts
import { createApp } from './app';
import { config } from './config';

/**
 * Start the server
 */
export async function startServer() {
  try {
    console.log('Creating app...');
    const app = await createApp();
    console.log('App created successfully');

    // Use configuration instead of process.env
    const port = config.server.port;
    const host = config.server.host;

    console.log(`Attempting to listen on ${host}:${port}...`);
    // Start listening
    await app.listen({ port, host });

    console.log(`Server is running at http://${host}:${port}`);
    console.log(`Documentation available at http://${host}:${port}/documentation`);

    return app;
  } catch (err) {
    console.error('Failed to start server:', err);
    process.exit(1);
  }
}

// Start the server if this file is run directly
if (require.main === module) {
  startServer()
    .then(() => {
      console.log('Server is ready and listening');
    })
    .catch((err) => {
      console.error('Server startup failed:', err);
      process.exit(1);
    });
}
