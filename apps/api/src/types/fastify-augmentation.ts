// types/fastify.ts
import '@fastify/jwt';
import type { DB } from '@repo/db'; // Import the DB type directly from @repo/db
import type { FastifyReply, FastifyRequest } from 'fastify';
import type * as Minio from 'minio';
import type { Config } from '../config';

// Extend the JWT module's declaration
declare module '@fastify/jwt' {
  interface FastifyJWT {
    payload: {
      userId: string;
      email: string;
      organizationId?: string;
      workspaceId?: string;
      roles?: string[];
    };
    user: {
      id: string; // Add id field for consistency
      userId: string;
      email: string;
      organizationId?: string;
      workspaceId?: string;
      roles?: string[];
      isActive: boolean;
      isStaff: boolean;
    };
  }
}

declare module 'fastify' {
  interface FastifyInstance {
    db: DB;
    minio: Minio.Client;
    config: Config;
    authenticate: (request: FastifyRequest, reply: FastifyReply) => Promise<void>;
  }

  interface FastifyRequest {
    context?: {
      correlationId: string;
      startTime: number;
      userId?: string;
      resource?: string;
      operation?: string;
    };
  }
}
