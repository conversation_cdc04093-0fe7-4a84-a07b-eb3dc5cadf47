import { type ZodType, z } from 'zod';
import { errorResponseSchema, idParamSchema, queryStringSchema } from '../../../utils/crud/schemas';

/**
 * Automated schema registry for OpenAPI documentation
 * Automatically collects and manages schemas from routes
 */

export class SchemaRegistry {
  private static instance: SchemaRegistry;
  private schemas: Map<string, ZodType> = new Map();
  private schemaNames: Map<ZodType, string> = new Map();
  private relationMaps: Map<string, Record<string, string>> = new Map();

  private constructor() {
    // Initialize with common schemas
    this.registerCommonSchemas();
  }

  static getInstance(): SchemaRegistry {
    if (!SchemaRegistry.instance) {
      SchemaRegistry.instance = new SchemaRegistry();
    }
    return SchemaRegistry.instance;
  }

  /**
   * Register a schema with a name
   * @param name The name to use in OpenAPI components
   * @param schema The Zod schema
   */
  registerSchema(name: string, schema: ZodType): void {
    // Normalize the name to PascalCase
    const normalizedName = this.normalizeName(name);
    this.schemas.set(normalizedName, schema);
    this.schemaNames.set(schema, normalizedName);
  }

  /**
   * Register a relation map for an entity
   * @param entityName The entity name
   * @param relationMap The relation map (field -> target entity)
   */
  registerRelationMap(entityName: string, relationMap: Record<string, string>): void {
    const normalizedName = this.normalizeName(entityName);
    this.relationMaps.set(normalizedName, relationMap);
  }

  /**
   * Get the relation map for an entity
   * @param entityName The entity name
   * @returns The relation map or undefined
   */
  getRelationMap(entityName: string): Record<string, string> | undefined {
    const normalizedName = this.normalizeName(entityName);
    return this.relationMaps.get(normalizedName);
  }

  /**
   * Get all relation maps
   */
  getAllRelationMaps(): Record<string, Record<string, string>> {
    return Object.fromEntries(this.relationMaps);
  }

  /**
   * Get all registered schemas for OpenAPI components
   */
  getAllSchemas(): Record<string, ZodType> {
    return Object.fromEntries(this.schemas);
  }

  /**
   * Get the component name for a schema
   */
  getSchemaName(schema: ZodType): string | undefined {
    return this.schemaNames.get(schema);
  }

  /**
   * Check if a schema is registered
   */
  hasSchema(schema: ZodType): boolean {
    return this.schemaNames.has(schema);
  }

  /**
   * Check if a schema with the given name is registered
   */
  hasSchemaByName(name: string): boolean {
    return this.schemas.has(name);
  }

  /**
   * Register common schemas used across the API
   */
  private registerCommonSchemas(): void {
    // Error schemas
    this.registerSchema('ErrorResponse', errorResponseSchema);
    this.registerSchema(
      'ValidationError',
      errorResponseSchema.extend({
        statusCode: z.literal(400),
      }),
    );
    this.registerSchema(
      'UnauthorizedError',
      errorResponseSchema.extend({
        statusCode: z.literal(401),
      }),
    );
    this.registerSchema(
      'ForbiddenError',
      errorResponseSchema.extend({
        statusCode: z.literal(403),
      }),
    );
    this.registerSchema(
      'NotFoundError',
      errorResponseSchema.extend({
        statusCode: z.literal(404),
      }),
    );
    this.registerSchema(
      'ConflictError',
      errorResponseSchema.extend({
        statusCode: z.literal(409),
      }),
    );
    this.registerSchema(
      'InternalServerError',
      errorResponseSchema.extend({
        statusCode: z.literal(500),
      }),
    );

    // Parameter schemas
    this.registerSchema('IdParam', idParamSchema);
    this.registerSchema('QueryString', queryStringSchema);

    // Common entity schemas that appear in relations
    // User schema (commonly appears as creator, assignee, etc.)
    this.registerSchema(
      'User',
      z.object({
        id: z.string().uuid(),
        email: z.string().email(),
        firstName: z.string(),
        lastName: z.string(),
        displayName: z.string(),
        isActive: z.boolean(),
        isStaff: z.boolean(),
        isEmailVerified: z.boolean(),
        avatarUrl: z.string().nullable().optional(),
        createdAt: z.string().datetime(),
        updatedAt: z.string().datetime(),
      }),
    );
  }

  /**
   * Normalize schema name to PascalCase
   */
  private normalizeName(name: string): string {
    // If already PascalCase, return as is
    if (/^[A-Z][a-zA-Z0-9]*$/.test(name)) {
      return name;
    }

    // Convert kebab-case or snake_case to PascalCase
    return name
      .split(/[-_]/)
      .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
      .join('');
  }
}

// Export singleton instance
export const schemaRegistry = SchemaRegistry.getInstance();
