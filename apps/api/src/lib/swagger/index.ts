/**
 * Swagger transformation module
 *
 * Provides functionality to transform OpenAPI/Swagger specifications
 * to use proper component references instead of inline schemas.
 *
 * Features:
 * - Automatic conversion of inline schemas to component references
 * - Relation detection based on database schema definitions
 * - Support for nullable relations and array types
 * - Clean separation of concerns with dedicated processors
 */

import FastifySwagger from '@fastify/swagger';
import FastifySwaggerUI from '@fastify/swagger-ui';
import type { FastifyInstance } from 'fastify';
import { jsonSchemaTransform as originalTransform } from 'fastify-type-provider-zod';
import { SwaggerTransformer } from './core/transformer';
import type { SwaggerDocument } from './types';

// Create singleton transformer instance
const transformer = new SwaggerTransformer();

// Create a safe wrapper around jsonSchemaTransform
const safeJsonSchemaTransform = (obj: any) => {
  try {
    return originalTransform(obj);
  } catch (_error) {
    // Return a safe fallback for failed schemas
    if (obj?.schema) {
      return {
        ...obj,
        schema: {
          type: 'object',
          description: 'Schema transformation failed',
        },
      };
    }
    return obj;
  }
};

/**
 * Transform a swagger specification to use component references
 *
 * @param swaggerObject - The OpenAPI/Swagger document to transform
 * @returns The transformed swagger document
 *
 * @example
 * ```typescript
 * const transformedSwagger = transformSwaggerSpec(originalSwagger);
 * ```
 */
export function transformSwaggerSpec(swaggerObject: SwaggerDocument): SwaggerDocument {
  return transformer.transform(swaggerObject);
}

/**
 * Configure and register Swagger/OpenAPI documentation
 */
export async function registerSwagger(app: FastifyInstance): Promise<void> {
  // Register Swagger plugin
  await app.register(FastifySwagger, {
    openapi: {
      info: {
        title: 'API Documentation',
        description: 'API documentation for workspace management',
        version: '1.0.0',
      },
      components: {
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
          },
        },
      },
    },
    transform: safeJsonSchemaTransform,
  });

  // Register Swagger UI
  await app.register(FastifySwaggerUI, {
    routePrefix: '/documentation',
    uiConfig: {
      docExpansion: 'list',
      deepLinking: true,
    },
    transformSpecification: (swaggerObject: any, _request: any, _reply: any) => {
      try {
        // First apply the existing transformation
        const transformed = transformSwaggerSpec(swaggerObject);

        // Then fix nullable types in the entire document
        const fixNullableTypes = (schema: any): any => {
          if (!schema || typeof schema !== 'object') {
            return schema;
          }

          // Handle anyOf with null
          if (schema.anyOf && Array.isArray(schema.anyOf) && schema.anyOf.length === 2) {
            const hasNull = schema.anyOf.some((item: any) => item && item.type === 'null');
            const nonNull = schema.anyOf.find((item: any) => item && item.type !== 'null');

            if (hasNull && nonNull) {
              // Convert to proper nullable format
              const fixed = { ...nonNull, nullable: true };
              // Remove anyOf
              fixed.anyOf = undefined;
              return fixed;
            }
          }

          // Handle type array with null
          if (Array.isArray(schema.type) && schema.type.includes('null')) {
            const nonNullType = schema.type.find((t: any) => t !== 'null');
            const fixed = { ...schema, type: nonNullType, nullable: true };
            return fixed;
          }

          // Recursively fix nested objects
          const result = { ...schema };

          if (result.properties && typeof result.properties === 'object') {
            result.properties = {};
            for (const [key, value] of Object.entries(schema.properties)) {
              result.properties[key] = fixNullableTypes(value);
            }
          }

          if (result.items) {
            result.items = fixNullableTypes(result.items);
          }

          // Fix additionalProperties
          if (result.additionalProperties && typeof result.additionalProperties === 'object') {
            result.additionalProperties = fixNullableTypes(result.additionalProperties);
          }

          // Fix allOf/oneOf/anyOf
          if (result.allOf) {
            result.allOf = result.allOf.map(fixNullableTypes);
          }
          if (result.oneOf) {
            result.oneOf = result.oneOf.map(fixNullableTypes);
          }
          if (result.anyOf) {
            result.anyOf = result.anyOf.map(fixNullableTypes);
          }

          return result;
        };

        // Fix all schemas in components
        if (transformed.components?.schemas) {
          for (const [schemaName, schema] of Object.entries(transformed.components.schemas)) {
            transformed.components.schemas[schemaName] = fixNullableTypes(schema);
          }
        }

        // Fix all paths
        const fixPath = (pathObj: any): any => {
          if (!pathObj || typeof pathObj !== 'object') {
            return pathObj;
          }

          const result = { ...pathObj };

          // Fix each method
          for (const method of ['get', 'post', 'put', 'patch', 'delete']) {
            if (result[method]?.responses) {
              for (const [_status, response] of Object.entries(result[method].responses)) {
                if ((response as any).content) {
                  for (const [_contentType, content] of Object.entries((response as any).content)) {
                    if ((content as any).schema) {
                      (content as any).schema = fixNullableTypes((content as any).schema);
                    }
                  }
                }
              }

              // Fix request body
              if (result[method].requestBody?.content) {
                for (const [_contentType, content] of Object.entries(
                  result[method].requestBody.content,
                )) {
                  if ((content as any).schema) {
                    (content as any).schema = fixNullableTypes((content as any).schema);
                  }
                }
              }

              // Fix parameters
              if (result[method].parameters) {
                result[method].parameters = result[method].parameters.map((param: any) => {
                  if (param.schema) {
                    param.schema = fixNullableTypes(param.schema);
                  }
                  return param;
                });
              }
            }
          }

          return result;
        };

        if (transformed.paths) {
          for (const [path, pathObj] of Object.entries(transformed.paths)) {
            transformed.paths[path] = fixPath(pathObj);
          }
        }
        return transformed;
      } catch (_error) {
        // Return the original swagger object if transformation fails
        return swaggerObject;
      }
    },
  });
}

// Re-export registry for use in other parts of the app
export { schemaRegistry } from './registry/schema-registry';

// Re-export types
export type { JsonSchema, RelationMap, SwaggerDocument } from './types';
