/**
 * Type definitions for swagger transformation system
 */

export interface SwaggerDocument {
  openapi: string;
  info: any;
  servers?: any[];
  paths: Record<string, any>;
  components: {
    schemas: Record<string, any>;
    securitySchemes?: any;
  };
  tags?: any[];
}

export interface RelationMap {
  [propertyName: string]: string;
}

export interface SchemaProcessorOptions {
  componentSchemas: Record<string, any>;
  relationMaps?: Record<string, RelationMap>;
}

export interface JsonSchema {
  type?: string;
  properties?: Record<string, any>;
  items?: any;
  anyOf?: any[];
  oneOf?: any[];
  allOf?: any[];
  $ref?: string;
  enum?: any[];
  required?: string[];
  additionalProperties?: boolean;
}

export interface ProcessingContext {
  availableEntities: Set<string>;
  relationMap: RelationMap;
  componentSchemas: Record<string, any>;
}
