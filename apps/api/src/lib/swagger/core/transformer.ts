/**
 * Core swagger transformation logic
 * Transforms OpenAPI specifications to use proper component references
 */

import { zodToJsonSchema } from 'zod-to-json-schema';
import { SchemaProcessor } from '../processors/schema-processor';
import { schemaRegistry } from '../registry/schema-registry';
import type { ProcessingContext, SwaggerDocument } from '../types';

export class SwaggerTransformer {
  /**
   * Transform a swagger document to use proper component references
   */
  transform(swagger: SwaggerDocument): SwaggerDocument {
    // Ensure components structure exists
    this.ensureComponentStructure(swagger);

    // Add schemas from registry
    this.addRegistrySchemas(swagger);

    // Create processing context
    const context = this.createProcessingContext(swagger);

    // Create schema processor
    const processor = new SchemaProcessor(context);

    // Process component schemas
    this.processComponentSchemas(swagger, processor);

    // Process all paths
    this.processAllPaths(swagger, processor);

    return swagger;
  }

  /**
   * Ensure swagger has proper component structure
   */
  private ensureComponentStructure(swagger: SwaggerDocument): void {
    swagger.components = swagger.components || {};
    swagger.components.schemas = swagger.components.schemas || {};
  }

  /**
   * Add all registered schemas to swagger components
   */
  private addRegistrySchemas(swagger: SwaggerDocument): void {
    const registrySchemas = schemaRegistry.getAllSchemas();

    for (const [name, zodSchema] of Object.entries(registrySchemas)) {
      try {
        // Convert Zod schema to JSON schema
        const jsonSchema = zodToJsonSchema(zodSchema, {
          name,
          target: 'openApi3',
          $refStrategy: 'none', // Avoid nested refs in conversion
        });

        // The result might be wrapped in a definitions object
        if (jsonSchema && typeof jsonSchema === 'object') {
          // Remove the wrapper if present
          const unwrapped = jsonSchema.definitions?.[name] || jsonSchema;
          swagger.components.schemas[name] = unwrapped;
        }
      } catch (_error) {
        // Skip this schema if conversion fails
      }
    }
  }

  /**
   * Create processing context from swagger document
   */
  private createProcessingContext(swagger: SwaggerDocument): ProcessingContext {
    // Get all available entities
    const availableEntities = new Set(Object.keys(swagger.components.schemas));

    // Combine all relation maps from registry
    const relationMaps = schemaRegistry.getAllRelationMaps();
    const combinedRelationMap: Record<string, string> = {};

    for (const relationMap of Object.values(relationMaps)) {
      Object.assign(combinedRelationMap, relationMap);
    }

    return {
      availableEntities,
      relationMap: combinedRelationMap,
      componentSchemas: swagger.components.schemas,
    };
  }

  /**
   * Process component schemas to replace nested relations
   */
  private processComponentSchemas(swagger: SwaggerDocument, processor: SchemaProcessor): void {
    const schemas = swagger.components.schemas;

    for (const [schemaName, schema] of Object.entries(schemas)) {
      schemas[schemaName] = processor.processSchema(schema);
    }
  }

  /**
   * Process all API paths to replace inline schemas
   */
  private processAllPaths(swagger: SwaggerDocument, processor: SchemaProcessor): void {
    if (!swagger.paths) {
      return;
    }

    for (const pathItem of Object.values(swagger.paths)) {
      for (const operation of Object.values(pathItem)) {
        if (typeof operation !== 'object' || !operation) {
          continue;
        }

        this.processOperation(operation, processor);
      }
    }
  }

  /**
   * Process a single operation (GET, POST, etc.)
   */
  private processOperation(operation: any, processor: SchemaProcessor): void {
    // Process request body
    if (operation.requestBody?.content?.['application/json']?.schema) {
      operation.requestBody.content['application/json'].schema = processor.processSchema(
        operation.requestBody.content['application/json'].schema,
      );
    }

    // Process responses
    if (operation.responses) {
      this.processResponses(operation.responses, processor);
    }
  }

  /**
   * Process operation responses
   */
  private processResponses(responses: Record<string, any>, processor: SchemaProcessor): void {
    for (const response of Object.values(responses)) {
      if (!response || typeof response !== 'object') {
        continue;
      }

      if (response.content?.['application/json']?.schema) {
        response.content['application/json'].schema = processor.processResponseSchema(
          response.content['application/json'].schema,
        );
      }
    }
  }
}
