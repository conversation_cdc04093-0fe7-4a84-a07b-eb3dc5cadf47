/**
 * Processes schema relations using database-defined mappings
 */

import type { JsonSchema, RelationMap } from '../types';

export class RelationProcessor {
  constructor(
    private readonly relationMap: RelationMap,
    private readonly availableEntities: Set<string>,
  ) {}

  /**
   * Process a property to determine if it should be replaced with a $ref
   */
  processProperty(propertyName: string, schema: JsonSchema): JsonSchema {
    // Skip non-object types
    if (!this.isObjectLikeSchema(schema)) {
      return schema;
    }

    // Check if this property has a known relation mapping
    const targetEntity = this.relationMap[propertyName];
    if (!(targetEntity && this.availableEntities.has(targetEntity))) {
      return schema;
    }

    // Verify this looks like an entity relation
    if (!this.isEntityRelation(schema)) {
      return schema;
    }

    // Convert to $ref
    return this.createRefSchema(schema, targetEntity);
  }

  /**
   * Check if schema represents an object-like type
   */
  private isObjectLikeSchema(schema: JsonSchema): boolean {
    if (schema.$ref || schema.enum) {
      return false;
    }

    if (schema.type === 'object' || schema.type === 'array') {
      return true;
    }

    if (schema.anyOf?.some((s) => s.type === 'object')) {
      return true;
    }

    return false;
  }

  /**
   * Check if schema looks like an entity relation
   */
  private isEntityRelation(schema: JsonSchema): boolean {
    // Handle nullable patterns (anyOf with object + null)
    if (schema.anyOf && Array.isArray(schema.anyOf)) {
      const objectSchema = schema.anyOf.find((s) => s.type === 'object');
      const nullSchema = schema.anyOf.find((s) => s.type === 'null');

      if (objectSchema && nullSchema) {
        return this.isEntityObject(objectSchema);
      }
    }

    // Handle direct objects
    if (schema.type === 'object') {
      return this.isEntityObject(schema);
    }

    // Handle arrays of entities
    if (schema.type === 'array' && schema.items) {
      return this.isEntityObject(schema.items);
    }

    return false;
  }

  /**
   * Check if an object schema has entity-like properties
   */
  private isEntityObject(schema: JsonSchema): boolean {
    if (!schema.properties) {
      return false;
    }

    const properties = Object.keys(schema.properties);

    // Entities typically have an id and timestamps
    const hasId = properties.includes('id');
    const hasEntityProperties = properties.length > 2;

    return hasId && hasEntityProperties;
  }

  /**
   * Create a $ref schema for the target entity
   */
  private createRefSchema(originalSchema: JsonSchema, targetEntity: string): JsonSchema {
    // Handle nullable relations
    if (originalSchema.anyOf?.find((s) => s.type === 'null')) {
      return {
        anyOf: [{ $ref: `#/components/schemas/${targetEntity}` }, { type: 'null' }],
      };
    }

    // Handle arrays
    if (originalSchema.type === 'array') {
      return {
        type: 'array',
        items: { $ref: `#/components/schemas/${targetEntity}` },
      };
    }

    // Direct reference
    return { $ref: `#/components/schemas/${targetEntity}` };
  }
}
