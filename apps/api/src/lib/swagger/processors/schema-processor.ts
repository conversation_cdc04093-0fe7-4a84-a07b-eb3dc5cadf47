/**
 * Main schema processor for swagger transformations
 */

import type { JsonSchema, ProcessingContext } from '../types';
import { RelationProcessor } from './relation-processor';

export class SchemaProcessor {
  private readonly relationProcessor: RelationProcessor;
  private readonly context: ProcessingContext;

  constructor(context: ProcessingContext) {
    this.context = context;
    this.relationProcessor = new RelationProcessor(context.relationMap, context.availableEntities);
  }

  /**
   * Process a schema recursively to replace relations with $refs
   */
  processSchema(schema: JsonSchema): JsonSchema {
    if (!schema || typeof schema !== 'object') {
      return schema;
    }

    // Skip if already a ref
    if (schema.$ref) {
      return schema;
    }

    // Handle arrays
    if (Array.isArray(schema)) {
      return schema.map((s) => this.processSchema(s)) as any;
    }

    // Handle anyOf/oneOf/allOf patterns
    if (schema.anyOf) {
      return this.processCompositeSchema(schema, 'anyOf');
    }
    if (schema.oneOf) {
      return this.processCompositeSchema(schema, 'oneOf');
    }
    if (schema.allOf) {
      return this.processCompositeSchema(schema, 'allOf');
    }

    // Handle object schemas
    if (schema.type === 'object' && schema.properties) {
      return this.processObjectSchema(schema);
    }

    // Handle array schemas
    if (schema.type === 'array' && schema.items) {
      return {
        ...schema,
        items: this.processSchema(schema.items),
      };
    }

    return schema;
  }

  /**
   * Process composite schemas (anyOf, oneOf, allOf)
   */
  private processCompositeSchema(
    schema: JsonSchema,
    compositeType: 'anyOf' | 'oneOf' | 'allOf',
  ): JsonSchema {
    const items = schema[compositeType];
    if (!Array.isArray(items)) {
      return schema;
    }

    // Check if already contains refs
    if (items.some((item) => item.$ref)) {
      return schema;
    }

    // Process each item
    const processedItems = items.map((item) => this.processSchema(item));

    return {
      ...schema,
      [compositeType]: processedItems,
    };
  }

  /**
   * Process object schemas and their properties
   */
  private processObjectSchema(schema: JsonSchema): JsonSchema {
    const processedProperties: Record<string, any> = {};

    for (const [propName, propSchema] of Object.entries(schema.properties!)) {
      // First try to process as a relation
      const processed = this.relationProcessor.processProperty(propName, propSchema);

      // If not processed as a relation, recursively process the schema
      processedProperties[propName] =
        processed === propSchema ? this.processSchema(propSchema) : processed;
    }

    return {
      ...schema,
      properties: processedProperties,
    };
  }

  /**
   * Process response schemas with special handling for wrapper patterns
   */
  processResponseSchema(schema: JsonSchema): JsonSchema {
    if (!schema || typeof schema !== 'object') {
      return schema;
    }

    if (schema.$ref) {
      return schema;
    }

    // Check if this is a response wrapper (data + meta)
    if (this.isResponseWrapper(schema)) {
      return this.processResponseWrapper(schema);
    }

    // Check if this matches a component schema
    const matchingComponent = this.findMatchingComponent(schema);
    if (matchingComponent) {
      return { $ref: `#/components/schemas/${matchingComponent}` };
    }

    // Process normally
    return this.processSchema(schema);
  }

  /**
   * Check if schema is a paginated response wrapper
   */
  private isResponseWrapper(schema: JsonSchema): boolean {
    return !!(schema.type === 'object' && schema.properties?.data && schema.properties?.meta);
  }

  /**
   * Process response wrapper schemas
   */
  private processResponseWrapper(schema: JsonSchema): JsonSchema {
    const dataSchema = schema.properties?.data;
    let processedData: JsonSchema;

    // Handle array responses
    if (dataSchema.type === 'array' && dataSchema.items) {
      const processedItems = this.processItemSchema(dataSchema.items);
      processedData = {
        type: 'array',
        items: processedItems,
      };
    } else {
      // Handle single item responses
      processedData = this.processItemSchema(dataSchema);
    }

    return {
      ...schema,
      properties: {
        ...schema.properties,
        data: processedData,
      },
    };
  }

  /**
   * Process an item schema, attempting to match it to a component
   */
  private processItemSchema(schema: JsonSchema): JsonSchema {
    if (!schema || typeof schema !== 'object') {
      return schema;
    }

    if (schema.$ref) {
      return schema;
    }

    // Try to match to a component schema
    const matchingComponent = this.findMatchingComponent(schema);
    if (matchingComponent) {
      return { $ref: `#/components/schemas/${matchingComponent}` };
    }

    // Process normally
    return this.processSchema(schema);
  }

  /**
   * Find a matching component schema by property comparison
   */
  private findMatchingComponent(schema: JsonSchema): string | null {
    if (schema.type !== 'object' || !schema.properties) {
      return null;
    }

    const schemaProps = Object.keys(schema.properties).sort();

    for (const [componentName, componentSchema] of Object.entries(this.context.componentSchemas)) {
      if (componentSchema.type === 'object' && componentSchema.properties) {
        const componentProps = Object.keys(componentSchema.properties).sort();

        // If properties match, assume it's the same schema
        if (JSON.stringify(schemaProps) === JSON.stringify(componentProps)) {
          return componentName;
        }
      }
    }

    return null;
  }
}
