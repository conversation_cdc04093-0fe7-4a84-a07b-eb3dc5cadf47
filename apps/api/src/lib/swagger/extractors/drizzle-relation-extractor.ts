/**
 * Extract relation information from Drizzle relations
 * This provides proper mapping based on actual database relations
 * instead of guessing from schema structure
 */

export interface RelationInfo {
  field: string;
  targetEntity: string;
  type: 'one' | 'many';
}

/**
 * Extract relations from Drizzle relation definitions
 * @param relations The Drizzle relations object (e.g., organizationRelations)
 * @returns Array of relation information
 */
export function extractDrizzleRelations(relations: any): RelationInfo[] {
  if (!relations || typeof relations !== 'object') {
    return [];
  }

  const relationInfos: RelationInfo[] = [];

  // Drizzle relations have a config property that can be a function
  // We need to get the actual config by calling it if it's a function
  let config: any = null;

  if (typeof relations.config === 'function') {
    // The config function needs to be called with helpers
    try {
      // Create mock helpers for one and many relations with all required methods
      const helpers = {
        one: (table: any, config: any) => ({
          ...config,
          _relationBrand: 'one',
          referencedTable: table,
          withFieldName: (name: string) => ({
            ...config,
            fieldName: name,
            _relationBrand: 'one',
            referencedTable: table,
          }),
        }),
        many: (table: any, config: any) => ({
          ...config,
          _relationBrand: 'many',
          referencedTable: table,
          withFieldName: (name: string) => ({
            ...config,
            fieldName: name,
            _relationBrand: 'many',
            referencedTable: table,
          }),
        }),
      };
      config = relations.config(helpers);
    } catch (_e) {
      // Try alternative extraction method for newer Drizzle versions
      try {
        // Check if we can access the fields directly from the relations object
        if (
          relations.creator ||
          relations.organization ||
          relations.workspace ||
          relations.project
        ) {
          config = {};
          for (const [key, value] of Object.entries(relations)) {
            if (key !== 'table' && key !== 'config' && typeof value === 'object' && value) {
              config[key] = value;
            }
          }
        }
      } catch (_e2) {
        // Failed to extract config
      }
    }
  } else if (relations.config && typeof relations.config === 'object') {
    config = relations.config;
  }

  // If we still don't have config, check if relations itself contains the field definitions
  if (!config) {
    // Sometimes the relations are directly on the object
    for (const [key, value] of Object.entries(relations)) {
      if (
        key !== 'table' &&
        key !== 'config' &&
        key !== 'constructor' &&
        !key.startsWith('_') &&
        value &&
        typeof value === 'object' &&
        'referencedTable' in value
      ) {
        config = { [key]: value };
        break;
      }
    }
  }

  if (!config || typeof config !== 'object') {
    return [];
  }

  // Process each relation in the config
  for (const [fieldName, relationDef] of Object.entries(config)) {
    if (relationDef && typeof relationDef === 'object' && 'referencedTable' in relationDef) {
      const targetTableName = extractTableName(relationDef.referencedTable);
      if (targetTableName) {
        const relationType = (relationDef as any)._relationBrand === 'many' ? 'many' : 'one';

        relationInfos.push({
          field: fieldName,
          targetEntity: tableNameToEntityName(targetTableName),
          type: relationType,
        });
      }
    }
  }

  return relationInfos;
}

/**
 * Extract table name from a Drizzle table object
 * Drizzle tables have their name stored in various places depending on the version
 */
function extractTableName(table: any): string | null {
  if (!table || typeof table !== 'object') {
    return null;
  }

  // Try different ways to get the table name
  // 1. Direct property access (most common)
  if (table._ && typeof table._ === 'string') {
    return table._;
  }

  // 2. Symbol property (used in newer Drizzle versions)
  const drizzleSymbol = Symbol.for('drizzle:Name');
  if (table[drizzleSymbol]) {
    return table[drizzleSymbol];
  }

  // 3. Check for name property
  if (table.name && typeof table.name === 'string') {
    return table.name;
  }

  // 4. For PgTable instances, the name might be in config
  if (table.config?.name) {
    return table.config.name;
  }

  return null;
}

/**
 * Convert Drizzle table name to entity name
 * @param tableName The table name (e.g., 'user', 'work_item', 'workspace')
 * @returns The entity name (e.g., 'User', 'WorkItem', 'Workspace')
 */
function tableNameToEntityName(tableName: string): string {
  if (!tableName) {
    return '';
  }

  // Handle snake_case to PascalCase
  if (tableName.includes('_')) {
    return tableName
      .split('_')
      .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
      .join('');
  }

  // Handle kebab-case to PascalCase
  if (tableName.includes('-')) {
    return tableName
      .split('-')
      .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
      .join('');
  }

  // Handle simple camelCase to PascalCase
  return tableName.charAt(0).toUpperCase() + tableName.slice(1);
}

/**
 * Create a relation map from Drizzle relations for a specific entity
 * @param relations The Drizzle relations object
 * @param entityName The name of the entity (for debugging)
 * @returns Record mapping field names to entity names
 */
export function createRelationMap(relations: any, _entityName?: string): Record<string, string> {
  const relationInfos = extractDrizzleRelations(relations);
  const relationMap: Record<string, string> = {};

  for (const info of relationInfos) {
    relationMap[info.field] = info.targetEntity;
  }

  return relationMap;
}
