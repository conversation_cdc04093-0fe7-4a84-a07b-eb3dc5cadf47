// Centralized error handling system
import type { FastifyReply, FastifyRequest } from 'fastify';

export const ErrorCode = {
  // Client Errors (4xx)
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  DUPLICATE_VALUE: 'DUPLICATE_VALUE',
  NOT_FOUND: 'NOT_FOUND',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  CONFLICT: 'CONFLICT',

  // Server Errors (5xx)
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
} as const;

export type ErrorCode = (typeof ErrorCode)[keyof typeof ErrorCode];

export class ApiError extends Error {
  readonly code: ErrorCode;
  readonly details?: any;
  readonly cause?: Error;

  constructor(
    readonly statusCode: number,
    message: string,
    code?: ErrorCode,
    details?: any,
    cause?: Error,
  ) {
    super(message);
    this.name = 'ApiError';
    this.code = code || this.getDefaultErrorCode(statusCode);
    this.details = details;
    this.cause = cause;
  }

  private getDefaultErrorCode(statusCode: number): ErrorCode {
    switch (statusCode) {
      case 400:
        return ErrorCode.VALIDATION_ERROR;
      case 401:
        return ErrorCode.UNAUTHORIZED;
      case 403:
        return ErrorCode.FORBIDDEN;
      case 404:
        return ErrorCode.NOT_FOUND;
      case 409:
        return ErrorCode.CONFLICT;
      default:
        return ErrorCode.INTERNAL_SERVER_ERROR;
    }
  }

  static badRequest(message: string, details?: any): ApiError {
    return new ApiError(400, message, ErrorCode.VALIDATION_ERROR, details);
  }

  static notFound(resource: string, id?: string): ApiError {
    return new ApiError(
      404,
      `${resource}${id ? ` with id ${id}` : ''} not found`,
      ErrorCode.NOT_FOUND,
    );
  }

  static conflict(message: string, details?: any): ApiError {
    return new ApiError(409, message, ErrorCode.CONFLICT, details);
  }

  static duplicate(field: string, value: string): ApiError {
    return new ApiError(400, `${field} '${value}' already exists`, ErrorCode.DUPLICATE_VALUE);
  }

  static internal(message: string, cause?: Error): ApiError {
    return new ApiError(500, message, ErrorCode.INTERNAL_SERVER_ERROR, undefined, cause);
  }

  static database(operation: string, cause?: Error): ApiError {
    return new ApiError(
      500,
      `Database operation failed: ${operation}`,
      ErrorCode.DATABASE_ERROR,
      undefined,
      cause,
    );
  }
}

export function handleDatabaseError(error: any, operation: string): ApiError {
  // PostgreSQL error codes
  switch (error.code) {
    case '23505': {
      // Unique constraint violation
      const match = error.message.match(/Key \(([^)]+)\)=\(([^)]+)\)/);
      if (match) {
        return ApiError.duplicate(match[1], match[2]);
      }
      return ApiError.duplicate('field', 'value');
    }

    case '23503': // Foreign key violation
      return new ApiError(400, 'Referenced resource not found', ErrorCode.VALIDATION_ERROR);

    case '42703': // Undefined column
      return new ApiError(400, 'Invalid field in request', ErrorCode.VALIDATION_ERROR);

    default:
      return ApiError.database(operation, error);
  }
}

export function createErrorHandler(): (
  error: any,
  request: FastifyRequest,
  reply: FastifyReply,
) => void {
  return (error: any, request: FastifyRequest, reply: FastifyReply) => {
    const correlationId = request.context?.correlationId || 'unknown';

    // Handle our custom ApiError
    if (error instanceof ApiError) {
      const response = {
        statusCode: error.statusCode,
        errorCode: error.code,
        message: error.message,
        correlationId,
        ...(error.details && { details: error.details }),
      };

      // Log error with context
      request.log.error(
        {
          correlationId,
          errorCode: error.code,
          statusCode: error.statusCode,
          operation: request.context?.operation,
          userId: request.context?.userId,
          cause: error.cause?.message,
        },
        error.message,
      );

      return reply.code(error.statusCode).send(response);
    }

    // Handle validation errors (already in place)
    if (error.validation) {
      // Convert to ApiError for consistency
      const apiError = ApiError.badRequest('Validation failed', error.validation);
      return createErrorHandler()(apiError, request, reply);
    }

    // Handle unexpected errors
    const apiError = ApiError.internal('An unexpected error occurred', error);
    return createErrorHandler()(apiError, request, reply);
  };
}
