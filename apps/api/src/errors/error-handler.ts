import type { FastifyError, FastifyReply, FastifyRequest } from 'fastify';
import { hasZodFastifySchemaValidationErrors } from 'fastify-type-provider-zod';
import { toErrorResponseSchema } from '../utils/crud/schemas';
import { ApiError, ErrorCode } from './api-error';

/**
 * Global error handler for Fastify application
 * Provides consistent error responses and proper logging
 */
export function globalErrorHandler(
  error: FastifyError,
  request: FastifyRequest,
  reply: FastifyReply,
) {
  // Log the error with context
  const context = {
    correlationId: request.context?.correlationId,
    method: request.method,
    url: request.url,
    userId: request.context?.userId,
    errorCode: error.code,
    errorName: error.name,
  };

  // Handle Zod validation errors
  if (hasZodFastifySchemaValidationErrors(error)) {
    request.log.warn({ error, ...context }, 'Validation error');
    return reply.code(400).send(toErrorResponseSchema(error));
  }

  // Handle our custom ApiError instances
  if (error instanceof ApiError) {
    const level = error.statusCode >= 500 ? 'error' : 'warn';
    request.log[level]({ error, ...context }, `API Error: ${error.message}`);

    return reply.code(error.statusCode).send({
      statusCode: error.statusCode,
      errorCode: error.code,
      message: error.message,
      ...(error.details && { details: error.details }),
    });
  }

  // Handle Fastify errors
  if ('statusCode' in error && typeof error.statusCode === 'number') {
    const level = error.statusCode >= 500 ? 'error' : 'warn';
    request.log[level]({ error, ...context }, `HTTP Error: ${error.message}`);

    return reply.code(error.statusCode).send({
      statusCode: error.statusCode,
      errorCode: ErrorCode.INTERNAL_SERVER_ERROR,
      message: error.message,
    });
  }

  // Handle unexpected errors
  request.log.error({ error, ...context }, 'Unexpected error');

  // Don't expose internal error details in production
  const isDevelopment = process.env.NODE_ENV === 'development';

  const response = {
    statusCode: 500,
    errorCode: ErrorCode.INTERNAL_SERVER_ERROR,
    message: isDevelopment ? error.message || 'Internal Server Error' : 'Internal Server Error',
    ...(isDevelopment && error.stack && { stack: error.stack }),
  };

  // Explicitly bypass any response schema validation for error responses
  reply.code(500).header('content-type', 'application/json').send(JSON.stringify(response));
}
