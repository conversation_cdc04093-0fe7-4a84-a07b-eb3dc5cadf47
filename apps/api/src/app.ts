import FastifyCors from "@fastify/cors";
import FastifyHelmet from "@fastify/helmet";
import FastifyJWT from "@fastify/jwt";
import Fastify, {
  type FastifyInstance,
  type FastifyServerOptions,
} from "fastify";
import {
  serializerCompiler,
  validatorCompiler,
} from "fastify-type-provider-zod";

import { config } from "./config";
import { globalErrorHandler } from "./errors/error-handler";
import { registerSwagger } from "./lib/swagger";
import { authenticate } from "./middleware/auth";
import { loggingMiddleware } from "./middleware/logging";
import { performanceMiddleware } from "./middleware/performance";
import dbPlugin from "./plugins/db";
import minioPlugin from "./plugins/minio";
import routes from "./routes";
import "./types/fastify-augmentation";

/**
 * Create and configure the Fastify application
 */
export async function createApp(
  options: FastifyServerOptions = {}
): Promise<FastifyInstance> {
  // Create Fastify instance
  const app = Fastify({
    logger: {
      level: config.server.logLevel,
      transport:
        config.server.nodeEnv === "development"
          ? {
              target: "pino-pretty",
              options: {
                translateTime: "HH:MM:ss Z",
                ignore: "pid,hostname",
              },
            }
          : undefined,
    },
    ...options,
  });

  // Set schema compiler and serializer
  app.setValidatorCompiler(validatorCompiler);
  app.setSerializerCompiler(serializerCompiler);

  // Decorate app with config
  app.decorate("config", config);

  // Log environment configuration
  app.log.info(
    {
      environment: config.server.nodeEnv,
      host: config.server.host,
      port: config.server.port,
      logLevel: config.server.logLevel,
    },
    "Starting Fastify application"
  );

  // Add middlewares using hooks
  app.addHook("onRequest", loggingMiddleware);
  app.addHook("onRequest", performanceMiddleware);

  // Register CORS
  await registerCors(app);

  // Add security headers
  await app.register(FastifyHelmet, {
    contentSecurityPolicy: false, // Disable CSP for API
  });

  // Register JWT
  await registerJWT(app);

  // Register database plugin
  await app.register(dbPlugin);

  // Register MinIO plugin
  await app.register(minioPlugin);

  // Register Swagger/OpenAPI documentation
  if (config.features.enableSwagger) {
    try {
      await registerSwagger(app);
    } catch (error) {
      app.log.error("Failed to register Swagger:", error);
      // Continue without Swagger if it fails
    }
  }

  // Global error handler
  app.setErrorHandler(globalErrorHandler);

  // Register application routes AFTER Swagger
  app.log.info("Registering application routes...");
  try {
    await app.register(routes);
    app.log.info("Routes registered successfully");
  } catch (err) {
    app.log.error("Failed to register routes:", err);
    throw err;
  }

  // Add redirect from /docs to /documentation
  app.get("/docs", async (_request, reply) => {
    return reply.redirect("/documentation");
  });

  // Skip app.ready() for now - it's hanging
  // try {
  //   await app.ready();
  //   app.log.info('App is ready');
  // } catch (err) {
  //   app.log.error('Failed to ready app:', err);
  //   throw err;
  // }

  return app;
}

/**
 * Configure CORS settings
 */
async function registerCors(app: FastifyInstance): Promise<void> {
  await app.register(FastifyCors, {
    origin: (origin, cb) => {
      // Allow requests with no origin (e.g., mobile apps, Postman)
      if (!origin) {
        cb(null, true);
        return;
      }

      // Check against allowed origins
      const allowedOrigins = config.cors.origins;
      if (
        allowedOrigins.includes("*") ||
        allowedOrigins.some((allowed: string) => {
          if (allowed.includes("*")) {
            // Handle wildcard subdomains (e.g., *.example.com)
            const pattern = allowed.replace("*", ".*");
            return new RegExp(`^${pattern}$`).test(origin);
          }
          return allowed === origin;
        })
      ) {
        cb(null, true);
      } else {
        cb(new Error("Not allowed by CORS"), false);
      }
    },
    credentials: config.cors.credentials,
    methods: config.cors.methods,
  });
}

/**
 * Configure JWT authentication
 */
async function registerJWT(app: FastifyInstance): Promise<void> {
  await app.register(FastifyJWT, {
    secret: config.auth.jwtSecret,
    sign: {
      expiresIn: config.auth.jwtExpiresIn,
    },
  });

  // Decorate fastify instance with authenticate method
  app.decorate("authenticate", authenticate);
}
