import { randomUUID } from 'node:crypto';
import type { FastifyReply, FastifyRequest } from 'fastify';

declare module 'fastify' {
  interface FastifyRequest {
    context?: {
      correlationId: string;
      userId?: string;
      resource?: string;
      operation?: string;
      startTime: number;
    };
  }
}

/**
 * Logging middleware that adds correlation IDs and structured logging context
 */
export async function loggingMiddleware(request: FastifyRequest, reply: FastifyReply) {
  // Generate or extract correlation ID
  const correlationId = (request.headers['x-correlation-id'] as string) || randomUUID();

  // Add correlation ID to response headers
  reply.header('x-correlation-id', correlationId);

  // Parse route information
  const routeParts = (request as any).routerPath?.split('/') || request.url.split('/');
  const resource = routeParts[3] || 'unknown'; // Skip /api/v1/
  const operation = `${request.method.toLowerCase()}:${resource}`;

  // Add context to request
  request.context = {
    correlationId,
    userId: (request as any).user?.id,
    resource,
    operation,
    startTime: Date.now(),
  };

  // Log request start
  request.log.info(
    {
      correlationId,
      method: request.method,
      url: request.url,
      userAgent: request.headers['user-agent'],
      ip: request.ip,
      userId: request.context.userId,
      resource,
      operation,
    },
    'Request started',
  );
}
