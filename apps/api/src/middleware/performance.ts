// Performance monitoring and metrics collection
import type { FastifyReply, FastifyRequest } from 'fastify';

interface PerformanceMetrics {
  requests: {
    total: number;
    byStatus: Record<number, number>;
    byEndpoint: Record<string, { count: number; avgDuration: number }>;
  };
  database: {
    queries: number;
    avgQueryTime: number;
  };
  errors: {
    total: number;
    byType: Record<string, number>;
  };
}

class MetricsCollector {
  private metrics: PerformanceMetrics = {
    requests: {
      total: 0,
      byStatus: {},
      byEndpoint: {},
    },
    database: {
      queries: 0,
      avgQueryTime: 0,
    },
    errors: {
      total: 0,
      byType: {},
    },
  };

  recordRequest(endpoint: string, statusCode: number, duration: number) {
    this.metrics.requests.total++;
    this.metrics.requests.byStatus[statusCode] =
      (this.metrics.requests.byStatus[statusCode] || 0) + 1;

    if (!this.metrics.requests.byEndpoint[endpoint]) {
      this.metrics.requests.byEndpoint[endpoint] = { count: 0, avgDuration: 0 };
    }

    const endpointMetrics = this.metrics.requests.byEndpoint[endpoint];
    endpointMetrics.avgDuration =
      (endpointMetrics.avgDuration * endpointMetrics.count + duration) /
      (endpointMetrics.count + 1);
    endpointMetrics.count++;
  }

  recordError(errorType: string) {
    this.metrics.errors.total++;
    this.metrics.errors.byType[errorType] = (this.metrics.errors.byType[errorType] || 0) + 1;
  }

  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  getHealthStatus() {
    const recentErrorRate = this.calculateErrorRate();
    const avgResponseTime = this.calculateAvgResponseTime();

    return {
      status: this.determineHealthStatus(recentErrorRate, avgResponseTime),
      metrics: {
        totalRequests: this.metrics.requests.total,
        errorRate: recentErrorRate,
        avgResponseTime,
      },
    };
  }

  private calculateErrorRate(): number {
    const total = this.metrics.requests.total;
    if (total === 0) {
      return 0;
    }

    const errors = Object.entries(this.metrics.requests.byStatus)
      .filter(([status]) => Number.parseInt(status, 10) >= 400)
      .reduce((sum, [, count]) => sum + count, 0);

    return (errors / total) * 100;
  }

  private calculateAvgResponseTime(): number {
    const endpoints = Object.values(this.metrics.requests.byEndpoint);
    if (endpoints.length === 0) {
      return 0;
    }

    const totalDuration = endpoints.reduce(
      (sum, endpoint) => sum + endpoint.avgDuration * endpoint.count,
      0,
    );
    const totalRequests = endpoints.reduce((sum, endpoint) => sum + endpoint.count, 0);

    return totalRequests > 0 ? totalDuration / totalRequests : 0;
  }

  private determineHealthStatus(
    errorRate: number,
    avgResponseTime: number,
  ): 'healthy' | 'warning' | 'critical' {
    if (errorRate > 10 || avgResponseTime > 2000) {
      return 'critical';
    }
    if (errorRate > 5 || avgResponseTime > 1000) {
      return 'warning';
    }
    return 'healthy';
  }
}

export const metricsCollector = new MetricsCollector();

export async function performanceMiddleware(request: FastifyRequest, _reply: FastifyReply) {
  const startTime = Date.now();
  const _endpoint = `${request.method} ${(request as any).routerPath || request.url}`;

  // Store start time for later use
  if (request.context) {
    request.context.startTime = startTime;
  } else {
    request.context = { correlationId: 'unknown', startTime };
  }

  // Note: Response timing will be logged in the onSend hook registered at server level
}

// Health check endpoint data
export function getHealthCheckData() {
  return {
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    ...metricsCollector.getHealthStatus(),
  };
}

// Performance report for monitoring
export function getPerformanceReport() {
  const metrics = metricsCollector.getMetrics();

  // Calculate top slow endpoints
  const slowEndpoints = Object.entries(metrics.requests.byEndpoint)
    .sort(([, a], [, b]) => b.avgDuration - a.avgDuration)
    .slice(0, 10)
    .map(([endpoint, data]) => ({
      endpoint,
      avgDuration: Math.round(data.avgDuration),
      count: data.count,
    }));

  return {
    summary: {
      totalRequests: metrics.requests.total,
      totalErrors: metrics.errors.total,
      errorRate: metricsCollector.getHealthStatus().metrics.errorRate,
    },
    slowEndpoints,
    errorsByType: metrics.errors.byType,
    statusCodes: metrics.requests.byStatus,
    generatedAt: new Date().toISOString(),
  };
}
