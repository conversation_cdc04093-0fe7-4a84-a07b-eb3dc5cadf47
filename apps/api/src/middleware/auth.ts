import { db } from '@repo/db';
import { user } from '@repo/db/schema';
import { eq } from 'drizzle-orm';
import type { FastifyReply, FastifyRequest } from 'fastify';
import { ApiError } from '../errors/api-error';
import { verifyAccessToken } from '../utils/auth/jwt';

// Module declaration moved to types/fastify-augmentation.ts

export async function authenticate(request: FastifyRequest, _reply: FastifyReply): Promise<void> {
  try {
    // Get token from Authorization header
    const authHeader = request.headers.authorization;
    if (!authHeader?.startsWith('Bearer ')) {
      throw new ApiError(401, 'Missing or invalid authorization header');
    }

    const token = authHeader.substring(7);

    // Verify token
    const payload = verifyAccessToken(token);

    // Get user from database to check if still active
    const dbUser = await db.query.user.findFirst({
      where: eq(user.id, payload.userId),
      columns: {
        id: true,
        email: true,
        isActive: true,
        isStaff: true,
        password: false,
      },
    });

    if (!dbUser?.isActive) {
      throw new ApiError(401, 'User not found or inactive');
    }

    // Attach user to request
    request.user = {
      id: payload.userId, // Add id field for consistency
      ...payload,
      isActive: dbUser.isActive,
      isStaff: dbUser.isStaff,
    };
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError(401, 'Invalid or expired token');
  }
}

export async function requireAuth(request: FastifyRequest, reply: FastifyReply): Promise<void> {
  await authenticate(request, reply);
}

export async function requireStaff(request: FastifyRequest, reply: FastifyReply): Promise<void> {
  await authenticate(request, reply);

  if (!request.user?.isStaff) {
    throw new ApiError(403, 'Staff access required');
  }
}

export async function optionalAuth(request: FastifyRequest, reply: FastifyReply): Promise<void> {
  try {
    await authenticate(request, reply);
  } catch (_error) {
    // Ignore authentication errors for optional auth
    // Don't set request.user to undefined, just leave it unset
  }
}
