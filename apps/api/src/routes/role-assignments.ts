import { db } from '@repo/db';
import {
  insertRoleAssignmentSchema,
  patchRoleAssignmentSchema,
  roleAssignment,
  roleAssignmentRelations,
  selectRoleAssignmentSchema,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { roleAssignmentService } from '../services/role-assignment-service';
import { createCrudRouter } from '../utils/crud/create-crud-router';

// Use the extended schema that includes the enriched fields
// This matches what the service returns after enrichment

/**
 * Role Assignment routes plugin
 * Will be automatically loaded and registered at /api/v1/role-assignments
 */
export default async function roleAssignmentRoutes(fastify: FastifyInstance) {
  // Use the createCrudRouter to automatically generate role assignment routes
  const roleAssignmentRouter = createCrudRouter({
    // Resource information
    name: ['role-assignment', 'role-assignments'],
    selectSchema: selectRoleAssignmentSchema,
    insertSchema: insertRoleAssignmentSchema,
    patchSchema: patchRoleAssignmentSchema,
    table: roleAssignment,
    queryApi: db.query.roleAssignment,
    relations: roleAssignmentRelations,
    with: {
      user: {
        columns: {
          id: true,
          email: true,
          displayName: true,
        },
      },
      role: {
        columns: {
          id: true,
          name: true,
          identifier: true,
        },
      },
    },

    // Service integration
    service: roleAssignmentService,

    // Response transformation to include scope objects
    postFindMany: async (data: any) => {
      return await roleAssignmentService.enrichMultipleWithScopeObjects(data);
    },
    postFindOne: async (data: any) => {
      return await roleAssignmentService.enrichWithScopeObject(data);
    },

    // Route configuration
    tags: ['Role Assignments'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [{ field: 'createdAt', order: 'desc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,
  });

  // Register the CRUD router
  await fastify.register(roleAssignmentRouter);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/role-assignments';
