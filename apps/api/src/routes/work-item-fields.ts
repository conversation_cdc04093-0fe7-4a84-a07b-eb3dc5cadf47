import { db } from '@repo/db';
import {
  insertWorkItemFieldSchema,
  patchWorkItemFieldSchema,
  selectWorkItemFieldSchema,
  workItemField,
  workItemFieldRelations,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { createCrudRouter } from '../utils/crud/create-crud-router';

/**
 * Work Item Field routes plugin
 * Will be automatically loaded and registered at /api/v1/work-item-fields
 */
export default async function workItemFieldRoutes(fastify: FastifyInstance) {
  // Use the createCrudRouter to automatically generate work item field routes
  const workItemFieldRouter = createCrudRouter({
    // Resource information
    name: ['work-item-field', 'work-item-fields'],
    selectSchema: selectWorkItemFieldSchema,
    insertSchema: insertWorkItemFieldSchema,
    patchSchema: patchWorkItemFieldSchema,
    table: workItemField,
    queryApi: db.query.workItemField,
    relations: workItemFieldRelations,
    with: {
      workItemType: {
        columns: {
          id: true,
          name: true,
        },
      },
      organization: {
        columns: {
          id: true,
          name: true,
          slug: true,
        },
      },
    },

    // Route configuration
    tags: ['Work Item Fields'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [{ field: 'order', order: 'asc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,
  });

  // Register the CRUD router
  await fastify.register(workItemFieldRouter);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/work-item-fields';
