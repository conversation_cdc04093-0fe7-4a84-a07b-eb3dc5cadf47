import { db } from '@repo/db';
import { createUserFcmTokenSchema, selectUserFcmTokenSchema, userFcmToken } from '@repo/db/schema';
import { and, eq } from 'drizzle-orm';
import type { FastifyInstance } from 'fastify';
import type { ZodTypeProvider } from 'fastify-type-provider-zod';
import { z } from 'zod';
import { requireAuth } from '../middleware/auth';
import { errorResponseSchema } from '../utils/crud/schemas';

const createTokenRequestSchema = createUserFcmTokenSchema;
const createTokenResponseSchema = selectUserFcmTokenSchema;

export const autoPrefix = '/user-fcm-tokens';

export default async function userFcmTokenRoutes(fastify: FastifyInstance) {
  // Create FCM token
  fastify.withTypeProvider<ZodTypeProvider>().post(
    '/',
    {
      preValidation: [requireAuth],
      schema: {
        body: createTokenRequestSchema,
        response: {
          201: createTokenResponseSchema,
          400: errorResponseSchema,
          401: errorResponseSchema,
          500: errorResponseSchema,
        },
        tags: ['User FCM Tokens'],
        summary: 'Register FCM token',
        description: 'Register a new FCM token for the authenticated user',
        security: [{ bearerAuth: [] }],
      },
    },
    async (request, reply) => {
      const userId = request.user?.userId;
      if (!userId) {
        return reply.code(401).send({
          statusCode: 401,
          errorCode: 'UNAUTHORIZED',
          message: 'User ID is required',
        });
      }

      const data = request.body;

      // Check if the token already exists for this user
      const existing = await db
        .select()
        .from(userFcmToken)
        .where(and(eq(userFcmToken.userId, userId), eq(userFcmToken.fcmToken, data.fcmToken)))
        .limit(1);

      if (existing.length > 0) {
        // Update existing token
        const [updated] = await db
          .update(userFcmToken)
          .set({
            deviceType: data.deviceType,
            deviceModel: data.deviceModel,
            os: data.os,
            browser: data.browser,
            lastActive: new Date(),
            updatedAt: new Date(),
          })
          .where(eq(userFcmToken.id, existing[0].id))
          .returning();

        return reply.code(200).send(updated);
      }

      // Create new token
      const [newToken] = await db
        .insert(userFcmToken)
        .values({
          userId,
          fcmToken: data.fcmToken,
          deviceType: data.deviceType,
          deviceModel: data.deviceModel,
          os: data.os,
          browser: data.browser,
        })
        .returning();

      return reply.code(201).send(newToken);
    },
  );

  // Get user's FCM tokens
  fastify.withTypeProvider<ZodTypeProvider>().get(
    '/',
    {
      preValidation: [requireAuth],
      schema: {
        response: {
          200: z.array(selectUserFcmTokenSchema),
          401: errorResponseSchema,
          500: errorResponseSchema,
        },
        tags: ['User FCM Tokens'],
        summary: 'Get user FCM tokens',
        description: 'Get all FCM tokens for the authenticated user',
        security: [{ bearerAuth: [] }],
      },
    },
    async (request, reply) => {
      const userId = request.user?.userId;
      if (!userId) {
        return reply.code(401).send({
          statusCode: 401,
          errorCode: 'UNAUTHORIZED',
          message: 'User ID is required',
        });
      }

      const tokens = await db
        .select()
        .from(userFcmToken)
        .where(eq(userFcmToken.userId, userId))
        .orderBy(userFcmToken.lastActive);

      return reply.send(tokens);
    },
  );

  // Delete FCM token
  fastify.withTypeProvider<ZodTypeProvider>().delete(
    '/:id',
    {
      preValidation: [requireAuth],
      schema: {
        params: z.object({
          id: z.string().uuid(),
        }),
        response: {
          204: z.void(),
          401: errorResponseSchema,
          404: errorResponseSchema,
          500: errorResponseSchema,
        },
        tags: ['User FCM Tokens'],
        summary: 'Delete FCM token',
        description: 'Delete an FCM token',
        security: [{ bearerAuth: [] }],
      },
    },
    async (request, reply) => {
      const userId = request.user?.userId;
      const { id } = request.params;

      if (!userId) {
        return reply.code(401).send({
          statusCode: 401,
          errorCode: 'UNAUTHORIZED',
          message: 'User ID is required',
        });
      }

      const _result = await db
        .delete(userFcmToken)
        .where(and(eq(userFcmToken.id, id), eq(userFcmToken.userId, userId)));

      return reply.code(204).send();
    },
  );
}
