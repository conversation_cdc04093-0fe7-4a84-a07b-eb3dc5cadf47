import { db } from '@repo/db';
import {
  insertWorkflowSchema,
  patchWorkflowSchema,
  selectWorkflowSchema,
  workflow,
  workflowRelations,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { createCrudRouter } from '../utils/crud/create-crud-router';

/**
 * Workflow routes plugin
 * Will be automatically loaded and registered at /api/v1/workflows
 */
export default async function workflowRoutes(fastify: FastifyInstance) {
  // Use the createCrudRouter to automatically generate workflow routes
  const workflowRouter = createCrudRouter({
    // Resource information
    name: ['workflow', 'workflows'],
    selectSchema: selectWorkflowSchema,
    insertSchema: insertWorkflowSchema,
    patchSchema: patchWorkflowSchema,
    table: workflow,
    queryApi: db.query.workflow,
    relations: workflowRelations,
    with: {
      organization: {
        columns: {
          id: true,
          name: true,
          slug: true,
        },
      },
      creator: {
        columns: {
          id: true,
          email: true,
          displayName: true,
        },
      },
    },

    // Route configuration
    tags: ['Workflows'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [{ field: 'createdAt', order: 'desc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,
  });

  // Register the CRUD router
  await fastify.register(workflowRouter);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/workflows';
