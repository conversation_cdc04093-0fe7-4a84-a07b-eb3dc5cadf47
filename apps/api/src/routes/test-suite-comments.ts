import { db } from '@repo/db';
import {
  insertTestSuiteCommentSchema,
  patchTestSuiteCommentSchema,
  selectTestSuiteCommentSchema,
  testSuiteComment,
  testSuiteCommentRelations,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { createCrudRouter } from '../utils/crud/create-crud-router';

export default async function testSuiteCommentRoutes(fastify: FastifyInstance) {
  const testSuiteCommentRouter = createCrudRouter({
    // Resource information
    name: ['test suite comment', 'test suite comments'],
    selectSchema: selectTestSuiteCommentSchema,
    insertSchema: insertTestSuiteCommentSchema,
    patchSchema: patchTestSuiteCommentSchema,
    table: testSuiteComment,
    queryApi: db.query.testSuiteComment,
    relations: testSuiteCommentRelations,
    with: {
      testSuite: {
        columns: {
          id: true,
          name: true,
          testPlanId: true,
        },
      },
      author: {
        columns: {
          id: true,
          email: true,
          displayName: true,
          avatarUrl: true,
        },
      },
      parent: {
        columns: {
          id: true,
          content: true,
          authorId: true,
        },
      },
      replies: {
        columns: {
          id: true,
          content: true,
          authorId: true,
          isEdited: true,
          isDeleted: true,
          createdAt: true,
          updatedAt: true,
        },
        with: {
          author: {
            columns: {
              id: true,
              email: true,
              displayName: true,
              avatarUrl: true,
            },
          },
        },
      },
    },

    // Route configuration
    tags: ['Test Suite Comments'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [{ field: 'createdAt', order: 'asc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,
  });

  await fastify.register(testSuiteCommentRouter);
}

export const autoPrefix = '/test-suite-comments';
