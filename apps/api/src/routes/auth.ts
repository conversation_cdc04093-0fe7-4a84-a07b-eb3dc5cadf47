import { db } from '@repo/db';
import { user } from '@repo/db/schema';
import { eq } from 'drizzle-orm';
import type { FastifyPluginAsync } from 'fastify';
import type { ZodTypeProvider } from 'fastify-type-provider-zod';
import { z } from 'zod';
import { ApiError } from '../errors/api-error';
import { requireAuth } from '../middleware/auth';
import { authService } from '../services/auth-service';
import { fcmService } from '../services/fcm-service';
import { errorResponseSchema } from '../utils/crud/schemas';
import {
  authResponseSchema,
  type ChangePasswordInput,
  changePasswordSchema,
  type ForgotPasswordInput,
  forgotPasswordSchema,
  type LoginInput,
  loginSchema,
  messageResponseSchema,
  type RefreshTokenInput,
  type RegisterInput,
  type ResendVerificationInput,
  type ResetPasswordInput,
  refreshTokenSchema,
  registerSchema,
  resendVerificationSchema,
  resetPasswordSchema,
  tokenResponseSchema,
  userResponseSchema,
  type VerifyEmailInput,
  verifyEmailSchema,
} from '../validation/auth';

// Logout schema for FCM token handling
const logoutSchema = z.object({
  fcmToken: z.string().optional(),
});

const authRoutes: FastifyPluginAsync = async (fastify) => {
  // Register new user
  fastify.withTypeProvider<ZodTypeProvider>().post(
    '/register',
    {
      schema: {
        body: registerSchema,
        response: {
          200: authResponseSchema,
          404: errorResponseSchema,
          500: errorResponseSchema,
          401: errorResponseSchema,
          403: errorResponseSchema,
        },
        tags: ['Auth'],
        summary: 'Register a new user',
        description: 'Create a new user account with email and password',
      },
    },
    async (request, reply) => {
      const result = await authService.register(request.body as RegisterInput);
      reply.code(201).send(result);
    },
  );

  // Login
  fastify.withTypeProvider<ZodTypeProvider>().post(
    '/login',
    {
      schema: {
        body: loginSchema,
        response: {
          200: authResponseSchema,
          404: errorResponseSchema,
          500: errorResponseSchema,
          401: errorResponseSchema,
          403: errorResponseSchema,
        },
        tags: ['Auth'],
        summary: 'User login',
        description: 'Authenticate user with email and password',
      },
    },
    async (request, reply) => {
      const result = await authService.login(request.body as LoginInput);
      reply.send(result);
    },
  );

  // Refresh token
  fastify.withTypeProvider<ZodTypeProvider>().post(
    '/refresh',
    {
      schema: {
        body: refreshTokenSchema,
        response: {
          200: tokenResponseSchema,
          404: errorResponseSchema,
          500: errorResponseSchema,
          401: errorResponseSchema,
          403: errorResponseSchema,
        },
        tags: ['Auth'],
        summary: 'Refresh access token',
        description: 'Get a new access token using a refresh token',
      },
    },
    async (request, reply) => {
      const { refreshToken } = request.body as RefreshTokenInput;
      const tokens = await authService.refreshToken(refreshToken);
      reply.send(tokens);
    },
  );

  // Forgot password
  fastify.withTypeProvider<ZodTypeProvider>().post(
    '/forgot-password',
    {
      schema: {
        body: forgotPasswordSchema,
        response: {
          200: messageResponseSchema,
          404: errorResponseSchema,
          500: errorResponseSchema,
          401: errorResponseSchema,
          403: errorResponseSchema,
        },
        tags: ['Auth'],
        summary: 'Request password reset',
        description: 'Send password reset email to user',
      },
    },
    async (request, reply) => {
      const { email } = request.body as ForgotPasswordInput;
      await authService.forgotPassword(email);
      reply.send({
        message: 'If the email exists, a password reset link has been sent',
      });
    },
  );

  // Reset password
  fastify.withTypeProvider<ZodTypeProvider>().post(
    '/reset-password',
    {
      schema: {
        body: resetPasswordSchema,
        response: {
          200: messageResponseSchema,
          404: errorResponseSchema,
          500: errorResponseSchema,
          401: errorResponseSchema,
          403: errorResponseSchema,
        },
        tags: ['Auth'],
        summary: 'Reset password',
        description: 'Reset user password using reset token',
      },
    },
    async (request, reply) => {
      const { token, password } = request.body as ResetPasswordInput;
      await authService.resetPassword(token, password);
      reply.send({ message: 'Password has been reset successfully' });
    },
  );

  // Verify email
  fastify.withTypeProvider<ZodTypeProvider>().post(
    '/verify-email',
    {
      schema: {
        body: verifyEmailSchema,
        response: {
          200: messageResponseSchema,
          404: errorResponseSchema,
          500: errorResponseSchema,
          401: errorResponseSchema,
          403: errorResponseSchema,
        },
        tags: ['Auth'],
        summary: 'Verify email address',
        description: 'Verify user email using verification token',
      },
    },
    async (request, reply) => {
      const { token } = request.body as VerifyEmailInput;
      await authService.verifyEmail(token);
      reply.send({ message: 'Email verified successfully' });
    },
  );

  // Change password (requires authentication)
  fastify.withTypeProvider<ZodTypeProvider>().post(
    '/change-password',
    {
      preValidation: [requireAuth],
      schema: {
        body: changePasswordSchema,
        response: {
          200: messageResponseSchema,
          404: errorResponseSchema,
          500: errorResponseSchema,
          401: errorResponseSchema,
          403: errorResponseSchema,
        },
        tags: ['Auth'],
        summary: 'Change password',
        description: 'Change user password (requires authentication)',
        security: [{ bearerAuth: [] }],
      },
    },
    async (request, reply) => {
      if (!request.user) {
        throw new ApiError(401, 'Unauthorized');
      }

      const { currentPassword, newPassword } = request.body as ChangePasswordInput;
      await authService.changePassword(request.user.userId, currentPassword, newPassword);
      reply.send({ message: 'Password changed successfully' });
    },
  );

  // Resend verification email
  fastify.withTypeProvider<ZodTypeProvider>().post(
    '/resend-verification',
    {
      schema: {
        body: resendVerificationSchema,
        response: {
          200: messageResponseSchema,
          404: errorResponseSchema,
          500: errorResponseSchema,
          401: errorResponseSchema,
          403: errorResponseSchema,
        },
        tags: ['Auth'],
        summary: 'Resend verification email',
        description: 'Resend email verification link',
      },
    },
    async (request, reply) => {
      const { email } = request.body as ResendVerificationInput;
      await authService.resendVerificationEmail(email);
      reply.send({
        message: 'If the email exists, a verification link has been sent',
      });
    },
  );

  // Get current user (requires authentication)
  fastify.withTypeProvider<ZodTypeProvider>().get(
    '/me',
    {
      preValidation: [requireAuth],
      schema: {
        response: {
          200: userResponseSchema,
          404: errorResponseSchema,
          500: errorResponseSchema,
          401: errorResponseSchema,
          403: errorResponseSchema,
        },
        tags: ['Auth'],
        summary: 'Get current user',
        description: 'Get authenticated user information',
        security: [{ bearerAuth: [] }],
      },
    },
    async (request, reply) => {
      if (!request.user) {
        throw new ApiError(401, 'Unauthorized');
      }

      // Get user from database
      const currentUser = await db.query.user.findFirst({
        where: eq(user.id, request.user.userId),
        columns: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          displayName: true,
          isEmailVerified: true,
          isActive: true,
          isStaff: true,
          lastLogin: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!currentUser) {
        throw new ApiError(404, 'User not found');
      }

      reply.send({
        ...currentUser,
        createdAt: currentUser.createdAt.toISOString(),
        updatedAt: currentUser.updatedAt.toISOString(),
        lastLogin: currentUser.lastLogin?.toISOString() || null,
      });
    },
  );

  // Logout (client-side token removal, but we can use this for logging)
  fastify.withTypeProvider<ZodTypeProvider>().post(
    '/logout',
    {
      preValidation: [requireAuth],
      schema: {
        body: logoutSchema,
        response: {
          200: messageResponseSchema,
          404: errorResponseSchema,
          500: errorResponseSchema,
          401: errorResponseSchema,
          403: errorResponseSchema,
        },
        tags: ['Auth'],
        summary: 'Logout user',
        description: 'Logout authenticated user',
        security: [{ bearerAuth: [] }],
      },
    },
    async (request, reply) => {
      const { fcmToken } = request.body as z.infer<typeof logoutSchema>;
      const userId = request.user?.userId;

      // Handle FCM token cleanup and notification
      if (fcmToken && userId) {
        try {
          await fcmService.sendLogoutNotification(fcmToken, userId);
        } catch (error) {
          request.log.warn({ error, userId, fcmToken }, 'Failed to send logout notification');
          // Don't fail the logout if FCM notification fails
        }
      }

      request.log.info({ userId }, 'User logged out');
      reply.send({ message: 'Logged out successfully' });
    },
  );
};

export default authRoutes;
export const autoPrefix = '/auth';
