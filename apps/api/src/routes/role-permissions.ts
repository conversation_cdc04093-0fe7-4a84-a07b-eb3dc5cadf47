import { db } from '@repo/db';
import { permission, role, rolePermission } from '@repo/db/schema';
import { and, eq, inArray } from 'drizzle-orm';
import type { FastifyInstance } from 'fastify';
import { z } from 'zod';
import { ApiError } from '../errors/api-error';

const rolePermissionsQuerySchema = z.object({
  roleId: z.string().uuid(),
});

const updateRolePermissionsSchema = z.object({
  permissionIds: z.array(z.string().uuid()),
});

export default async function rolePermissionRoutes(app: FastifyInstance) {
  // Get all permissions for a role
  app.get<{
    Querystring: z.infer<typeof rolePermissionsQuerySchema>;
  }>(
    '/',
    {
      schema: {
        querystring: rolePermissionsQuerySchema,
        response: {
          200: z.array(
            z.object({
              id: z.string().uuid(),
              identifier: z.string(),
              name: z.string(),
              description: z.string().nullable(),
              category: z.string(),
              scopeType: z.string(),
            }),
          ),
        },
      },
    },
    async (request, reply) => {
      const { roleId } = request.query;

      // Verify role exists
      const roleExists = await db.query.role.findFirst({
        where: eq(role.id, roleId),
      });

      if (!roleExists) {
        throw new ApiError(404, 'Role not found');
      }

      // Get permissions for the role
      const permissions = await db
        .select({
          id: permission.id,
          identifier: permission.identifier,
          name: permission.name,
          description: permission.description,
          category: permission.category,
          scopeType: permission.scopeType,
        })
        .from(rolePermission)
        .innerJoin(permission, eq(rolePermission.permissionId, permission.id))
        .where(eq(rolePermission.roleId, roleId))
        .orderBy(permission.category, permission.name);

      return reply.send(permissions);
    },
  );

  // Update permissions for a role (replace all)
  app.put<{
    Params: { id: string };
    Body: z.infer<typeof updateRolePermissionsSchema>;
  }>(
    '/:id/permissions',
    {
      schema: {
        params: z.object({ id: z.string().uuid() }),
        body: updateRolePermissionsSchema,
        response: {
          200: z.object({
            message: z.string(),
            count: z.number(),
          }),
        },
      },
    },
    async (request, reply) => {
      const roleId = request.params.id;
      const { permissionIds } = request.body;

      // Verify role exists
      const roleExists = await db.query.role.findFirst({
        where: eq(role.id, roleId),
      });

      if (!roleExists) {
        throw new ApiError(404, 'Role not found');
      }

      // Verify system roles cannot be modified
      if (roleExists.isSystem) {
        throw new ApiError(403, 'System roles cannot be modified');
      }

      // Verify all permissions exist and match the role's scope
      if (permissionIds.length > 0) {
        const permissions = await db.query.permission.findMany({
          where: inArray(permission.id, permissionIds),
        });

        if (permissions.length !== permissionIds.length) {
          throw new ApiError(404, 'One or more permissions not found');
        }

        // Check scope compatibility
        const roleScope = roleExists.level === 'organization' ? 'org' : 'ws';
        const invalidPermissions = permissions.filter((p) => p.scopeType !== roleScope);

        if (invalidPermissions.length > 0) {
          throw new ApiError(
            400,
            `Invalid permissions for ${roleExists.level} role: ${invalidPermissions.map((p: any) => p.identifier).join(', ')}`,
          );
        }
      }

      // Start transaction
      await db.transaction(async (tx: any) => {
        // Delete existing permissions
        await tx.delete(rolePermission).where(eq(rolePermission.roleId, roleId));

        // Insert new permissions
        if (permissionIds.length > 0) {
          await tx.insert(rolePermission).values(
            permissionIds.map((permissionId) => ({
              roleId,
              permissionId,
            })),
          );
        }
      });

      return reply.send({
        message: 'Permissions updated successfully',
        count: permissionIds.length,
      });
    },
  );

  // Add specific permissions to a role
  app.post<{
    Params: { id: string };
    Body: { permissionIds: string[] };
  }>(
    '/:id/permissions',
    {
      schema: {
        params: z.object({ id: z.string().uuid() }),
        body: z.object({ permissionIds: z.array(z.string().uuid()) }),
        response: {
          201: z.object({
            message: z.string(),
            added: z.number(),
          }),
        },
      },
    },
    async (request, reply) => {
      const roleId = request.params.id;
      const { permissionIds } = request.body;

      if (permissionIds.length === 0) {
        return reply.code(201).send({ message: 'No permissions to add', added: 0 });
      }

      // Verify role exists
      const roleExists = await db.query.role.findFirst({
        where: eq(role.id, roleId),
      });

      if (!roleExists) {
        throw new ApiError(404, 'Role not found');
      }

      // Verify system roles cannot be modified
      if (roleExists.isSystem) {
        throw new ApiError(403, 'System roles cannot be modified');
      }

      // Get existing permissions
      const existing = await db
        .select({ permissionId: rolePermission.permissionId })
        .from(rolePermission)
        .where(eq(rolePermission.roleId, roleId));

      const existingIds = new Set(existing.map((e) => e.permissionId));
      const newPermissionIds = permissionIds.filter((id) => !existingIds.has(id));

      if (newPermissionIds.length === 0) {
        return reply.code(201).send({ message: 'All permissions already assigned', added: 0 });
      }

      // Verify new permissions exist and have correct scope
      const permissions = await db.query.permission.findMany({
        where: inArray(permission.id, newPermissionIds),
      });

      if (permissions.length !== newPermissionIds.length) {
        throw new ApiError(404, 'One or more permissions not found');
      }

      // Check scope compatibility
      const roleScope = roleExists.level === 'organization' ? 'org' : 'ws';
      const invalidPermissions = permissions.filter((p) => p.scopeType !== roleScope);

      if (invalidPermissions.length > 0) {
        throw new ApiError(
          400,
          `Invalid permissions for ${roleExists.level} role: ${invalidPermissions.map((p: any) => p.identifier).join(', ')}`,
        );
      }

      // Add new permissions
      await db.insert(rolePermission).values(
        newPermissionIds.map((permissionId) => ({
          roleId,
          permissionId,
        })),
      );

      return reply.code(201).send({
        message: 'Permissions added successfully',
        added: newPermissionIds.length,
      });
    },
  );

  // Remove specific permissions from a role
  app.delete<{
    Params: { id: string };
    Body: { permissionIds: string[] };
  }>(
    '/:id/permissions',
    {
      schema: {
        params: z.object({ id: z.string().uuid() }),
        body: z.object({ permissionIds: z.array(z.string().uuid()) }),
        response: {
          200: z.object({
            message: z.string(),
            removed: z.number(),
          }),
        },
      },
    },
    async (request, reply) => {
      const roleId = request.params.id;
      const { permissionIds } = request.body;

      if (permissionIds.length === 0) {
        return reply.send({ message: 'No permissions to remove', removed: 0 });
      }

      // Verify role exists
      const roleExists = await db.query.role.findFirst({
        where: eq(role.id, roleId),
      });

      if (!roleExists) {
        throw new ApiError(404, 'Role not found');
      }

      // Verify system roles cannot be modified
      if (roleExists.isSystem) {
        throw new ApiError(403, 'System roles cannot be modified');
      }

      // Remove permissions
      const _result = await db
        .delete(rolePermission)
        .where(
          and(
            eq(rolePermission.roleId, roleId),
            inArray(rolePermission.permissionId, permissionIds),
          ),
        );

      return reply.send({
        message: 'Permissions removed successfully',
        removed: permissionIds.length,
      });
    },
  );
}

export const autoPrefix = '/api/v1/role-permissions';
