import { db } from '@repo/db';
import {
  insertTestPlanCommentSchema,
  patchTestPlanCommentSchema,
  selectTestPlanCommentSchema,
  testPlanComment,
  testPlanCommentRelations,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { createCrudRouter } from '../utils/crud/create-crud-router';

export default async function testPlanCommentRoutes(fastify: FastifyInstance) {
  const testPlanCommentRouter = createCrudRouter({
    // Resource information
    name: ['test plan comment', 'test plan comments'],
    selectSchema: selectTestPlanCommentSchema,
    insertSchema: insertTestPlanCommentSchema,
    patchSchema: patchTestPlanCommentSchema,
    table: testPlanComment,
    queryApi: db.query.testPlanComment,
    relations: testPlanCommentRelations,
    with: {
      testPlan: {
        columns: {
          id: true,
          name: true,
          projectId: true,
        },
      },
      author: {
        columns: {
          id: true,
          email: true,
          displayName: true,
          avatarUrl: true,
        },
      },
      parent: {
        columns: {
          id: true,
          content: true,
          authorId: true,
        },
      },
      replies: {
        columns: {
          id: true,
          content: true,
          authorId: true,
          isEdited: true,
          isDeleted: true,
          createdAt: true,
          updatedAt: true,
        },
        with: {
          author: {
            columns: {
              id: true,
              email: true,
              displayName: true,
              avatarUrl: true,
            },
          },
        },
      },
    },

    // Route configuration
    tags: ['Test Plan Comments'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [{ field: 'createdAt', order: 'asc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,
  });

  await fastify.register(testPlanCommentRouter);
}

export const autoPrefix = '/test-plan-comments';
