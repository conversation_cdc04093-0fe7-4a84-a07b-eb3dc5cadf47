import { db } from '@repo/db';
import {
  insertWorkItemLinkSchema,
  patchWorkItemLinkSchema,
  selectWorkItemLinkSchema,
  workItemLink,
  workItemLinkRelations,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { createCrudRouter } from '../utils/crud/create-crud-router';

/**
 * Work Item Link routes plugin
 * Will be automatically loaded and registered at /api/v1/workItemLink
 */
export default async function workItemLinksRoutes(fastify: FastifyInstance): Promise<void> {
  const router = createCrudRouter({
    // Resource information
    name: ['workItemLink', 'workItemLinks'],
    selectSchema: selectWorkItemLinkSchema,
    insertSchema: insertWorkItemLinkSchema,
    patchSchema: patchWorkItemLinkSchema,
    table: workItemLink,
    queryApi: db.query.workItemLink,
    relations: workItemLinkRelations,
    with: {
      sourceWorkItem: {
        columns: {
          id: true,
          title: true,
          projectId: true,
        },
        with: {
          project: {
            columns: {
              id: true,
              key: true,
              name: true,
            },
          },
        },
      },
      targetWorkItem: {
        columns: {
          id: true,
          title: true,
          projectId: true,
        },
        with: {
          project: {
            columns: {
              id: true,
              key: true,
              name: true,
            },
          },
        },
      },
      createdBy: {
        columns: {
          id: true,
          email: true,
          displayName: true,
        },
      },
    },

    // Route configuration
    tags: ['Work Item Links'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [{ field: 'createdAt', order: 'desc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,
  });

  // Register the CRUD router
  await fastify.register(router);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/workItemLink';
