import { db } from '@repo/db';
import {
  insertResolutionSchema,
  patchResolutionSchema,
  resolution,
  resolutionRelations,
  selectResolutionSchema,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { createCrudRouter } from '../utils/crud/create-crud-router';

/**
 * Resolution routes plugin
 * Will be automatically loaded and registered at /api/v1/resolutions
 */
export default async function resolutionRoutes(fastify: FastifyInstance) {
  // Use the createCrudRouter to automatically generate resolution routes
  const resolutionRouter = createCrudRouter({
    // Resource information
    name: ['resolution', 'resolutions'],
    selectSchema: selectResolutionSchema,
    insertSchema: insertResolutionSchema,
    patchSchema: patchResolutionSchema,
    table: resolution,
    queryApi: db.query.resolution,
    relations: resolutionRelations,
    with: {
      organization: {
        columns: {
          id: true,
          name: true,
          slug: true,
        },
      },
    },

    // Route configuration
    tags: ['Resolutions'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [{ field: 'name', order: 'asc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,
  });

  // Register the CRUD router
  await fastify.register(resolutionRouter);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/resolutions';
