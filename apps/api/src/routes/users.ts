import { db } from '@repo/db';
import {
  insertUserSchema,
  patchUserSchema,
  selectUserSchema,
  user,
  userRelations,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { createCrudRouter } from '../utils/crud/create-crud-router';

/**
 * User routes plugin
 * Will be automatically loaded and registered at /api/v1/users
 */
export default async function userRoutes(fastify: FastifyInstance) {
  // Use the createCrudRouter to automatically generate user routes
  const userRouter = createCrudRouter({
    // Resource information
    name: ['user', 'users'],
    selectSchema: selectUserSchema,
    insertSchema: insertUserSchema,
    patchSchema: patchUserSchema,
    table: user,
    queryApi: db.query.user,
    relations: userRelations,

    // Route configuration
    tags: ['Users'],
    enabled: {
      findMany: true,
      findOne: true,
      create: false, // User creation handled by auth routes
      update: true,
      delete: true,
    },

    // Query defaults
    defaultSort: [{ field: 'displayName', order: 'asc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,
  });

  // Register the CRUD router
  await fastify.register(userRouter);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/users';
