import { db } from '@repo/db';
import {
  insertTestCaseCommentSchema,
  patchTestCaseCommentSchema,
  selectTestCaseCommentSchema,
  testCaseComment,
  testCaseCommentRelations,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { createCrudRouter } from '../utils/crud/create-crud-router';

export default async function testCaseCommentRoutes(fastify: FastifyInstance) {
  const testCaseCommentRouter = createCrudRouter({
    // Resource information
    name: ['test case comment', 'test case comments'],
    selectSchema: selectTestCaseCommentSchema,
    insertSchema: insertTestCaseCommentSchema,
    patchSchema: patchTestCaseCommentSchema,
    table: testCaseComment,
    queryApi: db.query.testCaseComment,
    relations: testCaseCommentRelations,
    with: {
      testCase: {
        columns: {
          id: true,
          title: true,
          projectId: true,
        },
      },
      author: {
        columns: {
          id: true,
          email: true,
          displayName: true,
          avatarUrl: true,
        },
      },
      parent: {
        columns: {
          id: true,
          content: true,
          authorId: true,
        },
      },
      replies: {
        columns: {
          id: true,
          content: true,
          authorId: true,
          isEdited: true,
          isDeleted: true,
          createdAt: true,
          updatedAt: true,
        },
        with: {
          author: {
            columns: {
              id: true,
              email: true,
              displayName: true,
              avatarUrl: true,
            },
          },
        },
      },
    },

    // Route configuration
    tags: ['Test Case Comments'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [{ field: 'createdAt', order: 'asc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,
  });

  await fastify.register(testCaseCommentRouter);
}

export const autoPrefix = '/test-case-comments';
