import { db } from '@repo/db';
import {
  insertWorkItemSchema,
  patchWorkItemSchema,
  selectWorkItemSchema,
  workItem,
  type workItemChangeTypeEnum,
  workItemHistory,
  workItemRelations,
} from '@repo/db/schema';
import type { FastifyInstance, FastifyRequest } from 'fastify';
import { workItemService } from '../services/work-item-service';
import '../types/fastify-augmentation';
import { createCrudRouter } from '../utils/crud/create-crud-router';
import type { UserContext } from '../utils/crud/crud-handler';

/**
 * Helper function to create work item history entries
 */
async function createHistoryEntry(
  workItemId: string,
  changeType: (typeof workItemChangeTypeEnum.enumValues)[number],
  changedBy: string,
  changedFields?: Record<string, { oldValue: any; newValue: any }>,
  metadata?: Record<string, any>,
  summary?: string,
) {
  try {
    await db.insert(workItemHistory).values({
      workItemId,
      changeType,
      changedBy,
      changedFields,
      metadata,
      summary,
    });
  } catch (_error) {}
}

/**
 * Helper function to generate human-readable summary
 */
function generateSummary(changedFields: Record<string, { oldValue: any; newValue: any }>): string {
  const fieldLabels: Record<string, string> = {
    title: 'title',
    description: 'description',
    statusId: 'status',
    priorityId: 'priority',
    assigneeId: 'assignee',
    sprintId: 'sprint',
    typeId: 'type',
    parentId: 'parent',
  };

  const changedFieldNames = Object.keys(changedFields);

  if (changedFieldNames.length === 1) {
    const field = changedFieldNames[0];
    return `Updated ${fieldLabels[field] || field}`;
  }
  if (changedFieldNames.length <= 3) {
    const fields = changedFieldNames.map((f) => fieldLabels[f] || f).join(', ');
    return `Updated ${fields}`;
  }
  return `Updated ${changedFieldNames.length} fields`;
}

/**
 * Helper function to compare and track field changes
 */
async function trackFieldChanges(originalData: any, updatedData: any, userId: string) {
  const fieldsToTrack = [
    'title',
    'description',
    'statusId',
    'priorityId',
    'assigneeId',
    'sprintId',
    'typeId',
    'parentId',
    'estimate',
    'tags',
    'isActive',
  ];

  const changedFields: Record<string, { oldValue: any; newValue: any }> = {};

  // Compare all trackable fields
  for (const field of fieldsToTrack) {
    const oldValue = originalData?.[field];
    const newValue = updatedData?.[field];

    // Only track if the value actually changed
    if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
      changedFields[field] = { oldValue, newValue };
    }
  }

  // If any fields changed, create a single history entry
  if (Object.keys(changedFields).length > 0) {
    const summary = generateSummary(changedFields);
    await createHistoryEntry(updatedData.id, 'UPDATE', userId, changedFields, undefined, summary);
  }
}

/**
 * Helper function to track comment changes
 */
export async function trackCommentChange(
  workItemId: string,
  changeType: 'COMMENT_ADDED' | 'COMMENT_UPDATED' | 'COMMENT_DELETED',
  userId: string,
  commentData: any,
  summary?: string,
) {
  await createHistoryEntry(
    workItemId,
    changeType,
    userId,
    undefined,
    { comment: commentData },
    summary || `${changeType.toLowerCase().replace('_', ' ')}`,
  );
}

/**
 * Helper function to track attachment changes
 */
export async function trackAttachmentChange(
  workItemId: string,
  changeType: 'ATTACHMENT_ADDED' | 'ATTACHMENT_DELETED',
  userId: string,
  attachmentData: any,
  summary?: string,
) {
  await createHistoryEntry(
    workItemId,
    changeType,
    userId,
    undefined,
    { attachment: attachmentData },
    summary || `${changeType.toLowerCase().replace('_', ' ')}`,
  );
}

/**
 * Work Item routes plugin
 * Will be automatically loaded and registered at /api/v1/work-items
 */
export default async function workItemRoutes(fastify: FastifyInstance) {
  // Use the createCrudRouter to automatically generate work item routes
  const workItemRouter = createCrudRouter({
    // Resource information
    name: ['work-item', 'work-items'],
    selectSchema: selectWorkItemSchema,
    insertSchema: insertWorkItemSchema,
    patchSchema: patchWorkItemSchema,
    table: workItem,
    queryApi: db.query.workItem,
    relations: workItemRelations,
    service: workItemService,
    with: {
      project: {
        columns: {
          id: true,
          name: true,
          key: true,
        },
      },
      type: {
        columns: {
          id: true,
          name: true,
          icon: true,
          color: true,
        },
      },
      assignee: {
        columns: {
          id: true,
          email: true,
          displayName: true,
          avatarUrl: true,
        },
      },
      reporter: {
        columns: {
          id: true,
          email: true,
          displayName: true,
        },
      },
      sprint: {
        columns: {
          id: true,
          name: true,
        },
      },
      initialSprint: {
        columns: {
          id: true,
          name: true,
        },
      },
      status: {
        columns: {
          id: true,
          name: true,
          color: true,
          statusType: true,
        },
      },
      priority: {
        columns: {
          id: true,
          name: true,
          level: true,
          color: true,
          icon: true,
        },
      },
      creator: {
        columns: {
          id: true,
          email: true,
          displayName: true,
        },
      },
      parent: {
        columns: {
          id: true,
          title: true,
        },
      },
      updater: {
        columns: {
          id: true,
          email: true,
          displayName: true,
        },
      },
    },

    // Route configuration
    tags: ['Work Items'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [
      { field: 'order', order: 'asc' },
      { field: 'createdAt', order: 'desc' },
    ],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,

    // Lifecycle hooks for history tracking
    postCreate: async (data: any, request: FastifyRequest) => {
      const user = request.user as UserContext;

      // Create history entry for work item creation
      await createHistoryEntry(
        data.id,
        'CREATE',
        user.userId,
        undefined,
        { workItem: data },
        'Created work item',
      );

      return data;
    },

    preUpdate: async (request: FastifyRequest) => {
      const { id } = request.params as { id: string };

      // Fetch and store original data before update
      const originalData = await db.query.workItem.findFirst({
        where: (workItem, { eq }) => eq(workItem.id, id),
      });

      // Store original data in request context for use in postUpdate
      if (request.context) {
        (request.context as any).originalWorkItem = originalData;
      } else {
        (request as any).context = { originalWorkItem: originalData };
      }
    },

    postUpdate: async (data: any, request: FastifyRequest) => {
      const user = request.user as UserContext;
      const originalData = (request.context as any)?.originalWorkItem;

      // Track field changes and create specific history entries
      if (originalData) {
        await trackFieldChanges(originalData, data, user.userId);
      }

      return data;
    },
  });

  // Register the CRUD router
  await fastify.register(workItemRouter);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/work-items';
