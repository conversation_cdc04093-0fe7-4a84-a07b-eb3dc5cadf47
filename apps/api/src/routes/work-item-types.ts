import { db } from '@repo/db';
import {
  insertWorkItemTypeSchema,
  patchWorkItemTypeSchema,
  selectWorkItemTypeSchema,
  workItemType,
  workItemTypeRelations,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { createCrudRouter } from '../utils/crud/create-crud-router';

/**
 * Work Item Type routes plugin
 * Will be automatically loaded and registered at /api/v1/work-item-types
 */
export default async function workItemTypeRoutes(fastify: FastifyInstance) {
  // Use the createCrudRouter to automatically generate work item type routes
  const workItemTypeRouter = createCrudRouter({
    // Resource information
    name: ['work-item-type', 'work-item-types'],
    selectSchema: selectWorkItemTypeSchema,
    insertSchema: insertWorkItemTypeSchema,
    patchSchema: patchWorkItemTypeSchema,
    table: workItemType,
    queryApi: db.query.workItemType,
    relations: workItemTypeRelations,
    with: {
      organization: {
        columns: {
          id: true,
          name: true,
          slug: true,
        },
      },
    },

    // Route configuration
    tags: ['Work Item Types'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [{ field: 'name', order: 'asc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,
  });

  // Register the CRUD router
  await fastify.register(workItemTypeRouter);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/work-item-types';
