import { db } from '@repo/db';
import {
  insertWorkItemCommentSchema,
  patchWorkItemCommentSchema,
  selectWorkItemCommentSchema,
  workItemComment,
  workItemCommentRelations,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { workItemCommentService } from '../services/work-item-comment-service';
import { createCrudRouter } from '../utils/crud/create-crud-router';

/**
 * Work Item Comment routes plugin
 * Will be automatically loaded and registered at /api/v1/work-item-comments
 */
export default async function workItemCommentRoutes(fastify: FastifyInstance) {
  // Use the createCrudRouter to automatically generate work item comment routes
  const workItemCommentRouter = createCrudRouter({
    // Resource information
    name: ['work-item-comment', 'work-item-comments'],
    selectSchema: selectWorkItemCommentSchema,
    insertSchema: insertWorkItemCommentSchema,
    patchSchema: patchWorkItemCommentSchema,
    table: workItemComment,
    queryApi: db.query.workItemComment,
    service: workItemCommentService,
    relations: workItemCommentRelations,
    with: {
      workItem: {
        columns: {
          id: true,
          title: true,
          projectId: true,
        },
      },
      author: {
        columns: {
          id: true,
          email: true,
          displayName: true,
          avatarUrl: true,
        },
      },
      parent: {
        columns: {
          id: true,
          content: true,
          authorId: true,
        },
      },
      replies: {
        columns: {
          id: true,
          content: true,
          authorId: true,
          isEdited: true,
          isDeleted: true,
          createdAt: true,
          updatedAt: true,
        },
        with: {
          author: {
            columns: {
              id: true,
              email: true,
              displayName: true,
              avatarUrl: true,
            },
          },
        },
      },
    },

    // Route configuration
    tags: ['Work Item Comments'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [{ field: 'createdAt', order: 'asc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,
  });

  // Register the CRUD router
  await fastify.register(workItemCommentRouter);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/work-item-comments';
