import { db } from '@repo/db';
import {
  auditLog,
  auditLogRelations,
  insertAuditLogSchema,
  patchAuditLogSchema,
  selectAuditLogSchema,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { auditLogService } from '../services/audit-log-service';
import { createCrudRouter } from '../utils/crud/create-crud-router';

/**
 * Audit Log routes plugin
 * Will be automatically loaded and registered at /api/v1/audit-logs
 */
export default async function auditLogRoutes(fastify: FastifyInstance) {
  // Use the createCrudRouter to automatically generate audit log routes
  const auditLogRouter = createCrudRouter({
    // Resource information
    name: ['audit-log', 'audit-logs'],
    selectSchema: selectAuditLogSchema,
    insertSchema: insertAuditLogSchema,
    patchSchema: patchAuditLogSchema,
    table: auditLog,
    queryApi: db.query.auditLog,
    relations: auditLogRelations,
    with: {
      user: {
        columns: {
          id: true,
          email: true,
          displayName: true,
        },
      },
    },

    // Service integration
    service: auditLogService,

    // Response transformation to include entity objects
    postFindMany: async (data: any) => {
      return await auditLogService.enrichMultipleWithEntityObjects(data);
    },
    postFindOne: async (data: any) => {
      return await auditLogService.enrichWithEntityObjects(data);
    },

    // Route configuration
    tags: ['Audit Logs'],
    enabled: {
      findMany: true,
      findOne: true,
      create: false, // Audit logs should be created by the system
      update: false, // Audit logs should be immutable
      delete: false, // Audit logs should be immutable
    },

    // Query defaults
    defaultSort: [{ field: 'timestamp', order: 'desc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,
  });

  // Register the CRUD router
  await fastify.register(auditLogRouter);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/audit-logs';
