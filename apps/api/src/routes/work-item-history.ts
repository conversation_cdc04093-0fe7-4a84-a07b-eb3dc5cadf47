import { db } from '@repo/db';
import {
  insertWorkItemHistorySchema,
  patchWorkItemHistorySchema,
  selectWorkItemHistorySchema,
  workItemHistory,
  workItemHistoryRelations,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { createCrudRouter } from '../utils/crud/create-crud-router';

/**
 * Work Item History routes plugin
 * Will be automatically loaded and registered at /api/v1/work-item-history
 */
export default async function workItemHistoryRoutes(fastify: FastifyInstance) {
  // Use the createCrudRouter to automatically generate work item history routes
  const workItemHistoryRouter = createCrudRouter({
    // Resource information
    name: ['work-item-history', 'work-item-history'],
    selectSchema: selectWorkItemHistorySchema,
    insertSchema: insertWorkItemHistorySchema,
    patchSchema: patchWorkItemHistorySchema,
    table: workItemHistory,
    queryApi: db.query.workItemHistory,
    relations: workItemHistoryRelations,
    with: {
      workItem: {
        columns: {
          id: true,
          title: true,
          projectId: true,
        },
      },
      user: {
        columns: {
          id: true,
          email: true,
          displayName: true,
          avatarUrl: true,
        },
      },
    },

    // Route configuration
    tags: ['Work Item History'],
    enabled: {
      all: true,
      create: false, // History entries should be created automatically, not manually
      update: false, // History entries should be immutable
      delete: false, // History entries should not be deleted
    },

    // Query defaults
    defaultSort: [{ field: 'createdAt', order: 'desc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,
  });

  // Register the CRUD router
  await fastify.register(workItemHistoryRouter);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/work-item-history';
