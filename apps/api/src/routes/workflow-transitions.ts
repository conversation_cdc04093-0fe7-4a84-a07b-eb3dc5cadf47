import { db } from '@repo/db';
import {
  insertWorkflowTransitionSchema,
  patchWorkflowTransitionSchema,
  selectWorkflowTransitionSchema,
  workflowTransition,
  workflowTransitionRelations,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { createCrudRouter } from '../utils/crud/create-crud-router';

/**
 * Workflow Transition routes plugin
 * Will be automatically loaded and registered at /api/v1/workflow-transitions
 */
export default async function workflowTransitionRoutes(fastify: FastifyInstance) {
  // Use the createCrudRouter to automatically generate workflow transition routes
  const workflowTransitionRouter = createCrudRouter({
    // Resource information
    name: ['workflow-transition', 'workflow-transitions'],
    selectSchema: selectWorkflowTransitionSchema,
    insertSchema: insertWorkflowTransitionSchema,
    patchSchema: patchWorkflowTransitionSchema,
    table: workflowTransition,
    queryApi: db.query.workflowTransition,
    relations: workflowTransitionRelations,
    with: {
      workflow: {
        columns: {
          id: true,
          name: true,
        },
      },
      fromStatus: {
        columns: {
          id: true,
        },
        with: {
          status: {
            columns: {
              id: true,
              name: true,
            },
          },
        },
      },
      toStatus: {
        columns: {
          id: true,
        },
        with: {
          status: {
            columns: {
              id: true,
              name: true,
            },
          },
        },
      },
    },

    // Route configuration
    tags: ['Workflow Transitions'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [{ field: 'order', order: 'asc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,
  });

  // Register the CRUD router
  await fastify.register(workflowTransitionRouter);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/workflow-transitions';
