import { db } from '@repo/db';
import {
  insertRoleSchema,
  patchRoleSchema,
  role,
  roleRelations,
  selectRoleSchema,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { createCrudRouter } from '../utils/crud/create-crud-router';

/**
 * Role routes plugin
 * Will be automatically loaded and registered at /api/v1/roles
 */
export default async function roleRoutes(fastify: FastifyInstance) {
  // Use the createCrudRouter to automatically generate role routes
  const roleRouter = createCrudRouter({
    // Resource information
    name: ['role', 'roles'],
    selectSchema: selectRoleSchema,
    insertSchema: insertRoleSchema,
    patchSchema: patchRoleSchema,
    table: role,
    queryApi: db.query.role,
    relations: roleRelations,
    with: {
      creator: {
        columns: {
          id: true,
          email: true,
          displayName: true,
        },
      },
    },

    // Route configuration
    tags: ['Roles'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [{ field: 'name', order: 'asc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,
  });

  // Register the CRUD router
  await fastify.register(roleRouter);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/roles';
