import { db } from '@repo/db';
import {
  insertStatusCategorySchema,
  patchStatusCategorySchema,
  selectStatusCategorySchema,
  statusCategory,
  statusCategoryRelations,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { createCrudRouter } from '../utils/crud/create-crud-router';

/**
 * Status Category routes plugin
 * Will be automatically loaded and registered at /api/v1/status-categories
 */
export default async function statusCategoryRoutes(fastify: FastifyInstance) {
  // Use the createCrudRouter to automatically generate status category routes
  const statusCategoryRouter = createCrudRouter({
    // Resource information
    name: ['status-category', 'status-categories'],
    selectSchema: selectStatusCategorySchema,
    insertSchema: insertStatusCategorySchema,
    patchSchema: patchStatusCategorySchema,
    table: statusCategory,
    queryApi: db.query.statusCategory,
    relations: statusCategoryRelations,

    // Route configuration
    tags: ['Status Categories'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [{ field: 'name', order: 'asc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,
  });

  // Register the CRUD router
  await fastify.register(statusCategoryRouter);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/status-categories';
