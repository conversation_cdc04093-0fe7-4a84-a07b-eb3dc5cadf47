import path from 'node:path';
import { fileURLToPath } from 'node:url';
import autoload from '@fastify/autoload';
import type { FastifyInstance, FastifyPluginOptions } from 'fastify';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default async function routes(fastify: FastifyInstance, options: FastifyPluginOptions) {
  // Register all routes with /api/v1 prefix
  fastify.register(
    async (instance) => {
      // Use autoload to automatically load all route files
      await instance.register(autoload, {
        dir: __dirname,
        // Options for autoload
        indexPattern: /^no-file-will-match$/, // Don't use index.ts as a special case
        ignorePattern: /index\.ts/, // Ignore this file
        dirNameRoutePrefix: false, // Don't use directory names as route prefixes
        maxDepth: 1, // Only load files directly in the routes directory, no nested directories
        options: { ...options }, // Pass all options to loaded plugins
      });
    },
    { prefix: '/api/v1' },
  );

  // Log all registered routes when the server starts
  fastify.ready(() => {
    const _routeTable = fastify.printRoutes();
    fastify.log.info('Registered routes:');
  });
}
