import { db } from '@repo/db';
import {
  insertTestSuiteSchema,
  patchTestSuiteSchema,
  selectTestCaseSchema,
  selectTestSuiteSchema,
  testSuite,
  testSuiteRelations,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { z } from 'zod';
import { TestCaseService } from '../services/test-case-service';
import { TestSuiteService } from '../services/test-suite-service';
import { createCrudRouter } from '../utils/crud/create-crud-router';

export default async function testSuiteRoutes(fastify: FastifyInstance) {
  // Initialize the services
  const testSuiteService = new TestSuiteService();
  const testCaseService = new TestCaseService();

  const testSuiteRouter = createCrudRouter({
    // Resource information
    name: ['test suite', 'test suites'],
    selectSchema: selectTestSuiteSchema,
    insertSchema: insertTestSuiteSchema,
    patchSchema: patchTestSuiteSchema,
    table: testSuite,
    queryApi: db.query.testSuite,
    relations: testSuiteRelations,
    service: testSuiteService,

    // Include related data
    with: {
      testPlan: {
        columns: {
          id: true,
          name: true,
          status: true,
        },
      },
    },

    // Route configuration
    tags: ['Test Suites'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [
      { field: 'order', order: 'asc' },
      { field: 'createdAt', order: 'desc' },
    ],

    // Authentication - all endpoints require authentication
    authenticate: {
      findMany: true,
      findOne: true,
      create: true,
      update: true,
      delete: true,
    },

    // Options
    softDelete: false,
  });

  await fastify.register(testSuiteRouter);

  // Additional test case association endpoints - register as a plugin
  await fastify.register(async function testSuiteTestCaseRoutes(app) {
    // Get test cases for a specific suite
    const testCaseWithSuitePropsSchema = selectTestCaseSchema.extend({
      suiteStatus: z
        .object({
          id: z.string().uuid(),
          name: z.string(),
        })
        .nullable(),
      suitePriority: z
        .object({
          id: z.string().uuid(),
          name: z.string(),
        })
        .nullable(),
      suiteOrder: z.number(),
      // Override date fields to handle serialization
      createdAt: z.union([z.date(), z.string()]),
      updatedAt: z.union([z.date(), z.string()]),
    });

    const getTestCasesSchema = {
      params: z.object({
        id: z.string().uuid(),
      }),
      response: {
        200: z.array(testCaseWithSuitePropsSchema),
      },
    };

    app.get<{
      Params: z.infer<typeof getTestCasesSchema.params>;
    }>(
      '/:id/test-cases',
      {
        schema: getTestCasesSchema,
        preHandler: app.authenticate,
      },
      async (request, reply) => {
        const { id } = request.params;
        const testCases = await testCaseService.getTestCasesForSuite(id, {
          userId: request.user?.id,
          with: {
            project: true,
            assignee: true,
            reporter: true,
            creator: true,
            workItem: true,
          },
        });

        // Log the response structure to debug schema issues
        request.log.info(`Returning ${testCases.length} test cases for suite ${id}`);
        if (testCases.length > 0) {
          request.log.debug('First test case structure:', JSON.stringify(testCases[0], null, 2));
        }

        return reply.send(testCases);
      },
    );

    // Link test cases to a suite
    const linkTestCasesSchema = {
      params: z.object({
        id: z.string().uuid(),
      }),
      body: z.object({
        testCaseIds: z.array(z.string().uuid()).min(1),
        statusId: z.string().uuid().nullable().optional(),
        priorityId: z.string().uuid().nullable().optional(),
      }),
      response: {
        201: z.object({
          message: z.string(),
        }),
      },
    };

    app.post<{
      Params: z.infer<typeof linkTestCasesSchema.params>;
      Body: z.infer<typeof linkTestCasesSchema.body>;
    }>(
      '/:id/test-cases/link',
      {
        schema: linkTestCasesSchema,
        preHandler: app.authenticate,
      },
      async (request, reply) => {
        const { id } = request.params;
        const { testCaseIds, statusId, priorityId } = request.body;

        await Promise.all(
          testCaseIds.map((testCaseId) =>
            testCaseService.addToSuite(
              testCaseId,
              id,
              {
                statusId,
                priorityId,
                createdBy: request.user?.id,
              },
              { userId: request.user?.id },
            ),
          ),
        );

        return reply.code(201).send({ message: 'Test cases linked successfully' });
      },
    );

    // Remove test case from suite
    const removeTestCaseSchema = {
      params: z.object({
        id: z.string().uuid(),
        testCaseId: z.string().uuid(),
      }),
      response: {
        204: z.null(),
      },
    };

    app.delete<{
      Params: z.infer<typeof removeTestCaseSchema.params>;
    }>(
      '/:id/test-cases/:testCaseId',
      {
        schema: removeTestCaseSchema,
        preHandler: app.authenticate,
      },
      async (request, reply) => {
        const { id, testCaseId } = request.params;
        await testCaseService.removeFromSuite(testCaseId, id);
        return reply.code(204).send();
      },
    );

    // Update test case in suite
    const updateTestCaseSchema = {
      params: z.object({
        id: z.string().uuid(),
        testCaseId: z.string().uuid(),
      }),
      body: z.object({
        statusId: z.string().uuid().nullable().optional(),
        priorityId: z.string().uuid().nullable().optional(),
        order: z.number().optional(),
      }),
      response: {
        200: z.object({
          message: z.string(),
        }),
      },
    };

    app.patch<{
      Params: z.infer<typeof updateTestCaseSchema.params>;
      Body: z.infer<typeof updateTestCaseSchema.body>;
    }>(
      '/:id/test-cases/:testCaseId',
      {
        schema: updateTestCaseSchema,
        preHandler: app.authenticate,
      },
      async (request, reply) => {
        const { id, testCaseId } = request.params;
        const data = request.body;

        await testCaseService.updateInSuite(testCaseId, id, data);

        return reply.send({ message: 'Test case updated successfully' });
      },
    );

    // Get available test cases for linking to a suite
    const getAvailableTestCasesSchema = {
      params: z.object({
        id: z.string().uuid(),
      }),
      querystring: z.object({
        projectId: z.string().uuid().optional(),
      }),
      response: {
        200: z.array(
          z.object({
            id: z.string().uuid(),
            title: z.string(),
            description: z.string().nullable(),
            projectId: z.string().uuid(),
            project: z
              .object({
                id: z.string().uuid(),
                name: z.string(),
                key: z.string(),
              })
              .optional(),
            assignee: z
              .object({
                id: z.string().uuid(),
                email: z.string(),
                displayName: z.string(),
              })
              .nullable()
              .optional(),
          }),
        ),
      },
    };

    app.get<{
      Params: z.infer<typeof getAvailableTestCasesSchema.params>;
      Querystring: z.infer<typeof getAvailableTestCasesSchema.querystring>;
    }>(
      '/:id/available-test-cases',
      {
        schema: getAvailableTestCasesSchema,
        preHandler: app.authenticate,
      },
      async (request, reply) => {
        const { id } = request.params;
        const { projectId } = request.query;

        const availableTestCases = await testCaseService.getAvailableForSuite(id, projectId, {
          userId: request.user?.id,
        });

        return reply.send(availableTestCases);
      },
    );

    // Get all test cases across all test suites for copying
    const getAllTestCasesSchema = {
      querystring: z.object({
        projectId: z.string().uuid().optional(),
        search: z.string().optional(),
        limit: z.coerce.number().min(1).max(100).default(50),
        offset: z.coerce.number().min(0).default(0),
      }),
      response: {
        200: z.object({
          data: z.array(
            z.object({
              id: z.string().uuid(),
              title: z.string(),
              description: z.string().nullable(),
              projectId: z.string().uuid(),
              project: z
                .object({
                  id: z.string().uuid(),
                  name: z.string(),
                  key: z.string(),
                })
                .optional(),
              assignee: z
                .object({
                  id: z.string().uuid(),
                  email: z.string(),
                  displayName: z.string(),
                })
                .nullable()
                .optional(),
              testSuites: z
                .array(
                  z.object({
                    id: z.string().uuid(),
                    name: z.string(),
                  }),
                )
                .optional(),
            }),
          ),
          total: z.number(),
        }),
      },
    };

    app.get<{
      Querystring: z.infer<typeof getAllTestCasesSchema.querystring>;
    }>(
      '/all-test-cases',
      {
        schema: getAllTestCasesSchema,
        preHandler: app.authenticate,
      },
      async (request, reply) => {
        const { projectId, search, limit, offset } = request.query;

        const allTestCases = await testCaseService.getAllForCopying(
          { projectId, search, limit, offset },
          { userId: request.user?.id },
        );

        return reply.send(allTestCases);
      },
    );

    // Get all test suites for importing
    const getAllTestSuitesSchema = {
      querystring: z.object({
        testPlanId: z.string().uuid().optional(),
        search: z.string().optional(),
        limit: z.coerce.number().min(1).max(100).default(50),
        offset: z.coerce.number().min(0).default(0),
      }),
      response: {
        200: z.object({
          data: z.array(
            z.object({
              id: z.string().uuid(),
              name: z.string(),
              description: z.string().nullable(),
              testPlanId: z.string().uuid(),
              testPlan: z
                .object({
                  id: z.string().uuid(),
                  name: z.string(),
                })
                .optional(),
              testCaseCount: z.number(),
            }),
          ),
          total: z.number(),
        }),
      },
    };

    app.get<{
      Querystring: z.infer<typeof getAllTestSuitesSchema.querystring>;
    }>(
      '/all-test-suites',
      {
        schema: getAllTestSuitesSchema,
        preHandler: app.authenticate,
      },
      async (request, reply) => {
        const { testPlanId, search, limit, offset } = request.query;

        const allTestSuites = await testSuiteService.getAllForImporting(
          { testPlanId, search, limit, offset },
          { userId: request.user?.id },
        );

        return reply.send(allTestSuites);
      },
    );
  }); // End of plugin
}

export const autoPrefix = '/test-suites';
