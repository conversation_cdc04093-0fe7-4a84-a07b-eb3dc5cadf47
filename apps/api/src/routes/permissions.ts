import { db } from '@repo/db';
import { permission } from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { z } from 'zod';
import { createCrudRouter } from '../utils/crud/create-crud-router';

const permissionSchema = z.object({
  id: z.string().uuid(),
  identifier: z.string().max(255),
  name: z.string().max(255),
  description: z.string().nullable(),
  category: z.string().max(100),
  scopeType: z.string().max(50),
  isActive: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

const createPermissionSchema = z.object({
  identifier: z.string().min(1).max(255),
  name: z.string().min(1).max(255),
  description: z.string().nullable().optional(),
  category: z.string().min(1).max(100),
  scopeType: z.enum(['org', 'ws']),
  isActive: z.boolean().optional().default(true),
});

const updatePermissionSchema = createPermissionSchema.partial();

export default async function permissionRoutes(app: FastifyInstance) {
  const router = createCrudRouter({
    name: ['permission', 'permissions'],
    table: permission,
    queryApi: db.query.permission,
    selectSchema: permissionSchema,
    insertSchema: createPermissionSchema,
    patchSchema: updatePermissionSchema,
    softDelete: false,
    relations: [],
    defaultSort: [
      { field: 'category', order: 'asc' },
      { field: 'name', order: 'asc' },
    ],
  });

  await app.register(router);
}

export const autoPrefix = '/api/v1/permissions';
