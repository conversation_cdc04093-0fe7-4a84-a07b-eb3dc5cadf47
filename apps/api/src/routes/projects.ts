import { db } from '@repo/db';
import {
  insertProjectSchema,
  patchProjectSchema,
  project,
  projectRelations,
  selectProjectSchema,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { ProjectService } from '../services/project-service';
import { createCrudRouter } from '../utils/crud/create-crud-router';

/**
 * Project routes plugin
 * Will be automatically loaded and registered at /api/v1/projects
 */
export default async function projectRoutes(fastify: FastifyInstance) {
  // Create project service instance
  const projectService = new ProjectService(fastify);

  // Use the createCrudRouter to automatically generate project routes
  const projectRouter = createCrudRouter({
    // Resource information
    name: ['project', 'projects'],
    selectSchema: selectProjectSchema,
    insertSchema: insertProjectSchema,
    patchSchema: patchProjectSchema,
    table: project,
    queryApi: db.query.project,
    relations: projectRelations,
    service: projectService,
    with: {
      workspace: {
        columns: {
          id: true,
          name: true,
          slug: true,
        },
      },
      workflow: {
        columns: {
          id: true,
          name: true,
        },
      },
      creator: {
        columns: {
          id: true,
          email: true,
          displayName: true,
        },
      },
      defaultAssignee: {
        columns: {
          id: true,
          email: true,
          displayName: true,
        },
      },
    },

    // Route configuration
    tags: ['Projects'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [{ field: 'createdAt', order: 'desc' }],

    // Authentication
    authenticate: false,

    // Options
    softDelete: false,
  });

  // Register the CRUD router
  await fastify.register(projectRouter);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/projects';
