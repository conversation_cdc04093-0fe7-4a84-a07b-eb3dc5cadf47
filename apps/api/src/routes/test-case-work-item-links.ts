import { db } from '@repo/db';
import {
  insertTestCaseWorkItemLinkSchema,
  patchTestCaseWorkItemLinkSchema,
  selectTestCaseWorkItemLinkSchema,
  testCaseWorkItemLink,
  testCaseWorkItemLinkRelations,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { createCrudRouter } from '../utils/crud/create-crud-router';

export default async function testCaseWorkItemLinkRoutes(fastify: FastifyInstance) {
  const testCaseWorkItemLinkRouter = createCrudRouter({
    // Resource information
    name: ['test case work item link', 'test case work item links'],
    selectSchema: selectTestCaseWorkItemLinkSchema,
    insertSchema: insertTestCaseWorkItemLinkSchema,
    patchSchema: patchTestCaseWorkItemLinkSchema,
    table: testCaseWorkItemLink,
    queryApi: db.query.testCaseWorkItemLink,
    relations: testCaseWorkItemLinkRelations,
    with: {
      testCase: {
        columns: {
          id: true,
          title: true,
          description: true,
        },
      },
      workItem: {
        columns: {
          id: true,
          title: true,
        },
        with: {
          project: {
            columns: {
              id: true,
              key: true,
              name: true,
            },
          },
          status: {
            columns: {
              id: true,
              name: true,
            },
          },
          assignee: {
            columns: {
              id: true,
              email: true,
              displayName: true,
            },
          },
        },
      },
      createdBy: {
        columns: {
          id: true,
          email: true,
          displayName: true,
        },
      },
    },

    // Route configuration
    tags: ['Test Case Work Item Links'],
    enabled: {
      findMany: true,
      findOne: true,
      create: true,
      delete: true,
      update: false, // Links are immutable once created
    },

    // Query defaults
    defaultSort: [{ field: 'createdAt', order: 'desc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,
  });

  await fastify.register(testCaseWorkItemLinkRouter);
}

export const autoPrefix = '/test-case-work-item-links';
