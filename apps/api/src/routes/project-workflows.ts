import { db } from '@repo/db';
import {
  insertProjectWorkflowSchema,
  patchProjectWorkflowSchema,
  projectWorkflow,
  projectWorkflowRelations,
  selectProjectWorkflowSchema,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { createCrudRouter } from '../utils/crud/create-crud-router';

/**
 * Project Workflow routes plugin
 * Will be automatically loaded and registered at /api/v1/project-workflows
 */
export default async function projectWorkflowRoutes(fastify: FastifyInstance) {
  // Use the createCrudRouter to automatically generate project workflow routes
  const projectWorkflowRouter = createCrudRouter({
    // Resource information
    name: ['project-workflow', 'project-workflows'],
    selectSchema: selectProjectWorkflowSchema,
    insertSchema: insertProjectWorkflowSchema,
    patchSchema: patchProjectWorkflowSchema,
    table: projectWorkflow,
    queryApi: db.query.projectWorkflow,
    relations: projectWorkflowRelations,
    with: {
      project: {
        columns: {
          id: true,
          name: true,
          key: true,
        },
      },
      workflow: {
        columns: {
          id: true,
          name: true,
          status: true,
        },
      },
    },

    // Route configuration
    tags: ['Project Workflows'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [{ field: 'createdAt', order: 'desc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,
  });

  // Register the CRUD router
  await fastify.register(projectWorkflowRouter);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/project-workflows';
