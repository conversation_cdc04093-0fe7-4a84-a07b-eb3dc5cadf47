import { db } from '@repo/db';
import {
  insertTestPlanSchema,
  patchTestPlanSchema,
  selectTestPlanSchema,
  testPlan,
  testPlanRelations,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { createCrudRouter } from '../utils/crud/create-crud-router';

export default async function testPlanRoutes(fastify: FastifyInstance) {
  const testPlanRouter = createCrudRouter({
    // Resource information
    name: ['test plan', 'test plans'],
    selectSchema: selectTestPlanSchema,
    insertSchema: insertTestPlanSchema,
    patchSchema: patchTestPlanSchema,
    table: testPlan,
    queryApi: db.query.testPlan,
    relations: testPlanRelations,

    // Include related data
    with: {
      project: {
        columns: {
          id: true,
          name: true,
          key: true,
        },
      },
      createdBy: {
        columns: {
          id: true,
          email: true,
          displayName: true,
        },
      },
    },

    // Route configuration
    tags: ['Test Plans'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [{ field: 'createdAt', order: 'desc' }],

    // Authentication - all endpoints require authentication
    authenticate: {
      findMany: true,
      findOne: true,
      create: true,
      update: true,
      delete: true,
    },

    // Options
    softDelete: false,
  });

  await fastify.register(testPlanRouter);
}

export const autoPrefix = '/test-plans';
