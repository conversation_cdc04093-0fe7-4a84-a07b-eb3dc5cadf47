import { db } from '@repo/db';
import {
  insertStatusSchema,
  patchStatusSchema,
  selectStatusSchema,
  status,
  statusRelations,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { createCrudRouter } from '../utils/crud/create-crud-router';

/**
 * Status routes plugin
 * Will be automatically loaded and registered at /api/v1/statuses
 */
export default async function statusRoutes(fastify: FastifyInstance) {
  // Use the createCrudRouter to automatically generate status routes
  const statusRouter = createCrudRouter({
    // Resource information
    name: ['status', 'statuses'],
    selectSchema: selectStatusSchema,
    insertSchema: insertStatusSchema,
    patchSchema: patchStatusSchema,
    table: status,
    queryApi: db.query.status,
    relations: statusRelations,
    with: {
      organization: {
        columns: {
          id: true,
          name: true,
          slug: true,
        },
      },
      statusCategory: {
        columns: {
          id: true,
          name: true,
          color: true,
        },
      },
    },

    // Route configuration
    tags: ['Statuses'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [{ field: 'order', order: 'asc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,
  });

  // Register the CRUD router
  await fastify.register(statusRouter);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/statuses';
