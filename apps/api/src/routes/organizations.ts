import { db } from '@repo/db';
import {
  insertOrganizationSchema,
  organization,
  organizationRelations,
  patchOrganizationSchema,
  selectOrganizationSchema,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { OrganizationService } from '../services/organization-service';
import { createCrudRouter } from '../utils/crud/create-crud-router';

/**
 * Organization routes plugin
 * Will be automatically loaded and registered at /api/v1/organizations
 */
export default async function organizationRoutes(fastify: FastifyInstance) {
  // Initialize the organization service with fastify instance
  const organizationService = new OrganizationService(fastify);

  // Use the createCrudRouter to automatically generate organization routes
  const organizationRouter = createCrudRouter({
    // Resource information
    name: ['organization', 'organizations'],
    selectSchema: selectOrganizationSchema,
    insertSchema: insertOrganizationSchema,
    patchSchema: patchOrganizationSchema,
    table: organization,
    queryApi: db.query.organization,
    relations: organizationRelations,
    with: {
      creator: {
        columns: {
          id: true,
          email: true,
          displayName: true,
        },
      },
    },

    // Route configuration
    tags: ['Organizations'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [{ field: 'createdAt', order: 'desc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,

    // Service integration
    service: organizationService,
  });

  // Register the CRUD router
  await fastify.register(organizationRouter);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/organizations';
