import { db } from '@repo/db';
import {
  insertInvitationSchema,
  invitation,
  invitationRelations,
  patchInvitationSchema,
  selectInvitationSchema,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { invitationService } from '../services/invitation-service';
import { createCrudRouter } from '../utils/crud/create-crud-router';

/**
 * Invitation routes plugin
 * Will be automatically loaded and registered at /api/v1/invitations
 */
export default async function invitationRoutes(fastify: FastifyInstance) {
  // Use the createCrudRouter to automatically generate invitation routes
  const invitationRouter = createCrudRouter({
    // Resource information
    name: ['invitation', 'invitations'],
    selectSchema: selectInvitationSchema,
    insertSchema: insertInvitationSchema,
    patchSchema: patchInvitationSchema,
    table: invitation,
    queryApi: db.query.invitation,
    relations: invitationRelations,
    with: {
      inviter: {
        columns: {
          id: true,
          email: true,
          displayName: true,
        },
      },
      role: {
        columns: {
          id: true,
          name: true,
          identifier: true,
        },
      },
    },

    // Service integration
    service: invitationService,

    // Response transformation to include scope objects
    postFindMany: async (data: any) => {
      return await invitationService.enrichMultipleWithScopeObjects(data);
    },
    postFindOne: async (data: any) => {
      return await invitationService.enrichWithScopeObject(data);
    },
    postCreate: async (data: any) => {
      return await invitationService.enrichWithScopeObject(data);
    },
    postUpdate: async (data: any) => {
      return await invitationService.enrichWithScopeObject(data);
    },

    // Route configuration
    tags: ['Invitations'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [{ field: 'createdAt', order: 'desc' }],

    // Authentication - make GET endpoints public for invitation lookups
    authenticate: {
      findMany: true,
      findOne: false,
      create: true,
      update: true,
      delete: true,
    },

    // Options
    softDelete: false,
  });

  // Register the CRUD router
  await fastify.register(invitationRouter);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/invitations';
