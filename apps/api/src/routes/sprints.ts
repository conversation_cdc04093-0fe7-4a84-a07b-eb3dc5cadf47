import { db } from '@repo/db';
import {
  insertSprintSchema,
  patchSprintSchema,
  selectSprintSchema,
  sprint,
  sprintRelations,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { createCrudRouter } from '../utils/crud/create-crud-router';

/**
 * Sprint routes plugin
 * Will be automatically loaded and registered at /api/v1/sprints
 */
export default async function sprintRoutes(fastify: FastifyInstance) {
  // Use the createCrudRouter to automatically generate sprint routes
  const sprintRouter = createCrudRouter({
    // Resource information
    name: ['sprint', 'sprints'],
    selectSchema: selectSprintSchema,
    insertSchema: insertSprintSchema,
    patchSchema: patchSprintSchema,
    table: sprint,
    queryApi: db.query.sprint,
    relations: sprintRelations,
    with: {
      project: {
        columns: {
          id: true,
          name: true,
          key: true,
        },
      },
      creator: {
        columns: {
          id: true,
          email: true,
          displayName: true,
        },
      },
    },

    // Route configuration
    tags: ['Sprints'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [{ field: 'startDate', order: 'desc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,
  });

  // Register the CRUD router
  await fastify.register(sprintRouter);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/sprints';
