import multipart from '@fastify/multipart';
import { db } from '@repo/db';
import {
  attachment,
  attachmentEntityTypeEnum,
  attachmentRelations,
  insertAttachmentSchema,
  patchAttachmentSchema,
  selectAttachmentSchema,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { z } from 'zod';
import { AttachmentService } from '../services/attachment-service';
import { createCrudRouter } from '../utils/crud/create-crud-router';

/**
 * Attachment routes plugin
 * Will be automatically loaded and registered at /api/v1/attachments
 */
export default async function attachmentRoutes(fastify: FastifyInstance) {
  // Register multipart plugin for this route
  await fastify.register(multipart, {
    limits: {
      fileSize: fastify.config.fileUpload.maxFileSize,
      files: 1, // Limit to one file per request
    },
  });

  // Initialize attachment service
  const attachmentService = new AttachmentService(fastify);

  // Upload file endpoint
  const uploadSchema = {
    querystring: z.object({
      entityType: z.enum(attachmentEntityTypeEnum.enumValues),
      entityId: z.string().uuid(),
    }),
    response: {
      200: z.object({
        id: z.string().uuid(),
        fileName: z.string(),
        url: z.string().url(),
        size: z.number(),
        fileType: z.string(),
      }),
    },
  };

  fastify.post<{
    Querystring: z.infer<typeof uploadSchema.querystring>;
  }>(
    '/upload',
    {
      schema: uploadSchema,
      preHandler: fastify.authenticate,
    },
    async (request, reply) => {
      const { entityType, entityId } = request.query;
      const data = await request.file();

      if (!data) {
        return reply.code(400).send({
          error: 'No file uploaded',
          code: 'NO_FILE',
        });
      }

      try {
        // Convert multipart file to our expected format
        const fileBuffer = await data.toBuffer();
        const uploadFile = {
          fieldname: data.fieldname,
          filename: data.filename,
          mimetype: data.mimetype,
          size: fileBuffer.length,
          data: fileBuffer,
        };

        const result = await attachmentService.uploadFile(
          uploadFile,
          { entityType, entityId },
          request.user.id,
        );

        return reply.send(result);
      } catch (error) {
        fastify.log.error(`File upload failed: ${error}`);
        return reply.code(500).send({
          error: 'File upload failed',
          code: 'UPLOAD_FAILED',
        });
      }
    },
  );

  // Get signed URL endpoint
  const signedUrlSchema = {
    params: z.object({
      id: z.string().uuid(),
    }),
    querystring: z.object({
      expiry: z.coerce.number().min(60).max(604_800).optional(), // 1 min to 7 days
    }),
    response: {
      200: z.object({
        url: z.string().url(),
        expiresIn: z.number(),
      }),
    },
  };

  fastify.get<{
    Params: z.infer<typeof signedUrlSchema.params>;
    Querystring: z.infer<typeof signedUrlSchema.querystring>;
  }>(
    '/:id/signed-url',
    {
      schema: signedUrlSchema,
      preHandler: fastify.authenticate,
    },
    async (request, reply) => {
      const { id } = request.params;
      const { expiry = 3600 } = request.query;

      try {
        const signedUrl = await attachmentService.getSignedUrl(id, expiry);
        return reply.send({
          url: signedUrl,
          expiresIn: expiry,
        });
      } catch (error: any) {
        if (error?.statusCode === 404) {
          return reply.code(404).send({
            error: 'Attachment not found',
            code: 'NOT_FOUND',
          });
        }
        throw error;
      }
    },
  );

  // List attachments for an entity
  const listByEntitySchema = {
    querystring: z.object({
      entityType: z.enum(attachmentEntityTypeEnum.enumValues),
      entityId: z.string().uuid(),
    }),
    response: {
      200: z.array(selectAttachmentSchema),
    },
  };

  fastify.get<{
    Querystring: z.infer<typeof listByEntitySchema.querystring>;
  }>(
    '/by-entity',
    {
      schema: listByEntitySchema,
      preHandler: fastify.authenticate,
    },
    async (request, reply) => {
      const { entityType, entityId } = request.query;

      const attachments = await attachmentService.listAttachments(entityType, entityId);
      return reply.send(attachments);
    },
  );

  // Use the createCrudRouter for standard CRUD operations
  const attachmentRouter = createCrudRouter({
    // Resource information
    name: ['attachment', 'attachments'],
    selectSchema: selectAttachmentSchema,
    insertSchema: insertAttachmentSchema,
    patchSchema: patchAttachmentSchema,
    table: attachment,
    queryApi: db.query.attachment,
    relations: attachmentRelations,
    with: {
      uploader: {
        columns: {
          id: true,
          email: true,
          displayName: true,
        },
      },
    },

    // Response transformation to include entity objects
    postFindMany: async (data: any) => {
      return await attachmentService.enrichMultipleWithEntityObjects(data);
    },
    postFindOne: async (data: any) => {
      return await attachmentService.enrichWithEntityObject(data);
    },

    // Route configuration
    tags: ['Attachments'],
    enabled: {
      findMany: true,
      findOne: true,
      create: false, // Use upload endpoint instead
      update: true,
      delete: false, // Use custom delete to remove from MinIO
    },

    // Query defaults
    defaultSort: [{ field: 'uploadedAt', order: 'desc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,
  });

  // Custom delete endpoint that also removes from MinIO
  const deleteSchema = {
    params: z.object({
      id: z.string().uuid(),
    }),
    response: {
      204: z.null(),
    },
  };

  fastify.delete<{
    Params: z.infer<typeof deleteSchema.params>;
  }>(
    '/:id',
    {
      schema: deleteSchema,
      preHandler: fastify.authenticate,
    },
    async (request, reply) => {
      const { id } = request.params;

      try {
        await attachmentService.deleteFile(id, request.user.id);
        return reply.code(204).send();
      } catch (error: any) {
        if (error?.statusCode === 404) {
          return reply.code(404).send({
            error: 'Attachment not found',
            code: 'NOT_FOUND',
          });
        }
        throw error;
      }
    },
  );

  // Register the CRUD router
  await fastify.register(attachmentRouter);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/attachments';
