import { db } from '@repo/db';
import {
  insertWorkflowStatusSchema,
  patchWorkflowStatusSchema,
  selectWorkflowStatusSchema,
  workflowStatus,
  workflowStatusRelations,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { createCrudRouter } from '../utils/crud/create-crud-router';

/**
 * Workflow Status routes plugin
 * Will be automatically loaded and registered at /api/v1/workflow-statuses
 */
export default async function workflowStatusRoutes(fastify: FastifyInstance) {
  // Use the createCrudRouter to automatically generate workflow status routes
  const workflowStatusRouter = createCrudRouter({
    // Resource information
    name: ['workflow-status', 'workflow-statuses'],
    selectSchema: selectWorkflowStatusSchema,
    insertSchema: insertWorkflowStatusSchema,
    patchSchema: patchWorkflowStatusSchema,
    table: workflowStatus,
    queryApi: db.query.workflowStatus,
    relations: workflowStatusRelations,
    with: {
      workflow: {
        columns: {
          id: true,
          name: true,
        },
      },
      status: {
        columns: {
          id: true,
          name: true,
          color: true,
          statusType: true,
        },
      },
    },

    // Route configuration
    tags: ['Workflow Statuses'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [{ field: 'createdAt', order: 'desc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,
  });

  // Register the CRUD router
  await fastify.register(workflowStatusRouter);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/workflow-statuses';
