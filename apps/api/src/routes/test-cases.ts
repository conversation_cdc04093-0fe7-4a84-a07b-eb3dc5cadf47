import { db } from "@repo/db";
import {
  insertTestCaseSchema,
  patchTestCaseSchema,
  selectTestCaseSchema,
  testCase,
  testCaseRelations,
} from "@repo/db/schema";
import type { FastifyInstance } from "fastify";
import { testCaseService } from "../services/test-case-service";
import { createCrudRouter } from "../utils/crud/create-crud-router";

export default async function testCaseRoutes(fastify: FastifyInstance) {
  const testCaseRouter = createCrudRouter({
    // Resource information
    name: ["test case", "test cases"],
    selectSchema: selectTestCaseSchema,
    insertSchema: insertTestCaseSchema,
    patchSchema: patchTestCaseSchema,
    table: testCase,
    queryApi: db.query.testCase,
    relations: testCaseRelations,

    // Service integration for history tracking
    service: testCaseService,

    // Pre-handler to handle legacy testSuiteId filter
    preFindMany: async (request, reply) => {
      const { filters } = request.query as any;
      if (filters) {
        try {
          const parsedFilters = JSON.parse(filters);
          if (parsedFilters.testSuiteId?.$eq) {
            const testSuiteId = parsedFilters.testSuiteId.$eq;
            // Get test cases for this suite using our service
            const testCases = await testCaseService.getTestCasesForSuite(
              testSuiteId,
              {
                userId: request.user?.id,
                with: {
                  project: true,
                  assignee: true,
                  reporter: true,
                  creator: true,
                  workItem: true,
                },
              }
            );

            // Return in the expected format
            return reply.send({
              data: testCases,
              meta: {
                totalItems: testCases.length,
                itemCount: testCases.length,
                itemsPerPage: 100,
                totalPages: 1,
                currentPage: 1,
              },
            });
          }
        } catch (e) {
          request.log.error(e);
          // If it's not a testSuiteId filter, continue to the normal handler
        }
      }
      // Continue to the normal CRUD handler
    },

    // Include related data
    with: {
      project: {
        columns: {
          id: true,
          name: true,
          key: true,
        },
      },
      assignee: {
        columns: {
          id: true,
          email: true,
          displayName: true,
        },
      },
      reporter: {
        columns: {
          id: true,
          email: true,
          displayName: true,
        },
      },
      creator: {
        columns: {
          id: true,
          email: true,
          displayName: true,
        },
      },
      workItem: {
        columns: {
          id: true,
          title: true,
          key: true,
        },
      },
    },

    // Route configuration
    tags: ["Test Cases"],
    enabled: { all: true },

    // Query defaults
    defaultSort: [
      { field: "order", order: "asc" },
      { field: "createdAt", order: "desc" },
    ],

    // Authentication - all endpoints require authentication
    authenticate: {
      findMany: true,
      findOne: true,
      create: true,
      update: true,
      delete: true,
    },

    // Options
    softDelete: false,
  });

  await fastify.register(testCaseRouter);
}

export const autoPrefix = "/test-cases";
