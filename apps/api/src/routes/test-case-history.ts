import { db } from '@repo/db';
import {
  selectTestCaseHistorySchema,
  testCaseHistory,
  testCaseHistoryRelations,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { createCrudRouter } from '../utils/crud/create-crud-router';

export default async function testCaseHistoryRoutes(fastify: FastifyInstance) {
  const testCaseHistoryRouter = createCrudRouter({
    // Resource information
    name: ['test case history', 'test case history entries'],
    selectSchema: selectTestCaseHistorySchema,
    insertSchema: selectTestCaseHistorySchema, // Use select schema as a placeholder
    patchSchema: selectTestCaseHistorySchema, // Use select schema as a placeholder
    table: testCaseHistory,
    queryApi: db.query.testCaseHistory,
    relations: testCaseHistoryRelations,

    // Include related data
    with: {
      testCase: {
        columns: {
          id: true,
          title: true,
        },
      },
      user: {
        columns: {
          id: true,
          email: true,
          displayName: true,
        },
      },
    },

    // Route configuration
    tags: ['Test Case History'],

    // Only enable read operations - history is created automatically
    enabled: {
      findMany: true,
      findOne: true,
      create: false,
      update: false,
      delete: false,
    },

    // Query defaults
    defaultSort: [{ field: 'createdAt', order: 'desc' }],

    // Authentication - all endpoints require authentication
    authenticate: {
      findMany: true,
      findOne: true,
    },

    // Options
    softDelete: false,
  });

  await fastify.register(testCaseHistoryRouter);
}

export const autoPrefix = '/test-case-history';
