import { db } from '@repo/db';
import {
  insertWorkspaceSchema,
  patchWorkspaceSchema,
  selectWorkspaceSchema,
  workspace,
  workspaceRelations,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { WorkspaceService } from '../services/workspace-service';
import { createCrudRouter } from '../utils/crud/create-crud-router';

/**
 * Workspace routes plugin
 * Will be automatically loaded and registered at /api/v1/workspaces
 */
export default async function workspaceRoutes(fastify: FastifyInstance) {
  // Create workspace service instance with fastify instance
  const workspaceService = new WorkspaceService(fastify);

  // Use the createCrudRouter to automatically generate workspace routes
  const workspaceRouter = createCrudRouter({
    // Resource information
    name: ['workspace', 'workspaces'],
    selectSchema: selectWorkspaceSchema,
    insertSchema: insertWorkspaceSchema,
    patchSchema: patchWorkspaceSchema,
    table: workspace,
    queryApi: db.query.workspace,
    relations: workspaceRelations,
    service: workspaceService,
    with: {
      organization: {
        columns: {
          id: true,
          name: true,
          slug: true,
        },
      },
      creator: {
        columns: {
          id: true,
          email: true,
          displayName: true,
        },
      },
    },

    // Route configuration
    tags: ['Workspaces'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [{ field: 'createdAt', order: 'desc' }],

    // Authentication
    authenticate: false,

    // Options
    softDelete: false,
  });

  // Register the CRUD router
  await fastify.register(workspaceRouter);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/workspaces';
