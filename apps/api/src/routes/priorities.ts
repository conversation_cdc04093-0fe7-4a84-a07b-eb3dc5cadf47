import { db } from '@repo/db';
import {
  insertPrioritySchema,
  patchPrioritySchema,
  priority,
  priorityRelations,
  selectPrioritySchema,
} from '@repo/db/schema';
import type { FastifyInstance } from 'fastify';
import { createCrudRouter } from '../utils/crud/create-crud-router';

/**
 * Priority routes plugin
 * Will be automatically loaded and registered at /api/v1/priorities
 */
export default async function priorityRoutes(fastify: FastifyInstance) {
  // Use the createCrudRouter to automatically generate priority routes
  const priorityRouter = createCrudRouter({
    // Resource information
    name: ['priority', 'priorities'],
    selectSchema: selectPrioritySchema,
    insertSchema: insertPrioritySchema,
    patchSchema: patchPrioritySchema,
    table: priority,
    queryApi: db.query.priority,
    relations: priorityRelations,
    with: {
      organization: {
        columns: {
          id: true,
          name: true,
          slug: true,
        },
      },
    },

    // Route configuration
    tags: ['Priorities'],
    enabled: { all: true },

    // Query defaults
    defaultSort: [{ field: 'level', order: 'asc' }],

    // Authentication
    authenticate: true,

    // Options
    softDelete: false,
  });

  // Register the CRUD router
  await fastify.register(priorityRouter);
}

// This is required by autoload to automatically assign the right prefix
export const autoPrefix = '/priorities';
