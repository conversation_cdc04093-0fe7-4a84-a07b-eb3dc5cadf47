// Health check and monitoring endpoints
import type { FastifyInstance } from 'fastify';
import { z } from 'zod';
import {
  getHealthCheckData,
  getPerformanceReport,
  metricsCollector,
} from '../../middleware/performance';

export default async function healthRoutes(fastify: FastifyInstance) {
  // Basic health check
  fastify.route({
    method: 'GET',
    url: '/health',
    schema: {
      tags: ['Health'],
      summary: 'Health check endpoint',
      response: {
        200: z.object({
          status: z.enum(['healthy', 'warning', 'critical']),
          timestamp: z.string(),
          uptime: z.number(),
          metrics: z.object({
            totalRequests: z.number(),
            errorRate: z.number(),
            avgResponseTime: z.number(),
          }),
        }),
      },
    },
    handler: async (_request, reply) => {
      const healthData = getHealthCheckData();
      return reply.send(healthData);
    },
  });

  // Detailed metrics (admin only)
  fastify.route({
    method: 'GET',
    url: '/metrics',
    schema: {
      tags: ['Health'],
      summary: 'Detailed performance metrics',
      security: [{ bearerAuth: [] }],
    },
    preHandler: [fastify.authenticate],
    handler: async (_request, reply) => {
      // Add role check here if needed
      const performanceReport = getPerformanceReport();
      return reply.send(performanceReport);
    },
  });

  // Live metrics endpoint for real-time monitoring
  fastify.route({
    method: 'GET',
    url: '/metrics/live',
    schema: {
      tags: ['Health'],
      summary: 'Live metrics data',
      security: [{ bearerAuth: [] }],
    },
    preHandler: [fastify.authenticate],
    handler: async (_request, reply) => {
      const metrics = metricsCollector.getMetrics();
      return reply.send({
        timestamp: new Date().toISOString(),
        requests: metrics.requests,
        errors: metrics.errors,
        memory: process.memoryUsage(),
        uptime: process.uptime(),
      });
    },
  });
}

export const autoPrefix = '/admin';
