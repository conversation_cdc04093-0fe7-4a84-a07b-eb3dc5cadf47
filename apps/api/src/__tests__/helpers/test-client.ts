import type { FastifyInstance } from 'fastify';
import request from 'supertest';
import { expect } from 'vitest';

export class TestClient {
  constructor(private app: FastifyInstance) {}

  // Organizations endpoints
  organizations = {
    getAll: (query: Record<string, any> = {}) => {
      const queryString = new URLSearchParams(query).toString();
      const url = `/api/v1/organizations${queryString ? `?${queryString}` : ''}`;
      return request(this.app.server).get(url);
    },

    getById: (id: string) => {
      return request(this.app.server).get(`/api/v1/organizations/${id}`);
    },

    create: (data: any) => {
      return request(this.app.server).post('/api/v1/organizations').send(data);
    },

    update: (id: string, data: any) => {
      return request(this.app.server).patch(`/api/v1/organizations/${id}`).send(data);
    },

    delete: (id: string) => {
      return request(this.app.server).delete(`/api/v1/organizations/${id}`);
    },
  };

  // Helper methods for common patterns
  async expectSuccessResponse(response: any, expectedStatus = 200) {
    expect(response.status).toBe(expectedStatus);
    expect(response.body).toHaveProperty('data');
    return response.body.data;
  }

  async expectErrorResponse(response: any, expectedStatus: number, expectedError?: string) {
    expect(response.status).toBe(expectedStatus);
    expect(response.body).toHaveProperty('errorCode');
    expect(response.body).toHaveProperty('message');
    if (expectedError) {
      expect(response.body.message).toContain(expectedError);
    }
    return response.body;
  }

  async expectListResponse(response: any, expectedStatus = 200) {
    expect(response.status).toBe(expectedStatus);
    expect(response.body).toHaveProperty('data');
    expect(response.body).toHaveProperty('meta');
    expect(Array.isArray(response.body.data)).toBe(true);
    expect(response.body.meta).toHaveProperty('totalItems');
    expect(response.body.meta).toHaveProperty('currentPage');
    expect(response.body.meta).toHaveProperty('totalPages');
    return response.body;
  }
}
