import { organization, project, testCase, testPlan, testSuite, workspace } from '@repo/db/schema';
import { afterEach, beforeEach, describe, expect, it } from 'vitest';
import { MinimalTestApp } from '../setup/test-app-minimal';

describe('Test Suite Routes', () => {
  let testApp: MinimalTestApp;
  let app: any;
  let testPlanId: string;
  let projectId: string;

  beforeEach(async () => {
    testApp = new MinimalTestApp();
    app = await testApp.build();

    // Clean up before each test
    await testApp.cleanup();

    // Create test data directly in database
    const [org] = await app.db
      .insert(organization)
      .values({
        name: 'Test Organization',
        slug: 'test-org',
      })
      .returning();

    const [ws] = await app.db
      .insert(workspace)
      .values({
        name: 'Test Workspace',
        organizationId: org.id,
      })
      .returning();

    const [proj] = await app.db
      .insert(project)
      .values({
        name: 'Test Project',
        key: 'TEST',
        workspaceId: ws.id,
      })
      .returning();
    projectId = proj.id;

    // Create a test plan directly in database
    const [plan] = await app.db
      .insert(testPlan)
      .values({
        name: 'Test Plan',
        projectId,
      })
      .returning();
    testPlanId = plan.id;
  });

  afterEach(async () => {
    if (testApp) {
      await testApp.close();
    }
  });

  describe('DELETE /api/v1/test-suites/:id', () => {
    it('should delete a test suite without dependencies', async () => {
      // Create a test suite
      const [suite] = await app.db
        .insert(testSuite)
        .values({
          name: 'Test Suite to Delete',
          testPlanId,
        })
        .returning();

      const response = await app.inject({
        method: 'DELETE',
        url: `/api/v1/test-suites/${suite.id}`,
      });

      expect(response.statusCode).toBe(204);

      // Verify the test suite was deleted
      const deletedSuite = await app.db.query.testSuite.findFirst({
        where: (testSuite, { eq }) => eq(testSuite.id, suite.id),
      });
      expect(deletedSuite).toBeUndefined();
    });

    it('should not delete a test suite with test cases', async () => {
      // Create a test suite
      const [suite] = await app.db
        .insert(testSuite)
        .values({
          name: 'Test Suite with Cases',
          testPlanId,
        })
        .returning();

      // Create a test case in the suite
      await app.db.insert(testCase).values({
        title: 'Test Case 1',
        testSuiteId: suite.id,
        projectId,
      });

      const response = await app.inject({
        method: 'DELETE',
        url: `/api/v1/test-suites/${suite.id}`,
      });

      expect(response.statusCode).toBe(409);
      const body = JSON.parse(response.body);
      expect(body.message).toContain('contains test cases');

      // Verify the test suite was not deleted
      const existingSuite = await app.db.query.testSuite.findFirst({
        where: (testSuite, { eq }) => eq(testSuite.id, suite.id),
      });
      expect(existingSuite).toBeDefined();
    });

    it('should not delete a test suite with child suites', async () => {
      // Create a parent test suite
      const [parentSuite] = await app.db
        .insert(testSuite)
        .values({
          name: 'Parent Test Suite',
          testPlanId,
        })
        .returning();

      // Create a child test suite
      await app.db.insert(testSuite).values({
        name: 'Child Test Suite',
        testPlanId,
        parentSuiteId: parentSuite.id,
      });

      const response = await app.inject({
        method: 'DELETE',
        url: `/api/v1/test-suites/${parentSuite.id}`,
      });

      expect(response.statusCode).toBe(409);
      const body = JSON.parse(response.body);
      expect(body.message).toContain('contains child test suites');

      // Verify the parent suite was not deleted
      const existingSuite = await app.db.query.testSuite.findFirst({
        where: (testSuite, { eq }) => eq(testSuite.id, parentSuite.id),
      });
      expect(existingSuite).toBeDefined();
    });

    it('should return 404 for non-existent test suite', async () => {
      const response = await app.inject({
        method: 'DELETE',
        url: '/api/v1/test-suites/af0106c5-f2cd-4bd5-8d95-182cb143949b',
      });

      expect(response.statusCode).toBe(404);
      const body = JSON.parse(response.body);
      expect(body.message).toContain('not found');
    });
  });
});
