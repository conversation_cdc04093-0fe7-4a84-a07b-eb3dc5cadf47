import { db } from '@repo/db';
import { invitation, organization, roleAssignment, user } from '@repo/db/schema';
import { eq } from 'drizzle-orm';
import { afterEach, beforeEach, describe, expect, it } from 'vitest';
import { ApiError } from '../../errors/api-error';
import { InvitationService } from '../../services/invitation-service';
import { OrganizationService } from '../../services/organization-service';
import { RoleAssignmentService } from '../../services/role-assignment-service';

const invitationService = new InvitationService();
const roleAssignmentService = new RoleAssignmentService();
const organizationService = new OrganizationService();

describe('Invitation Validation Edge Cases', () => {
  let testUser: any;
  let inviterUser: any;
  let testOrg: any;
  let viewerRole: any;
  let adminRole: any;

  beforeEach(async () => {
    // Clean up database
    await db.delete(invitation);
    await db.delete(roleAssignment);
    await db.delete(organization);
    await db.delete(user);

    // Get test roles (they should exist from seeding)
    const roles = await db.query.role.findMany({
      where: (r, { inArray }) => inArray(r.identifier, ['org_member', 'org_admin', 'org_owner']),
    });

    viewerRole = roles.find((r) => r.identifier === 'org_member');
    adminRole = roles.find((r) => r.identifier === 'org_admin');

    if (!(viewerRole && adminRole)) {
      throw new Error('Required roles not found. Please run database seeding.');
    }

    // Create test users
    const users = await db
      .insert(user)
      .values([
        {
          email: '<EMAIL>',
          firstName: 'Inviter',
          lastName: 'User',
          displayName: 'Inviter User',
          password: 'test',
        },
        {
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          displayName: 'Test User',
          password: 'test',
        },
      ])
      .returning();

    inviterUser = users[0];
    testUser = users[1];

    // Create test organization
    testOrg = await organizationService.create(
      {
        name: 'Test Organization',
        slug: `test-org-${Date.now()}`,
      },
      { userId: inviterUser.id },
    );
  });

  afterEach(async () => {
    // Cleanup
    await db.delete(invitation);
    await db.delete(roleAssignment);
    await db.delete(organization);
    await db.delete(user);
  });

  describe('Duplicate Invitation Prevention', () => {
    it('should prevent duplicate pending invitations to the same scope', async () => {
      // Create first invitation
      await invitationService.create(
        {
          email: '<EMAIL>',
          invitedBy: inviterUser.id,
          roleId: viewerRole.id,
          scopeType: 'organization',
          scopeId: testOrg.id,
        },
        { userId: inviterUser.id },
      );

      // Try to create duplicate invitation
      await expect(
        invitationService.create(
          {
            email: '<EMAIL>',
            invitedBy: inviterUser.id,
            roleId: viewerRole.id,
            scopeType: 'organization',
            scopeId: testOrg.id,
          },
          { userId: inviterUser.id },
        ),
      ).rejects.toThrow(ApiError);
    });

    it('should prevent invitations with different roles to the same scope', async () => {
      // Create first invitation with viewer role
      await invitationService.create(
        {
          email: '<EMAIL>',
          invitedBy: inviterUser.id,
          roleId: viewerRole.id,
          scopeType: 'organization',
          scopeId: testOrg.id,
        },
        { userId: inviterUser.id },
      );

      // Try to create invitation with admin role
      await expect(
        invitationService.create(
          {
            email: '<EMAIL>',
            invitedBy: inviterUser.id,
            roleId: adminRole.id,
            scopeType: 'organization',
            scopeId: testOrg.id,
          },
          { userId: inviterUser.id },
        ),
      ).rejects.toThrow(/pending invitation already exists.*Please revoke/);
    });

    it('should prevent invitations to users with existing active roles', async () => {
      // Assign role to user
      await roleAssignmentService.create(
        {
          userId: testUser.id,
          roleId: viewerRole.id,
          scopeType: 'organization',
          scopeId: testOrg.id,
          isActive: true,
        },
        { userId: inviterUser.id },
      );

      // Try to invite user who already has a role
      await expect(
        invitationService.create(
          {
            email: testUser.email,
            invitedBy: inviterUser.id,
            roleId: adminRole.id,
            scopeType: 'organization',
            scopeId: testOrg.id,
          },
          { userId: inviterUser.id },
        ),
      ).rejects.toThrow(/already has an active role/);
    });
  });

  describe('Role Assignment Uniqueness', () => {
    it('should prevent multiple active roles for the same user in the same scope', async () => {
      // Create first role assignment
      await roleAssignmentService.create(
        {
          userId: testUser.id,
          roleId: viewerRole.id,
          scopeType: 'organization',
          scopeId: testOrg.id,
          isActive: true,
        },
        { userId: inviterUser.id },
      );

      // Try to create another role assignment
      await expect(
        roleAssignmentService.create(
          {
            userId: testUser.id,
            roleId: adminRole.id,
            scopeType: 'organization',
            scopeId: testOrg.id,
            isActive: true,
          },
          { userId: inviterUser.id },
        ),
      ).rejects.toThrow(/already has an active role/);
    });
  });

  describe('Auto-Assignment Edge Cases', () => {
    it('should auto-accept pending invitations when user creates an entity', async () => {
      // Create pending invitation for testUser
      const inv = await invitationService.create(
        {
          email: testUser.email,
          invitedBy: inviterUser.id,
          roleId: viewerRole.id,
          scopeType: 'organization',
          scopeId: testOrg.id,
        },
        { userId: inviterUser.id },
      );

      // Simulate assigning role to user (as would happen during entity creation)
      await organizationService.assignUserRole(testOrg.id, testUser.id, 'org_owner');

      // Check that invitation was auto-accepted
      const updatedInvitation = await db.query.invitation.findFirst({
        where: eq(invitation.id, inv.id),
      });

      expect(updatedInvitation?.status).toBe('accepted');
    });
  });
});
