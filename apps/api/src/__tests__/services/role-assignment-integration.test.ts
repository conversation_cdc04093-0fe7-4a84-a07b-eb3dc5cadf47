import { db } from '@repo/db';
import { and, eq } from '@repo/db/orm';
import { organization, role, roleAssignment, user, workspace } from '@repo/db/schema';
import { afterEach, beforeEach, describe, expect, it } from 'vitest';
import { OrganizationService } from '../../services/organization-service';
import { ProjectService } from '../../services/project-service';
import { WorkspaceService } from '../../services/workspace-service';
import { MinimalTestApp } from '../setup/test-app-minimal';

describe('Role Assignment Integration Tests', () => {
  let testApp: MinimalTestApp;
  let workspaceService: WorkspaceService;
  let projectService: ProjectService;
  let organizationService: OrganizationService;

  let testUser: any;
  let testOrg: any;
  let adminRole: any;

  beforeEach(async () => {
    // Set up test app
    testApp = new MinimalTestApp();
    await testApp.build();
    await testApp.cleanup();

    // Initialize services
    workspaceService = new WorkspaceService();
    projectService = new ProjectService();
    organizationService = new OrganizationService();

    // Create test data
    // Create admin role
    [adminRole] = await db
      .insert(role)
      .values({
        name: 'Admin',
        identifier: 'admin',
        description: 'Administrator role',
        level: 'admin',
        isSystem: true,
        isActive: true,
      })
      .returning();

    // Create test user
    [testUser] = await db
      .insert(user)
      .values({
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        displayName: 'Test User',
        password: 'hashed_password', // In real tests, this would be properly hashed
        isActive: true,
        isEmailVerified: true,
      })
      .returning();

    // Create test organization
    [testOrg] = await db
      .insert(organization)
      .values({
        name: 'Test Organization',
        slug: 'test-org',
        settings: {},
        isActive: true,
      })
      .returning();
  });

  afterEach(async () => {
    // Clean up - since we're using a test database, we can safely truncate
    await testApp.cleanup();
    await testApp.close();
  });

  describe('Organization Role Assignment', () => {
    it('should assign admin role to creator when organization is created', async () => {
      const context = { userId: testUser.id };

      // Create organization through service
      const orgData = {
        name: 'New Organization',
        slug: 'new-org',
        settings: {},
        isActive: true,
        createdBy: testUser.id,
      };

      const createdOrg = await organizationService.create(orgData, context);

      // Check if role assignment was created
      const assignment = await db.query.roleAssignment.findFirst({
        where: and(
          eq(roleAssignment.userId, testUser.id),
          eq(roleAssignment.roleId, adminRole.id),
          eq(roleAssignment.scopeType, 'organization'),
          eq(roleAssignment.scopeId, createdOrg.id),
        ),
      });

      expect(assignment).toBeTruthy();
      expect(assignment?.isActive).toBe(true);
    });
  });

  describe('Workspace Role Assignment', () => {
    it('should assign admin role to creator when workspace is created', async () => {
      const context = { userId: testUser.id };

      // Create workspace through service
      const workspaceData = {
        name: 'New Workspace',
        slug: 'new-workspace',
        organizationId: testOrg.id,
        settings: {},
        isActive: true,
      };

      const createdWorkspace = await workspaceService.create(workspaceData, context);

      // Check if role assignment was created
      const assignment = await db.query.roleAssignment.findFirst({
        where: and(
          eq(roleAssignment.userId, testUser.id),
          eq(roleAssignment.roleId, adminRole.id),
          eq(roleAssignment.scopeType, 'workspace'),
          eq(roleAssignment.scopeId, createdWorkspace.id),
        ),
      });

      expect(assignment).toBeTruthy();
      expect(assignment?.isActive).toBe(true);
    });

    it('should not create duplicate role assignment if called multiple times', async () => {
      const context = { userId: testUser.id };

      // Create workspace
      const workspaceData = {
        name: 'Test Workspace',
        slug: 'test-workspace',
        organizationId: testOrg.id,
        settings: {},
        isActive: true,
      };

      const createdWorkspace = await workspaceService.create(workspaceData, context);

      // Manually call afterCreate again
      await workspaceService.afterCreate(createdWorkspace, context);

      // Check that only one assignment exists
      const assignments = await db.query.roleAssignment.findMany({
        where: and(
          eq(roleAssignment.userId, testUser.id),
          eq(roleAssignment.scopeType, 'workspace'),
          eq(roleAssignment.scopeId, createdWorkspace.id),
        ),
      });

      expect(assignments).toHaveLength(1);
    });
  });

  describe('Project Role Assignment', () => {
    it('should assign admin role to creator when project is created', async () => {
      const context = { userId: testUser.id };

      // First create a workspace
      const [testWorkspace] = await db
        .insert(workspace)
        .values({
          name: 'Test Workspace',
          slug: 'test-workspace',
          organizationId: testOrg.id,
          settings: {},
          isActive: true,
        })
        .returning();

      // Create project through service
      const projectData = {
        name: 'New Project',
        key: 'NEWP',
        workspaceId: testWorkspace.id,
        settings: {},
        isActive: true,
      };

      const createdProject = await projectService.create(projectData, context);

      // Check if role assignment was created
      const assignment = await db.query.roleAssignment.findFirst({
        where: and(
          eq(roleAssignment.userId, testUser.id),
          eq(roleAssignment.roleId, adminRole.id),
          eq(roleAssignment.scopeType, 'project'),
          eq(roleAssignment.scopeId, createdProject.id),
        ),
      });

      expect(assignment).toBeTruthy();
      expect(assignment?.isActive).toBe(true);
    });

    it('should auto-generate project key and assign role', async () => {
      const context = { userId: testUser.id };

      // Create workspace
      const [testWorkspace] = await db
        .insert(workspace)
        .values({
          name: 'Test Workspace',
          slug: 'test-workspace',
          organizationId: testOrg.id,
          settings: {},
          isActive: true,
        })
        .returning();

      // Create project without key
      const projectData = {
        name: 'My Awesome Project',
        workspaceId: testWorkspace.id,
        settings: {},
        isActive: true,
      };

      const createdProject = await projectService.create(projectData as any, context);

      // Check project key was generated
      expect(createdProject.key).toBe('MAP'); // First letter of each word

      // Check role assignment
      const assignment = await db.query.roleAssignment.findFirst({
        where: and(
          eq(roleAssignment.userId, testUser.id),
          eq(roleAssignment.scopeType, 'project'),
          eq(roleAssignment.scopeId, createdProject.id),
        ),
      });

      expect(assignment).toBeTruthy();
    });
  });

  describe('Cross-scope Role Assignments', () => {
    it('should allow same user to have roles in organization, workspace, and project', async () => {
      const context = { userId: testUser.id };

      // Create organization
      const orgData = {
        name: 'Multi-scope Org',
        slug: 'multi-scope-org',
        settings: {},
        isActive: true,
        createdBy: testUser.id,
      };
      const createdOrg = await organizationService.create(orgData, context);

      // Create workspace
      const workspaceData = {
        name: 'Multi-scope Workspace',
        slug: 'multi-scope-workspace',
        organizationId: createdOrg.id,
        settings: {},
        isActive: true,
      };
      const createdWorkspace = await workspaceService.create(workspaceData, context);

      // Create project
      const projectData = {
        name: 'Multi-scope Project',
        key: 'MSP',
        workspaceId: createdWorkspace.id,
        settings: {},
        isActive: true,
      };
      const createdProject = await projectService.create(projectData, context);

      // Check all three role assignments exist
      const assignments = await db.query.roleAssignment.findMany({
        where: eq(roleAssignment.userId, testUser.id),
      });

      // Should have 3 assignments (org, workspace, project)
      expect(assignments.length).toBeGreaterThanOrEqual(3);

      // Check each scope
      const orgAssignment = assignments.find(
        (a) => a.scopeType === 'organization' && a.scopeId === createdOrg.id,
      );
      const wsAssignment = assignments.find(
        (a) => a.scopeType === 'workspace' && a.scopeId === createdWorkspace.id,
      );
      const projAssignment = assignments.find(
        (a) => a.scopeType === 'project' && a.scopeId === createdProject.id,
      );

      expect(orgAssignment).toBeTruthy();
      expect(wsAssignment).toBeTruthy();
      expect(projAssignment).toBeTruthy();
    });
  });
});
