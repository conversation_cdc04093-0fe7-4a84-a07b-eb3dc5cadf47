import { beforeEach, describe, expect, it, vi } from 'vitest';
import type { ServiceContext } from '../../services/base-service';
import { OrganizationService } from '../../services/organization-service';

// Mock the upload utility functions
vi.mock('../../utils/upload/file-upload', () => ({
  generatePublicUrl: vi.fn((key: string) => `https://minio.example.com/${key}`),
  generateStorageKey: vi.fn(
    (entityType: string, entityId: string, filename: string) =>
      `${entityType}/${entityId}/${filename}`,
  ),
}));

describe('OrganizationService - Base64 Upload', () => {
  let organizationService: OrganizationService;
  let mockFastify: any;

  beforeEach(() => {
    // Create a mock Fastify instance
    mockFastify = {
      minio: {
        putObject: vi.fn().mockResolvedValue(undefined),
        removeObject: vi.fn().mockResolvedValue(undefined),
      },
      config: {
        minio: {
          bucketName: 'test-bucket',
        },
      },
    };

    organizationService = new OrganizationService(mockFastify);
  });

  describe('beforeCreate', () => {
    it('should handle base64 logo upload during organization creation', async () => {
      const context: ServiceContext = {
        userId: 'user-123',
        organizationId: 'org-123',
      };

      const base64Logo =
        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

      const organizationData = {
        name: 'Test Organization',
        slug: 'test-org',
        createdBy: 'user-123',
        logoUrl: base64Logo,
      };

      const result = await organizationService.beforeCreate(organizationData, context);

      // Verify that the base64 was converted to a URL
      expect(result.logoUrl).toMatch(
        /^https:\/\/minio\.example\.com\/organization\/temp-\d+\/test-organization-logo-\d+\.png$/,
      );
      expect(result.logoUrl).not.toContain('data:image');

      // Verify MinIO upload was called
      expect(mockFastify.minio.putObject).toHaveBeenCalled();
    });

    it("should not modify logoUrl if it's already a URL", async () => {
      const context: ServiceContext = {
        userId: 'user-123',
        organizationId: 'org-123',
      };

      const organizationData = {
        name: 'Test Organization',
        slug: 'test-org',
        createdBy: 'user-123',
        logoUrl: 'https://example.com/logo.png',
      };

      const result = await organizationService.beforeCreate(organizationData, context);

      // Verify URL was not modified
      expect(result.logoUrl).toBe('https://example.com/logo.png');
      expect(mockFastify.minio.putObject).not.toHaveBeenCalled();
    });

    it('should handle organization creation without logo', async () => {
      const context: ServiceContext = {
        userId: 'user-123',
        organizationId: 'org-123',
      };

      const organizationData = {
        name: 'Test Organization',
        slug: 'test-org',
        createdBy: 'user-123',
      };

      const result = await organizationService.beforeCreate(organizationData, context);

      // Verify no logoUrl was added
      expect(result.logoUrl).toBeUndefined();
      expect(mockFastify.minio.putObject).not.toHaveBeenCalled();
    });
  });

  describe('beforeUpdate', () => {
    it('should handle base64 logo upload during organization update', async () => {
      const context: ServiceContext = {
        userId: 'user-123',
        organizationId: 'org-123',
      };

      const base64Logo =
        'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCwAA8A/9k=';

      const updateData = {
        logoUrl: base64Logo,
      };

      // Mock findById to return existing organization
      vi.spyOn(organizationService, 'findById').mockResolvedValue({
        id: 'org-123',
        name: 'Test Organization',
        logoUrl: 'https://minio.example.com/old-logo.png',
      } as any);

      const result = await organizationService.beforeUpdate('org-123', updateData, context);

      // Verify that the base64 was converted to a URL
      expect(result.logoUrl).toMatch(
        /^https:\/\/minio\.example\.com\/organization\/temp-\d+\/test-organization-logo-\d+\.jpeg$/,
      );
      expect(result.logoUrl).not.toContain('data:image');

      // Verify old logo deletion was attempted
      expect(mockFastify.minio.removeObject).toHaveBeenCalled();

      // Verify new logo upload
      expect(mockFastify.minio.putObject).toHaveBeenCalled();
    });
  });
});
