import { describe, expect, it } from 'vitest';
import { createDateSerializer, serializeDates } from '../../utils/date-serialization';

describe('Date Serialization Utils', () => {
  describe('serializeDates', () => {
    it('should convert Date objects to ISO strings', () => {
      const date = new Date('2024-01-15T10:30:00Z');
      const result = serializeDates(date);
      expect(result).toBe('2024-01-15T10:30:00.000Z');
    });

    it('should handle null and undefined values', () => {
      expect(serializeDates(null)).toBe(null);
      expect(serializeDates(undefined)).toBe(undefined);
    });

    it('should recursively convert dates in objects', () => {
      const obj = {
        id: '123',
        name: 'Test',
        createdAt: new Date('2024-01-15T10:30:00Z'),
        updatedAt: new Date('2024-01-16T15:45:00Z'),
        nested: {
          date: new Date('2024-01-17T20:00:00Z'),
          value: 42,
        },
      };

      const result = serializeDates(obj);

      expect(result).toEqual({
        id: '123',
        name: 'Test',
        createdAt: '2024-01-15T10:30:00.000Z',
        updatedAt: '2024-01-16T15:45:00.000Z',
        nested: {
          date: '2024-01-17T20:00:00.000Z',
          value: 42,
        },
      });
    });

    it('should handle arrays with dates', () => {
      const arr = [
        { id: 1, date: new Date('2024-01-15T10:30:00Z') },
        { id: 2, date: new Date('2024-01-16T15:45:00Z') },
        'string',
        42,
        null,
      ];

      const result = serializeDates(arr);

      expect(result).toEqual([
        { id: 1, date: '2024-01-15T10:30:00.000Z' },
        { id: 2, date: '2024-01-16T15:45:00.000Z' },
        'string',
        42,
        null,
      ]);
    });

    it('should handle complex nested structures', () => {
      const complex = {
        projects: [
          {
            id: 'p1',
            startDate: new Date('2024-01-01T00:00:00Z'),
            endDate: new Date('2024-12-31T23:59:59Z'),
            tasks: [
              {
                id: 't1',
                createdAt: new Date('2024-02-01T10:00:00Z'),
                assignee: {
                  lastLogin: new Date('2024-03-01T12:00:00Z'),
                },
              },
            ],
          },
        ],
        metadata: {
          lastSync: new Date('2024-01-20T08:00:00Z'),
          settings: {
            timezone: 'UTC',
            updatedAt: new Date('2024-01-10T16:30:00Z'),
          },
        },
      };

      const result = serializeDates(complex);

      expect(result.projects[0].startDate).toBe('2024-01-01T00:00:00.000Z');
      expect(result.projects[0].endDate).toBe('2024-12-31T23:59:59.000Z');
      expect(result.projects[0].tasks[0].createdAt).toBe('2024-02-01T10:00:00.000Z');
      expect(result.projects[0].tasks[0].assignee.lastLogin).toBe('2024-03-01T12:00:00.000Z');
      expect(result.metadata.lastSync).toBe('2024-01-20T08:00:00.000Z');
      expect(result.metadata.settings.updatedAt).toBe('2024-01-10T16:30:00.000Z');
    });

    it('should not modify non-date values', () => {
      const obj = {
        string: 'hello',
        number: 42,
        boolean: true,
        array: [1, 2, 3],
        object: { key: 'value' },
      };

      const result = serializeDates(obj);
      expect(result).toEqual(obj);
    });
  });

  describe('createDateSerializer', () => {
    it('should return a function that serializes dates', () => {
      const serializer = createDateSerializer();
      const data = {
        id: '123',
        createdAt: new Date('2024-01-15T10:30:00Z'),
        user: {
          lastLogin: new Date('2024-01-16T15:45:00Z'),
        },
      };

      const result = serializer(data);

      expect(result).toEqual({
        id: '123',
        createdAt: '2024-01-15T10:30:00.000Z',
        user: {
          lastLogin: '2024-01-16T15:45:00.000Z',
        },
      });
    });
  });
});
