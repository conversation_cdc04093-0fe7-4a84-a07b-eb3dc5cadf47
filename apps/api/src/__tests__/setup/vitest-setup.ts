// Set test environment variables BEFORE any imports
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'postgresql://postgres:postgres@localhost:5432/spark_test';

// Note: We're not using TestContainer for now since we have a dedicated test database
// If you want to use TestContainer, uncomment the code below

/*
let testContainer: TestContainer;

beforeAll(async () => {
  testContainer = new TestContainer();
  await testContainer.start();
  // Override with container URL
  process.env.DATABASE_URL = testContainer.getConnectionString();
}, 60000);

afterAll(async () => {
  if (testContainer) {
    await testContainer.stop();
  }
});
*/
