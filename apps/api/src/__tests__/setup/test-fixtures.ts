import { db } from '@repo/db';
import type { OrganizationInsert } from '@repo/db/schema';
import { organization, user } from '@repo/db/schema';

export class TestFixtures {
  // User fixtures
  static async createUser(userData: Partial<typeof user.$inferInsert> = {}) {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(7);

    const defaultUser = {
      email: `test-${timestamp}-${random}@example.com`,
      password: 'hashedpassword123',
      firstName: 'Test',
      lastName: 'User',
      displayName: 'Test User',
      isActive: true,
      ...userData,
    };

    const [created] = await db.insert(user).values(defaultUser).returning();
    return created;
  }

  // Organization fixtures
  static async createOrganization(orgData: Partial<OrganizationInsert> = {}) {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(7);

    // Handle planExpiresAt conversion from string to Date if needed
    const processedOrgData = { ...orgData };
    if (processedOrgData.planExpiresAt && typeof processedOrgData.planExpiresAt === 'string') {
      processedOrgData.planExpiresAt = new Date(processedOrgData.planExpiresAt);
    }

    const defaultOrganization = {
      name: `Test Organization ${timestamp}`,
      slug: `test-org-${timestamp}-${random}`,
      description: 'A test organization',
      isActive: true,
      settings: {},
      domains: {},
      ...processedOrgData,
    };

    const [created] = await db
      .insert(organization)
      .values(defaultOrganization as any)
      .returning();
    return created;
  }

  // Create organization with creator
  static async createOrganizationWithCreator(
    orgData: Partial<OrganizationInsert> = {},
    userData: Partial<typeof user.$inferInsert> = {},
  ) {
    const creator = await TestFixtures.createUser(userData);
    const org = await TestFixtures.createOrganization({
      ...orgData,
      createdBy: creator.id,
    });
    return { organization: org, creator };
  }

  // Helper to generate unique organization data
  static generateOrganizationData(overrides: Partial<OrganizationInsert> = {}): OrganizationInsert {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(7);

    return {
      name: `Test Org ${timestamp}`,
      slug: `test-org-${timestamp}-${random}`,
      description: 'Test organization description',
      website: 'https://example.com',
      logoUrl: 'https://example.com/logo.png',
      isActive: true,
      createdBy: null,
      settings: { theme: 'light' },
      domains: { primary: 'example.com' },
      billingEmail: '<EMAIL>',
      billingAddress: '123 Test Street, Test City, TC 12345',
      billingPlan: 'free',
      ...overrides,
    };
  }
}
