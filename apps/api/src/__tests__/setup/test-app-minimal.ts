import FastifyCors from '@fastify/cors';
import FastifyHelmet from '@fastify/helmet';
import FastifyJWT from '@fastify/jwt';
import { db } from '@repo/db';
import Fastify, { type FastifyInstance, type FastifyReply, type FastifyRequest } from 'fastify';
import { serializerCompiler, validatorCompiler } from 'fastify-type-provider-zod';
import { globalErrorHandler } from '../../errors/error-handler';
import { loggingMiddleware } from '../../middleware/logging';
import { performanceMiddleware } from '../../middleware/performance';
import dbPlugin from '../../plugins/db';
import organizationRoutes from '../../routes/organizations';

export class MinimalTestApp {
  private app: FastifyInstance | null = null;

  async build(): Promise<FastifyInstance> {
    if (this.app) {
      return this.app;
    }

    // Create Fastify instance
    this.app = Fastify({
      logger: false, // Disable logging during tests
    });

    // Set up Zod validation and serialization
    this.app.setValidatorCompiler(validatorCompiler);
    this.app.setSerializerCompiler(serializerCompiler);

    // Register security plugins
    await this.app.register(FastifyCors, {
      origin: true,
      credentials: true,
    });

    await this.app.register(FastifyHelmet, {
      contentSecurityPolicy: false,
    });

    // Setup JWT authentication
    await this.app.register(FastifyJWT, {
      secret: 'test-secret',
      sign: {
        expiresIn: '1d',
      },
    });

    // Add authenticate decorator
    this.app.decorate('authenticate', async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        await request.jwtVerify();
      } catch (_err) {
        reply.code(401).send({
          statusCode: 401,
          error: 'Unauthorized',
          message: 'Authentication required',
        });
      }
    });

    // Register database plugin
    await this.app.register(dbPlugin);

    // Add middleware hooks
    this.app.addHook('preHandler', loggingMiddleware);
    this.app.addHook('preHandler', performanceMiddleware);

    // Use the global error handler that properly handles ApiError and validation
    this.app.setErrorHandler(globalErrorHandler);

    // Register just the organization routes
    await this.app.register(organizationRoutes, {
      prefix: '/api/v1/organizations',
    });

    await this.app.ready();
    return this.app;
  }

  async close() {
    if (this.app) {
      await this.app.close();
      this.app = null;
    }
  }

  async cleanup() {
    // Clean up all tables for fresh test state using SQL
    try {
      // Use raw SQL to truncate tables with CASCADE to handle foreign keys
      await db.execute(`TRUNCATE TABLE "organization" CASCADE`);
      await db.execute(`TRUNCATE TABLE "user" CASCADE`);
    } catch (_error) {}
  }
}
