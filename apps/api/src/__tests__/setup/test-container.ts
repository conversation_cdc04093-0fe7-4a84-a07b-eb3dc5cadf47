import { PostgreSqlContainer, type StartedPostgreSqlContainer } from '@testcontainers/postgresql';

export class TestContainer {
  private container: StartedPostgreSqlContainer | null = null;

  async start() {
    if (this.container) {
      return;
    }
    this.container = await new PostgreSqlContainer('postgres:15')
      .withDatabase('test_db')
      .withUsername('test_user')
      .withPassword('test_password')
      .withExposedPorts(5432)
      .start();
  }

  async stop() {
    if (this.container) {
      await this.container.stop();
      this.container = null;
    }
  }

  getConnectionString(): string {
    if (!this.container) {
      throw new Error('Container not started');
    }

    const host = this.container.getHost();
    const port = this.container.getMappedPort(5432);
    const database = this.container.getDatabase();
    const username = this.container.getUsername();
    const password = this.container.getPassword();

    return `postgresql://${username}:${password}@${host}:${port}/${database}`;
  }
}
