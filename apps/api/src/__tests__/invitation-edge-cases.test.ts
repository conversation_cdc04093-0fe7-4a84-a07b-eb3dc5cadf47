import { db, initializeDatabase } from '@repo/db';
import { invitation, role, roleAssignment, user } from '@repo/db/schema';
import { PostgreSqlContainer, type StartedPostgreSqlContainer } from '@testcontainers/postgresql';
import { eq } from 'drizzle-orm';
import { afterAll, beforeAll, beforeEach, describe, expect, it } from 'vitest';
import { ApiError } from '../errors/api-error';
import { invitationService } from '../services/invitation-service';
import { organizationService } from '../services/organization-service';
import { projectService } from '../services/project-service';
import { roleAssignmentService } from '../services/role-assignment-service';
import { workspaceService } from '../services/workspace-service';

describe('Invitation Edge Cases', () => {
  let container: StartedPostgreSqlContainer;
  let testOrg: any;
  let testWorkspace: any;
  let _testProject: any;
  let testUser: any;
  let inviterUser: any;
  let viewerRole: any;
  let adminRole: any;
  let _ownerRole: any;

  beforeAll(async () => {
    // Start test database container
    container = await new PostgreSqlContainer('postgres:16-alpine')
      .withDatabase('testdb')
      .withUsername('testuser')
      .withPassword('testpass')
      .start();

    const connectionString = `postgresql://testuser:testpass@${container.getHost()}:${container.getPort()}/testdb`;
    await initializeDatabase(connectionString);

    // Create test roles
    const roles = await db
      .insert(role)
      .values([
        {
          name: 'Organization Owner',
          identifier: 'org_owner',
          description: 'Full control over the organization',
          type: 'organization',
        },
        {
          name: 'Organization Admin',
          identifier: 'org_admin',
          description: 'Administrative access to organization',
          type: 'organization',
        },
        {
          name: 'Organization Viewer',
          identifier: 'org_viewer',
          description: 'View-only access to organization',
          type: 'organization',
        },
      ])
      .returning();

    _ownerRole = roles[0];
    adminRole = roles[1];
    viewerRole = roles[2];

    // Create test users
    const users = await db
      .insert(user)
      .values([
        {
          email: '<EMAIL>',
          displayName: 'Inviter User',
          passwordHash: 'hash',
        },
        {
          email: '<EMAIL>',
          displayName: 'Test User',
          passwordHash: 'hash',
        },
      ])
      .returning();

    inviterUser = users[0];
    testUser = users[1];

    // Create test organization
    testOrg = await organizationService.create(
      {
        name: 'Test Organization',
        slug: 'test-org',
      },
      { userId: inviterUser.id },
    );

    // Create test workspace
    testWorkspace = await workspaceService.create(
      {
        name: 'Test Workspace',
        slug: 'test-ws',
        organizationId: testOrg.id,
      },
      { userId: inviterUser.id },
    );

    // Create test project
    _testProject = await projectService.create(
      {
        name: 'Test Project',
        key: 'TEST',
        workspaceId: testWorkspace.id,
      },
      { userId: inviterUser.id },
    );
  });

  afterAll(async () => {
    await container.stop();
  });

  beforeEach(async () => {
    // Clean up invitations and role assignments before each test
    await db.delete(invitation);
    await db.delete(roleAssignment).where(eq(roleAssignment.userId, testUser.id));
  });

  describe('Duplicate Invitation Prevention', () => {
    it('should prevent duplicate pending invitations to the same scope', async () => {
      // Create first invitation
      await invitationService.create(
        {
          email: '<EMAIL>',
          invitedBy: inviterUser.id,
          roleId: viewerRole.id,
          scopeType: 'organization',
          scopeId: testOrg.id,
        },
        { userId: inviterUser.id },
      );

      // Try to create duplicate invitation
      await expect(
        invitationService.create(
          {
            email: '<EMAIL>',
            invitedBy: inviterUser.id,
            roleId: viewerRole.id,
            scopeType: 'organization',
            scopeId: testOrg.id,
          },
          { userId: inviterUser.id },
        ),
      ).rejects.toThrow(ApiError);
    });

    it('should prevent invitations with different roles to the same scope', async () => {
      // Create first invitation with viewer role
      await invitationService.create(
        {
          email: '<EMAIL>',
          invitedBy: inviterUser.id,
          roleId: viewerRole.id,
          scopeType: 'organization',
          scopeId: testOrg.id,
        },
        { userId: inviterUser.id },
      );

      // Try to create invitation with admin role
      await expect(
        invitationService.create(
          {
            email: '<EMAIL>',
            invitedBy: inviterUser.id,
            roleId: adminRole.id,
            scopeType: 'organization',
            scopeId: testOrg.id,
          },
          { userId: inviterUser.id },
        ),
      ).rejects.toThrow(/pending invitation already exists.*Please revoke/);
    });

    it('should prevent invitations to users with existing active roles', async () => {
      // Assign role to user
      await roleAssignmentService.create(
        {
          userId: testUser.id,
          roleId: viewerRole.id,
          scopeType: 'organization',
          scopeId: testOrg.id,
          isActive: true,
        },
        { userId: inviterUser.id },
      );

      // Try to invite user who already has a role
      await expect(
        invitationService.create(
          {
            email: testUser.email,
            invitedBy: inviterUser.id,
            roleId: adminRole.id,
            scopeType: 'organization',
            scopeId: testOrg.id,
          },
          { userId: inviterUser.id },
        ),
      ).rejects.toThrow(/already has an active role/);
    });

    it('should allow invitations to different scopes', async () => {
      // Create invitation to organization
      await invitationService.create(
        {
          email: '<EMAIL>',
          invitedBy: inviterUser.id,
          roleId: viewerRole.id,
          scopeType: 'organization',
          scopeId: testOrg.id,
        },
        { userId: inviterUser.id },
      );

      // Should allow invitation to workspace
      const wsInvitation = await invitationService.create(
        {
          email: '<EMAIL>',
          invitedBy: inviterUser.id,
          roleId: viewerRole.id,
          scopeType: 'workspace',
          scopeId: testWorkspace.id,
        },
        { userId: inviterUser.id },
      );

      expect(wsInvitation).toBeDefined();
      expect(wsInvitation.scopeType).toBe('workspace');
    });
  });

  describe('Role Assignment Uniqueness', () => {
    it('should prevent multiple active roles for the same user in the same scope', async () => {
      // Create first role assignment
      await roleAssignmentService.create(
        {
          userId: testUser.id,
          roleId: viewerRole.id,
          scopeType: 'organization',
          scopeId: testOrg.id,
          isActive: true,
        },
        { userId: inviterUser.id },
      );

      // Try to create another role assignment
      await expect(
        roleAssignmentService.create(
          {
            userId: testUser.id,
            roleId: adminRole.id,
            scopeType: 'organization',
            scopeId: testOrg.id,
            isActive: true,
          },
          { userId: inviterUser.id },
        ),
      ).rejects.toThrow(/already has an active role/);
    });

    it('should allow inactive role assignments alongside active ones', async () => {
      // Create active role assignment
      await roleAssignmentService.create(
        {
          userId: testUser.id,
          roleId: viewerRole.id,
          scopeType: 'organization',
          scopeId: testOrg.id,
          isActive: true,
        },
        { userId: inviterUser.id },
      );

      // Create inactive role assignment (should work)
      const inactiveAssignment = await roleAssignmentService.create(
        {
          userId: testUser.id,
          roleId: adminRole.id,
          scopeType: 'organization',
          scopeId: testOrg.id,
          isActive: false,
        },
        { userId: inviterUser.id },
      );

      expect(inactiveAssignment).toBeDefined();
      expect(inactiveAssignment.isActive).toBe(false);
    });
  });

  describe('Auto-Assignment with Pending Invitations', () => {
    it('should auto-accept pending invitations when user is assigned a role during entity creation', async () => {
      // Create pending invitation for the inviter user
      const inv = await invitationService.create(
        {
          email: inviterUser.email,
          invitedBy: testUser.id,
          roleId: viewerRole.id,
          scopeType: 'organization',
          scopeId: testOrg.id,
        },
        { userId: testUser.id },
      );

      // Create a new organization (which auto-assigns owner role)
      const newOrg = await organizationService.create(
        {
          name: 'New Organization',
          slug: 'new-org',
        },
        { userId: inviterUser.id },
      );

      // Check that invitation was auto-accepted
      const updatedInvitation = await db.query.invitation.findFirst({
        where: eq(invitation.id, inv.id),
      });

      // The invitation should remain pending because it's for a different organization
      expect(updatedInvitation?.status).toBe('pending');

      // Create invitation for the new org
      const inv2 = await invitationService.create(
        {
          email: testUser.email,
          invitedBy: inviterUser.id,
          roleId: viewerRole.id,
          scopeType: 'organization',
          scopeId: newOrg.id,
        },
        { userId: inviterUser.id },
      );

      // Now create another org with testUser
      const _anotherOrg = await organizationService.create(
        {
          name: 'Another Organization',
          slug: 'another-org',
        },
        { userId: testUser.id },
      );

      // Check invitation for new org was auto-accepted
      const updatedInv2 = await db.query.invitation.findFirst({
        where: eq(invitation.id, inv2.id),
      });

      // This should still be pending as it's for a different org
      expect(updatedInv2?.status).toBe('pending');
    });

    it('should handle workspace creation with pending invitations', async () => {
      // Create pending invitation
      const inv = await invitationService.create(
        {
          email: inviterUser.email,
          invitedBy: testUser.id,
          roleId: viewerRole.id,
          scopeType: 'workspace',
          scopeId: testWorkspace.id,
        },
        { userId: testUser.id },
      );

      // User creates a new workspace (gets auto-assigned admin role)
      const _newWorkspace = await workspaceService.create(
        {
          name: 'User Created Workspace',
          slug: 'user-ws',
          organizationId: testOrg.id,
        },
        { userId: inviterUser.id },
      );

      // Original invitation should still be pending (different workspace)
      const updatedInv = await db.query.invitation.findFirst({
        where: eq(invitation.id, inv.id),
      });
      expect(updatedInv?.status).toBe('pending');
    });
  });

  describe('Edge Case: Invitation After Auto-Assignment', () => {
    it('should prevent invitations to entity creators who got auto-assigned roles', async () => {
      // User creates organization (gets auto-assigned owner role)
      const userOrg = await organizationService.create(
        {
          name: 'User Organization',
          slug: 'user-org',
        },
        { userId: testUser.id },
      );

      // Try to invite the creator to their own organization
      await expect(
        invitationService.create(
          {
            email: testUser.email,
            invitedBy: inviterUser.id,
            roleId: viewerRole.id,
            scopeType: 'organization',
            scopeId: userOrg.id,
          },
          { userId: inviterUser.id },
        ),
      ).rejects.toThrow(/already has an active role/);
    });
  });

  describe('Database Constraints', () => {
    it('should enforce unique pending invitation constraint at database level', async () => {
      // Create first invitation
      await db.insert(invitation).values({
        email: '<EMAIL>',
        token: 'token1',
        invitedBy: inviterUser.id,
        roleId: viewerRole.id,
        scopeType: 'organization',
        scopeId: testOrg.id,
        status: 'pending',
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      });

      // Try to create duplicate at database level
      await expect(
        db.insert(invitation).values({
          email: '<EMAIL>',
          token: 'token2',
          invitedBy: inviterUser.id,
          roleId: adminRole.id,
          scopeType: 'organization',
          scopeId: testOrg.id,
          status: 'pending',
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        }),
      ).rejects.toThrow();
    });

    it('should enforce unique active role constraint at database level', async () => {
      // Create first role assignment
      await db.insert(roleAssignment).values({
        userId: testUser.id,
        roleId: viewerRole.id,
        scopeType: 'organization',
        scopeId: testOrg.id,
        isActive: true,
      });

      // Try to create duplicate at database level
      await expect(
        db.insert(roleAssignment).values({
          userId: testUser.id,
          roleId: adminRole.id,
          scopeType: 'organization',
          scopeId: testOrg.id,
          isActive: true,
        }),
      ).rejects.toThrow();
    });
  });
});
