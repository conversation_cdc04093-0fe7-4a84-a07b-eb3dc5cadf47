import jwt from 'jsonwebtoken';
import env from '../../config/env';

export interface JWTPayload {
  userId: string;
  email: string;
  organizationId?: string;
  workspaceId?: string;
  roles?: string[];
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
}

export const JWT_CONFIG = {
  accessTokenExpiry: '1d',
  refreshTokenExpiry: '7d',
  issuer: 'spark-api',
  audience: 'spark-app',
};

export function generateTokens(payload: JWTPayload): TokenPair {
  const accessToken = jwt.sign(payload, env.JWT_SECRET, {
    expiresIn: JWT_CONFIG.accessTokenExpiry,
    issuer: JWT_CONFIG.issuer,
    audience: JWT_CONFIG.audience,
  } as jwt.SignOptions);

  const refreshToken = jwt.sign(
    { userId: payload.userId, type: 'refresh' },
    env.JWT_REFRESH_SECRET,
    {
      expiresIn: JWT_CONFIG.refreshTokenExpiry,
      issuer: JWT_CONFIG.issuer,
      audience: JWT_CONFIG.audience,
    } as jwt.SignOptions,
  );

  return { accessToken, refreshToken };
}

export function verifyAccessToken(token: string): JWTPayload {
  return jwt.verify(token, env.JWT_SECRET, {
    issuer: JWT_CONFIG.issuer,
    audience: JWT_CONFIG.audience,
  }) as JWTPayload;
}

export function verifyRefreshToken(token: string): { userId: string } {
  return jwt.verify(token, env.JWT_REFRESH_SECRET, {
    issuer: JWT_CONFIG.issuer,
    audience: JWT_CONFIG.audience,
  }) as { userId: string };
}

export function decodeToken(token: string): JWTPayload | null {
  try {
    return jwt.decode(token) as JWTPayload;
  } catch {
    return null;
  }
}

export function generateEmailVerificationToken(email: string): string {
  return jwt.sign({ email, type: 'email-verification' }, env.JWT_SECRET, {
    expiresIn: '24h',
    issuer: JWT_CONFIG.issuer,
  });
}

export function generatePasswordResetToken(userId: string, email: string): string {
  return jwt.sign({ userId, email, type: 'password-reset' }, env.JWT_SECRET, {
    expiresIn: '1h',
    issuer: JWT_CONFIG.issuer,
  });
}

export function verifyEmailToken(token: string): { email: string } {
  const decoded = jwt.verify(token, env.JWT_SECRET, {
    issuer: JWT_CONFIG.issuer,
  }) as any;

  if (decoded.type !== 'email-verification') {
    throw new Error('Invalid token type');
  }

  return { email: decoded.email };
}

export function verifyPasswordResetToken(token: string): {
  userId: string;
  email: string;
} {
  const decoded = jwt.verify(token, env.JWT_SECRET, {
    issuer: JWT_CONFIG.issuer,
  }) as any;

  if (decoded.type !== 'password-reset') {
    throw new Error('Invalid token type');
  }

  return { userId: decoded.userId, email: decoded.email };
}
