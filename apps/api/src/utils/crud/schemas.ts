import { z } from 'zod';

export const errorResponseSchema = z.object({
  statusCode: z.number(),
  errorCode: z.string(),
  message: z.string(),
  errors: z
    .array(
      z.object({
        field: z.string(),
        message: z.string(),
        code: z.string(),
        type: z.string(),
        path: z.array(z.string()),
      }),
    )
    .optional(),
  data: z.any().optional(),
});

/**
 * Pagination metadata schema for list responses
 */
export const paginationMetaSchema = z.object({
  totalItems: z.number(),
  itemCount: z.number(),
  itemsPerPage: z.number(),
  totalPages: z.number(),
  currentPage: z.number(),
});

/**
 * Custom transform function for sort parameter
 * Handles three formats:
 * 1. Single field: "createdAt" => [{ field: "createdAt", order: "asc" }]
 * 2. Single field with direction: "-createdAt" => [{ field: "createdAt", order: "desc" }]
 * 3. JSON array: '["createdAt", "-name"]' => [{ field: "createdAt", order: "asc" }, { field: "name", order: "desc" }]
 */
const transformSort = (sortStr: string | undefined) => {
  if (!sortStr) {
    return;
  }

  try {
    // Try to parse as JSON (handles array format)
    const parsed = JSON.parse(sortStr);

    if (Array.isArray(parsed)) {
      // Handle array format like ["createdAt", "-name"]
      return parsed.map((item) => {
        if (typeof item === 'string') {
          if (item.startsWith('-')) {
            return { field: item.substring(1), order: 'desc' as const };
          }
          return { field: item, order: 'asc' as const };
        }
        // If it's already in object format with field/order, return as is
        if (typeof item === 'object' && item && 'field' in item && 'order' in item) {
          return item;
        }
        throw new Error('Invalid sort format');
      });
    }

    // If parsed but not an array, it might be an object already
    if (typeof parsed === 'object' && parsed && 'field' in parsed) {
      return [parsed];
    }

    throw new Error('Invalid sort format');
  } catch (_e) {
    // Not valid JSON, treat as single field
    if (sortStr.startsWith('-')) {
      return [{ field: sortStr.substring(1), order: 'desc' as const }];
    }
    return [{ field: sortStr, order: 'asc' as const }];
  }
};

export type SortField = { field: string; order: 'asc' | 'desc' };

/**
 * Common query parameters schema for filtering, sorting, and pagination
 */
export const queryStringSchema = z.object({
  filters: z.string().optional(),
  sort: z.string().optional().transform(transformSort),
  page: z.coerce.number().default(1),
  limit: z.coerce.number().default(20),
  search: z.string().optional(),
});

/**
 * Common UUID parameter schema for resource IDs
 */
export const idParamSchema = z.object({
  id: z.string().uuid(),
});

/**
 * Create a single resource response schema for any entity schema
 */
export function createSingleResponseSchema<T extends z.ZodType>(schema: T) {
  return z.object({
    data: schema,
  });
}

/**
 * Create a paginated list response schema for any entity schema
 */
export function createListResponseSchema<T extends z.ZodType>(schema: T) {
  return z.object({
    data: z.array(schema),
    meta: paginationMetaSchema,
  });
}

// Add to src/helpers/schemas.ts

// Define types for error structures
interface FastifyValidationErrorItem {
  keyword: string;
  message: string;
  params?: {
    issue?: {
      code: string;
      message?: string;
      path?: Array<string | number>;
      type?: string;
      expected?: string;
    };
  };
}

interface ZodIssue {
  code: string;
  message: string;
  path?: Array<string | number>;
  expected?: string;
  type?: string;
}

/**
 * Converts Fastify validation errors or Zod errors to the standardized error response format
 * @param error The validation error object
 * @param statusCode Optional status code override
 */
export function toErrorResponseSchema(
  error: any,
  statusCode = 400,
): z.infer<typeof errorResponseSchema> {
  // Initialize the base response
  const response: z.infer<typeof errorResponseSchema> = {
    statusCode,
    errorCode: error?.code || 'VALIDATION_ERROR',
    message: error?.message || 'Validation error',
    errors: [],
  };

  // For Fastify validation errors
  if (error?.validation && Array.isArray(error.validation)) {
    response.errors = error.validation.map((item: FastifyValidationErrorItem) => {
      const issue = item.params?.issue;
      const field = issue?.path?.[issue.path.length - 1] || 'unknown';

      let message = issue?.message || item.message;
      // Replace type names with field names at the start of messages
      if (/^(String|Number|Boolean|Array|Object)\b/.test(message)) {
        message = message.replace(/^(String|Number|Boolean|Array|Object)/, String(field));
      }

      return {
        field: String(field),
        message,
        code: issue?.code || item.keyword,
        type: issue?.type || 'unknown',
        path: Array.isArray(issue?.path) ? issue.path.map(String) : [],
      };
    });
  }
  // For Zod errors in the issues format
  else if (error?.issues && Array.isArray(error.issues)) {
    response.errors = error.issues.map((issue: ZodIssue) => {
      const field = issue.path?.[issue.path.length - 1] || 'unknown';

      return {
        field: String(field),
        message: enhanceMessage(issue.message, field),
        code: issue.code,
        type: issue.expected || issue.type || 'unknown',
        path: Array.isArray(issue.path) ? issue.path.map(String) : [],
      };
    });
  }
  // Fallback for any other error format
  else {
    response.errors = [
      {
        field: 'unknown',
        message: error?.message || 'Unknown validation error',
        code: 'unknown_error',
        type: 'validation',
        path: [],
      },
    ];
  }

  return response;
}

/**
 * Helper to enhance error messages with field names
 */
function enhanceMessage(message: string, field: any): string {
  if (!message || typeof message !== 'string') {
    return 'Validation error';
  }
  if (!field || message.includes(String(field))) {
    return message;
  }

  // Replace type references with field name
  return message.replace(/^(String|Number|Boolean|Array|Object)/, String(field));
}
