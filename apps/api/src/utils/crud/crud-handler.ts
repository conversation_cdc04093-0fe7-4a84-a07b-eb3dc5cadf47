// Modular CRUD handler classes for better maintainability
import { eq } from '@repo/db/orm';
import type { FastifyReply, FastifyRequest, RouteHandlerMethod } from 'fastify';
import { ApiError, handleDatabaseError } from '../../errors/api-error';
import type { CrudResourceConfig } from './create-crud-router';
import {
  buildOrderBy,
  buildPaginationMeta,
  buildWhere,
  countWithFilters,
  parseSort,
} from './query-builder';

// Type definitions for better type safety
export interface QueryParams {
  filters?: string;
  sort?: string[] | Array<{ field: string; order: 'asc' | 'desc' }>;
  page: number;
  limit: number;
  search?: string;
}

export interface IdParams {
  id: string;
}

export interface CrudRequestBody {
  [key: string]: any;
}

export interface UserContext {
  id?: string; // Sometimes the auth middleware uses 'id' instead of 'userId'
  userId: string;
  email: string;
  organizationId?: string;
  workspaceId?: string;
  roles?: string[];
  isActive: boolean;
  isStaff: boolean;
}

export interface ServiceContext {
  userId?: string;
  userRole?: string;
  correlationId?: string;
  user?: UserContext; // Full user object for services that need it
  [key: string]: any;
}

export abstract class BaseCrudHandler<TSelect = unknown, TInsert = unknown, TPatch = unknown> {
  constructor(
    protected config: CrudResourceConfig<TSelect, TInsert, TPatch>,
    protected fastify: any,
  ) {}

  protected async runPreHandler(
    handler: Function | undefined,
    request: FastifyRequest,
    reply: FastifyReply,
  ) {
    if (typeof handler === 'function') {
      await handler(request, reply);
    }
  }

  protected async runPostHandler<T>(
    handler: Function | undefined,
    data: T,
    request: FastifyRequest,
    reply: FastifyReply,
  ): Promise<T> {
    if (typeof handler === 'function') {
      return await handler(data, request, reply);
    }
    return data;
  }

  protected logOperation(request: FastifyRequest, operation: string, details?: any) {
    request.log.info(
      {
        correlationId: request.context?.correlationId,
        operation: `${this.config.name[0]}:${operation}`,
        ...details,
      },
      `${operation} operation on ${this.config.name[0]}`,
    );
  }

  protected buildServiceContext(request: FastifyRequest): ServiceContext {
    const user = request.user as UserContext | undefined;
    return {
      userId: user?.userId || user?.id, // Support both userId and id fields
      userRole: user?.roles?.[0], // Take first role if available
      correlationId: request.context?.correlationId,
      user, // Pass the full user object for services that need it
    };
  }
}

export class FindManyHandler<TSelect, TInsert, TPatch> extends BaseCrudHandler<
  TSelect,
  TInsert,
  TPatch
> {
  handle: RouteHandlerMethod = async (request, reply) => {
    try {
      await this.runPreHandler(this.config.preFindMany, request, reply);

      const { filters, sort, page, limit } = request.query as QueryParams;

      this.logOperation(request, 'findMany', {
        page,
        limit,
        hasFilters: !!filters,
      });

      let parsedFilters: Record<string, any>;
      try {
        parsedFilters = filters ? JSON.parse(filters) : this.config.defaultFilters || {};
      } catch (_error) {
        throw ApiError.badRequest('Invalid filter JSON format');
      }

      const sortData = sort ||
        this.config.defaultSort || [{ field: 'createdAt', order: 'desc' as const }];
      const parsedSort = parseSort(sortData);

      // Build query using shared utilities
      const where = buildWhere(this.config.table, parsedFilters);
      const orderByClause = buildOrderBy(this.config.table, parsedSort);
      const items = await this.config.queryApi.findMany({
        where,
        limit,
        offset: (page - 1) * limit,
        with: this.config.with,
        ...(orderByClause && { orderBy: orderByClause }),
      });

      // Get total count for pagination
      const totalItems = await countWithFilters(this.fastify.db, this.config.table, where);
      const meta = buildPaginationMeta(totalItems, items.length, page, limit);

      let data = items;
      if (this.config.transformResponse) {
        data = data.map((item: TSelect) => this.config.transformResponse?.(item, request));
      }

      data = await this.runPostHandler(this.config.postFindMany, data, request, reply);

      return reply.send({ data, meta });
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw handleDatabaseError(error, 'findMany');
    }
  };
}

export class FindOneHandler<TSelect, TInsert, TPatch> extends BaseCrudHandler<
  TSelect,
  TInsert,
  TPatch
> {
  handle: RouteHandlerMethod = async (request, reply) => {
    try {
      await this.runPreHandler(this.config.preFindOne, request, reply);

      const { id } = request.params as IdParams;
      this.logOperation(request, 'findOne', { id });

      const item = await this.config.queryApi.findFirst({
        where: eq(this.config.table.id, id),
        with: this.config.with,
      });

      if (!item) {
        throw ApiError.notFound(this.config.name[0], id);
      }

      let data = item;
      if (this.config.transformResponse) {
        data = this.config.transformResponse(data, request);
      }

      data = await this.runPostHandler(this.config.postFindOne, data, request, reply);

      return reply.send({ data });
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw handleDatabaseError(error, 'findOne');
    }
  };
}

export class CreateHandler<TSelect, TInsert, TPatch> extends BaseCrudHandler<
  TSelect,
  TInsert,
  TPatch
> {
  handle: RouteHandlerMethod = async (request, reply) => {
    try {
      await this.runPreHandler(this.config.preCreate, request, reply);

      let data =
        typeof request.body === 'object' && request.body !== null
          ? { ...(request.body as CrudRequestBody) }
          : ({} as CrudRequestBody);

      // Add audit fields if the table supports them
      // Only add createdBy if not already provided in the request body
      if (request.user && this.config.table.createdBy && !data.createdBy) {
        const user = request.user as UserContext;
        data.createdBy = user.userId;
      }
      // Also set updatedBy on creation if the table has it
      if (request.user && this.config.table.updatedBy && !data.updatedBy) {
        const user = request.user as UserContext;
        data.updatedBy = user.userId;
      }

      if (this.config.transformRequest) {
        data = this.config.transformRequest(data, request);
      }

      this.logOperation(request, 'create', {
        hasData: !!Object.keys(data).length,
      });

      // Service integration: use service if configured, otherwise direct DB
      let item;
      if (this.config.service) {
        const context = this.buildServiceContext(request);
        const created = await this.config.service.create(data, context);

        // Re-fetch with relations if needed
        if (this.config.with) {
          item = await this.config.queryApi.findFirst({
            where: eq(this.config.table.id, created.id),
            with: this.config.with,
          });
        } else {
          item = created;
        }
      } else {
        const [created] = await this.fastify.db.insert(this.config.table).values(data).returning();

        item = await this.config.queryApi.findFirst({
          where: eq(this.config.table.id, created.id),
          with: this.config.with,
        });
      }

      let resp = item;
      if (this.config.transformResponse) {
        resp = this.config.transformResponse(item, request);
      }

      resp = await this.runPostHandler(this.config.postCreate, resp, request, reply);

      return reply.code(201).send({ data: resp });
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw handleDatabaseError(error, 'create');
    }
  };
}

export class UpdateHandler<TSelect, TInsert, TPatch> extends BaseCrudHandler<
  TSelect,
  TInsert,
  TPatch
> {
  handle: RouteHandlerMethod = async (request, reply) => {
    try {
      await this.runPreHandler(this.config.preUpdate, request, reply);

      const { id } = request.params as IdParams;
      let data =
        typeof request.body === 'object' && request.body !== null
          ? { ...(request.body as CrudRequestBody) }
          : ({} as CrudRequestBody);

      // Add audit fields if the table supports them
      if (this.config.table.updatedAt) {
        data.updatedAt = new Date();
      }
      if (request.user && this.config.table.updatedBy) {
        const user = request.user as UserContext;
        data.updatedBy = user.userId;
      }

      if (this.config.transformRequest) {
        data = this.config.transformRequest(data, request);
      }

      this.logOperation(request, 'update', {
        id,
        hasData: !!Object.keys(data).length,
      });

      // Service integration: use service if configured, otherwise direct DB
      let item;
      if (this.config.service) {
        const context = this.buildServiceContext(request);
        const updated = await this.config.service.update(id, data, context);

        // Re-fetch with relations if needed
        if (this.config.with) {
          item = await this.config.queryApi.findFirst({
            where: eq(this.config.table.id, updated.id),
            with: this.config.with,
          });
        } else {
          item = updated;
        }
      } else {
        // Check if exists
        const existing = await this.config.queryApi.findFirst({
          where: eq(this.config.table.id, id),
        });

        if (!existing) {
          throw ApiError.notFound(this.config.name[0], id);
        }

        await this.fastify.db
          .update(this.config.table)
          .set(data)
          .where(eq(this.config.table.id, id));

        item = await this.config.queryApi.findFirst({
          where: eq(this.config.table.id, id),
          with: this.config.with,
        });
      }

      let resp = item;
      if (this.config.transformResponse) {
        resp = this.config.transformResponse(item, request);
      }

      resp = await this.runPostHandler(this.config.postUpdate, resp, request, reply);

      return reply.send({ data: resp });
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw handleDatabaseError(error, 'update');
    }
  };
}

export class DeleteHandler<TSelect, TInsert, TPatch> extends BaseCrudHandler<
  TSelect,
  TInsert,
  TPatch
> {
  handle: RouteHandlerMethod = async (request, reply) => {
    try {
      await this.runPreHandler(this.config.preDelete, request, reply);

      const { id } = request.params as IdParams;
      this.logOperation(request, 'delete', {
        id,
        softDelete: this.config.softDelete,
      });

      // Service integration: use service if configured, otherwise direct DB
      if (this.config.service) {
        const context = this.buildServiceContext(request);
        await this.config.service.delete(id, context);
      } else {
        const existing = await this.config.queryApi.findFirst({
          where: eq(this.config.table.id, id),
        });

        if (!existing) {
          throw ApiError.notFound(this.config.name[0], id);
        }

        if (this.config.softDelete && this.config.table.deletedAt) {
          await this.fastify.db
            .update(this.config.table)
            .set({ deletedAt: new Date() })
            .where(eq(this.config.table.id, id));
        } else {
          await this.fastify.db.delete(this.config.table).where(eq(this.config.table.id, id));
        }
      }

      await this.runPostHandler(this.config.postDelete, null, request, reply);

      return reply.code(204).send(null);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw handleDatabaseError(error, 'delete');
    }
  };
}
