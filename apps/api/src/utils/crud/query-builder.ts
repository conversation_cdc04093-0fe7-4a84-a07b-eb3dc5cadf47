import {
  and,
  asc,
  between,
  count,
  desc,
  eq,
  gt,
  gte,
  ilike,
  inArray,
  isNotNull,
  isNull,
  lt,
  lte,
  ne,
  not,
  notInArray,
  or,
} from '@repo/db/orm';

/**
 * Builds a WHERE clause from filter object supporting various operators
 *
 * Supported operators:
 * - Logical: $and, $or, $not
 * - Comparison: $eq, $ne, $gt, $gte, $lt, $lte, $between
 * - String: $contains, $startsWith, $endsWith
 * - Array: $in, $nin
 * - Null: $null (true/false), $notNull (true)
 *
 * Examples:
 * - { name: "<PERSON>" } => name = 'John'
 * - { age: { $gte: 18 } } => age >= 18
 * - { age: { $between: [18, 65] } } => age BETWEEN 18 AND 65
 * - { name: { $contains: "john" } } => name ILIKE '%john%'
 * - { status: { $in: ["active", "pending"] } } => status IN ('active', 'pending')
 * - { $and: [{ age: { $gte: 18 } }, { age: { $lt: 65 } }] } => age >= 18 AND age < 65
 * - { $or: [{ status: "active" }, { priority: "high" }] } => status = 'active' OR priority = 'high'
 * - { deletedAt: { $null: true } } => deletedAt IS NULL
 *
 * @param table - The Drizzle table reference
 * @param filters - Object with filter conditions
 * @returns WHERE clause or undefined if no conditions
 */
export function buildWhere(table: any, filters: Record<string, any>): any {
  const conditions = [];

  // Handle $and operator at root level
  if (filters.$and) {
    // If $and is an array, process each condition
    if (Array.isArray(filters.$and)) {
      const andConditions = filters.$and.map((filter) => buildWhere(table, filter)).filter(Boolean);
      if (andConditions.length > 0) {
        return andConditions.length === 1 ? andConditions[0] : and(...andConditions);
      }
    } else {
      // If $and is an object, treat it as a single filter
      const andConditions = buildWhere(table, filters.$and);
      if (andConditions) {
        return andConditions;
      }
    }
  }

  // Handle $or operator at root level
  if (filters.$or) {
    // If $or is an array, process each condition
    if (Array.isArray(filters.$or)) {
      const orConditions = filters.$or.map((filter) => buildWhere(table, filter)).filter(Boolean);
      if (orConditions.length > 0) {
        return orConditions.length === 1 ? orConditions[0] : or(...orConditions);
      }
    } else {
      // If $or is an object, treat it as a single filter
      const orConditions = buildWhere(table, filters.$or);
      if (orConditions) {
        return orConditions;
      }
    }
  }

  // Handle $not operator at root level
  if (filters.$not) {
    const notCondition = buildWhere(table, filters.$not);
    if (notCondition) {
      return not(notCondition);
    }
  }

  for (const key in filters) {
    // Skip operator keys
    if (key.startsWith('$')) {
      continue;
    }

    if (typeof filters[key] === 'object' && filters[key] !== null) {
      // Handle null checks
      if (filters[key] === null || filters[key].$null === true) {
        conditions.push(isNull(table[key]));
      } else if (filters[key].$null === false || filters[key].$notNull === true) {
        conditions.push(isNotNull(table[key]));
      }
      // String operators
      else if (filters[key].$contains !== undefined) {
        conditions.push(ilike(table[key], `%${filters[key].$contains}%`));
      } else if (filters[key].$startsWith !== undefined) {
        conditions.push(ilike(table[key], `${filters[key].$startsWith}%`));
      } else if (filters[key].$endsWith !== undefined) {
        conditions.push(ilike(table[key], `%${filters[key].$endsWith}`));
      }
      // Comparison operators
      else if (filters[key].$eq !== undefined) {
        conditions.push(eq(table[key], filters[key].$eq));
      } else if (filters[key].$ne !== undefined) {
        conditions.push(ne(table[key], filters[key].$ne));
      } else if (filters[key].$gt !== undefined) {
        conditions.push(gt(table[key], filters[key].$gt));
      } else if (filters[key].$gte !== undefined) {
        conditions.push(gte(table[key], filters[key].$gte));
      } else if (filters[key].$lt !== undefined) {
        conditions.push(lt(table[key], filters[key].$lt));
      } else if (filters[key].$lte !== undefined) {
        conditions.push(lte(table[key], filters[key].$lte));
      } else if (
        filters[key].$between !== undefined &&
        Array.isArray(filters[key].$between) &&
        filters[key].$between.length === 2
      ) {
        conditions.push(between(table[key], filters[key].$between[0], filters[key].$between[1]));
      }
      // Array operators
      else if (filters[key].$in !== undefined && Array.isArray(filters[key].$in)) {
        conditions.push(inArray(table[key], filters[key].$in));
      } else if (filters[key].$nin !== undefined && Array.isArray(filters[key].$nin)) {
        conditions.push(notInArray(table[key], filters[key].$nin));
      }
      // Nested logical operators
      else if (filters[key].$and !== undefined) {
        const nestedAnd = buildWhere(table, { [key]: filters[key].$and });
        if (nestedAnd) {
          conditions.push(nestedAnd);
        }
      } else if (filters[key].$or !== undefined) {
        const nestedOr = buildWhere(table, { [key]: filters[key].$or });
        if (nestedOr) {
          conditions.push(nestedOr);
        }
      } else if (filters[key].$not !== undefined) {
        const nestedNot = buildWhere(table, { [key]: filters[key].$not });
        if (nestedNot) {
          conditions.push(not(nestedNot));
        }
      }
    } else {
      // Direct equality comparison
      conditions.push(eq(table[key], filters[key]));
    }
  }

  if (conditions.length === 0) {
    return;
  }
  if (conditions.length === 1) {
    return conditions[0];
  }
  return and(...conditions);
}

/**
 * Parses sort syntax from string array or object array format
 * @param sortArray - Array like ["field", "-field2"] or [{field: "field", order: "asc"}]
 * @returns Standardized sort object array
 */
export function parseSort(
  sortArray: string[] | Array<{ field: string; order: 'asc' | 'desc' }>,
): Array<{ field: string; order: 'asc' | 'desc' }> {
  // If it's already in the expected format, return it as is
  if (
    Array.isArray(sortArray) &&
    sortArray.length > 0 &&
    typeof sortArray[0] === 'object' &&
    'field' in sortArray[0]
  ) {
    return sortArray as Array<{ field: string; order: 'asc' | 'desc' }>;
  }

  // Convert string array format to object array
  return (sortArray as string[]).map((item) => {
    if (item.startsWith('-')) {
      return { field: item.substring(1), order: 'desc' as const };
    }
    return { field: item, order: 'asc' as const };
  });
}

/**
 * Builds ORDER BY clause from parsed sort array
 * @param table - The Drizzle table reference
 * @param parsedSort - Array of sort objects with field and order
 * @returns Array of Drizzle orderBy expressions or undefined
 */
export function buildOrderBy(
  table: any,
  parsedSort: Array<{ field: string; order: 'asc' | 'desc' }>,
) {
  if (!parsedSort || parsedSort.length === 0) {
    return;
  }

  const orderByExpressions = parsedSort.map(({ field, order }) => {
    // Access the table column and apply asc() or desc() from the ORM
    return order === 'asc' ? asc(table[field]) : desc(table[field]);
  });

  // Always return an array for consistency
  return orderByExpressions;
}

/**
 * Counts total records with optional filters applied
 * @param db - Database connection
 * @param table - The Drizzle table reference
 * @param where - Optional WHERE clause from buildWhere
 * @returns Promise resolving to total count
 */
export async function countWithFilters(db: any, table: any, where?: any): Promise<number> {
  const [countResult] = await db.select({ value: count() }).from(table).where(where);

  return countResult?.value || 0;
}

/**
 * Builds pagination metadata object
 * @param totalItems - Total number of items
 * @param currentItems - Number of items in current page
 * @param page - Current page number
 * @param limit - Items per page
 * @returns Pagination metadata
 */
export function buildPaginationMeta(
  totalItems: number,
  currentItems: number,
  page: number,
  limit: number,
) {
  return {
    totalItems,
    itemCount: currentItems,
    itemsPerPage: limit,
    totalPages: Math.ceil(totalItems / limit),
    currentPage: page,
  };
}
