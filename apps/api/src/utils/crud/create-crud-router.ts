// Modern CRUD router with modular handlers, type safety, and service integration
import type { FastifyInstance, FastifyPluginAsync } from 'fastify';
import type { ZodTypeProvider } from 'fastify-type-provider-zod';
import { type ZodType, z } from 'zod';
import { schemaRegistry } from '../../lib/swagger';
import { createRelationMap } from '../../lib/swagger/extractors/drizzle-relation-extractor';
import { getSimpleRelationMap } from '../../lib/swagger/extractors/simple-relation-extractor';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DeleteH<PERSON>ler,
  FindManyHandler,
  FindOneHandler,
  UpdateHandler,
} from './crud-handler';
import {
  createListResponseSchema,
  createSingleResponseSchema,
  errorResponseSchema,
  idParamSchema,
  queryStringSchema,
} from './schemas';

// Types for config
export type PreHandler = (request: any, reply: any) => Promise<any> | any;

// Configuration interface for CRUD resources
export interface CrudResourceConfig<TSelect = any, TInsert = any, TPatch = any> {
  // Resource information
  name: [string, string]; // [singular, plural]
  selectSchema: ZodType<TSelect>;
  insertSchema: ZodType<TInsert>;
  patchSchema: ZodType<TPatch>;
  table: any;
  queryApi: any;
  // Relations using Drizzle terminology
  with?: Record<
    string,
    | boolean
    | {
        columns?: Record<string, boolean>;
        with?: Record<string, any>; // Support nested relations
      }
  >;

  // Drizzle relations definition (e.g., organizationRelations)
  relations?: any;

  // Route configuration
  tags?: string[];
  enabled?: {
    findMany?: boolean;
    findOne?: boolean;
    create?: boolean;
    update?: boolean;
    delete?: boolean;
    all?: boolean;
  };

  // Query defaults
  defaultSort?: Array<{ field: string; order: 'asc' | 'desc' }>;
  defaultFilters?: Record<string, any>;
  searchConfig?: any;

  // Authentication
  // Can be a boolean (applies to all routes) or an object for granular control
  // Default: true (all routes require authentication)
  // Examples:
  //   authenticate: true                              // All routes require auth
  //   authenticate: false                             // No routes require auth
  //   authenticate: { all: false, create: true }      // Only create requires auth
  //   authenticate: { findMany: false, findOne: false } // List/read are public
  authenticate?:
    | boolean
    | {
        findMany?: boolean;
        findOne?: boolean;
        create?: boolean;
        update?: boolean;
        delete?: boolean;
        all?: boolean; // Override for all routes
      };
  preHandlers?: Record<string, PreHandler | PreHandler[]>;

  // Lifecycle handlers
  preFindMany?: PreHandler;
  postFindMany?: (data: any, request: any, reply: any) => Promise<any>;
  preFindOne?: PreHandler;
  postFindOne?: (data: any, request: any, reply: any) => Promise<any>;
  preCreate?: PreHandler;
  postCreate?: (data: any, request: any, reply: any) => Promise<any>;
  preUpdate?: PreHandler;
  postUpdate?: (data: any, request: any, reply: any) => Promise<any>;
  preDelete?: PreHandler;
  postDelete?: (data: any, request: any, reply: any) => Promise<any>;

  // Custom handlers
  findManyHandler?: any;
  findOneHandler?: any;
  createHandler?: any;
  updateHandler?: any;
  deleteHandler?: any;

  // Transformers
  transformRequest?: (data: any, req: any) => any;
  transformResponse?: (data: any, req: any) => any;

  // Options
  softDelete?: boolean;

  // Service integration
  service?: any;
}

export function createCrudRouter<TSelect = any, TInsert = any, TPatch = any>(
  config: CrudResourceConfig<TSelect, TInsert, TPatch>,
): FastifyPluginAsync {
  return async function crudPlugin(fastify: FastifyInstance) {
    // Use TypeProvider for better type inference
    const app = fastify.withTypeProvider<ZodTypeProvider>();

    const {
      name,
      selectSchema,
      insertSchema,
      patchSchema,
      enabled = { all: true },
      authenticate,
      preHandlers,
    } = config;

    const [singular, plural] = name;
    const tags = config.tags || [plural];

    // Automatically register schemas in the registry
    // Convert kebab-case to PascalCase: 'work-item-type' -> 'WorkItemType'
    const entityName = singular
      .split('-')
      .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
      .join('');

    // Register the main entity schema
    schemaRegistry.registerSchema(entityName, selectSchema);

    // Register input schemas
    schemaRegistry.registerSchema(`Create${entityName}Input`, insertSchema);
    schemaRegistry.registerSchema(`Update${entityName}Input`, patchSchema);

    // Extract relation info from Drizzle relations if provided
    let relationMap: Record<string, string> = {};
    if (config.relations) {
      // Try to extract from Drizzle relations
      relationMap = createRelationMap(config.relations, entityName);

      // If that fails, use the simple relation map
      if (Object.keys(relationMap).length === 0) {
        relationMap = getSimpleRelationMap(entityName);
      }

      // Register relation map with schema registry
      if (Object.keys(relationMap).length > 0) {
        schemaRegistry.registerRelationMap(entityName, relationMap);
      }
    }

    // Create handler instances
    const findManyHandler = new FindManyHandler(config, fastify);
    const findOneHandler = new FindOneHandler(config, fastify);
    const createHandler = new CreateHandler(config, fastify);
    const updateHandler = new UpdateHandler(config, fastify);
    const deleteHandler = new DeleteHandler(config, fastify);

    // Helper to check if authentication is required for a route
    const isAuthRequired = (
      action: 'findMany' | 'findOne' | 'create' | 'update' | 'delete',
    ): boolean => {
      if (authenticate === undefined || authenticate === true) {
        return true;
      }
      if (authenticate === false) {
        return false;
      }
      // authenticate is an object
      if (typeof authenticate === 'object' && authenticate !== null) {
        if (authenticate.all !== undefined) {
          return authenticate.all;
        }
        return authenticate[action] ?? true;
      }
      return true;
    };

    // Helper to apply auth preHandlers
    const getPreHandlers = (route: string): any[] => {
      const handlers: any[] = [];
      const actionMap: Record<string, 'findMany' | 'findOne' | 'create' | 'update' | 'delete'> = {
        findMany: 'findMany',
        findOne: 'findOne',
        create: 'create',
        update: 'update',
        delete: 'delete',
      };

      const action = actionMap[route];
      if (action && isAuthRequired(action)) {
        handlers.push(fastify.authenticate);
      }

      if (preHandlers?.[route]) {
        const preHandler = preHandlers[route];
        if (Array.isArray(preHandler)) {
          handlers.push(...preHandler);
        } else {
          handlers.push(preHandler);
        }
      }
      return handlers;
    };

    // GET /:resource - Find many
    if (enabled.all || enabled.findMany) {
      app.route({
        method: 'GET',
        url: '/',
        schema: {
          tags,
          summary: `Get ${plural} list`,
          description: `Retrieve a paginated list of ${plural} resources with optional filtering and sorting`,
          querystring: queryStringSchema,
          response: {
            200: createListResponseSchema(selectSchema),
            400: errorResponseSchema,
            500: errorResponseSchema,
          },
          ...(isAuthRequired('findMany') && { security: [{ bearerAuth: [] }] }),
        },
        preHandler: getPreHandlers('findMany'),
        handler: config.findManyHandler || findManyHandler.handle,
      });
    }

    // GET /:resource/:id - Find one
    if (enabled.all || enabled.findOne) {
      app.route({
        method: 'GET',
        url: '/:id',
        schema: {
          tags,
          summary: `Get ${singular} by ID`,
          description: `Retrieve a specific ${singular} resource by its unique identifier`,
          params: idParamSchema,
          response: {
            200: createSingleResponseSchema(selectSchema),
            404: errorResponseSchema,
            500: errorResponseSchema,
          },
          ...(isAuthRequired('findOne') && { security: [{ bearerAuth: [] }] }),
        },
        preHandler: getPreHandlers('findOne'),
        handler: config.findOneHandler || findOneHandler.handle,
      });
    }

    // POST /:resource - Create
    if (enabled.all || enabled.create) {
      app.route({
        method: 'POST',
        url: '/',
        schema: {
          tags,
          summary: `Create ${singular}`,
          description: `Create a new ${singular} resource`,
          body: insertSchema,
          response: {
            201: createSingleResponseSchema(selectSchema),
            400: errorResponseSchema,
            409: errorResponseSchema,
            500: errorResponseSchema,
          },
          ...(isAuthRequired('create') && { security: [{ bearerAuth: [] }] }),
        },
        preHandler: getPreHandlers('create'),
        handler: config.createHandler || createHandler.handle,
      });
    }

    // PATCH /:resource/:id - Update
    if (enabled.all || enabled.update) {
      app.route({
        method: 'PATCH',
        url: '/:id',
        schema: {
          tags,
          summary: `Update ${singular}`,
          description: `Update an existing ${singular} resource`,
          params: idParamSchema,
          body: patchSchema,
          response: {
            200: createSingleResponseSchema(selectSchema),
            400: errorResponseSchema,
            404: errorResponseSchema,
            409: errorResponseSchema,
            500: errorResponseSchema,
          },
          ...(isAuthRequired('update') && { security: [{ bearerAuth: [] }] }),
        },
        preHandler: getPreHandlers('update'),
        handler: config.updateHandler || updateHandler.handle,
      });
    }

    // DELETE /:resource/:id - Delete
    if (enabled.all || enabled.delete) {
      app.route({
        method: 'DELETE',
        url: '/:id',
        schema: {
          tags,
          summary: `Delete ${singular}`,
          description: `Delete an existing ${singular} resource`,
          params: idParamSchema,
          response: {
            204: z.null().describe('Successfully deleted'),
            404: errorResponseSchema,
            500: errorResponseSchema,
          },
          ...(isAuthRequired('delete') && { security: [{ bearerAuth: [] }] }),
        },
        preHandler: getPreHandlers('delete'),
        handler: config.deleteHandler || deleteHandler.handle,
      });
    }
  };
}
