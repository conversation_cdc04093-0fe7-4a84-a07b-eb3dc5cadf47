import * as admin from 'firebase-admin';
import env from '../config/env';
import { ApiError } from '../errors/api-error';

// Initialize Firebase Admin SDK
let firebaseApp: admin.app.App | null = null;

function initializeFirebase() {
  if (firebaseApp) {
    return firebaseApp;
  }

  try {
    // Check if service account path is provided
    if (env.FCM_SERVICE_ACCOUNT_PATH) {
      // Initialize with service account file
      const serviceAccount = require(env.FCM_SERVICE_ACCOUNT_PATH);
      firebaseApp = admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
      });
    } else if (env.FCM_PROJECT_ID && env.FCM_PRIVATE_KEY && env.FCM_CLIENT_EMAIL) {
      // Initialize with environment variables
      firebaseApp = admin.initializeApp({
        credential: admin.credential.cert({
          projectId: env.FCM_PROJECT_ID,
          privateKey: env.FCM_PRIVATE_KEY.replace(/\\n/g, '\n'),
          clientEmail: env.FCM_CLIENT_EMAIL,
        }),
      });
    } else {
      throw new Error(
        'FCM configuration not found. Please provide either FCM_SERVICE_ACCOUNT_PATH or FCM_PROJECT_ID, FCM_PRIVATE_KEY, and FCM_CLIENT_EMAIL',
      );
    }
    return firebaseApp;
  } catch (_error) {
    throw new ApiError('Failed to initialize FCM', 500, 'FCM_INIT_FAILED');
  }
}

export interface SendNotificationParams {
  tokens: string[];
  title: string;
  body: string;
  data?: Record<string, string>;
  id?: string;
}

export async function sendPushNotification({
  tokens,
  title,
  body,
  data,
  id,
}: SendNotificationParams) {
  if (!tokens || tokens.length === 0) {
    return { success: 0, failure: 0, responses: [] };
  }

  try {
    // Initialize Firebase if not already initialized
    if (!firebaseApp) {
      initializeFirebase();
    }

    const messaging = admin.messaging();

    // Prepare the message
    const message: admin.messaging.MulticastMessage = {
      tokens,
      notification: {
        title,
        body,
      },
      data: {
        ...data,
        id: id || 'notification',
        timestamp: new Date().toISOString(),
      },
      // Additional options
      android: {
        priority: 'high',
        notification: {
          icon: 'ic_notification',
          color: '#1a73e8',
        },
      },
      webpush: {
        notification: {
          icon: '/icon-192x192.png',
          badge: '/icon-192x192.png',
        },
      },
    };

    // Send the notification
    const response = await messaging.sendEachForMulticast(message);

    // Log any failures for debugging
    if (response.failureCount > 0) {
      response.responses.forEach((resp, _idx) => {
        if (!resp.success) {
        }
      });
    }

    return {
      success: response.successCount,
      failure: response.failureCount,
      responses: response.responses,
    };
  } catch (_error) {
    throw new ApiError('Failed to send notification', 500, 'FCM_SEND_FAILED');
  }
}

// Check if FCM is configured
export function isFCMConfigured(): boolean {
  return !!(
    env.FCM_SERVICE_ACCOUNT_PATH ||
    (env.FCM_PROJECT_ID && env.FCM_PRIVATE_KEY && env.FCM_CLIENT_EMAIL)
  );
}
