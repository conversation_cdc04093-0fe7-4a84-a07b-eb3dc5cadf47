import { db } from '@repo/db';
import { user } from '@repo/db/schema';
import { and, eq, ilike, or } from 'drizzle-orm';

export interface MentionMatch {
  type: 'username' | 'email' | 'name';
  value: string;
  userId?: string;
}

export function extractMentions(text: string): string[] {
  // First check for UUID-based mentions from frontend: @[user:uuid]
  const uuidMentionRegex = /@\[user:([a-f0-9-]{36})\]/g;
  const uuidMentions: string[] = [];
  let match;

  while ((match = uuidMentionRegex.exec(text)) !== null) {
    uuidMentions.push(match[1]); // Extract just the UUID
  }

  // If we found UUID mentions, return those
  if (uuidMentions.length > 0) {
    return uuidMentions;
  }

  // Otherwise fall back to text-based mentions (for backward compatibility)
  const mentionRegex = /@(\w+(?:\.\w+)*(?:@[\w.-]+)?|\S+@[\w.-]+|\w+\s+\w+)/g;
  const mentions: string[] = [];

  while ((match = mentionRegex.exec(text)) !== null) {
    mentions.push(match[1]);
  }

  return mentions;
}

export async function resolveMentions(mentions: string[]): Promise<MentionMatch[]> {
  if (mentions.length === 0) {
    return [];
  }

  const resolvedMentions: MentionMatch[] = [];

  for (const mention of mentions) {
    let mentionMatch: MentionMatch | null = null;

    // Check if it's a UUID (from frontend mentions)
    const uuidRegex = /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i;
    if (uuidRegex.test(mention)) {
      const users = await db.select().from(user).where(eq(user.id, mention)).limit(1);

      if (users.length > 0) {
        mentionMatch = {
          type: 'username',
          value: users[0].displayName || users[0].email || mention,
          userId: users[0].id,
        };
      }
    }
    // Check if it's an email
    else if (mention.includes('@') && mention.includes('.')) {
      const users = await db.select().from(user).where(eq(user.email, mention)).limit(1);

      if (users.length > 0) {
        mentionMatch = {
          type: 'email',
          value: mention,
          userId: users[0].id,
        };
      }
    } else if (mention.includes(' ')) {
      // Check if it's a first name + last name combination
      const [firstName, lastName] = mention.split(' ');
      const users = await db
        .select()
        .from(user)
        .where(
          or(
            // Exact match for first and last name
            and(ilike(user.firstName, firstName), ilike(user.lastName, lastName)),
            // Check if it matches display name
            ilike(user.displayName, mention),
          ),
        )
        .limit(1);

      if (users.length > 0) {
        mentionMatch = {
          type: 'name',
          value: mention,
          userId: users[0].id,
        };
      }
    } else {
      // Check if it's a username (could be firstName, lastName, or displayName)
      const users = await db
        .select()
        .from(user)
        .where(
          or(
            ilike(user.firstName, mention),
            ilike(user.lastName, mention),
            ilike(user.displayName, mention),
            // Also check if mention contains part of email before @
            ilike(user.email, `${mention}%`),
          ),
        )
        .limit(1);

      if (users.length > 0) {
        mentionMatch = {
          type: 'username',
          value: mention,
          userId: users[0].id,
        };
      }
    }

    if (mentionMatch) {
      resolvedMentions.push(mentionMatch);
    } else {
      // Add unresolved mention
      resolvedMentions.push({
        type: 'username',
        value: mention,
      });
    }
  }

  return resolvedMentions;
}

/**
 * Extract user IDs from UUID-based mentions in the format @[user:uuid]
 * This is the format used by the frontend
 */
export function extractUuidMentions(text: string): string[] {
  const regex = /@\[user:([a-f0-9-]{36})\]/gi;
  const userIds: string[] = [];
  let match;

  while ((match = regex.exec(text)) !== null) {
    userIds.push(match[1]);
  }

  return [...new Set(userIds)]; // Remove duplicates
}

export async function extractAndResolveMentions(text: string): Promise<{
  mentions: string[];
  resolved: MentionMatch[];
  userIds: string[];
}> {
  // Check if we have UUID-based mentions first
  const uuidMentionRegex = /@\[user:([a-f0-9-]{36})\]/g;
  const uuidMatches = text.match(uuidMentionRegex);

  if (uuidMatches && uuidMatches.length > 0) {
    // Extract just the UUIDs
    const userIds: string[] = [];
    let match;
    uuidMentionRegex.lastIndex = 0; // Reset regex

    while ((match = uuidMentionRegex.exec(text)) !== null) {
      userIds.push(match[1]);
    }

    // Remove duplicates
    const uniqueUserIds = [...new Set(userIds)];

    // Create resolved matches for UUID mentions
    const resolved: MentionMatch[] = uniqueUserIds.map((userId) => ({
      type: 'username' as const,
      value: userId,
      userId,
    }));

    return {
      mentions: uniqueUserIds,
      resolved,
      userIds: uniqueUserIds,
    };
  }

  // Fallback to old mention resolution for backward compatibility
  const mentions = extractMentions(text);
  const resolved = await resolveMentions(mentions);
  const userIds = resolved
    .filter((mention) => mention.userId)
    .map((mention) => mention.userId!)
    // Remove duplicates
    .filter((userId, index, array) => array.indexOf(userId) === index);

  return {
    mentions,
    resolved,
    userIds,
  };
}
