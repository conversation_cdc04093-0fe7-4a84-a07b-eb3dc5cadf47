import { randomUUID } from 'node:crypto';
import { extname } from 'node:path';
import { env } from 'node:process';
import { config } from '../../config';

export interface FileUploadOptions {
  entityType: string;
  entityId: string;
  allowedTypes?: string[];
  maxSize?: number;
}

export interface UploadedFile {
  fieldname: string;
  filename: string;
  mimetype: string;
  size: number;
}

/**
 * Generate a unique storage key for a file
 */
export function generateStorageKey(
  entityType: string,
  entityId: string,
  originalFilename: string,
): string {
  const ext = extname(originalFilename);
  const uuid = randomUUID();
  const timestamp = Date.now();

  // Structure: entity-type/entity-id/timestamp-uuid-filename
  return `${entityType}/${entityId}/${timestamp}-${uuid}${ext}`;
}

/**
 * Check if file type is allowed
 */
export function isFileTypeAllowed(
  mimetype: string,
  allowedTypes: string[] = config.fileUpload.allowedFileTypes,
): boolean {
  return allowedTypes.some((type) => {
    if (type.endsWith('/*')) {
      // Handle wildcard types like "image/*"
      const baseType = type.slice(0, -2);
      return mimetype.startsWith(baseType);
    }

    if (type.startsWith('.')) {
      // Handle file extensions
      return mimetype.includes(type.slice(1));
    }

    return mimetype === type;
  });
}

/**
 * Check if file size is within limits
 */
export function isFileSizeAllowed(
  size: number,
  maxSize: number = config.fileUpload.maxFileSize,
): boolean {
  return size <= maxSize;
}

/**
 * Generate a public URL for a file
 */
export function generatePublicUrl(storageKey: string): string {
  // const protocol = config.minio.useSSL ? "https" : "http";
  const _port =
    config.minio.port === 80 || config.minio.port === 443 ? '' : `:${config.minio.port}`;

  return `${env.FRONT_END_URL}/${config.minio.bucketName}/${storageKey}`;
}

/**
 * Validate file upload
 */
export function validateFileUpload(
  file: UploadedFile,
  options: FileUploadOptions,
): { valid: boolean; error?: string } {
  // Check file size
  if (!isFileSizeAllowed(file.size, options.maxSize)) {
    return {
      valid: false,
      error: `File size exceeds maximum allowed size of ${options.maxSize || config.fileUpload.maxFileSize} bytes`,
    };
  }

  // Check file type
  if (!isFileTypeAllowed(file.mimetype, options.allowedTypes)) {
    return {
      valid: false,
      error: `File type ${file.mimetype} is not allowed`,
    };
  }

  return { valid: true };
}

/**
 * Extract file extension from mimetype or filename
 */
export function getFileExtension(filename: string, mimetype: string): string {
  const ext = extname(filename);
  if (ext) {
    return ext;
  }

  // Fallback to common mimetype mappings
  const mimeToExt: Record<string, string> = {
    'image/jpeg': '.jpg',
    'image/png': '.png',
    'image/gif': '.gif',
    'image/webp': '.webp',
    'application/pdf': '.pdf',
    'text/plain': '.txt',
    'application/msword': '.doc',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
    'application/vnd.ms-excel': '.xls',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
    'application/vnd.ms-powerpoint': '.ppt',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': '.pptx',
  };

  return mimeToExt[mimetype] || '';
}
