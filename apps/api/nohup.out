
> api@1.0.0 dev /Users/<USER>/Desktop/spms.buildbot.tech/apps/api
> node scripts/dev-with-deps.js

[10:51:31 UTC] INFO: Starting Fastify application
    environment: "development"
    host: "0.0.0.0"
    port: 3001
    logLevel: "info"
[10:51:31 UTC] INFO: Database connection established and attached to Fastify
[10:51:31 UTC] INFO: MinIO bucket exists: spark
[10:51:31 UTC] INFO: MinIO client initialized successfully
Completed running 'src/server.ts'
