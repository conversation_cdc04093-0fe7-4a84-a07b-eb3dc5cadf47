/**
 * Quick test script to verify date serialization is working
 * Run with: node test-date-serialization.js
 */

const axios = require('axios');

async function testDateSerialization() {
  const API_BASE = 'http://localhost:3000/api/v1';

  try {
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123',
    });

    const token = loginResponse.data.data.accessToken;

    // Set up axios with auth header
    const api = axios.create({
      baseURL: API_BASE,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    const orgsResponse = await api.get('/organizations');

    // Check if dates are strings
    if (orgsResponse.data.data.length > 0) {
      const org = orgsResponse.data.data[0];

      if (typeof org.createdAt === 'string' && typeof org.updatedAt === 'string') {
      } else {
      }
    }
    const projectsResponse = await api.get('/projects');

    if (projectsResponse.data.data.length > 0) {
      const project = projectsResponse.data.data[0];
      if (project.startDate) {
      }
      if (project.targetDate) {
      }
    }
  } catch (error) {
    if (error.response) {
    } else {
    }
  }
}

// Run the test
testDateSerialization();
