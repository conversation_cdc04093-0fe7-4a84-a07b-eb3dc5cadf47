{"name": "api", "version": "1.0.0", "description": "", "scripts": {"dev": "node scripts/dev-with-deps.js", "dev:direct": "tsx --watch src/server.ts", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@fastify/autoload": "^6.3.0", "@fastify/cors": "^11.0.1", "@fastify/helmet": "^13.0.1", "@fastify/jwt": "^9.1.0", "@fastify/multipart": "^9.0.3", "@fastify/swagger": "^9.4.0", "@fastify/swagger-ui": "^5.2.0", "@repo/db": "workspace:*", "argon2": "^0.41.1", "axios": "^1.7.9", "bcrypt": "^6.0.0", "dotenv": "^16.3.1", "dotenv-expand": "^12.0.1", "drizzle-orm": "0.41.0", "env-schema": "^6.0.0", "fastify": "^5.2.0", "fastify-plugin": "^5.0.1", "fastify-type-provider-zod": "^4.0.2", "firebase-admin": "^13.4.0", "http-status-codes": "^2.3.0", "jsonwebtoken": "^9.0.2", "minio": "^8.0.5", "nodemailer": "^7.0.3", "pg-error-enum": "^0.7.3", "pino": "^9.5.0", "pino-pretty": "^13.0.0", "postgres": "^3.4.5", "slugify": "^1.6.6", "zod": "^3.24.1"}, "devDependencies": {"@testcontainers/postgresql": "^10.16.0", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.10.1", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.2", "@vitest/ui": "^2.1.8", "eslint": "^9.17.0", "globals": "^15.14.0", "json-schema-to-ts": "^3.1.1", "supertest": "^7.0.0", "tsx": "^4.19.2", "typescript": "^5.7.2", "typescript-eslint": "^8.18.1", "vitest": "^2.1.8", "zod-to-json-schema": "^3.24.5"}}