# Use Node.js 20 slim as the base image
FROM node:20-slim

# Set working directory
WORKDIR /app

# Install pnpm globally
RUN npm install -g pnpm@9.0.0

# Copy root-level configuration files for the monorepo
COPY package.json pnpm-workspace.yaml turbo.json pnpm-lock.yaml ./

# Copy only the necessary apps and packages
COPY apps/api ./apps/api
COPY packages ./packages

# Install dependencies for the entire workspace
RUN pnpm install --frozen-lockfile

# Expose the API's default port
EXPOSE 3000

# Run the dev command for the api app
CMD ["./apps/api/migrate.sh"]