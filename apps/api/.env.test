# Test environment for E2E tests
NODE_ENV=test
PORT=3001
LOG_LEVEL=error

# Database - will use test database
DB_HOST=localhost
DB_PORT=5444
DB_USER=jaswanth
DB_PASSWORD=mysecretpassword
DB_NAME=spark_local_e2e_test

# Direct connection URL for test database
DATABASE_URL=postgresql://jaswanth:mysecretpassword@localhost:5444/spark_local_e2e_test

# JWT
JWT_SECRET=test-jwt-secret-for-e2e-tests

# Email (mock for tests)
EMAIL_HOST=localhost
EMAIL_PORT=1025
EMAIL_USER=test
EMAIL_PASS=test
EMAIL_FROM=<EMAIL>

# MinIO (using same instance)
MINIO_ENDPOINT=localhost
MINIO_PORT=9100
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET_NAME=spark-test-uploads
MINIO_USE_SSL=false