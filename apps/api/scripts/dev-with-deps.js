#!/usr/bin/env node

const { spawn, execSync } = require('node:child_process');
const _path = require('node:path');

// Colors for console output
const _colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
};

function log(_message, _color = 'reset') {}

// Check if MinIO is running
function isMinioRunning() {
  try {
    execSync('curl -f http://localhost:9000/minio/health/live', {
      stdio: 'ignore',
    });
    return true;
  } catch (_error) {
    return true; // update with remote linke latter 👆
  }
}

// Check if PostgreSQL is running
function isPostgresRunning() {
  try {
    execSync('pg_isready -h localhost -p 5432', { stdio: 'ignore' });
    return true;
  } catch (_error) {
    return true; // update with remote linke latter 👆
  }
}

// Main function
async function main() {
  log('🚀 Starting API development server...', 'green');

  // Check dependencies
  const checks = {
    'PostgreSQL (port 5432)': isPostgresRunning(),
    'MinIO (port 9000)': isMinioRunning(),
  };

  let hasErrors = false;

  log('\nChecking dependencies:', 'blue');
  for (const [service, isRunning] of Object.entries(checks)) {
    if (isRunning) {
      log(`  ✅ ${service} is running`, 'green');
    } else {
      log(`  ❌ ${service} is not running`, 'red');
      hasErrors = true;
    }
  }

  if (hasErrors) {
    log('\n⚠️  Some services are not running!', 'yellow');
    log('\nTo start all services, run from the root directory:', 'yellow');
    log('  pnpm minio:start   # Start MinIO', 'yellow');
    log('  docker-compose up -d db   # Start PostgreSQL', 'yellow');
    log('\nOr start everything with Docker Compose:', 'yellow');
    log('  docker-compose up -d', 'yellow');

    log('\n💡 Tip: Use "pnpm dev" from the root to start MinIO automatically', 'blue');

    // Ask user if they want to continue anyway
    log('\nDo you want to continue anyway? The API might not work properly. (y/N)', 'yellow');

    const readline = require('node:readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    const answer = await new Promise((resolve) => {
      rl.question('', (answer) => {
        rl.close();
        resolve(answer.toLowerCase());
      });
    });

    if (answer !== 'y' && answer !== 'yes') {
      log('Exiting...', 'red');
      process.exit(1);
    }
  }

  log('\nStarting development server...', 'green');

  // Start the actual dev server
  const child = spawn('tsx', ['--watch', 'src/server.ts'], {
    stdio: 'inherit',
    cwd: process.cwd(),
  });

  child.on('error', (error) => {
    log(`Failed to start server: ${error.message}`, 'red');
    process.exit(1);
  });

  child.on('exit', (code) => {
    process.exit(code || 0);
  });
}

// Run the main function
main().catch((error) => {
  log(`Error: ${error.message}`, 'red');
  process.exit(1);
});
