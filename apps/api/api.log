
> api@1.0.0 dev /Users/<USER>/Desktop/spms.buildbot.tech/apps/api
> node scripts/dev-with-deps.js

Creating app...
[12:14:15 UTC] INFO: Starting Fastify application
    environment: "development"
    host: "0.0.0.0"
    port: 3001
    logLevel: "info"
[12:14:15 UTC] INFO: Database connection established and attached to Fastify
[12:14:15 UTC] INFO: MinIO bucket exists: spark
[12:14:15 UTC] INFO: MinIO client initialized successfully
[12:14:15 UTC] INFO: Registering application routes...
App created successfully
Attempting to listen on 0.0.0.0:3001...
Server is running at http://0.0.0.0:3001
Documentation available at http://0.0.0.0:3001/documentation
Server is ready and listening
[12:14:15 UTC] INFO: Routes registered successfully
[12:14:15 UTC] INFO: Registered routes:
[12:14:15 UTC] INFO: Server listening at http://127.0.0.1:3001
[12:14:15 UTC] INFO: Server listening at http://*************:3001
[12:14:30 UTC] INFO: incoming request
    reqId: "req-1"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 59573
    }
[12:14:30 UTC] INFO: Request started
    reqId: "req-1"
    correlationId: "6454ada6-62ff-45dc-87ff-5ae9f578bf6b"
    method: "GET"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases"
    userAgent: "curl/8.7.1"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:14:30 UTC] INFO: Returning 1 test cases for suite 370e1ac0-834b-40ba-86c6-8e5ae0b46100
    reqId: "req-1"
[12:14:30 UTC] INFO: request completed
    reqId: "req-1"
    res: {
      "statusCode": 200
    }
    responseTime: 37.742750000208616
Restarting 'src/server.ts'
Creating app...
[12:14:47 UTC] INFO: Starting Fastify application
    environment: "development"
    host: "0.0.0.0"
    port: 3001
    logLevel: "info"
[12:14:47 UTC] INFO: Database connection established and attached to Fastify
[12:14:47 UTC] INFO: MinIO bucket exists: spark
[12:14:47 UTC] INFO: MinIO client initialized successfully
[12:14:47 UTC] INFO: Registering application routes...
App created successfully
Attempting to listen on 0.0.0.0:3001...
Server is running at http://0.0.0.0:3001
Documentation available at http://0.0.0.0:3001/documentation
Server is ready and listening
[12:14:47 UTC] INFO: Routes registered successfully
[12:14:47 UTC] INFO: Registered routes:
[12:14:47 UTC] INFO: Server listening at http://127.0.0.1:3001
[12:14:47 UTC] INFO: Server listening at http://*************:3001
[12:22:23 UTC] INFO: incoming request
    reqId: "req-1"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 61578
    }
[12:22:23 UTC] INFO: Request started
    reqId: "req-1"
    correlationId: "0fa23aa2-9f41-4ae3-93fd-8abc9ae8446c"
    method: "OPTIONS"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "options:role-assignments"
[12:22:23 UTC] INFO: request completed
    reqId: "req-1"
    res: {
      "statusCode": 204
    }
    responseTime: 3.755791001021862
[12:22:23 UTC] INFO: incoming request
    reqId: "req-2"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 61578
    }
[12:22:23 UTC] INFO: Request started
    reqId: "req-2"
    correlationId: "c0a3a62f-ab4a-45b5-818e-f2cc9f8eaef1"
    method: "OPTIONS"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "options:statuses"
[12:22:23 UTC] INFO: request completed
    reqId: "req-2"
    res: {
      "statusCode": 204
    }
    responseTime: 0.35016700252890587
[12:22:23 UTC] INFO: incoming request
    reqId: "req-3"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 61580
    }
[12:22:23 UTC] INFO: Request started
    reqId: "req-3"
    correlationId: "15d61cd5-91e0-43b6-bcee-537ab1cb74b8"
    method: "OPTIONS"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "options:workflow-transitions"
[12:22:23 UTC] INFO: request completed
    reqId: "req-3"
    res: {
      "statusCode": 204
    }
    responseTime: 0.36558400094509125
[12:22:23 UTC] INFO: incoming request
    reqId: "req-4"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 61578
    }
[12:22:23 UTC] INFO: Request started
    reqId: "req-4"
    correlationId: "94893d15-7cae-4f22-9062-20d532f3a7aa"
    method: "OPTIONS"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "options:priorities"
[12:22:23 UTC] INFO: request completed
    reqId: "req-4"
    res: {
      "statusCode": 204
    }
    responseTime: 0.25779199972748756
[12:22:23 UTC] INFO: incoming request
    reqId: "req-5"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 61582
    }
[12:22:23 UTC] INFO: Request started
    reqId: "req-5"
    correlationId: "182e6adc-51a6-4dfd-8a0a-cd5261c9477f"
    method: "OPTIONS"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "options:test-plans"
[12:22:23 UTC] INFO: request completed
    reqId: "req-5"
    res: {
      "statusCode": 204
    }
    responseTime: 0.37983400002121925
[12:22:23 UTC] INFO: incoming request
    reqId: "req-6"
    req: {
      "method": "GET",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 61578
    }
[12:22:23 UTC] INFO: Request started
    reqId: "req-6"
    correlationId: "4fda09a6-b127-4fa0-8e33-d5cbfc1948cd"
    method: "GET"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "get:statuses"
[12:22:23 UTC] INFO: incoming request
    reqId: "req-7"
    req: {
      "method": "GET",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 61582
    }
[12:22:23 UTC] INFO: Request started
    reqId: "req-7"
    correlationId: "ed1fd3ca-13b9-491a-be1b-db5ae3b09c4a"
    method: "GET"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "get:workflow-transitions"
[12:22:23 UTC] INFO: incoming request
    reqId: "req-8"
    req: {
      "method": "GET",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 61580
    }
[12:22:23 UTC] INFO: Request started
    reqId: "req-8"
    correlationId: "0df197d7-7064-47d0-a3d5-e21b8d63ef58"
    method: "GET"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "get:role-assignments"
[12:22:23 UTC] INFO: incoming request
    reqId: "req-9"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 61584
    }
[12:22:23 UTC] INFO: Request started
    reqId: "req-9"
    correlationId: "1c90bdeb-2d07-4390-accf-6ff373c25153"
    method: "OPTIONS"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:22:23 UTC] INFO: request completed
    reqId: "req-9"
    res: {
      "statusCode": 204
    }
    responseTime: 0.47749999910593033
[12:22:23 UTC] INFO: incoming request
    reqId: "req-a"
    req: {
      "method": "GET",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 61584
    }
[12:22:23 UTC] INFO: Request started
    reqId: "req-a"
    correlationId: "f53b19f0-08a7-4666-9240-7cd27ffb1b32"
    method: "GET"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "get:priorities"
[12:22:23 UTC] INFO: incoming request
    reqId: "req-b"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 61586
    }
[12:22:23 UTC] INFO: Request started
    reqId: "req-b"
    correlationId: "dc09b6a5-36ff-4884-adf7-3c10db4d8700"
    method: "OPTIONS"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:22:23 UTC] INFO: request completed
    reqId: "req-b"
    res: {
      "statusCode": 204
    }
    responseTime: 0.5984169989824295
[12:22:23 UTC] INFO: incoming request
    reqId: "req-c"
    req: {
      "method": "GET",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 61586
    }
[12:22:23 UTC] INFO: Request started
    reqId: "req-c"
    correlationId: "18ae0099-3f85-4652-8f57-28b7df644d04"
    method: "GET"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "get:test-plans"
[12:22:23 UTC] INFO: incoming request
    reqId: "req-d"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 61587
    }
[12:22:23 UTC] INFO: Request started
    reqId: "req-d"
    correlationId: "2cbce6a6-e6cf-4bd0-aee2-5774e7b7363d"
    method: "OPTIONS"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:22:23 UTC] INFO: request completed
    reqId: "req-d"
    res: {
      "statusCode": 204
    }
    responseTime: 0.27729200199246407
[12:22:23 UTC] INFO: incoming request
    reqId: "req-e"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 61587
    }
[12:22:23 UTC] INFO: Request started
    reqId: "req-e"
    correlationId: "ee084c02-30e6-4a64-b467-5a6cbd3d24c4"
    method: "GET"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:22:23 UTC] INFO: findMany operation on workflow-transition
    reqId: "req-7"
    correlationId: "ed1fd3ca-13b9-491a-be1b-db5ae3b09c4a"
    operation: "workflow-transition:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:22:23 UTC] INFO: findMany operation on role-assignment
    reqId: "req-8"
    correlationId: "0df197d7-7064-47d0-a3d5-e21b8d63ef58"
    operation: "role-assignment:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:22:23 UTC] INFO: findOne operation on test plan
    reqId: "req-c"
    correlationId: "18ae0099-3f85-4652-8f57-28b7df644d04"
    operation: "test plan:findOne"
    id: "9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
[12:22:23 UTC] INFO: findMany operation on status
    reqId: "req-6"
    correlationId: "4fda09a6-b127-4fa0-8e33-d5cbfc1948cd"
    operation: "status:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:22:23 UTC] INFO: findMany operation on priority
    reqId: "req-a"
    correlationId: "f53b19f0-08a7-4666-9240-7cd27ffb1b32"
    operation: "priority:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:22:23 UTC] INFO: findMany operation on test suite
    reqId: "req-e"
    correlationId: "ee084c02-30e6-4a64-b467-5a6cbd3d24c4"
    operation: "test suite:findMany"
    page: 1
    limit: 100
    hasFilters: true
[12:22:23 UTC] INFO: request completed
    reqId: "req-e"
    res: {
      "statusCode": 200
    }
    responseTime: 62.102125000208616
[12:22:23 UTC] INFO: incoming request
    reqId: "req-f"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 61587
    }
[12:22:23 UTC] INFO: Request started
    reqId: "req-f"
    correlationId: "5644a3c0-f4cf-44df-a1bc-420829a1d213"
    method: "GET"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:22:23 UTC] INFO: request completed
    reqId: "req-c"
    res: {
      "statusCode": 200
    }
    responseTime: 71.35620900243521
[12:22:23 UTC] INFO: incoming request
    reqId: "req-g"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 61586
    }
[12:22:23 UTC] INFO: Request started
    reqId: "req-g"
    correlationId: "8c8236e3-3506-4e59-a5f1-cf38620dedec"
    method: "GET"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:22:23 UTC] INFO: request completed
    reqId: "req-7"
    res: {
      "statusCode": 200
    }
    responseTime: 89.51404199749231
[12:22:23 UTC] INFO: request completed
    reqId: "req-a"
    res: {
      "statusCode": 200
    }
    responseTime: 82.27258300036192
[12:22:23 UTC] INFO: request completed
    reqId: "req-6"
    res: {
      "statusCode": 200
    }
    responseTime: 104.10024999827147
[12:22:23 UTC] INFO: request completed
    reqId: "req-8"
    res: {
      "statusCode": 200
    }
    responseTime: 94.26329100131989
[12:22:23 UTC] INFO: request completed
    reqId: "req-g"
    res: {
      "statusCode": 200
    }
    responseTime: 18.94275000318885
[12:22:23 UTC] INFO: Returning 1 test cases for suite 370e1ac0-834b-40ba-86c6-8e5ae0b46100
    reqId: "req-f"
[12:22:23 UTC] INFO: request completed
    reqId: "req-f"
    res: {
      "statusCode": 200
    }
    responseTime: 30.113667000085115
[12:29:02 UTC] INFO: incoming request
    reqId: "req-h"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63395
    }
[12:29:02 UTC] INFO: Request started
    reqId: "req-h"
    correlationId: "4e27db16-f672-4d61-9f14-fb342871e8e3"
    method: "OPTIONS"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "options:role-assignments"
[12:29:02 UTC] INFO: request completed
    reqId: "req-h"
    res: {
      "statusCode": 204
    }
    responseTime: 4.796999998390675
[12:29:02 UTC] INFO: incoming request
    reqId: "req-i"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63398
    }
[12:29:02 UTC] INFO: Request started
    reqId: "req-i"
    correlationId: "03c8f2ef-f679-4617-9d1b-77df991fc4b5"
    method: "OPTIONS"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "options:workflow-transitions"
[12:29:02 UTC] INFO: request completed
    reqId: "req-i"
    res: {
      "statusCode": 204
    }
    responseTime: 0.3728329986333847
[12:29:02 UTC] INFO: incoming request
    reqId: "req-j"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63395
    }
[12:29:02 UTC] INFO: Request started
    reqId: "req-j"
    correlationId: "06861f21-60d7-45dc-8221-4c7d51af2f56"
    method: "OPTIONS"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "options:statuses"
[12:29:02 UTC] INFO: request completed
    reqId: "req-j"
    res: {
      "statusCode": 204
    }
    responseTime: 0.36533400043845177
[12:29:02 UTC] INFO: incoming request
    reqId: "req-k"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63399
    }
[12:29:02 UTC] INFO: Request started
    reqId: "req-k"
    correlationId: "057d1db5-b3bd-4526-8675-7b8138ed182c"
    method: "OPTIONS"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "options:test-plans"
[12:29:02 UTC] INFO: request completed
    reqId: "req-k"
    res: {
      "statusCode": 204
    }
    responseTime: 0.38520800322294235
[12:29:02 UTC] INFO: incoming request
    reqId: "req-l"
    req: {
      "method": "GET",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63395
    }
[12:29:02 UTC] INFO: Request started
    reqId: "req-l"
    correlationId: "7d18ad30-520f-423e-aa87-d8462b9ed82e"
    method: "GET"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "get:role-assignments"
[12:29:03 UTC] INFO: incoming request
    reqId: "req-m"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63398
    }
[12:29:03 UTC] INFO: Request started
    reqId: "req-m"
    correlationId: "14d317d4-7470-40cc-a2fb-95effbe0eae1"
    method: "OPTIONS"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "options:priorities"
[12:29:03 UTC] INFO: request completed
    reqId: "req-m"
    res: {
      "statusCode": 204
    }
    responseTime: 0.2057499997317791
[12:29:03 UTC] INFO: incoming request
    reqId: "req-n"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63401
    }
[12:29:03 UTC] INFO: Request started
    reqId: "req-n"
    correlationId: "88bdf621-af81-47ec-b84e-d3dde67eace8"
    method: "OPTIONS"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:29:03 UTC] INFO: request completed
    reqId: "req-n"
    res: {
      "statusCode": 204
    }
    responseTime: 0.37154100090265274
[12:29:03 UTC] INFO: incoming request
    reqId: "req-o"
    req: {
      "method": "GET",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63398
    }
[12:29:03 UTC] INFO: Request started
    reqId: "req-o"
    correlationId: "2da253b6-4ad4-4e19-9b8d-463e4c08c13c"
    method: "GET"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "get:statuses"
[12:29:03 UTC] INFO: incoming request
    reqId: "req-p"
    req: {
      "method": "GET",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63399
    }
[12:29:03 UTC] INFO: Request started
    reqId: "req-p"
    correlationId: "5ed3b801-7182-40bc-be81-511c8dbaeded"
    method: "GET"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "get:workflow-transitions"
[12:29:03 UTC] INFO: incoming request
    reqId: "req-q"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63404
    }
[12:29:03 UTC] INFO: Request started
    reqId: "req-q"
    correlationId: "c58db61e-ef82-4c13-8a66-061282533248"
    method: "OPTIONS"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:29:03 UTC] INFO: request completed
    reqId: "req-q"
    res: {
      "statusCode": 204
    }
    responseTime: 0.17066700011491776
[12:29:03 UTC] INFO: incoming request
    reqId: "req-r"
    req: {
      "method": "GET",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63401
    }
[12:29:03 UTC] INFO: Request started
    reqId: "req-r"
    correlationId: "f1bb84ba-b967-43e4-b032-dac12706a073"
    method: "GET"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "get:test-plans"
[12:29:03 UTC] INFO: incoming request
    reqId: "req-s"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63405
    }
[12:29:03 UTC] INFO: Request started
    reqId: "req-s"
    correlationId: "bde81a80-6bb5-426a-ba76-82cb440e504e"
    method: "OPTIONS"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:29:03 UTC] INFO: request completed
    reqId: "req-s"
    res: {
      "statusCode": 204
    }
    responseTime: 0.11445800215005875
[12:29:03 UTC] INFO: incoming request
    reqId: "req-t"
    req: {
      "method": "GET",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63404
    }
[12:29:03 UTC] INFO: Request started
    reqId: "req-t"
    correlationId: "1575c6e3-3654-4152-93e6-5c46c7b70cec"
    method: "GET"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "get:priorities"
[12:29:03 UTC] INFO: incoming request
    reqId: "req-u"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63405
    }
[12:29:03 UTC] INFO: Request started
    reqId: "req-u"
    correlationId: "5d3ed0ca-6f70-466f-83fc-e489416b31fd"
    method: "GET"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:29:03 UTC] INFO: findMany operation on test suite
    reqId: "req-u"
    correlationId: "5d3ed0ca-6f70-466f-83fc-e489416b31fd"
    operation: "test suite:findMany"
    page: 1
    limit: 100
    hasFilters: true
[12:29:03 UTC] INFO: findMany operation on role-assignment
    reqId: "req-l"
    correlationId: "7d18ad30-520f-423e-aa87-d8462b9ed82e"
    operation: "role-assignment:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:29:03 UTC] INFO: findMany operation on status
    reqId: "req-o"
    correlationId: "2da253b6-4ad4-4e19-9b8d-463e4c08c13c"
    operation: "status:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:29:03 UTC] INFO: findMany operation on priority
    reqId: "req-t"
    correlationId: "1575c6e3-3654-4152-93e6-5c46c7b70cec"
    operation: "priority:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:29:03 UTC] INFO: findOne operation on test plan
    reqId: "req-r"
    correlationId: "f1bb84ba-b967-43e4-b032-dac12706a073"
    operation: "test plan:findOne"
    id: "9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
[12:29:03 UTC] INFO: findMany operation on workflow-transition
    reqId: "req-p"
    correlationId: "5ed3b801-7182-40bc-be81-511c8dbaeded"
    operation: "workflow-transition:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:29:03 UTC] INFO: request completed
    reqId: "req-r"
    res: {
      "statusCode": 200
    }
    responseTime: 20.34220799803734
[12:29:03 UTC] INFO: incoming request
    reqId: "req-v"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63401
    }
[12:29:03 UTC] INFO: Request started
    reqId: "req-v"
    correlationId: "77feac59-9d71-40a3-8049-41a4d4dc244b"
    method: "GET"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:29:03 UTC] INFO: request completed
    reqId: "req-p"
    res: {
      "statusCode": 200
    }
    responseTime: 29.85462500154972
[12:29:03 UTC] INFO: request completed
    reqId: "req-t"
    res: {
      "statusCode": 200
    }
    responseTime: 27.277667000889778
[12:29:03 UTC] INFO: request completed
    reqId: "req-u"
    res: {
      "statusCode": 200
    }
    responseTime: 26.94625000283122
[12:29:03 UTC] INFO: request completed
    reqId: "req-o"
    res: {
      "statusCode": 200
    }
    responseTime: 36.250124998390675
[12:29:03 UTC] INFO: incoming request
    reqId: "req-w"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63399
    }
[12:29:03 UTC] INFO: Request started
    reqId: "req-w"
    correlationId: "bafebf74-2dfb-4e7e-8376-046d58d9c8ba"
    method: "GET"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:29:03 UTC] INFO: request completed
    reqId: "req-l"
    res: {
      "statusCode": 200
    }
    responseTime: 55.82820900157094
[12:29:03 UTC] INFO: Returning 1 test cases for suite 370e1ac0-834b-40ba-86c6-8e5ae0b46100
    reqId: "req-v"
[12:29:03 UTC] INFO: request completed
    reqId: "req-v"
    res: {
      "statusCode": 200
    }
    responseTime: 15.034958001226187
[12:29:03 UTC] INFO: request completed
    reqId: "req-w"
    res: {
      "statusCode": 200
    }
    responseTime: 6.63162500038743
[12:29:07 UTC] INFO: incoming request
    reqId: "req-x"
    req: {
      "method": "GET",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63427
    }
[12:29:07 UTC] INFO: Request started
    reqId: "req-x"
    correlationId: "5eb7c056-6b06-41ac-8bee-1a77ddbe3856"
    method: "GET"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "get:role-assignments"
[12:29:07 UTC] INFO: incoming request
    reqId: "req-y"
    req: {
      "method": "GET",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63429
    }
[12:29:07 UTC] INFO: Request started
    reqId: "req-y"
    correlationId: "97248f3b-d6f5-499a-9966-173b9b7af507"
    method: "GET"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "get:workflow-transitions"
[12:29:07 UTC] INFO: incoming request
    reqId: "req-z"
    req: {
      "method": "GET",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63432
    }
[12:29:07 UTC] INFO: Request started
    reqId: "req-z"
    correlationId: "d7d3922d-b861-4740-9eb1-42c9dfd6f2b8"
    method: "GET"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "get:test-plans"
[12:29:07 UTC] INFO: incoming request
    reqId: "req-10"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63433
    }
[12:29:07 UTC] INFO: Request started
    reqId: "req-10"
    correlationId: "caab17e5-6d34-40c4-9309-996e117819bb"
    method: "GET"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:29:07 UTC] INFO: incoming request
    reqId: "req-11"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63436
    }
[12:29:07 UTC] INFO: Request started
    reqId: "req-11"
    correlationId: "8674b399-8fb3-4d33-b96c-49d6bd946456"
    method: "GET"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:29:07 UTC] INFO: incoming request
    reqId: "req-12"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63437
    }
[12:29:07 UTC] INFO: Request started
    reqId: "req-12"
    correlationId: "42ac305a-a395-4187-b87a-cd2df0670fdd"
    method: "GET"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:29:07 UTC] INFO: findOne operation on test plan
    reqId: "req-z"
    correlationId: "d7d3922d-b861-4740-9eb1-42c9dfd6f2b8"
    operation: "test plan:findOne"
    id: "9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
[12:29:07 UTC] INFO: findMany operation on workflow-transition
    reqId: "req-y"
    correlationId: "97248f3b-d6f5-499a-9966-173b9b7af507"
    operation: "workflow-transition:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:29:07 UTC] INFO: findMany operation on role-assignment
    reqId: "req-x"
    correlationId: "5eb7c056-6b06-41ac-8bee-1a77ddbe3856"
    operation: "role-assignment:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:29:07 UTC] INFO: findMany operation on test suite
    reqId: "req-10"
    correlationId: "caab17e5-6d34-40c4-9309-996e117819bb"
    operation: "test suite:findMany"
    page: 1
    limit: 100
    hasFilters: true
[12:29:07 UTC] INFO: request completed
    reqId: "req-z"
    res: {
      "statusCode": 200
    }
    responseTime: 29.699749998748302
[12:29:07 UTC] INFO: incoming request
    reqId: "req-13"
    req: {
      "method": "GET",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63432
    }
[12:29:07 UTC] INFO: Request started
    reqId: "req-13"
    correlationId: "66c81c38-0135-4f93-a3d3-c3de0c59d5f8"
    method: "GET"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "get:statuses"
[12:29:07 UTC] INFO: Returning 1 test cases for suite 370e1ac0-834b-40ba-86c6-8e5ae0b46100
    reqId: "req-11"
[12:29:07 UTC] INFO: request completed
    reqId: "req-11"
    res: {
      "statusCode": 200
    }
    responseTime: 32.268374998122454
[12:29:07 UTC] INFO: request completed
    reqId: "req-10"
    res: {
      "statusCode": 200
    }
    responseTime: 36.506666999310255
[12:29:07 UTC] INFO: findMany operation on status
    reqId: "req-13"
    correlationId: "66c81c38-0135-4f93-a3d3-c3de0c59d5f8"
    operation: "status:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:29:07 UTC] INFO: request completed
    reqId: "req-12"
    res: {
      "statusCode": 200
    }
    responseTime: 30.696791999042034
[12:29:07 UTC] INFO: request completed
    reqId: "req-y"
    res: {
      "statusCode": 200
    }
    responseTime: 46.80375000089407
[12:29:07 UTC] INFO: incoming request
    reqId: "req-14"
    req: {
      "method": "GET",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63436
    }
[12:29:07 UTC] INFO: Request started
    reqId: "req-14"
    correlationId: "23d7597f-b926-416b-ae1c-a333bf209e2a"
    method: "GET"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "get:priorities"
[12:29:07 UTC] INFO: findMany operation on priority
    reqId: "req-14"
    correlationId: "23d7597f-b926-416b-ae1c-a333bf209e2a"
    operation: "priority:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:29:07 UTC] INFO: request completed
    reqId: "req-x"
    res: {
      "statusCode": 200
    }
    responseTime: 57.85258299857378
[12:29:07 UTC] INFO: request completed
    reqId: "req-13"
    res: {
      "statusCode": 200
    }
    responseTime: 19.88791700080037
[12:29:07 UTC] INFO: request completed
    reqId: "req-14"
    res: {
      "statusCode": 200
    }
    responseTime: 9.08941600099206
[12:29:13 UTC] INFO: incoming request
    reqId: "req-15"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63475
    }
[12:29:13 UTC] INFO: Request started
    reqId: "req-15"
    correlationId: "d6d2264d-4285-4e3d-8f54-eee5b5b3aaa7"
    method: "OPTIONS"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "options:role-assignments"
[12:29:13 UTC] INFO: request completed
    reqId: "req-15"
    res: {
      "statusCode": 204
    }
    responseTime: 1.910207998007536
[12:29:13 UTC] INFO: incoming request
    reqId: "req-16"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63478
    }
[12:29:13 UTC] INFO: Request started
    reqId: "req-16"
    correlationId: "be4ecca1-530e-45f6-8c85-8766c5e8eaa5"
    method: "OPTIONS"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "options:workflow-transitions"
[12:29:13 UTC] INFO: request completed
    reqId: "req-16"
    res: {
      "statusCode": 204
    }
    responseTime: 0.36537500098347664
[12:29:13 UTC] INFO: incoming request
    reqId: "req-17"
    req: {
      "method": "GET",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63475
    }
[12:29:13 UTC] INFO: Request started
    reqId: "req-17"
    correlationId: "4c7ee8e8-b0ed-4a89-bfe8-c1489d8ce1b1"
    method: "GET"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "get:role-assignments"
[12:29:13 UTC] INFO: incoming request
    reqId: "req-18"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63479
    }
[12:29:13 UTC] INFO: Request started
    reqId: "req-18"
    correlationId: "cfcbd44d-b07d-4459-86f7-336e5241ebd4"
    method: "OPTIONS"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "options:test-plans"
[12:29:13 UTC] INFO: request completed
    reqId: "req-18"
    res: {
      "statusCode": 204
    }
    responseTime: 0.16266600042581558
[12:29:13 UTC] INFO: incoming request
    reqId: "req-19"
    req: {
      "method": "GET",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63478
    }
[12:29:13 UTC] INFO: Request started
    reqId: "req-19"
    correlationId: "7606ec19-25d8-410f-88bb-547b9c640f2e"
    method: "GET"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "get:workflow-transitions"
[12:29:13 UTC] INFO: incoming request
    reqId: "req-1a"
    req: {
      "method": "GET",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63479
    }
[12:29:13 UTC] INFO: Request started
    reqId: "req-1a"
    correlationId: "dc771cd0-1b40-44cd-809e-95770ae4984b"
    method: "GET"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "get:test-plans"
[12:29:13 UTC] INFO: incoming request
    reqId: "req-1b"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/user-fcm-tokens/",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63481
    }
[12:29:13 UTC] INFO: Request started
    reqId: "req-1b"
    correlationId: "18154e0c-a8ff-4d1d-827c-4cd0a5802521"
    method: "OPTIONS"
    url: "/api/v1/user-fcm-tokens/"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "user-fcm-tokens"
    operation: "options:user-fcm-tokens"
[12:29:13 UTC] INFO: request completed
    reqId: "req-1b"
    res: {
      "statusCode": 204
    }
    responseTime: 0.17799999937415123
[12:29:13 UTC] INFO: incoming request
    reqId: "req-1c"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/user-fcm-tokens/",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63483
    }
[12:29:13 UTC] INFO: Request started
    reqId: "req-1c"
    correlationId: "fd492bad-4914-4616-a9ef-c19e56b6f63b"
    method: "OPTIONS"
    url: "/api/v1/user-fcm-tokens/"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "user-fcm-tokens"
    operation: "options:user-fcm-tokens"
[12:29:13 UTC] INFO: request completed
    reqId: "req-1c"
    res: {
      "statusCode": 204
    }
    responseTime: 0.2722500003874302
[12:29:13 UTC] INFO: incoming request
    reqId: "req-1d"
    req: {
      "method": "POST",
      "url": "/api/v1/user-fcm-tokens/",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63481
    }
[12:29:13 UTC] INFO: Request started
    reqId: "req-1d"
    correlationId: "55aa91e6-011c-4c95-a9ec-1280bb63aee1"
    method: "POST"
    url: "/api/v1/user-fcm-tokens/"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "user-fcm-tokens"
    operation: "post:user-fcm-tokens"
[12:29:13 UTC] INFO: findMany operation on role-assignment
    reqId: "req-17"
    correlationId: "4c7ee8e8-b0ed-4a89-bfe8-c1489d8ce1b1"
    operation: "role-assignment:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:29:13 UTC] INFO: incoming request
    reqId: "req-1e"
    req: {
      "method": "POST",
      "url": "/api/v1/user-fcm-tokens/",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63483
    }
[12:29:13 UTC] INFO: Request started
    reqId: "req-1e"
    correlationId: "a3c1127b-b989-4a2a-a3fb-3f605eac9133"
    method: "POST"
    url: "/api/v1/user-fcm-tokens/"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "user-fcm-tokens"
    operation: "post:user-fcm-tokens"
[12:29:13 UTC] INFO: findMany operation on workflow-transition
    reqId: "req-19"
    correlationId: "7606ec19-25d8-410f-88bb-547b9c640f2e"
    operation: "workflow-transition:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:29:13 UTC] INFO: findOne operation on test plan
    reqId: "req-1a"
    correlationId: "dc771cd0-1b40-44cd-809e-95770ae4984b"
    operation: "test plan:findOne"
    id: "9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
[12:29:13 UTC] INFO: request completed
    reqId: "req-1a"
    res: {
      "statusCode": 200
    }
    responseTime: 23.14208399876952
[12:29:13 UTC] INFO: request completed
    reqId: "req-19"
    res: {
      "statusCode": 200
    }
    responseTime: 28.41449999809265
[12:29:13 UTC] INFO: request completed
    reqId: "req-17"
    res: {
      "statusCode": 200
    }
    responseTime: 35.588790997862816
[12:29:13 UTC] INFO: incoming request
    reqId: "req-1f"
    req: {
      "method": "GET",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63475
    }
[12:29:13 UTC] INFO: Request started
    reqId: "req-1f"
    correlationId: "bd321fdb-ecb4-4144-8429-11645ace9792"
    method: "GET"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "get:test-plans"
[12:29:13 UTC] INFO: incoming request
    reqId: "req-1g"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63478
    }
[12:29:13 UTC] INFO: Request started
    reqId: "req-1g"
    correlationId: "0ea6edeb-e74f-4fd2-b32b-666f9cfc8697"
    method: "OPTIONS"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:29:13 UTC] INFO: request completed
    reqId: "req-1g"
    res: {
      "statusCode": 204
    }
    responseTime: 0.14058399945497513
[12:29:13 UTC] INFO: request completed
    reqId: "req-1d"
    res: {
      "statusCode": 200
    }
    responseTime: 31.375707998871803
[12:29:13 UTC] INFO: request completed
    reqId: "req-1e"
    res: {
      "statusCode": 200
    }
    responseTime: 24.04620799794793
[12:29:13 UTC] INFO: incoming request
    reqId: "req-1h"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63478
    }
[12:29:13 UTC] INFO: Request started
    reqId: "req-1h"
    correlationId: "d8a42b98-8d77-4835-9f4b-4745a4b162db"
    method: "GET"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:29:14 UTC] INFO: findOne operation on test plan
    reqId: "req-1f"
    correlationId: "bd321fdb-ecb4-4144-8429-11645ace9792"
    operation: "test plan:findOne"
    id: "9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
[12:29:14 UTC] INFO: findMany operation on test suite
    reqId: "req-1h"
    correlationId: "d8a42b98-8d77-4835-9f4b-4745a4b162db"
    operation: "test suite:findMany"
    page: 1
    limit: 100
    hasFilters: true
[12:29:14 UTC] INFO: request completed
    reqId: "req-1f"
    res: {
      "statusCode": 200
    }
    responseTime: 11.956708997488022
[12:29:14 UTC] INFO: request completed
    reqId: "req-1h"
    res: {
      "statusCode": 200
    }
    responseTime: 11.021292001008987
[12:29:17 UTC] INFO: incoming request
    reqId: "req-1i"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63478
    }
[12:29:17 UTC] INFO: Request started
    reqId: "req-1i"
    correlationId: "0ad7df3a-1481-45b2-91b7-bb5a0fdcad66"
    method: "OPTIONS"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:29:17 UTC] INFO: request completed
    reqId: "req-1i"
    res: {
      "statusCode": 204
    }
    responseTime: 0.41804200038313866
[12:29:17 UTC] INFO: incoming request
    reqId: "req-1j"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63475
    }
[12:29:17 UTC] INFO: Request started
    reqId: "req-1j"
    correlationId: "a43b0874-ed54-4276-972c-a595bb71e149"
    method: "OPTIONS"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:29:17 UTC] INFO: request completed
    reqId: "req-1j"
    res: {
      "statusCode": 204
    }
    responseTime: 0.15062499791383743
[12:29:17 UTC] INFO: incoming request
    reqId: "req-1k"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63483
    }
[12:29:17 UTC] INFO: Request started
    reqId: "req-1k"
    correlationId: "e5d05ba6-eb39-49e2-85a3-9e67b8219a7d"
    method: "OPTIONS"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "options:statuses"
[12:29:17 UTC] INFO: request completed
    reqId: "req-1k"
    res: {
      "statusCode": 204
    }
    responseTime: 0.1402920000255108
[12:29:17 UTC] INFO: incoming request
    reqId: "req-1l"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63481
    }
[12:29:17 UTC] INFO: Request started
    reqId: "req-1l"
    correlationId: "a17bd047-f1be-4210-958b-3a92c04c029c"
    method: "OPTIONS"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "options:priorities"
[12:29:17 UTC] INFO: request completed
    reqId: "req-1l"
    res: {
      "statusCode": 204
    }
    responseTime: 0.14654099941253662
[12:29:17 UTC] INFO: incoming request
    reqId: "req-1m"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63478
    }
[12:29:17 UTC] INFO: Request started
    reqId: "req-1m"
    correlationId: "073c750a-7cc0-4e37-a23b-7cccfb6caf83"
    method: "GET"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:29:17 UTC] INFO: incoming request
    reqId: "req-1n"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63481
    }
[12:29:17 UTC] INFO: Request started
    reqId: "req-1n"
    correlationId: "ba767873-3678-4f41-a97d-792d624860f8"
    method: "GET"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:29:17 UTC] INFO: incoming request
    reqId: "req-1o"
    req: {
      "method": "GET",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63483
    }
[12:29:17 UTC] INFO: Request started
    reqId: "req-1o"
    correlationId: "8649afee-3a9c-4f5a-9d74-df794f7bcb3d"
    method: "GET"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "get:statuses"
[12:29:17 UTC] INFO: incoming request
    reqId: "req-1p"
    req: {
      "method": "GET",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63475
    }
[12:29:17 UTC] INFO: Request started
    reqId: "req-1p"
    correlationId: "07798f5f-a96d-4d4f-80b0-2f4876e286d6"
    method: "GET"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "get:priorities"
[12:29:17 UTC] INFO: findMany operation on priority
    reqId: "req-1p"
    correlationId: "07798f5f-a96d-4d4f-80b0-2f4876e286d6"
    operation: "priority:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:29:17 UTC] INFO: findMany operation on status
    reqId: "req-1o"
    correlationId: "8649afee-3a9c-4f5a-9d74-df794f7bcb3d"
    operation: "status:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:29:17 UTC] INFO: request completed
    reqId: "req-1o"
    res: {
      "statusCode": 200
    }
    responseTime: 11.991916999220848
[12:29:17 UTC] INFO: request completed
    reqId: "req-1p"
    res: {
      "statusCode": 200
    }
    responseTime: 11.52479200065136
[12:29:17 UTC] INFO: request completed
    reqId: "req-1m"
    res: {
      "statusCode": 200
    }
    responseTime: 19.030792001634836
[12:29:17 UTC] INFO: Returning 1 test cases for suite 370e1ac0-834b-40ba-86c6-8e5ae0b46100
    reqId: "req-1n"
[12:29:17 UTC] INFO: request completed
    reqId: "req-1n"
    res: {
      "statusCode": 200
    }
    responseTime: 18.05300000309944
[12:29:21 UTC] INFO: incoming request
    reqId: "req-1q"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63514
    }
[12:29:21 UTC] INFO: Request started
    reqId: "req-1q"
    correlationId: "673d708b-210d-4de8-956e-51d474e73a2c"
    method: "OPTIONS"
    url: "/api/v1/test-suites/"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:29:21 UTC] INFO: request completed
    reqId: "req-1q"
    res: {
      "statusCode": 204
    }
    responseTime: 0.4817500002682209
[12:29:21 UTC] INFO: incoming request
    reqId: "req-1r"
    req: {
      "method": "POST",
      "url": "/api/v1/test-suites/",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63514
    }
[12:29:21 UTC] INFO: Request started
    reqId: "req-1r"
    correlationId: "b82a4a93-e47c-4582-b642-340352eaa55a"
    method: "POST"
    url: "/api/v1/test-suites/"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "post:test-suites"
[12:29:21 UTC] INFO: create operation on test suite
    reqId: "req-1r"
    correlationId: "b82a4a93-e47c-4582-b642-340352eaa55a"
    operation: "test suite:create"
    hasData: true
[12:29:21 UTC] INFO: request completed
    reqId: "req-1r"
    res: {
      "statusCode": 201
    }
    responseTime: 13.152334000915289
[12:29:21 UTC] INFO: incoming request
    reqId: "req-1s"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63514
    }
[12:29:21 UTC] INFO: Request started
    reqId: "req-1s"
    correlationId: "6d27daa1-d8b6-4ffd-b9b3-3f77f9e0902b"
    method: "OPTIONS"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:29:21 UTC] INFO: request completed
    reqId: "req-1s"
    res: {
      "statusCode": 204
    }
    responseTime: 0.28779200091958046
[12:29:21 UTC] INFO: incoming request
    reqId: "req-1t"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63514
    }
[12:29:21 UTC] INFO: Request started
    reqId: "req-1t"
    correlationId: "e998b1a8-4328-4620-8eb3-cc489b72f686"
    method: "GET"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:29:21 UTC] INFO: findMany operation on test suite
    reqId: "req-1t"
    correlationId: "e998b1a8-4328-4620-8eb3-cc489b72f686"
    operation: "test suite:findMany"
    page: 1
    limit: 100
    hasFilters: true
[12:29:21 UTC] INFO: request completed
    reqId: "req-1t"
    res: {
      "statusCode": 200
    }
    responseTime: 5.385917000472546
[12:29:30 UTC] INFO: incoming request
    reqId: "req-1u"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63552
    }
[12:29:30 UTC] INFO: Request started
    reqId: "req-1u"
    correlationId: "b1d4f39b-9de9-4acb-8755-7573a365d34c"
    method: "OPTIONS"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:29:30 UTC] INFO: request completed
    reqId: "req-1u"
    res: {
      "statusCode": 204
    }
    responseTime: 0.3351670019328594
[12:29:30 UTC] INFO: incoming request
    reqId: "req-1v"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63552
    }
[12:29:30 UTC] INFO: Request started
    reqId: "req-1v"
    correlationId: "27039ff1-201d-4af7-8f3a-f1b9f70b30fa"
    method: "GET"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:29:30 UTC] INFO: findMany operation on test suite
    reqId: "req-1v"
    correlationId: "27039ff1-201d-4af7-8f3a-f1b9f70b30fa"
    operation: "test suite:findMany"
    page: 1
    limit: 100
    hasFilters: true
[12:29:30 UTC] INFO: request completed
    reqId: "req-1v"
    res: {
      "statusCode": 200
    }
    responseTime: 7.880915999412537
[12:29:50 UTC] INFO: incoming request
    reqId: "req-1w"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63639
    }
[12:29:50 UTC] INFO: Request started
    reqId: "req-1w"
    correlationId: "07ec9132-6222-4c6f-93ae-88a83c5b6e59"
    method: "OPTIONS"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "options:role-assignments"
[12:29:50 UTC] INFO: request completed
    reqId: "req-1w"
    res: {
      "statusCode": 204
    }
    responseTime: 1.159416999667883
[12:29:50 UTC] INFO: incoming request
    reqId: "req-1x"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63640
    }
[12:29:50 UTC] INFO: Request started
    reqId: "req-1x"
    correlationId: "3c6cbf81-3102-493c-9b70-5265b8814492"
    method: "OPTIONS"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "options:workflow-transitions"
[12:29:50 UTC] INFO: request completed
    reqId: "req-1x"
    res: {
      "statusCode": 204
    }
    responseTime: 0.2662080004811287
[12:29:50 UTC] INFO: incoming request
    reqId: "req-1y"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63643
    }
[12:29:50 UTC] INFO: Request started
    reqId: "req-1y"
    correlationId: "afaade8a-414e-4bfc-8e94-27202780804f"
    method: "OPTIONS"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "options:test-plans"
[12:29:50 UTC] INFO: request completed
    reqId: "req-1y"
    res: {
      "statusCode": 204
    }
    responseTime: 0.15833299979567528
[12:29:50 UTC] INFO: incoming request
    reqId: "req-1z"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63645
    }
[12:29:50 UTC] INFO: Request started
    reqId: "req-1z"
    correlationId: "2e58bdaa-38b6-4a89-9269-32c4eaf959ae"
    method: "OPTIONS"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:29:50 UTC] INFO: request completed
    reqId: "req-1z"
    res: {
      "statusCode": 204
    }
    responseTime: 0.2795420028269291
[12:29:50 UTC] INFO: incoming request
    reqId: "req-20"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63646
    }
[12:29:50 UTC] INFO: Request started
    reqId: "req-20"
    correlationId: "2285d2a8-1bd9-4c2a-8610-36470fc4ed21"
    method: "OPTIONS"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:29:50 UTC] INFO: request completed
    reqId: "req-20"
    res: {
      "statusCode": 204
    }
    responseTime: 0.1811250001192093
[12:29:50 UTC] INFO: incoming request
    reqId: "req-21"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63639
    }
[12:29:50 UTC] INFO: Request started
    reqId: "req-21"
    correlationId: "b5cf0655-13d7-4b78-9937-ada4fded2c02"
    method: "OPTIONS"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:29:50 UTC] INFO: request completed
    reqId: "req-21"
    res: {
      "statusCode": 204
    }
    responseTime: 0.5081660002470016
[12:29:50 UTC] INFO: incoming request
    reqId: "req-22"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63648
    }
[12:29:50 UTC] INFO: Request started
    reqId: "req-22"
    correlationId: "03736d06-932a-48e3-accd-00736e40e9ff"
    method: "OPTIONS"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "options:statuses"
[12:29:50 UTC] INFO: request completed
    reqId: "req-22"
    res: {
      "statusCode": 204
    }
    responseTime: 0.43266699835658073
[12:29:50 UTC] INFO: incoming request
    reqId: "req-23"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63640
    }
[12:29:50 UTC] INFO: Request started
    reqId: "req-23"
    correlationId: "a769bfbf-3b5e-49e2-8bae-d096d6a12b38"
    method: "OPTIONS"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "options:priorities"
[12:29:50 UTC] INFO: request completed
    reqId: "req-23"
    res: {
      "statusCode": 204
    }
    responseTime: 0.16370899975299835
[12:29:50 UTC] INFO: incoming request
    reqId: "req-24"
    req: {
      "method": "GET",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63646
    }
[12:29:50 UTC] INFO: Request started
    reqId: "req-24"
    correlationId: "5ad35ae4-a3af-410b-97eb-e4dd88e1f63d"
    method: "GET"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "get:test-plans"
[12:29:50 UTC] INFO: incoming request
    reqId: "req-25"
    req: {
      "method": "GET",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63643
    }
[12:29:50 UTC] INFO: Request started
    reqId: "req-25"
    correlationId: "382cd8b2-d11e-4a94-b8db-201632304c38"
    method: "GET"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "get:role-assignments"
[12:29:50 UTC] INFO: incoming request
    reqId: "req-26"
    req: {
      "method": "GET",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63645
    }
[12:29:50 UTC] INFO: Request started
    reqId: "req-26"
    correlationId: "22695c8b-d102-4bef-bc4a-8d2bfabe9ea0"
    method: "GET"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "get:workflow-transitions"
[12:29:50 UTC] INFO: incoming request
    reqId: "req-27"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63648
    }
[12:29:50 UTC] INFO: Request started
    reqId: "req-27"
    correlationId: "ce0ec49c-f7ba-4a8a-a902-c3ef357c832c"
    method: "GET"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:29:50 UTC] INFO: incoming request
    reqId: "req-28"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63640
    }
[12:29:50 UTC] INFO: Request started
    reqId: "req-28"
    correlationId: "54fc8a4b-8cf8-4a00-9829-5efe24e8e729"
    method: "GET"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:29:50 UTC] INFO: incoming request
    reqId: "req-29"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63639
    }
[12:29:50 UTC] INFO: Request started
    reqId: "req-29"
    correlationId: "e90f8df6-fdfb-4a43-9e27-7b02c51145ef"
    method: "GET"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:29:50 UTC] INFO: findOne operation on test plan
    reqId: "req-24"
    correlationId: "5ad35ae4-a3af-410b-97eb-e4dd88e1f63d"
    operation: "test plan:findOne"
    id: "9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
[12:29:50 UTC] INFO: findMany operation on role-assignment
    reqId: "req-25"
    correlationId: "382cd8b2-d11e-4a94-b8db-201632304c38"
    operation: "role-assignment:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:29:50 UTC] INFO: findMany operation on workflow-transition
    reqId: "req-26"
    correlationId: "22695c8b-d102-4bef-bc4a-8d2bfabe9ea0"
    operation: "workflow-transition:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:29:50 UTC] INFO: findMany operation on test suite
    reqId: "req-29"
    correlationId: "e90f8df6-fdfb-4a43-9e27-7b02c51145ef"
    operation: "test suite:findMany"
    page: 1
    limit: 100
    hasFilters: true
[12:29:50 UTC] INFO: request completed
    reqId: "req-24"
    res: {
      "statusCode": 200
    }
    responseTime: 26.894499998539686
[12:29:50 UTC] INFO: incoming request
    reqId: "req-2a"
    req: {
      "method": "GET",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63646
    }
[12:29:50 UTC] INFO: Request started
    reqId: "req-2a"
    correlationId: "7b3a3065-f19e-470c-8493-0e4f26efc4ba"
    method: "GET"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "get:statuses"
[12:29:50 UTC] INFO: request completed
    reqId: "req-29"
    res: {
      "statusCode": 200
    }
    responseTime: 23.571375001221895
[12:29:50 UTC] INFO: findMany operation on status
    reqId: "req-2a"
    correlationId: "7b3a3065-f19e-470c-8493-0e4f26efc4ba"
    operation: "status:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:29:50 UTC] INFO: request completed
    reqId: "req-28"
    res: {
      "statusCode": 200
    }
    responseTime: 27.19487500190735
[12:29:50 UTC] INFO: request completed
    reqId: "req-26"
    res: {
      "statusCode": 200
    }
    responseTime: 32.90470799803734
[12:29:50 UTC] INFO: Returning 1 test cases for suite 370e1ac0-834b-40ba-86c6-8e5ae0b46100
    reqId: "req-27"
[12:29:50 UTC] INFO: request completed
    reqId: "req-27"
    res: {
      "statusCode": 200
    }
    responseTime: 33.082750000059605
[12:29:50 UTC] INFO: incoming request
    reqId: "req-2b"
    req: {
      "method": "GET",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63639
    }
[12:29:50 UTC] INFO: Request started
    reqId: "req-2b"
    correlationId: "2a56fb99-3e27-4e05-b283-f737c08bdf97"
    method: "GET"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "get:priorities"
[12:29:50 UTC] INFO: findMany operation on priority
    reqId: "req-2b"
    correlationId: "2a56fb99-3e27-4e05-b283-f737c08bdf97"
    operation: "priority:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:29:50 UTC] INFO: request completed
    reqId: "req-25"
    res: {
      "statusCode": 200
    }
    responseTime: 41.764042001217604
[12:29:50 UTC] INFO: request completed
    reqId: "req-2a"
    res: {
      "statusCode": 200
    }
    responseTime: 16.055833000689745
[12:29:50 UTC] INFO: request completed
    reqId: "req-2b"
    res: {
      "statusCode": 200
    }
    responseTime: 8.400250002741814
[12:30:02 UTC] INFO: incoming request
    reqId: "req-2c"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63712
    }
[12:30:02 UTC] INFO: Request started
    reqId: "req-2c"
    correlationId: "1e977a42-7588-430e-9acf-48463b308fbb"
    method: "OPTIONS"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "options:role-assignments"
[12:30:02 UTC] INFO: request completed
    reqId: "req-2c"
    res: {
      "statusCode": 204
    }
    responseTime: 0.6577499993145466
[12:30:02 UTC] INFO: incoming request
    reqId: "req-2d"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63714
    }
[12:30:02 UTC] INFO: Request started
    reqId: "req-2d"
    correlationId: "379abb18-336d-4012-9dcd-ca5a792d5790"
    method: "OPTIONS"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "options:workflow-transitions"
[12:30:02 UTC] INFO: request completed
    reqId: "req-2d"
    res: {
      "statusCode": 204
    }
    responseTime: 0.4516660012304783
[12:30:02 UTC] INFO: incoming request
    reqId: "req-2e"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63716
    }
[12:30:02 UTC] INFO: Request started
    reqId: "req-2e"
    correlationId: "390fbdd2-d716-4321-abf7-c511159cfdfa"
    method: "OPTIONS"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "options:test-plans"
[12:30:02 UTC] INFO: request completed
    reqId: "req-2e"
    res: {
      "statusCode": 204
    }
    responseTime: 0.33354099839925766
[12:30:02 UTC] INFO: incoming request
    reqId: "req-2f"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63719
    }
[12:30:02 UTC] INFO: Request started
    reqId: "req-2f"
    correlationId: "e79bcdd1-6585-4a6c-aa3b-87979515e101"
    method: "OPTIONS"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:30:02 UTC] INFO: request completed
    reqId: "req-2f"
    res: {
      "statusCode": 204
    }
    responseTime: 0.29829199984669685
[12:30:02 UTC] INFO: incoming request
    reqId: "req-2g"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63712
    }
[12:30:02 UTC] INFO: Request started
    reqId: "req-2g"
    correlationId: "e0564a97-7c6e-4104-a260-9a5d62ac8b39"
    method: "OPTIONS"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:30:02 UTC] INFO: request completed
    reqId: "req-2g"
    res: {
      "statusCode": 204
    }
    responseTime: 0.1505419984459877
[12:30:02 UTC] INFO: incoming request
    reqId: "req-2h"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63721
    }
[12:30:02 UTC] INFO: Request started
    reqId: "req-2h"
    correlationId: "fa0be3c2-5f15-420e-bf02-cd8d83800df3"
    method: "OPTIONS"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:30:02 UTC] INFO: request completed
    reqId: "req-2h"
    res: {
      "statusCode": 204
    }
    responseTime: 0.4655419997870922
[12:30:02 UTC] INFO: incoming request
    reqId: "req-2i"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63722
    }
[12:30:02 UTC] INFO: Request started
    reqId: "req-2i"
    correlationId: "1a730f57-c0fa-434c-8395-abbe53b89ba8"
    method: "OPTIONS"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "options:statuses"
[12:30:02 UTC] INFO: request completed
    reqId: "req-2i"
    res: {
      "statusCode": 204
    }
    responseTime: 0.5612500011920929
[12:30:02 UTC] INFO: incoming request
    reqId: "req-2j"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63714
    }
[12:30:02 UTC] INFO: Request started
    reqId: "req-2j"
    correlationId: "2e0b7d51-16df-4102-8987-c94eab2dab5d"
    method: "OPTIONS"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "options:priorities"
[12:30:02 UTC] INFO: request completed
    reqId: "req-2j"
    res: {
      "statusCode": 204
    }
    responseTime: 0.41633300110697746
[12:30:02 UTC] INFO: incoming request
    reqId: "req-2k"
    req: {
      "method": "GET",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63716
    }
[12:30:02 UTC] INFO: Request started
    reqId: "req-2k"
    correlationId: "e00768d1-861e-42a0-b6ca-c56c98ea05fb"
    method: "GET"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "get:role-assignments"
[12:30:02 UTC] INFO: incoming request
    reqId: "req-2l"
    req: {
      "method": "GET",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63719
    }
[12:30:02 UTC] INFO: Request started
    reqId: "req-2l"
    correlationId: "7c675cee-c48e-4f83-b47d-f00461ecb5c4"
    method: "GET"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "get:workflow-transitions"
[12:30:02 UTC] INFO: incoming request
    reqId: "req-2m"
    req: {
      "method": "GET",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63712
    }
[12:30:02 UTC] INFO: Request started
    reqId: "req-2m"
    correlationId: "f4734163-ac06-4eba-b006-edeffa427c9d"
    method: "GET"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "get:test-plans"
[12:30:02 UTC] INFO: incoming request
    reqId: "req-2n"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63721
    }
[12:30:02 UTC] INFO: Request started
    reqId: "req-2n"
    correlationId: "266ec15b-0bc8-4e8b-a09d-1d71ee15032d"
    method: "GET"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:30:02 UTC] INFO: incoming request
    reqId: "req-2o"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63722
    }
[12:30:02 UTC] INFO: Request started
    reqId: "req-2o"
    correlationId: "25471e7e-6da4-4396-ab6e-5c6b13e3418c"
    method: "GET"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:30:02 UTC] INFO: incoming request
    reqId: "req-2p"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63714
    }
[12:30:02 UTC] INFO: Request started
    reqId: "req-2p"
    correlationId: "df791917-e229-4807-8084-9b5510f7a60c"
    method: "GET"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:30:02 UTC] INFO: findOne operation on test plan
    reqId: "req-2m"
    correlationId: "f4734163-ac06-4eba-b006-edeffa427c9d"
    operation: "test plan:findOne"
    id: "9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
[12:30:02 UTC] INFO: findMany operation on workflow-transition
    reqId: "req-2l"
    correlationId: "7c675cee-c48e-4f83-b47d-f00461ecb5c4"
    operation: "workflow-transition:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:30:02 UTC] INFO: findMany operation on test suite
    reqId: "req-2n"
    correlationId: "266ec15b-0bc8-4e8b-a09d-1d71ee15032d"
    operation: "test suite:findMany"
    page: 1
    limit: 100
    hasFilters: true
[12:30:02 UTC] INFO: findMany operation on role-assignment
    reqId: "req-2k"
    correlationId: "e00768d1-861e-42a0-b6ca-c56c98ea05fb"
    operation: "role-assignment:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:30:02 UTC] INFO: request completed
    reqId: "req-2n"
    res: {
      "statusCode": 200
    }
    responseTime: 41.373166002333164
[12:30:02 UTC] INFO: Returning 1 test cases for suite 370e1ac0-834b-40ba-86c6-8e5ae0b46100
    reqId: "req-2o"
[12:30:02 UTC] INFO: request completed
    reqId: "req-2o"
    res: {
      "statusCode": 200
    }
    responseTime: 43.07108299806714
[12:30:02 UTC] INFO: incoming request
    reqId: "req-2q"
    req: {
      "method": "GET",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63721
    }
[12:30:02 UTC] INFO: Request started
    reqId: "req-2q"
    correlationId: "ee270a13-abe8-4990-8948-389d548e15b8"
    method: "GET"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "get:statuses"
[12:30:02 UTC] INFO: request completed
    reqId: "req-2p"
    res: {
      "statusCode": 200
    }
    responseTime: 41.460124999284744
[12:30:02 UTC] INFO: incoming request
    reqId: "req-2r"
    req: {
      "method": "GET",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63722
    }
[12:30:02 UTC] INFO: Request started
    reqId: "req-2r"
    correlationId: "e6deded2-f890-46d1-bbc5-16e38f916946"
    method: "GET"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "get:priorities"
[12:30:02 UTC] INFO: request completed
    reqId: "req-2m"
    res: {
      "statusCode": 200
    }
    responseTime: 50.22233299911022
[12:30:02 UTC] INFO: request completed
    reqId: "req-2k"
    res: {
      "statusCode": 200
    }
    responseTime: 56.25754199922085
[12:30:02 UTC] INFO: findMany operation on status
    reqId: "req-2q"
    correlationId: "ee270a13-abe8-4990-8948-389d548e15b8"
    operation: "status:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:30:02 UTC] INFO: findMany operation on priority
    reqId: "req-2r"
    correlationId: "e6deded2-f890-46d1-bbc5-16e38f916946"
    operation: "priority:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:30:02 UTC] INFO: request completed
    reqId: "req-2l"
    res: {
      "statusCode": 200
    }
    responseTime: 56.36879199743271
[12:30:02 UTC] INFO: request completed
    reqId: "req-2q"
    res: {
      "statusCode": 200
    }
    responseTime: 9.394666999578476
[12:30:02 UTC] INFO: request completed
    reqId: "req-2r"
    res: {
      "statusCode": 200
    }
    responseTime: 7.641166999936104
[12:30:07 UTC] INFO: incoming request
    reqId: "req-2s"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63745
    }
[12:30:07 UTC] INFO: Request started
    reqId: "req-2s"
    correlationId: "77b4e203-5159-40b3-be52-8c830b5d08e6"
    method: "OPTIONS"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:30:07 UTC] INFO: request completed
    reqId: "req-2s"
    res: {
      "statusCode": 204
    }
    responseTime: 0.6252919994294643
[12:30:07 UTC] INFO: incoming request
    reqId: "req-2t"
    req: {
      "method": "PATCH",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63745
    }
[12:30:07 UTC] INFO: Request started
    reqId: "req-2t"
    correlationId: "dfaceaa3-70ae-435e-87ee-7732dc3edd2a"
    method: "PATCH"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "patch:test-suites"
[12:30:07 UTC] INFO: update operation on test suite
    reqId: "req-2t"
    correlationId: "dfaceaa3-70ae-435e-87ee-7732dc3edd2a"
    operation: "test suite:update"
    id: "70008509-7c87-485f-ba5e-a47543a15e21"
    hasData: true
[12:30:07 UTC] INFO: request completed
    reqId: "req-2t"
    res: {
      "statusCode": 200
    }
    responseTime: 15.303874999284744
[12:30:09 UTC] INFO: incoming request
    reqId: "req-2u"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63745
    }
[12:30:09 UTC] INFO: Request started
    reqId: "req-2u"
    correlationId: "96ba95dd-ba1a-4338-b426-de1fd9717288"
    method: "OPTIONS"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:30:09 UTC] INFO: request completed
    reqId: "req-2u"
    res: {
      "statusCode": 204
    }
    responseTime: 0.515625
[12:30:09 UTC] INFO: incoming request
    reqId: "req-2v"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63759
    }
[12:30:09 UTC] INFO: Request started
    reqId: "req-2v"
    correlationId: "ddd3e04a-3093-40d8-b271-b265bab5b31a"
    method: "OPTIONS"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:30:09 UTC] INFO: request completed
    reqId: "req-2v"
    res: {
      "statusCode": 204
    }
    responseTime: 0.6568749994039536
[12:30:09 UTC] INFO: incoming request
    reqId: "req-2w"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63745
    }
[12:30:09 UTC] INFO: Request started
    reqId: "req-2w"
    correlationId: "dbc82c19-cd89-4a61-958a-1391387f8eb5"
    method: "GET"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:30:09 UTC] INFO: incoming request
    reqId: "req-2x"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63759
    }
[12:30:09 UTC] INFO: Request started
    reqId: "req-2x"
    correlationId: "efcb9e63-1083-4542-b988-53f6e91738b9"
    method: "GET"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:30:09 UTC] INFO: Returning 1 test cases for suite 70008509-7c87-485f-ba5e-a47543a15e21
    reqId: "req-2x"
[12:30:09 UTC] INFO: request completed
    reqId: "req-2x"
    res: {
      "statusCode": 200
    }
    responseTime: 7.628833997994661
[12:30:09 UTC] INFO: request completed
    reqId: "req-2w"
    res: {
      "statusCode": 200
    }
    responseTime: 10.266459003090858
[12:30:11 UTC] INFO: incoming request
    reqId: "req-2y"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63745
    }
[12:30:11 UTC] INFO: Request started
    reqId: "req-2y"
    correlationId: "b904fade-a05f-4770-8828-84902d1b1d2e"
    method: "OPTIONS"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:30:11 UTC] INFO: request completed
    reqId: "req-2y"
    res: {
      "statusCode": 204
    }
    responseTime: 0.7292499989271164
[12:30:11 UTC] INFO: incoming request
    reqId: "req-2z"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63759
    }
[12:30:11 UTC] INFO: Request started
    reqId: "req-2z"
    correlationId: "48b49742-a8e9-4e2d-8d59-c88542d25276"
    method: "OPTIONS"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:30:11 UTC] INFO: request completed
    reqId: "req-2z"
    res: {
      "statusCode": 204
    }
    responseTime: 0.622333001345396
[12:30:11 UTC] INFO: incoming request
    reqId: "req-30"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63745
    }
[12:30:11 UTC] INFO: Request started
    reqId: "req-30"
    correlationId: "a3f4fbd4-1408-42eb-a5e1-1aaf4e873c1a"
    method: "GET"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:30:11 UTC] INFO: incoming request
    reqId: "req-31"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63759
    }
[12:30:11 UTC] INFO: Request started
    reqId: "req-31"
    correlationId: "33740ae1-b6a3-4328-a0bd-0c7fed6b2552"
    method: "GET"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:30:11 UTC] INFO: request completed
    reqId: "req-30"
    res: {
      "statusCode": 200
    }
    responseTime: 12.522916998714209
[12:30:11 UTC] INFO: Returning 1 test cases for suite 370e1ac0-834b-40ba-86c6-8e5ae0b46100
    reqId: "req-31"
[12:30:11 UTC] INFO: request completed
    reqId: "req-31"
    res: {
      "statusCode": 200
    }
    responseTime: 10.95075000077486
[12:30:11 UTC] INFO: incoming request
    reqId: "req-32"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63759
    }
[12:30:11 UTC] INFO: Request started
    reqId: "req-32"
    correlationId: "1f3749c1-6984-41b9-9a8a-24133377105b"
    method: "GET"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:30:11 UTC] INFO: incoming request
    reqId: "req-33"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63745
    }
[12:30:11 UTC] INFO: Request started
    reqId: "req-33"
    correlationId: "d3b90979-5286-4f20-902f-e76e8f85795a"
    method: "GET"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:30:11 UTC] INFO: request completed
    reqId: "req-32"
    res: {
      "statusCode": 200
    }
    responseTime: 13.649500001221895
[12:30:11 UTC] INFO: Returning 1 test cases for suite 70008509-7c87-485f-ba5e-a47543a15e21
    reqId: "req-33"
[12:30:11 UTC] INFO: request completed
    reqId: "req-33"
    res: {
      "statusCode": 200
    }
    responseTime: 12.749083999544382
[12:30:12 UTC] INFO: incoming request
    reqId: "req-34"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63745
    }
[12:30:12 UTC] INFO: Request started
    reqId: "req-34"
    correlationId: "af356f74-037f-4d1d-beda-87484c6ce8bb"
    method: "GET"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:30:12 UTC] INFO: incoming request
    reqId: "req-35"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63759
    }
[12:30:12 UTC] INFO: Request started
    reqId: "req-35"
    correlationId: "2b4686d6-a90c-41b9-b425-1d0f03be4bfb"
    method: "GET"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:30:12 UTC] INFO: request completed
    reqId: "req-34"
    res: {
      "statusCode": 200
    }
    responseTime: 20.940874997526407
[12:30:12 UTC] INFO: Returning 1 test cases for suite 370e1ac0-834b-40ba-86c6-8e5ae0b46100
    reqId: "req-35"
[12:30:12 UTC] INFO: request completed
    reqId: "req-35"
    res: {
      "statusCode": 200
    }
    responseTime: 21.20779199898243
[12:30:14 UTC] INFO: incoming request
    reqId: "req-36"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63759
    }
[12:30:14 UTC] INFO: Request started
    reqId: "req-36"
    correlationId: "2be18dcc-b43f-4e1b-8e27-5afd5baa157b"
    method: "GET"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:30:14 UTC] INFO: incoming request
    reqId: "req-37"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63745
    }
[12:30:14 UTC] INFO: Request started
    reqId: "req-37"
    correlationId: "77c0aafb-c071-416c-b1e2-eb20719212ca"
    method: "GET"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:30:14 UTC] INFO: Returning 1 test cases for suite 70008509-7c87-485f-ba5e-a47543a15e21
    reqId: "req-37"
[12:30:14 UTC] INFO: request completed
    reqId: "req-37"
    res: {
      "statusCode": 200
    }
    responseTime: 11.45983399823308
[12:30:14 UTC] INFO: request completed
    reqId: "req-36"
    res: {
      "statusCode": 200
    }
    responseTime: 14.836374998092651
[12:30:20 UTC] INFO: incoming request
    reqId: "req-38"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63759
    }
[12:30:20 UTC] INFO: Request started
    reqId: "req-38"
    correlationId: "f3fdf5b4-8140-4729-b91c-b2dfeee8fce1"
    method: "OPTIONS"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "options:role-assignments"
[12:30:20 UTC] INFO: request completed
    reqId: "req-38"
    res: {
      "statusCode": 204
    }
    responseTime: 1.2231249995529652
[12:30:20 UTC] INFO: incoming request
    reqId: "req-39"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63745
    }
[12:30:20 UTC] INFO: Request started
    reqId: "req-39"
    correlationId: "266f85ea-1cca-4211-a844-5c75b7c6e13e"
    method: "OPTIONS"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "options:workflow-transitions"
[12:30:20 UTC] INFO: request completed
    reqId: "req-39"
    res: {
      "statusCode": 204
    }
    responseTime: 0.48579199984669685
[12:30:20 UTC] INFO: incoming request
    reqId: "req-3a"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63809
    }
[12:30:20 UTC] INFO: Request started
    reqId: "req-3a"
    correlationId: "af99bf64-d52e-4392-aedb-3b8f8cc067a3"
    method: "OPTIONS"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "options:test-plans"
[12:30:20 UTC] INFO: request completed
    reqId: "req-3a"
    res: {
      "statusCode": 204
    }
    responseTime: 0.5849580019712448
[12:30:20 UTC] INFO: incoming request
    reqId: "req-3b"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63759
    }
[12:30:20 UTC] INFO: Request started
    reqId: "req-3b"
    correlationId: "202af8fa-1e26-415c-a193-7700a96b98f0"
    method: "OPTIONS"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:30:20 UTC] INFO: request completed
    reqId: "req-3b"
    res: {
      "statusCode": 204
    }
    responseTime: 0.45662499964237213
[12:30:20 UTC] INFO: incoming request
    reqId: "req-3c"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63812
    }
[12:30:20 UTC] INFO: Request started
    reqId: "req-3c"
    correlationId: "dfb56687-ada2-4340-9d31-a0f3c1f8d6cf"
    method: "OPTIONS"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "options:statuses"
[12:30:20 UTC] INFO: request completed
    reqId: "req-3c"
    res: {
      "statusCode": 204
    }
    responseTime: 1.1762499995529652
[12:30:20 UTC] INFO: incoming request
    reqId: "req-3d"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63814
    }
[12:30:20 UTC] INFO: Request started
    reqId: "req-3d"
    correlationId: "7e1e149e-370f-4652-bd03-ba06e1056915"
    method: "OPTIONS"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "options:priorities"
[12:30:20 UTC] INFO: request completed
    reqId: "req-3d"
    res: {
      "statusCode": 204
    }
    responseTime: 0.5596249997615814
[12:30:20 UTC] INFO: incoming request
    reqId: "req-3e"
    req: {
      "method": "GET",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63745
    }
[12:30:20 UTC] INFO: Request started
    reqId: "req-3e"
    correlationId: "4574537a-3ab1-465e-bc04-dbbabc9b528a"
    method: "GET"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "get:role-assignments"
[12:30:20 UTC] INFO: incoming request
    reqId: "req-3f"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63815
    }
[12:30:20 UTC] INFO: Request started
    reqId: "req-3f"
    correlationId: "d645e224-82e9-4893-ae10-539f4502384a"
    method: "OPTIONS"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:30:20 UTC] INFO: request completed
    reqId: "req-3f"
    res: {
      "statusCode": 204
    }
    responseTime: 0.2194170020520687
[12:30:20 UTC] INFO: incoming request
    reqId: "req-3g"
    req: {
      "method": "GET",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63812
    }
[12:30:20 UTC] INFO: Request started
    reqId: "req-3g"
    correlationId: "dd48dc3a-c9c9-4336-8874-bf57c3807f3d"
    method: "GET"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "get:test-plans"
[12:30:20 UTC] INFO: incoming request
    reqId: "req-3h"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63814
    }
[12:30:20 UTC] INFO: Request started
    reqId: "req-3h"
    correlationId: "445b498f-1eb6-4a44-95ab-4fb1e0fb4b78"
    method: "GET"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:30:20 UTC] INFO: incoming request
    reqId: "req-3i"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63809
    }
[12:30:20 UTC] INFO: Request started
    reqId: "req-3i"
    correlationId: "e3fa8593-3da0-4caa-8d34-d417d8f5b2dc"
    method: "OPTIONS"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:30:20 UTC] INFO: request completed
    reqId: "req-3i"
    res: {
      "statusCode": 204
    }
    responseTime: 0.19008300080895424
[12:30:20 UTC] INFO: incoming request
    reqId: "req-3j"
    req: {
      "method": "GET",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63759
    }
[12:30:20 UTC] INFO: Request started
    reqId: "req-3j"
    correlationId: "121ee9e7-5ab5-4791-b976-037cfae8eb88"
    method: "GET"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "get:workflow-transitions"
[12:30:20 UTC] INFO: incoming request
    reqId: "req-3k"
    req: {
      "method": "GET",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63809
    }
[12:30:20 UTC] INFO: Request started
    reqId: "req-3k"
    correlationId: "a50bf58c-853d-4e71-9a27-16ca05d073a7"
    method: "GET"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "get:priorities"
[12:30:20 UTC] INFO: incoming request
    reqId: "req-3l"
    req: {
      "method": "GET",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63815
    }
[12:30:20 UTC] INFO: Request started
    reqId: "req-3l"
    correlationId: "3a3cfc7d-b44b-4ef6-b52c-0c06317a8ddd"
    method: "GET"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "get:statuses"
[12:30:20 UTC] INFO: findOne operation on test plan
    reqId: "req-3g"
    correlationId: "dd48dc3a-c9c9-4336-8874-bf57c3807f3d"
    operation: "test plan:findOne"
    id: "9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
[12:30:20 UTC] INFO: findMany operation on test suite
    reqId: "req-3h"
    correlationId: "445b498f-1eb6-4a44-95ab-4fb1e0fb4b78"
    operation: "test suite:findMany"
    page: 1
    limit: 100
    hasFilters: true
[12:30:20 UTC] INFO: findMany operation on priority
    reqId: "req-3k"
    correlationId: "a50bf58c-853d-4e71-9a27-16ca05d073a7"
    operation: "priority:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:30:20 UTC] INFO: findMany operation on status
    reqId: "req-3l"
    correlationId: "3a3cfc7d-b44b-4ef6-b52c-0c06317a8ddd"
    operation: "status:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:30:20 UTC] INFO: findMany operation on workflow-transition
    reqId: "req-3j"
    correlationId: "121ee9e7-5ab5-4791-b976-037cfae8eb88"
    operation: "workflow-transition:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:30:20 UTC] INFO: findMany operation on role-assignment
    reqId: "req-3e"
    correlationId: "4574537a-3ab1-465e-bc04-dbbabc9b528a"
    operation: "role-assignment:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:30:20 UTC] INFO: request completed
    reqId: "req-3g"
    res: {
      "statusCode": 200
    }
    responseTime: 23.672083001583815
[12:30:20 UTC] INFO: incoming request
    reqId: "req-3m"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63812
    }
[12:30:20 UTC] INFO: Request started
    reqId: "req-3m"
    correlationId: "6bda5352-b158-4bfe-a3c1-eb5e29bbbd4d"
    method: "GET"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:30:20 UTC] INFO: request completed
    reqId: "req-3h"
    res: {
      "statusCode": 200
    }
    responseTime: 22.233333002775908
[12:30:20 UTC] INFO: request completed
    reqId: "req-3k"
    res: {
      "statusCode": 200
    }
    responseTime: 17.071459002792835
[12:30:20 UTC] INFO: request completed
    reqId: "req-3j"
    res: {
      "statusCode": 200
    }
    responseTime: 24.443583000451326
[12:30:20 UTC] INFO: incoming request
    reqId: "req-3n"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63814
    }
[12:30:20 UTC] INFO: Request started
    reqId: "req-3n"
    correlationId: "3c3ea51c-dac5-4e8e-a03f-a8f8a246a4fa"
    method: "GET"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:30:20 UTC] INFO: request completed
    reqId: "req-3l"
    res: {
      "statusCode": 200
    }
    responseTime: 22.107709001749754
[12:30:20 UTC] INFO: request completed
    reqId: "req-3e"
    res: {
      "statusCode": 200
    }
    responseTime: 44.45604199916124
[12:30:20 UTC] INFO: request completed
    reqId: "req-3n"
    res: {
      "statusCode": 200
    }
    responseTime: 15.642083000391722
[12:30:20 UTC] INFO: Returning 1 test cases for suite 70008509-7c87-485f-ba5e-a47543a15e21
    reqId: "req-3m"
[12:30:20 UTC] INFO: request completed
    reqId: "req-3m"
    res: {
      "statusCode": 200
    }
    responseTime: 23.067375000566244
[12:30:38 UTC] INFO: incoming request
    reqId: "req-3o"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63812
    }
[12:30:38 UTC] INFO: Request started
    reqId: "req-3o"
    correlationId: "63dce42f-8c55-478b-9ed4-f9a328b16a9b"
    method: "OPTIONS"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "options:role-assignments"
[12:30:38 UTC] INFO: request completed
    reqId: "req-3o"
    res: {
      "statusCode": 204
    }
    responseTime: 3.0140830017626286
[12:30:38 UTC] INFO: incoming request
    reqId: "req-3p"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63812
    }
[12:30:38 UTC] INFO: Request started
    reqId: "req-3p"
    correlationId: "1a569331-e313-4b5c-abce-739604f4b708"
    method: "OPTIONS"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:30:38 UTC] INFO: request completed
    reqId: "req-3p"
    res: {
      "statusCode": 204
    }
    responseTime: 0.1811250001192093
[12:30:38 UTC] INFO: incoming request
    reqId: "req-3q"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63814
    }
[12:30:38 UTC] INFO: Request started
    reqId: "req-3q"
    correlationId: "7640db9f-4091-4eab-b694-44e2a12f4238"
    method: "OPTIONS"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "options:workflow-transitions"
[12:30:38 UTC] INFO: request completed
    reqId: "req-3q"
    res: {
      "statusCode": 204
    }
    responseTime: 0.45512500032782555
[12:30:38 UTC] INFO: incoming request
    reqId: "req-3r"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63745
    }
[12:30:38 UTC] INFO: Request started
    reqId: "req-3r"
    correlationId: "4bfc689c-d0aa-4999-83e7-29ef9a6411b0"
    method: "OPTIONS"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "options:test-plans"
[12:30:38 UTC] INFO: request completed
    reqId: "req-3r"
    res: {
      "statusCode": 204
    }
    responseTime: 0.25095900148153305
[12:30:38 UTC] INFO: incoming request
    reqId: "req-3s"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63815
    }
[12:30:38 UTC] INFO: Request started
    reqId: "req-3s"
    correlationId: "8ff41e48-f06b-4d48-b76c-f58ce27a8456"
    method: "OPTIONS"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:30:38 UTC] INFO: request completed
    reqId: "req-3s"
    res: {
      "statusCode": 204
    }
    responseTime: 0.41429200023412704
[12:30:38 UTC] INFO: incoming request
    reqId: "req-3t"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63759
    }
[12:30:38 UTC] INFO: Request started
    reqId: "req-3t"
    correlationId: "77f10115-6954-4d19-95a3-2c64dffe6fc0"
    method: "OPTIONS"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "options:statuses"
[12:30:38 UTC] INFO: request completed
    reqId: "req-3t"
    res: {
      "statusCode": 204
    }
    responseTime: 0.8867500014603138
[12:30:38 UTC] INFO: incoming request
    reqId: "req-3u"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63809
    }
[12:30:38 UTC] INFO: Request started
    reqId: "req-3u"
    correlationId: "5620459f-f423-44f9-a8f9-15ec75c9b14a"
    method: "OPTIONS"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "options:priorities"
[12:30:38 UTC] INFO: request completed
    reqId: "req-3u"
    res: {
      "statusCode": 204
    }
    responseTime: 0.3145830035209656
[12:30:38 UTC] INFO: incoming request
    reqId: "req-3v"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63812
    }
[12:30:38 UTC] INFO: Request started
    reqId: "req-3v"
    correlationId: "ec8168b1-99db-4acd-8c28-f4ab567f4a9a"
    method: "OPTIONS"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:30:38 UTC] INFO: request completed
    reqId: "req-3v"
    res: {
      "statusCode": 204
    }
    responseTime: 0.23116600140929222
[12:30:38 UTC] INFO: incoming request
    reqId: "req-3w"
    req: {
      "method": "GET",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63814
    }
[12:30:38 UTC] INFO: Request started
    reqId: "req-3w"
    correlationId: "80a3ce78-908f-4799-b429-74337bcb1ef1"
    method: "GET"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "get:role-assignments"
[12:30:38 UTC] INFO: incoming request
    reqId: "req-3x"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63745
    }
[12:30:38 UTC] INFO: Request started
    reqId: "req-3x"
    correlationId: "dd153a60-a84f-4a9a-a52a-17fbaa81e6a3"
    method: "GET"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:30:38 UTC] INFO: incoming request
    reqId: "req-3y"
    req: {
      "method": "GET",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63815
    }
[12:30:38 UTC] INFO: Request started
    reqId: "req-3y"
    correlationId: "df1c0e45-4985-4ca1-bc6c-86372cff0a68"
    method: "GET"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "get:workflow-transitions"
[12:30:38 UTC] INFO: incoming request
    reqId: "req-3z"
    req: {
      "method": "GET",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63759
    }
[12:30:38 UTC] INFO: Request started
    reqId: "req-3z"
    correlationId: "2ee7a061-955d-453a-afdb-cdfb498e9e70"
    method: "GET"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "get:test-plans"
[12:30:38 UTC] INFO: incoming request
    reqId: "req-40"
    req: {
      "method": "GET",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63812
    }
[12:30:38 UTC] INFO: Request started
    reqId: "req-40"
    correlationId: "c26f8f59-d1d7-4d1f-b60a-4ae6e61109a0"
    method: "GET"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "get:statuses"
[12:30:38 UTC] INFO: incoming request
    reqId: "req-41"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63809
    }
[12:30:38 UTC] INFO: Request started
    reqId: "req-41"
    correlationId: "dd351f79-b70e-4c7a-bcb8-5cdaf215b9f8"
    method: "GET"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:30:38 UTC] INFO: findMany operation on workflow-transition
    reqId: "req-3y"
    correlationId: "df1c0e45-4985-4ca1-bc6c-86372cff0a68"
    operation: "workflow-transition:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:30:38 UTC] INFO: findMany operation on role-assignment
    reqId: "req-3w"
    correlationId: "80a3ce78-908f-4799-b429-74337bcb1ef1"
    operation: "role-assignment:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:30:38 UTC] INFO: findOne operation on test plan
    reqId: "req-3z"
    correlationId: "2ee7a061-955d-453a-afdb-cdfb498e9e70"
    operation: "test plan:findOne"
    id: "9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
[12:30:38 UTC] INFO: findMany operation on test suite
    reqId: "req-41"
    correlationId: "dd351f79-b70e-4c7a-bcb8-5cdaf215b9f8"
    operation: "test suite:findMany"
    page: 1
    limit: 100
    hasFilters: true
[12:30:38 UTC] INFO: findMany operation on status
    reqId: "req-40"
    correlationId: "c26f8f59-d1d7-4d1f-b60a-4ae6e61109a0"
    operation: "status:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:30:38 UTC] INFO: request completed
    reqId: "req-3z"
    res: {
      "statusCode": 200
    }
    responseTime: 21.156999997794628
[12:30:38 UTC] INFO: Returning 1 test cases for suite 70008509-7c87-485f-ba5e-a47543a15e21
    reqId: "req-3x"
[12:30:38 UTC] INFO: request completed
    reqId: "req-3x"
    res: {
      "statusCode": 200
    }
    responseTime: 31.112208001315594
[12:30:38 UTC] INFO: incoming request
    reqId: "req-42"
    req: {
      "method": "GET",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63759
    }
[12:30:38 UTC] INFO: Request started
    reqId: "req-42"
    correlationId: "38dc7020-43b3-4cba-a652-d1be9478b60f"
    method: "GET"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "get:priorities"
[12:30:38 UTC] INFO: incoming request
    reqId: "req-43"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63745
    }
[12:30:38 UTC] INFO: Request started
    reqId: "req-43"
    correlationId: "afb83591-be8a-4baa-bbf9-91e9c54d91fb"
    method: "GET"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:30:38 UTC] INFO: request completed
    reqId: "req-3y"
    res: {
      "statusCode": 200
    }
    responseTime: 33.28470800071955
[12:30:38 UTC] INFO: findMany operation on priority
    reqId: "req-42"
    correlationId: "38dc7020-43b3-4cba-a652-d1be9478b60f"
    operation: "priority:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:30:38 UTC] INFO: request completed
    reqId: "req-41"
    res: {
      "statusCode": 200
    }
    responseTime: 29.427207998931408
[12:30:38 UTC] INFO: request completed
    reqId: "req-40"
    res: {
      "statusCode": 200
    }
    responseTime: 32.21254199743271
[12:30:38 UTC] INFO: request completed
    reqId: "req-3w"
    res: {
      "statusCode": 200
    }
    responseTime: 48.597540996968746
[12:30:38 UTC] INFO: request completed
    reqId: "req-42"
    res: {
      "statusCode": 200
    }
    responseTime: 10.184832997620106
[12:30:38 UTC] INFO: request completed
    reqId: "req-43"
    res: {
      "statusCode": 200
    }
    responseTime: 9.374250002205372
[12:30:42 UTC] INFO: incoming request
    reqId: "req-44"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63745
    }
[12:30:42 UTC] INFO: Request started
    reqId: "req-44"
    correlationId: "1534aaa2-4d69-4202-8087-e9e5b1a4622b"
    method: "OPTIONS"
    url: "/api/v1/test-suites/"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:30:42 UTC] INFO: request completed
    reqId: "req-44"
    res: {
      "statusCode": 204
    }
    responseTime: 0.4723749980330467
[12:30:42 UTC] INFO: incoming request
    reqId: "req-45"
    req: {
      "method": "POST",
      "url": "/api/v1/test-suites/",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63745
    }
[12:30:42 UTC] INFO: Request started
    reqId: "req-45"
    correlationId: "6fe7f40e-3013-4828-9024-16f0803888cb"
    method: "POST"
    url: "/api/v1/test-suites/"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "post:test-suites"
[12:30:42 UTC] INFO: create operation on test suite
    reqId: "req-45"
    correlationId: "6fe7f40e-3013-4828-9024-16f0803888cb"
    operation: "test suite:create"
    hasData: true
[12:30:42 UTC] INFO: request completed
    reqId: "req-45"
    res: {
      "statusCode": 201
    }
    responseTime: 12.80604200065136
[12:30:42 UTC] INFO: incoming request
    reqId: "req-46"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63745
    }
[12:30:42 UTC] INFO: Request started
    reqId: "req-46"
    correlationId: "290245c5-2c79-42f2-846e-195a65d0e4e1"
    method: "GET"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:30:42 UTC] INFO: findMany operation on test suite
    reqId: "req-46"
    correlationId: "290245c5-2c79-42f2-846e-195a65d0e4e1"
    operation: "test suite:findMany"
    page: 1
    limit: 100
    hasFilters: true
[12:30:42 UTC] INFO: request completed
    reqId: "req-46"
    res: {
      "statusCode": 200
    }
    responseTime: 5.179790999740362
[12:30:45 UTC] INFO: incoming request
    reqId: "req-47"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63745
    }
[12:30:45 UTC] INFO: Request started
    reqId: "req-47"
    correlationId: "c44e6d73-257e-4bb8-9ab6-5d3fb7f75c85"
    method: "OPTIONS"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:30:45 UTC] INFO: request completed
    reqId: "req-47"
    res: {
      "statusCode": 204
    }
    responseTime: 0.37037499994039536
[12:30:45 UTC] INFO: incoming request
    reqId: "req-48"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 63745
    }
[12:30:45 UTC] INFO: Request started
    reqId: "req-48"
    correlationId: "b91121a3-e8b6-4980-817f-cb57b800e7f9"
    method: "GET"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:30:45 UTC] INFO: findMany operation on test suite
    reqId: "req-48"
    correlationId: "b91121a3-e8b6-4980-817f-cb57b800e7f9"
    operation: "test suite:findMany"
    page: 1
    limit: 100
    hasFilters: true
[12:30:45 UTC] INFO: request completed
    reqId: "req-48"
    res: {
      "statusCode": 200
    }
    responseTime: 5.5825000032782555
[12:32:04 UTC] INFO: incoming request
    reqId: "req-49"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 64274
    }
[12:32:04 UTC] INFO: Request started
    reqId: "req-49"
    correlationId: "efdae59f-c833-40a3-b146-87ce4abb14fa"
    method: "OPTIONS"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "options:role-assignments"
[12:32:04 UTC] INFO: request completed
    reqId: "req-49"
    res: {
      "statusCode": 204
    }
    responseTime: 0.649957999587059
[12:32:04 UTC] INFO: incoming request
    reqId: "req-4a"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 64277
    }
[12:32:04 UTC] INFO: Request started
    reqId: "req-4a"
    correlationId: "a3632413-ad7c-4a29-b61f-41d834513a8c"
    method: "OPTIONS"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "options:workflow-transitions"
[12:32:04 UTC] INFO: request completed
    reqId: "req-4a"
    res: {
      "statusCode": 204
    }
    responseTime: 0.19554100185632706
[12:32:04 UTC] INFO: incoming request
    reqId: "req-4b"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 64279
    }
[12:32:04 UTC] INFO: Request started
    reqId: "req-4b"
    correlationId: "d3feb4db-435c-4987-9fb7-e50e07a9a02a"
    method: "OPTIONS"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "options:test-plans"
[12:32:04 UTC] INFO: request completed
    reqId: "req-4b"
    res: {
      "statusCode": 204
    }
    responseTime: 0.5674589984118938
[12:32:04 UTC] INFO: incoming request
    reqId: "req-4c"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 64281
    }
[12:32:04 UTC] INFO: Request started
    reqId: "req-4c"
    correlationId: "f64df7c5-0e8b-450e-85c7-4da23a541399"
    method: "OPTIONS"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:32:04 UTC] INFO: request completed
    reqId: "req-4c"
    res: {
      "statusCode": 204
    }
    responseTime: 0.4246249981224537
[12:32:04 UTC] INFO: incoming request
    reqId: "req-4d"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 64274
    }
[12:32:04 UTC] INFO: Request started
    reqId: "req-4d"
    correlationId: "df8f212b-e688-4987-9ca2-603135ee476b"
    method: "OPTIONS"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "options:statuses"
[12:32:04 UTC] INFO: request completed
    reqId: "req-4d"
    res: {
      "statusCode": 204
    }
    responseTime: 0.32341599836945534
[12:32:04 UTC] INFO: incoming request
    reqId: "req-4e"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 64283
    }
[12:32:04 UTC] INFO: Request started
    reqId: "req-4e"
    correlationId: "4d817c11-e6b0-46b8-accb-d0b908fc6458"
    method: "OPTIONS"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "options:priorities"
[12:32:04 UTC] INFO: request completed
    reqId: "req-4e"
    res: {
      "statusCode": 204
    }
    responseTime: 0.41587499901652336
[12:32:04 UTC] INFO: incoming request
    reqId: "req-4f"
    req: {
      "method": "GET",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 64281
    }
[12:32:04 UTC] INFO: Request started
    reqId: "req-4f"
    correlationId: "e5ac51af-0995-4c03-bcd6-d2a1c22e0513"
    method: "GET"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "get:workflow-transitions"
[12:32:04 UTC] INFO: incoming request
    reqId: "req-4g"
    req: {
      "method": "GET",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 64274
    }
[12:32:04 UTC] INFO: Request started
    reqId: "req-4g"
    correlationId: "b86985ef-cf0c-4d88-a819-271495f408f9"
    method: "GET"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "get:test-plans"
[12:32:04 UTC] INFO: incoming request
    reqId: "req-4h"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 64277
    }
[12:32:04 UTC] INFO: Request started
    reqId: "req-4h"
    correlationId: "aef3d4f0-6d4a-4027-9175-9302b1d87b4c"
    method: "OPTIONS"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:32:04 UTC] INFO: request completed
    reqId: "req-4h"
    res: {
      "statusCode": 204
    }
    responseTime: 0.49845800176262856
[12:32:04 UTC] INFO: incoming request
    reqId: "req-4i"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 64284
    }
[12:32:04 UTC] INFO: Request started
    reqId: "req-4i"
    correlationId: "6caa662a-682c-4657-8653-9873dd536a61"
    method: "OPTIONS"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:32:04 UTC] INFO: request completed
    reqId: "req-4i"
    res: {
      "statusCode": 204
    }
    responseTime: 0.5845419988036156
[12:32:04 UTC] INFO: incoming request
    reqId: "req-4j"
    req: {
      "method": "GET",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 64279
    }
[12:32:04 UTC] INFO: Request started
    reqId: "req-4j"
    correlationId: "a61c5dac-74f4-4d25-9d28-148c4a630549"
    method: "GET"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "get:role-assignments"
[12:32:04 UTC] INFO: incoming request
    reqId: "req-4k"
    req: {
      "method": "GET",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 64277
    }
[12:32:04 UTC] INFO: Request started
    reqId: "req-4k"
    correlationId: "873420e9-55cd-4954-9b81-6b58e9a2821d"
    method: "GET"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "get:statuses"
[12:32:04 UTC] INFO: incoming request
    reqId: "req-4l"
    req: {
      "method": "GET",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 64284
    }
[12:32:04 UTC] INFO: Request started
    reqId: "req-4l"
    correlationId: "a4c25bf7-273c-491a-a0e8-0c85801da900"
    method: "GET"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "get:priorities"
[12:32:04 UTC] INFO: incoming request
    reqId: "req-4m"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 64283
    }
[12:32:04 UTC] INFO: Request started
    reqId: "req-4m"
    correlationId: "6a01a123-ce3b-49f0-9b4e-e03506fc3b37"
    method: "GET"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:32:04 UTC] INFO: findOne operation on test plan
    reqId: "req-4g"
    correlationId: "b86985ef-cf0c-4d88-a819-271495f408f9"
    operation: "test plan:findOne"
    id: "9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
[12:32:04 UTC] INFO: findMany operation on role-assignment
    reqId: "req-4j"
    correlationId: "a61c5dac-74f4-4d25-9d28-148c4a630549"
    operation: "role-assignment:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:32:04 UTC] INFO: findMany operation on priority
    reqId: "req-4l"
    correlationId: "a4c25bf7-273c-491a-a0e8-0c85801da900"
    operation: "priority:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:32:04 UTC] INFO: findMany operation on test suite
    reqId: "req-4m"
    correlationId: "6a01a123-ce3b-49f0-9b4e-e03506fc3b37"
    operation: "test suite:findMany"
    page: 1
    limit: 100
    hasFilters: true
[12:32:04 UTC] INFO: findMany operation on workflow-transition
    reqId: "req-4f"
    correlationId: "e5ac51af-0995-4c03-bcd6-d2a1c22e0513"
    operation: "workflow-transition:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:32:04 UTC] INFO: findMany operation on status
    reqId: "req-4k"
    correlationId: "873420e9-55cd-4954-9b81-6b58e9a2821d"
    operation: "status:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:32:04 UTC] INFO: request completed
    reqId: "req-4g"
    res: {
      "statusCode": 200
    }
    responseTime: 59.2553330026567
[12:32:04 UTC] INFO: incoming request
    reqId: "req-4n"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 64274
    }
[12:32:04 UTC] INFO: Request started
    reqId: "req-4n"
    correlationId: "ba3a0dd5-9c48-4906-9b92-df5a28475201"
    method: "GET"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:32:04 UTC] INFO: request completed
    reqId: "req-4l"
    res: {
      "statusCode": 200
    }
    responseTime: 53.172083001583815
[12:32:04 UTC] INFO: request completed
    reqId: "req-4f"
    res: {
      "statusCode": 200
    }
    responseTime: 69.90687499940395
[12:32:04 UTC] INFO: request completed
    reqId: "req-4k"
    res: {
      "statusCode": 200
    }
    responseTime: 56.86183400079608
[12:32:04 UTC] INFO: incoming request
    reqId: "req-4o"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 64284
    }
[12:32:04 UTC] INFO: Request started
    reqId: "req-4o"
    correlationId: "5e00bfb9-156e-4583-b7bd-752655be02ef"
    method: "GET"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:32:04 UTC] INFO: request completed
    reqId: "req-4j"
    res: {
      "statusCode": 200
    }
    responseTime: 70.85737499967217
[12:32:04 UTC] INFO: request completed
    reqId: "req-4m"
    res: {
      "statusCode": 200
    }
    responseTime: 60.464625000953674
[12:32:04 UTC] INFO: Returning 1 test cases for suite 70008509-7c87-485f-ba5e-a47543a15e21
    reqId: "req-4n"
[12:32:04 UTC] INFO: request completed
    reqId: "req-4n"
    res: {
      "statusCode": 200
    }
    responseTime: 16.942749999463558
[12:32:04 UTC] INFO: request completed
    reqId: "req-4o"
    res: {
      "statusCode": 200
    }
    responseTime: 7.905582997947931
Restarting 'src/server.ts'
Creating app...
[12:32:23 UTC] INFO: Starting Fastify application
    environment: "development"
    host: "0.0.0.0"
    port: 3001
    logLevel: "info"
[12:32:23 UTC] INFO: Database connection established and attached to Fastify
[12:32:23 UTC] INFO: MinIO bucket exists: spark
[12:32:23 UTC] INFO: MinIO client initialized successfully
[12:32:23 UTC] INFO: Registering application routes...
App created successfully
Attempting to listen on 0.0.0.0:3001...
Server is running at http://0.0.0.0:3001
Documentation available at http://0.0.0.0:3001/documentation
Server is ready and listening
[12:32:24 UTC] INFO: Routes registered successfully
[12:32:24 UTC] INFO: Registered routes:
[12:32:24 UTC] INFO: Server listening at http://127.0.0.1:3001
[12:32:24 UTC] INFO: Server listening at http://***********:3001
Restarting 'src/server.ts'
Creating app...
[12:32:29 UTC] INFO: Starting Fastify application
    environment: "development"
    host: "0.0.0.0"
    port: 3001
    logLevel: "info"
[12:32:29 UTC] INFO: Database connection established and attached to Fastify
[12:32:29 UTC] INFO: MinIO bucket exists: spark
[12:32:29 UTC] INFO: MinIO client initialized successfully
[12:32:29 UTC] INFO: Registering application routes...
App created successfully
Attempting to listen on 0.0.0.0:3001...
Server is running at http://0.0.0.0:3001
Documentation available at http://0.0.0.0:3001/documentation
Server is ready and listening
[12:32:29 UTC] INFO: Routes registered successfully
[12:32:29 UTC] INFO: Registered routes:
[12:32:29 UTC] INFO: Server listening at http://127.0.0.1:3001
[12:32:29 UTC] INFO: Server listening at http://***********:3001
Restarting 'src/server.ts'
Creating app...
[12:32:39 UTC] INFO: Starting Fastify application
    environment: "development"
    host: "0.0.0.0"
    port: 3001
    logLevel: "info"
[12:32:39 UTC] INFO: Database connection established and attached to Fastify
[12:32:39 UTC] INFO: MinIO bucket exists: spark
[12:32:39 UTC] INFO: MinIO client initialized successfully
[12:32:39 UTC] INFO: Registering application routes...
App created successfully
Attempting to listen on 0.0.0.0:3001...
Server is running at http://0.0.0.0:3001
Documentation available at http://0.0.0.0:3001/documentation
Server is ready and listening
[12:32:39 UTC] INFO: Routes registered successfully
[12:32:39 UTC] INFO: Registered routes:
[12:32:39 UTC] INFO: Server listening at http://127.0.0.1:3001
[12:32:39 UTC] INFO: Server listening at http://***********:3001
Restarting 'src/server.ts'
Creating app...
[12:32:46 UTC] INFO: Starting Fastify application
    environment: "development"
    host: "0.0.0.0"
    port: 3001
    logLevel: "info"
[12:32:46 UTC] INFO: Database connection established and attached to Fastify
[12:32:46 UTC] INFO: MinIO bucket exists: spark
[12:32:46 UTC] INFO: MinIO client initialized successfully
[12:32:47 UTC] INFO: Registering application routes...
App created successfully
Attempting to listen on 0.0.0.0:3001...
Server is running at http://0.0.0.0:3001
Documentation available at http://0.0.0.0:3001/documentation
Server is ready and listening
[12:32:47 UTC] INFO: Routes registered successfully
[12:32:47 UTC] INFO: Registered routes:
[12:32:47 UTC] INFO: Server listening at http://127.0.0.1:3001
[12:32:47 UTC] INFO: Server listening at http://***********:3001
[12:38:01 UTC] INFO: incoming request
    reqId: "req-1"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:01 UTC] INFO: Request started
    reqId: "req-1"
    correlationId: "28188be2-4f70-4f03-aed6-f08ada263c3b"
    method: "OPTIONS"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "options:role-assignments"
[12:38:01 UTC] INFO: request completed
    reqId: "req-1"
    res: {
      "statusCode": 204
    }
    responseTime: 5.595333997160196
[12:38:01 UTC] INFO: incoming request
    reqId: "req-2"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:01 UTC] INFO: Request started
    reqId: "req-2"
    correlationId: "89b57c43-8c20-4ba4-ab02-d7bf182ed21b"
    method: "OPTIONS"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:01 UTC] INFO: request completed
    reqId: "req-2"
    res: {
      "statusCode": 204
    }
    responseTime: 0.43312500044703484
[12:38:01 UTC] INFO: incoming request
    reqId: "req-3"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49445
    }
[12:38:01 UTC] INFO: Request started
    reqId: "req-3"
    correlationId: "45ac291c-5a5d-4c4d-b470-d9bec224f3a4"
    method: "OPTIONS"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "options:workflow-transitions"
[12:38:01 UTC] INFO: request completed
    reqId: "req-3"
    res: {
      "statusCode": 204
    }
    responseTime: 0.26912499964237213
[12:38:01 UTC] INFO: incoming request
    reqId: "req-4"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:01 UTC] INFO: Request started
    reqId: "req-4"
    correlationId: "39f18909-1240-4b91-9c2c-3449af9f1b81"
    method: "OPTIONS"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:01 UTC] INFO: request completed
    reqId: "req-4"
    res: {
      "statusCode": 204
    }
    responseTime: 0.3132910020649433
[12:38:01 UTC] INFO: incoming request
    reqId: "req-5"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49447
    }
[12:38:01 UTC] INFO: Request started
    reqId: "req-5"
    correlationId: "01fa8267-7aeb-4a6a-81b0-5ea42ac5f37f"
    method: "OPTIONS"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "options:test-plans"
[12:38:01 UTC] INFO: request completed
    reqId: "req-5"
    res: {
      "statusCode": 204
    }
    responseTime: 0.3330000005662441
[12:38:01 UTC] INFO: incoming request
    reqId: "req-6"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:01 UTC] INFO: Request started
    reqId: "req-6"
    correlationId: "29e5602b-6cbf-49ea-82fa-c3ff47d4c9ff"
    method: "GET"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:01 UTC] INFO: incoming request
    reqId: "req-7"
    req: {
      "method": "GET",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49447
    }
[12:38:01 UTC] INFO: Request started
    reqId: "req-7"
    correlationId: "827c2172-b22e-477c-989a-ea8d2214aaf9"
    method: "GET"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "get:workflow-transitions"
[12:38:01 UTC] INFO: incoming request
    reqId: "req-8"
    req: {
      "method": "GET",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49445
    }
[12:38:01 UTC] INFO: Request started
    reqId: "req-8"
    correlationId: "9ccc6520-a4c4-40e7-ab1f-dfc4e7756733"
    method: "GET"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "get:role-assignments"
[12:38:01 UTC] INFO: incoming request
    reqId: "req-9"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49449
    }
[12:38:01 UTC] INFO: Request started
    reqId: "req-9"
    correlationId: "e5f7db11-e0b7-4be3-b5ef-cf4050638594"
    method: "OPTIONS"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:01 UTC] INFO: request completed
    reqId: "req-9"
    res: {
      "statusCode": 204
    }
    responseTime: 0.3752500005066395
[12:38:01 UTC] INFO: incoming request
    reqId: "req-a"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49449
    }
[12:38:01 UTC] INFO: Request started
    reqId: "req-a"
    correlationId: "edbbcd16-b493-4807-ad0a-11bd1d43024a"
    method: "GET"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:01 UTC] INFO: incoming request
    reqId: "req-b"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:01 UTC] INFO: Request started
    reqId: "req-b"
    correlationId: "526f0b1b-5214-42d3-a4f6-4500a0ddf1c9"
    method: "OPTIONS"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "options:statuses"
[12:38:01 UTC] INFO: request completed
    reqId: "req-b"
    res: {
      "statusCode": 204
    }
    responseTime: 0.5888749994337559
[12:38:01 UTC] INFO: incoming request
    reqId: "req-c"
    req: {
      "method": "GET",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:01 UTC] INFO: Request started
    reqId: "req-c"
    correlationId: "fa67104b-a5ae-4d0c-a90f-858bb329ae6c"
    method: "GET"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "get:test-plans"
[12:38:01 UTC] INFO: incoming request
    reqId: "req-d"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49452
    }
[12:38:01 UTC] INFO: Request started
    reqId: "req-d"
    correlationId: "888deeb9-648a-4ab7-a29d-bffd981526b8"
    method: "OPTIONS"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "options:priorities"
[12:38:01 UTC] INFO: request completed
    reqId: "req-d"
    res: {
      "statusCode": 204
    }
    responseTime: 0.19424999877810478
[12:38:01 UTC] INFO: incoming request
    reqId: "req-e"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49452
    }
[12:38:01 UTC] INFO: Request started
    reqId: "req-e"
    correlationId: "6a7fa3af-da02-45f6-a5fe-317fcaeb952b"
    method: "GET"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:01 UTC] INFO: findMany operation on role-assignment
    reqId: "req-8"
    correlationId: "9ccc6520-a4c4-40e7-ab1f-dfc4e7756733"
    operation: "role-assignment:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:38:01 UTC] INFO: findMany operation on test suite
    reqId: "req-e"
    correlationId: "6a7fa3af-da02-45f6-a5fe-317fcaeb952b"
    operation: "test suite:findMany"
    page: 1
    limit: 100
    hasFilters: true
[12:38:01 UTC] INFO: findMany operation on workflow-transition
    reqId: "req-7"
    correlationId: "827c2172-b22e-477c-989a-ea8d2214aaf9"
    operation: "workflow-transition:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:38:01 UTC] INFO: findOne operation on test plan
    reqId: "req-c"
    correlationId: "fa67104b-a5ae-4d0c-a90f-858bb329ae6c"
    operation: "test plan:findOne"
    id: "9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
[12:38:01 UTC] INFO: request completed
    reqId: "req-c"
    res: {
      "statusCode": 200
    }
    responseTime: 38.488749999552965
[12:38:01 UTC] INFO: incoming request
    reqId: "req-f"
    req: {
      "method": "GET",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:01 UTC] INFO: Request started
    reqId: "req-f"
    correlationId: "63127b33-4b40-4bdd-8b5b-70da6543ad30"
    method: "GET"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "get:statuses"
[12:38:01 UTC] INFO: request completed
    reqId: "req-e"
    res: {
      "statusCode": 200
    }
    responseTime: 38.28216600045562
[12:38:01 UTC] INFO: request completed
    reqId: "req-a"
    res: {
      "statusCode": 200
    }
    responseTime: 47.26254199817777
[12:38:01 UTC] INFO: Returning 1 test cases for suite 70008509-7c87-485f-ba5e-a47543a15e21
    reqId: "req-6"
[12:38:01 UTC] INFO: request completed
    reqId: "req-6"
    res: {
      "statusCode": 200
    }
    responseTime: 95.24204200133681
[12:38:01 UTC] INFO: incoming request
    reqId: "req-g"
    req: {
      "method": "GET",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49452
    }
[12:38:01 UTC] INFO: Request started
    reqId: "req-g"
    correlationId: "7f8f130c-580b-474e-b341-c3dcdacfd0d0"
    method: "GET"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "get:priorities"
[12:38:01 UTC] INFO: findMany operation on status
    reqId: "req-f"
    correlationId: "63127b33-4b40-4bdd-8b5b-70da6543ad30"
    operation: "status:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:38:01 UTC] INFO: request completed
    reqId: "req-7"
    res: {
      "statusCode": 200
    }
    responseTime: 69.97699999809265
[12:38:01 UTC] INFO: findMany operation on priority
    reqId: "req-g"
    correlationId: "7f8f130c-580b-474e-b341-c3dcdacfd0d0"
    operation: "priority:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:38:01 UTC] INFO: request completed
    reqId: "req-8"
    res: {
      "statusCode": 200
    }
    responseTime: 66.86491699889302
[12:38:01 UTC] INFO: request completed
    reqId: "req-f"
    res: {
      "statusCode": 200
    }
    responseTime: 16.959542002528906
[12:38:01 UTC] INFO: request completed
    reqId: "req-g"
    res: {
      "statusCode": 200
    }
    responseTime: 14.01570900157094
[12:38:10 UTC] INFO: incoming request
    reqId: "req-h"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49452
    }
[12:38:10 UTC] INFO: Request started
    reqId: "req-h"
    correlationId: "32863371-586b-4607-bd61-09d42bbf2bfa"
    method: "OPTIONS"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "options:role-assignments"
[12:38:10 UTC] INFO: request completed
    reqId: "req-h"
    res: {
      "statusCode": 204
    }
    responseTime: 2.6650000028312206
[12:38:10 UTC] INFO: incoming request
    reqId: "req-i"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:10 UTC] INFO: Request started
    reqId: "req-i"
    correlationId: "5691a650-1edc-46bd-9fae-400c5f53c6d7"
    method: "OPTIONS"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "options:workflow-transitions"
[12:38:10 UTC] INFO: request completed
    reqId: "req-i"
    res: {
      "statusCode": 204
    }
    responseTime: 1.4797079972922802
[12:38:10 UTC] INFO: incoming request
    reqId: "req-j"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49445
    }
[12:38:10 UTC] INFO: Request started
    reqId: "req-j"
    correlationId: "eda2b47d-e1ba-4100-906a-e10fd62e59a5"
    method: "OPTIONS"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "options:test-plans"
[12:38:10 UTC] INFO: request completed
    reqId: "req-j"
    res: {
      "statusCode": 204
    }
    responseTime: 0.492917001247406
[12:38:10 UTC] INFO: incoming request
    reqId: "req-k"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49447
    }
[12:38:10 UTC] INFO: Request started
    reqId: "req-k"
    correlationId: "9965a697-6f6f-447a-af38-f3c84e861ba0"
    method: "OPTIONS"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:10 UTC] INFO: request completed
    reqId: "req-k"
    res: {
      "statusCode": 204
    }
    responseTime: 0.5775410011410713
[12:38:10 UTC] INFO: incoming request
    reqId: "req-l"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:10 UTC] INFO: Request started
    reqId: "req-l"
    correlationId: "af61f3b8-1ce5-4377-8591-6d4c73e7d95d"
    method: "OPTIONS"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "options:statuses"
[12:38:10 UTC] INFO: request completed
    reqId: "req-l"
    res: {
      "statusCode": 204
    }
    responseTime: 0.4659169986844063
[12:38:10 UTC] INFO: incoming request
    reqId: "req-m"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49449
    }
[12:38:10 UTC] INFO: Request started
    reqId: "req-m"
    correlationId: "9b6d5b34-08eb-44d9-b0dd-919eb8ac5081"
    method: "OPTIONS"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "options:priorities"
[12:38:10 UTC] INFO: request completed
    reqId: "req-m"
    res: {
      "statusCode": 204
    }
    responseTime: 0.5783329978585243
[12:38:10 UTC] INFO: incoming request
    reqId: "req-n"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:10 UTC] INFO: Request started
    reqId: "req-n"
    correlationId: "d78f73f5-29a0-47cc-b28d-89940cab142a"
    method: "OPTIONS"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:10 UTC] INFO: request completed
    reqId: "req-n"
    res: {
      "statusCode": 204
    }
    responseTime: 0.7742920033633709
[12:38:10 UTC] INFO: incoming request
    reqId: "req-o"
    req: {
      "method": "GET",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49445
    }
[12:38:10 UTC] INFO: Request started
    reqId: "req-o"
    correlationId: "a82783a2-182b-4395-b14e-701bc2bcf956"
    method: "GET"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "get:role-assignments"
[12:38:10 UTC] INFO: incoming request
    reqId: "req-p"
    req: {
      "method": "GET",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49447
    }
[12:38:10 UTC] INFO: Request started
    reqId: "req-p"
    correlationId: "5d1b0f19-2fd4-49f6-be4a-4ace78c4772f"
    method: "GET"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "get:workflow-transitions"
[12:38:10 UTC] INFO: incoming request
    reqId: "req-q"
    req: {
      "method": "GET",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:10 UTC] INFO: Request started
    reqId: "req-q"
    correlationId: "3ae032c6-ecc9-4f3b-81b6-cbf9b5bd6dc9"
    method: "GET"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "get:test-plans"
[12:38:10 UTC] INFO: incoming request
    reqId: "req-r"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49452
    }
[12:38:10 UTC] INFO: Request started
    reqId: "req-r"
    correlationId: "859ed31d-2b0b-45c5-8b6a-80d0ed1d6385"
    method: "OPTIONS"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:10 UTC] INFO: request completed
    reqId: "req-r"
    res: {
      "statusCode": 204
    }
    responseTime: 0.23891600221395493
[12:38:10 UTC] INFO: incoming request
    reqId: "req-s"
    req: {
      "method": "GET",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:10 UTC] INFO: Request started
    reqId: "req-s"
    correlationId: "61d645ba-48e5-4d08-9ca2-13167415e91c"
    method: "GET"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "get:statuses"
[12:38:10 UTC] INFO: incoming request
    reqId: "req-t"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49449
    }
[12:38:10 UTC] INFO: Request started
    reqId: "req-t"
    correlationId: "531be9b1-52e5-4780-96ad-d21894ff9fa6"
    method: "GET"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:10 UTC] INFO: incoming request
    reqId: "req-u"
    req: {
      "method": "GET",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49452
    }
[12:38:10 UTC] INFO: Request started
    reqId: "req-u"
    correlationId: "e1805774-3ea1-49a0-96b6-7587be9f09bf"
    method: "GET"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "get:priorities"
[12:38:10 UTC] INFO: findOne operation on test plan
    reqId: "req-q"
    correlationId: "3ae032c6-ecc9-4f3b-81b6-cbf9b5bd6dc9"
    operation: "test plan:findOne"
    id: "9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
[12:38:10 UTC] INFO: findMany operation on workflow-transition
    reqId: "req-p"
    correlationId: "5d1b0f19-2fd4-49f6-be4a-4ace78c4772f"
    operation: "workflow-transition:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:38:10 UTC] INFO: findMany operation on test suite
    reqId: "req-t"
    correlationId: "531be9b1-52e5-4780-96ad-d21894ff9fa6"
    operation: "test suite:findMany"
    page: 1
    limit: 100
    hasFilters: true
[12:38:10 UTC] INFO: findMany operation on status
    reqId: "req-s"
    correlationId: "61d645ba-48e5-4d08-9ca2-13167415e91c"
    operation: "status:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:38:10 UTC] INFO: findMany operation on role-assignment
    reqId: "req-o"
    correlationId: "a82783a2-182b-4395-b14e-701bc2bcf956"
    operation: "role-assignment:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:38:10 UTC] INFO: findMany operation on priority
    reqId: "req-u"
    correlationId: "e1805774-3ea1-49a0-96b6-7587be9f09bf"
    operation: "priority:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:38:10 UTC] INFO: request completed
    reqId: "req-p"
    res: {
      "statusCode": 200
    }
    responseTime: 52.14212499931455
[12:38:10 UTC] INFO: request completed
    reqId: "req-s"
    res: {
      "statusCode": 200
    }
    responseTime: 48.97208300232887
[12:38:10 UTC] INFO: incoming request
    reqId: "req-v"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49447
    }
[12:38:10 UTC] INFO: Request started
    reqId: "req-v"
    correlationId: "2feaa644-c6cd-47c2-981c-8fc3f62decbb"
    method: "GET"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:10 UTC] INFO: incoming request
    reqId: "req-w"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:10 UTC] INFO: Request started
    reqId: "req-w"
    correlationId: "863494a9-9a6e-43b2-ab5f-86e88669f0dd"
    method: "GET"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:10 UTC] INFO: request completed
    reqId: "req-t"
    res: {
      "statusCode": 200
    }
    responseTime: 50.964541003108025
[12:38:10 UTC] INFO: request completed
    reqId: "req-u"
    res: {
      "statusCode": 200
    }
    responseTime: 52.229416001588106
[12:38:10 UTC] INFO: request completed
    reqId: "req-q"
    res: {
      "statusCode": 200
    }
    responseTime: 64.32799999788404
[12:38:10 UTC] INFO: request completed
    reqId: "req-o"
    res: {
      "statusCode": 200
    }
    responseTime: 75.20516600087285
[12:38:10 UTC] INFO: Returning 1 test cases for suite 70008509-7c87-485f-ba5e-a47543a15e21
    reqId: "req-w"
[12:38:10 UTC] INFO: request completed
    reqId: "req-w"
    res: {
      "statusCode": 200
    }
    responseTime: 12.454207997769117
[12:38:10 UTC] INFO: request completed
    reqId: "req-v"
    res: {
      "statusCode": 200
    }
    responseTime: 14.824499998241663
[12:38:14 UTC] INFO: incoming request
    reqId: "req-x"
    req: {
      "method": "GET",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49447
    }
[12:38:14 UTC] INFO: Request started
    reqId: "req-x"
    correlationId: "90460e91-a12f-40c5-8f66-76be9e2a7198"
    method: "GET"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "get:role-assignments"
[12:38:14 UTC] INFO: incoming request
    reqId: "req-y"
    req: {
      "method": "GET",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:14 UTC] INFO: Request started
    reqId: "req-y"
    correlationId: "8a7ed5b2-bfeb-4189-aec1-6ed92e778b72"
    method: "GET"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "get:workflow-transitions"
[12:38:14 UTC] INFO: incoming request
    reqId: "req-z"
    req: {
      "method": "GET",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49445
    }
[12:38:14 UTC] INFO: Request started
    reqId: "req-z"
    correlationId: "78b9f5d9-bb63-442d-96ca-4761e7942924"
    method: "GET"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "get:test-plans"
[12:38:14 UTC] INFO: findMany operation on role-assignment
    reqId: "req-x"
    correlationId: "90460e91-a12f-40c5-8f66-76be9e2a7198"
    operation: "role-assignment:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:38:14 UTC] INFO: findMany operation on workflow-transition
    reqId: "req-y"
    correlationId: "8a7ed5b2-bfeb-4189-aec1-6ed92e778b72"
    operation: "workflow-transition:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:38:14 UTC] INFO: findOne operation on test plan
    reqId: "req-z"
    correlationId: "78b9f5d9-bb63-442d-96ca-4761e7942924"
    operation: "test plan:findOne"
    id: "9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
[12:38:14 UTC] INFO: incoming request
    reqId: "req-10"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/user-fcm-tokens/",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:14 UTC] INFO: Request started
    reqId: "req-10"
    correlationId: "ac68469e-ea97-4c16-b0da-e9d44ff1adb9"
    method: "OPTIONS"
    url: "/api/v1/user-fcm-tokens/"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "user-fcm-tokens"
    operation: "options:user-fcm-tokens"
[12:38:14 UTC] INFO: request completed
    reqId: "req-10"
    res: {
      "statusCode": 204
    }
    responseTime: 0.18170900270342827
[12:38:14 UTC] INFO: incoming request
    reqId: "req-11"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/user-fcm-tokens/",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49452
    }
[12:38:14 UTC] INFO: Request started
    reqId: "req-11"
    correlationId: "706e1a8c-f6ba-4963-aa3f-5a9d883799c2"
    method: "OPTIONS"
    url: "/api/v1/user-fcm-tokens/"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "user-fcm-tokens"
    operation: "options:user-fcm-tokens"
[12:38:14 UTC] INFO: request completed
    reqId: "req-11"
    res: {
      "statusCode": 204
    }
    responseTime: 0.0915829986333847
[12:38:14 UTC] INFO: incoming request
    reqId: "req-12"
    req: {
      "method": "POST",
      "url": "/api/v1/user-fcm-tokens/",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49452
    }
[12:38:14 UTC] INFO: Request started
    reqId: "req-12"
    correlationId: "73f2e8a7-cec4-4de3-836e-87f5a05df1dc"
    method: "POST"
    url: "/api/v1/user-fcm-tokens/"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "user-fcm-tokens"
    operation: "post:user-fcm-tokens"
[12:38:14 UTC] INFO: request completed
    reqId: "req-z"
    res: {
      "statusCode": 200
    }
    responseTime: 10.853417001664639
[12:38:14 UTC] INFO: incoming request
    reqId: "req-13"
    req: {
      "method": "POST",
      "url": "/api/v1/user-fcm-tokens/",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:14 UTC] INFO: Request started
    reqId: "req-13"
    correlationId: "6e22677b-6b54-4c2b-8b71-89fdec3d402f"
    method: "POST"
    url: "/api/v1/user-fcm-tokens/"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "user-fcm-tokens"
    operation: "post:user-fcm-tokens"
[12:38:14 UTC] INFO: request completed
    reqId: "req-y"
    res: {
      "statusCode": 200
    }
    responseTime: 19.731125000864267
[12:38:14 UTC] INFO: request completed
    reqId: "req-x"
    res: {
      "statusCode": 200
    }
    responseTime: 24.48699999973178
[12:38:14 UTC] INFO: incoming request
    reqId: "req-14"
    req: {
      "method": "GET",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49447
    }
[12:38:14 UTC] INFO: Request started
    reqId: "req-14"
    correlationId: "018b9b00-e53a-46c1-ad83-d7bbdd19f065"
    method: "GET"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "get:test-plans"
[12:38:14 UTC] INFO: incoming request
    reqId: "req-15"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:14 UTC] INFO: Request started
    reqId: "req-15"
    correlationId: "fe06b2e6-7278-4d8e-b952-1d3520f232a7"
    method: "GET"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:14 UTC] INFO: findOne operation on test plan
    reqId: "req-14"
    correlationId: "018b9b00-e53a-46c1-ad83-d7bbdd19f065"
    operation: "test plan:findOne"
    id: "9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
[12:38:14 UTC] INFO: findMany operation on test suite
    reqId: "req-15"
    correlationId: "fe06b2e6-7278-4d8e-b952-1d3520f232a7"
    operation: "test suite:findMany"
    page: 1
    limit: 100
    hasFilters: true
[12:38:14 UTC] INFO: request completed
    reqId: "req-12"
    res: {
      "statusCode": 200
    }
    responseTime: 20.301750000566244
[12:38:14 UTC] INFO: request completed
    reqId: "req-13"
    res: {
      "statusCode": 200
    }
    responseTime: 18.637874998152256
[12:38:14 UTC] INFO: request completed
    reqId: "req-14"
    res: {
      "statusCode": 200
    }
    responseTime: 9.382666997611523
[12:38:14 UTC] INFO: request completed
    reqId: "req-15"
    res: {
      "statusCode": 200
    }
    responseTime: 7.102416999638081
[12:38:16 UTC] INFO: incoming request
    reqId: "req-16"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:16 UTC] INFO: Request started
    reqId: "req-16"
    correlationId: "762c9ab1-7a3f-43d0-bb0e-a2ec2eb0a084"
    method: "OPTIONS"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:16 UTC] INFO: request completed
    reqId: "req-16"
    res: {
      "statusCode": 204
    }
    responseTime: 0.42125000059604645
[12:38:16 UTC] INFO: incoming request
    reqId: "req-17"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49447
    }
[12:38:16 UTC] INFO: Request started
    reqId: "req-17"
    correlationId: "49842522-09d8-425b-9e15-8127f7c11591"
    method: "OPTIONS"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:16 UTC] INFO: request completed
    reqId: "req-17"
    res: {
      "statusCode": 204
    }
    responseTime: 0.16691699996590614
[12:38:16 UTC] INFO: incoming request
    reqId: "req-18"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:16 UTC] INFO: Request started
    reqId: "req-18"
    correlationId: "51fa4711-50b4-461a-81c3-aa729dcb9af3"
    method: "OPTIONS"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "options:statuses"
[12:38:16 UTC] INFO: request completed
    reqId: "req-18"
    res: {
      "statusCode": 204
    }
    responseTime: 0.19779099896550179
[12:38:16 UTC] INFO: incoming request
    reqId: "req-19"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49452
    }
[12:38:16 UTC] INFO: Request started
    reqId: "req-19"
    correlationId: "1988099a-4109-4ec5-b20e-eea2738591b2"
    method: "OPTIONS"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "options:priorities"
[12:38:16 UTC] INFO: request completed
    reqId: "req-19"
    res: {
      "statusCode": 204
    }
    responseTime: 0.558874998241663
[12:38:16 UTC] INFO: incoming request
    reqId: "req-1a"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:16 UTC] INFO: Request started
    reqId: "req-1a"
    correlationId: "e79c4526-07f4-4ea4-9a29-be8e62c01f62"
    method: "GET"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:16 UTC] INFO: incoming request
    reqId: "req-1b"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:16 UTC] INFO: Request started
    reqId: "req-1b"
    correlationId: "f4ef3d84-6053-4fb6-b30d-3084b062ac3f"
    method: "GET"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:16 UTC] INFO: incoming request
    reqId: "req-1c"
    req: {
      "method": "GET",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49452
    }
[12:38:16 UTC] INFO: Request started
    reqId: "req-1c"
    correlationId: "a2411475-a2f2-4bc0-a973-6ad844029061"
    method: "GET"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "get:statuses"
[12:38:16 UTC] INFO: incoming request
    reqId: "req-1d"
    req: {
      "method": "GET",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49447
    }
[12:38:16 UTC] INFO: Request started
    reqId: "req-1d"
    correlationId: "9c79e307-10ee-4205-b97e-8ff589836e59"
    method: "GET"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "get:priorities"
[12:38:16 UTC] INFO: findMany operation on priority
    reqId: "req-1d"
    correlationId: "9c79e307-10ee-4205-b97e-8ff589836e59"
    operation: "priority:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:38:16 UTC] INFO: findMany operation on status
    reqId: "req-1c"
    correlationId: "a2411475-a2f2-4bc0-a973-6ad844029061"
    operation: "status:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:38:16 UTC] INFO: request completed
    reqId: "req-1d"
    res: {
      "statusCode": 200
    }
    responseTime: 21.617667000740767
[12:38:16 UTC] INFO: request completed
    reqId: "req-1c"
    res: {
      "statusCode": 200
    }
    responseTime: 23.33058400079608
[12:38:16 UTC] INFO: request completed
    reqId: "req-1a"
    res: {
      "statusCode": 200
    }
    responseTime: 29.363834001123905
[12:38:16 UTC] INFO: Returning 1 test cases for suite 370e1ac0-834b-40ba-86c6-8e5ae0b46100
    reqId: "req-1b"
[12:38:16 UTC] INFO: request completed
    reqId: "req-1b"
    res: {
      "statusCode": 200
    }
    responseTime: 27.375917002558708
[12:38:17 UTC] INFO: incoming request
    reqId: "req-1e"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:17 UTC] INFO: Request started
    reqId: "req-1e"
    correlationId: "f59bd3c4-2619-4b11-97c6-1335c6b3bd3c"
    method: "OPTIONS"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:17 UTC] INFO: request completed
    reqId: "req-1e"
    res: {
      "statusCode": 204
    }
    responseTime: 0.5206249989569187
[12:38:17 UTC] INFO: incoming request
    reqId: "req-1f"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:17 UTC] INFO: Request started
    reqId: "req-1f"
    correlationId: "2d5e9203-14d2-4469-817b-afa5d14da32b"
    method: "OPTIONS"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:17 UTC] INFO: request completed
    reqId: "req-1f"
    res: {
      "statusCode": 204
    }
    responseTime: 0.19770799949765205
[12:38:17 UTC] INFO: incoming request
    reqId: "req-1g"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:17 UTC] INFO: Request started
    reqId: "req-1g"
    correlationId: "14f43997-c49b-4bcd-b4dc-673d5811a36b"
    method: "GET"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:17 UTC] INFO: incoming request
    reqId: "req-1h"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:17 UTC] INFO: Request started
    reqId: "req-1h"
    correlationId: "f0d70b30-d674-4d91-8c3f-c42ac7813fea"
    method: "GET"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:17 UTC] INFO: request completed
    reqId: "req-1g"
    res: {
      "statusCode": 200
    }
    responseTime: 14.542957998812199
[12:38:17 UTC] INFO: Returning 1 test cases for suite 70008509-7c87-485f-ba5e-a47543a15e21
    reqId: "req-1h"
[12:38:17 UTC] INFO: request completed
    reqId: "req-1h"
    res: {
      "statusCode": 200
    }
    responseTime: 14.773124996572733
[12:38:17 UTC] INFO: incoming request
    reqId: "req-1i"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:17 UTC] INFO: Request started
    reqId: "req-1i"
    correlationId: "0bee7ae9-11d9-4423-8c24-ba24196d2732"
    method: "OPTIONS"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:17 UTC] INFO: request completed
    reqId: "req-1i"
    res: {
      "statusCode": 204
    }
    responseTime: 0.39499999955296516
[12:38:17 UTC] INFO: incoming request
    reqId: "req-1j"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:17 UTC] INFO: Request started
    reqId: "req-1j"
    correlationId: "a1b9317b-322b-43cd-b33c-16960faafec5"
    method: "OPTIONS"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:17 UTC] INFO: request completed
    reqId: "req-1j"
    res: {
      "statusCode": 204
    }
    responseTime: 0.1779160015285015
[12:38:17 UTC] INFO: incoming request
    reqId: "req-1k"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:17 UTC] INFO: Request started
    reqId: "req-1k"
    correlationId: "abcb5dfd-adf7-41b0-96f2-5e4b5c64fb66"
    method: "GET"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:17 UTC] INFO: incoming request
    reqId: "req-1l"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:17 UTC] INFO: Request started
    reqId: "req-1l"
    correlationId: "75bbfdb4-ea53-49b6-941f-7c15ee463d24"
    method: "GET"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:17 UTC] INFO: request completed
    reqId: "req-1k"
    res: {
      "statusCode": 200
    }
    responseTime: 13.54212499782443
[12:38:17 UTC] INFO: Returning 0 test cases for suite 2a3222ab-4ff0-4950-af5a-73d28c3ad81a
    reqId: "req-1l"
[12:38:17 UTC] INFO: request completed
    reqId: "req-1l"
    res: {
      "statusCode": 200
    }
    responseTime: 12.762875001877546
[12:38:20 UTC] INFO: incoming request
    reqId: "req-1m"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:20 UTC] INFO: Request started
    reqId: "req-1m"
    correlationId: "99f3f6d9-9dc8-4d06-b8ef-3ac29140be12"
    method: "OPTIONS"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:20 UTC] INFO: request completed
    reqId: "req-1m"
    res: {
      "statusCode": 204
    }
    responseTime: 0.3702090010046959
[12:38:20 UTC] INFO: incoming request
    reqId: "req-1n"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:20 UTC] INFO: Request started
    reqId: "req-1n"
    correlationId: "b25005e6-5856-42a2-a525-7d6172a6b37f"
    method: "GET"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:20 UTC] INFO: findMany operation on test suite
    reqId: "req-1n"
    correlationId: "b25005e6-5856-42a2-a525-7d6172a6b37f"
    operation: "test suite:findMany"
    page: 1
    limit: 100
    hasFilters: true
[12:38:20 UTC] INFO: request completed
    reqId: "req-1n"
    res: {
      "statusCode": 200
    }
    responseTime: 8.278000000864267
[12:38:23 UTC] INFO: incoming request
    reqId: "req-1o"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:23 UTC] INFO: Request started
    reqId: "req-1o"
    correlationId: "a83f9ea6-4809-442f-87fc-707cabec9615"
    method: "OPTIONS"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:23 UTC] INFO: request completed
    reqId: "req-1o"
    res: {
      "statusCode": 204
    }
    responseTime: 0.7417500019073486
[12:38:23 UTC] INFO: incoming request
    reqId: "req-1p"
    req: {
      "method": "PATCH",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:23 UTC] INFO: Request started
    reqId: "req-1p"
    correlationId: "9e571aed-3086-4e1c-b763-8669b10b1209"
    method: "PATCH"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "patch:test-suites"
[12:38:23 UTC] INFO: update operation on test suite
    reqId: "req-1p"
    correlationId: "9e571aed-3086-4e1c-b763-8669b10b1209"
    operation: "test suite:update"
    id: "2a3222ab-4ff0-4950-af5a-73d28c3ad81a"
    hasData: true
[12:38:23 UTC] INFO: request completed
    reqId: "req-1p"
    res: {
      "statusCode": 200
    }
    responseTime: 15.670875001698732
[12:38:25 UTC] INFO: incoming request
    reqId: "req-1q"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:25 UTC] INFO: Request started
    reqId: "req-1q"
    correlationId: "5b39c3e5-d8a1-4878-aa8f-1fb4efca99f5"
    method: "OPTIONS"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:25 UTC] INFO: request completed
    reqId: "req-1q"
    res: {
      "statusCode": 204
    }
    responseTime: 0.5974170006811619
[12:38:25 UTC] INFO: incoming request
    reqId: "req-1r"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:25 UTC] INFO: Request started
    reqId: "req-1r"
    correlationId: "c8f990f4-88b8-4059-99d0-93acb5402903"
    method: "OPTIONS"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:25 UTC] INFO: request completed
    reqId: "req-1r"
    res: {
      "statusCode": 204
    }
    responseTime: 0.16220800206065178
[12:38:25 UTC] INFO: incoming request
    reqId: "req-1s"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:25 UTC] INFO: Request started
    reqId: "req-1s"
    correlationId: "f5359b5f-d59b-40be-b869-f7a915c9751d"
    method: "GET"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:25 UTC] INFO: incoming request
    reqId: "req-1t"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:25 UTC] INFO: Request started
    reqId: "req-1t"
    correlationId: "42c8eea6-40cb-4c86-ac55-a86f6efb32fc"
    method: "GET"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:25 UTC] INFO: request completed
    reqId: "req-1s"
    res: {
      "statusCode": 200
    }
    responseTime: 14.820749998092651
[12:38:25 UTC] INFO: Returning 1 test cases for suite 70008509-7c87-485f-ba5e-a47543a15e21
    reqId: "req-1t"
[12:38:25 UTC] INFO: request completed
    reqId: "req-1t"
    res: {
      "statusCode": 200
    }
    responseTime: 13.238000001758337
[12:38:26 UTC] INFO: incoming request
    reqId: "req-1u"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:26 UTC] INFO: Request started
    reqId: "req-1u"
    correlationId: "812c84fc-0312-4330-9aa5-d6dd9c9a7938"
    method: "OPTIONS"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:26 UTC] INFO: request completed
    reqId: "req-1u"
    res: {
      "statusCode": 204
    }
    responseTime: 0.505791999399662
[12:38:26 UTC] INFO: incoming request
    reqId: "req-1v"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:26 UTC] INFO: Request started
    reqId: "req-1v"
    correlationId: "96f999c1-1cb0-40a4-a8e5-09a09c362a28"
    method: "OPTIONS"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:26 UTC] INFO: request completed
    reqId: "req-1v"
    res: {
      "statusCode": 204
    }
    responseTime: 0.15633299946784973
[12:38:26 UTC] INFO: incoming request
    reqId: "req-1w"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:26 UTC] INFO: Request started
    reqId: "req-1w"
    correlationId: "cb76f5d1-f867-4a5f-ab14-299cf6c9f9d1"
    method: "GET"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:26 UTC] INFO: incoming request
    reqId: "req-1x"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:26 UTC] INFO: Request started
    reqId: "req-1x"
    correlationId: "2d48f611-2f1f-4bcc-a1f7-e5a8f337f6ba"
    method: "GET"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:26 UTC] INFO: Returning 1 test cases for suite 2a3222ab-4ff0-4950-af5a-73d28c3ad81a
    reqId: "req-1x"
[12:38:26 UTC] INFO: request completed
    reqId: "req-1x"
    res: {
      "statusCode": 200
    }
    responseTime: 11.940082997083664
[12:38:26 UTC] INFO: request completed
    reqId: "req-1w"
    res: {
      "statusCode": 200
    }
    responseTime: 16.491542000323534
[12:38:45 UTC] INFO: incoming request
    reqId: "req-1y"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/priorities/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:45 UTC] INFO: Request started
    reqId: "req-1y"
    correlationId: "08ab2fdb-a1b1-4c98-8c71-26ad86cb5eae"
    method: "OPTIONS"
    url: "/api/v1/priorities/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "options:priorities"
[12:38:45 UTC] INFO: request completed
    reqId: "req-1y"
    res: {
      "statusCode": 204
    }
    responseTime: 0.4091250002384186
[12:38:45 UTC] INFO: incoming request
    reqId: "req-1z"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/statuses/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:45 UTC] INFO: Request started
    reqId: "req-1z"
    correlationId: "6bff6318-c2ee-42d6-917b-3cd4f4918ac9"
    method: "OPTIONS"
    url: "/api/v1/statuses/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "options:statuses"
[12:38:45 UTC] INFO: request completed
    reqId: "req-1z"
    res: {
      "statusCode": 204
    }
    responseTime: 0.14879199862480164
[12:38:45 UTC] INFO: incoming request
    reqId: "req-20"
    req: {
      "method": "GET",
      "url": "/api/v1/priorities/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:45 UTC] INFO: Request started
    reqId: "req-20"
    correlationId: "da209043-4871-4c5e-94a5-982fb5c791de"
    method: "GET"
    url: "/api/v1/priorities/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "get:priorities"
[12:38:45 UTC] INFO: incoming request
    reqId: "req-21"
    req: {
      "method": "GET",
      "url": "/api/v1/statuses/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:45 UTC] INFO: Request started
    reqId: "req-21"
    correlationId: "17200203-5a1f-4a77-a50d-bcee83603349"
    method: "GET"
    url: "/api/v1/statuses/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "get:statuses"
[12:38:45 UTC] INFO: findMany operation on priority
    reqId: "req-20"
    correlationId: "da209043-4871-4c5e-94a5-982fb5c791de"
    operation: "priority:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:38:45 UTC] INFO: findMany operation on status
    reqId: "req-21"
    correlationId: "17200203-5a1f-4a77-a50d-bcee83603349"
    operation: "status:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:38:45 UTC] INFO: request completed
    reqId: "req-20"
    res: {
      "statusCode": 200
    }
    responseTime: 32.38341600075364
[12:38:45 UTC] INFO: request completed
    reqId: "req-21"
    res: {
      "statusCode": 200
    }
    responseTime: 39.32508299872279
[12:38:52 UTC] INFO: incoming request
    reqId: "req-22"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-cases/de2c90a6-2e86-4c5f-80e2-e7bf5ffd3d7e",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:52 UTC] INFO: Request started
    reqId: "req-22"
    correlationId: "ed48d433-7fbd-4374-a13b-f13b826a0071"
    method: "OPTIONS"
    url: "/api/v1/test-cases/de2c90a6-2e86-4c5f-80e2-e7bf5ffd3d7e"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-cases"
    operation: "options:test-cases"
[12:38:52 UTC] INFO: request completed
    reqId: "req-22"
    res: {
      "statusCode": 204
    }
    responseTime: 0.3715829998254776
[12:38:52 UTC] INFO: incoming request
    reqId: "req-23"
    req: {
      "method": "PATCH",
      "url": "/api/v1/test-cases/de2c90a6-2e86-4c5f-80e2-e7bf5ffd3d7e",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:52 UTC] INFO: Request started
    reqId: "req-23"
    correlationId: "0399d2be-87c6-4e05-9a7a-39d1abe88e9c"
    method: "PATCH"
    url: "/api/v1/test-cases/de2c90a6-2e86-4c5f-80e2-e7bf5ffd3d7e"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-cases"
    operation: "patch:test-cases"
[12:38:52 UTC] INFO: update operation on test case
    reqId: "req-23"
    correlationId: "0399d2be-87c6-4e05-9a7a-39d1abe88e9c"
    operation: "test case:update"
    id: "de2c90a6-2e86-4c5f-80e2-e7bf5ffd3d7e"
    hasData: true
[12:38:52 UTC] INFO: request completed
    reqId: "req-23"
    res: {
      "statusCode": 200
    }
    responseTime: 16.70616700127721
[12:38:52 UTC] INFO: incoming request
    reqId: "req-24"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:52 UTC] INFO: Request started
    reqId: "req-24"
    correlationId: "673f598f-a099-4fe4-9bf8-979b730ec8e3"
    method: "OPTIONS"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:52 UTC] INFO: request completed
    reqId: "req-24"
    res: {
      "statusCode": 204
    }
    responseTime: 0.2884580008685589
[12:38:52 UTC] INFO: incoming request
    reqId: "req-25"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:52 UTC] INFO: Request started
    reqId: "req-25"
    correlationId: "3bb783e2-ca88-4710-8a2b-fcdbab2a884d"
    method: "GET"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:52 UTC] INFO: Returning 1 test cases for suite 2a3222ab-4ff0-4950-af5a-73d28c3ad81a
    reqId: "req-25"
[12:38:52 UTC] INFO: request completed
    reqId: "req-25"
    res: {
      "statusCode": 200
    }
    responseTime: 11.490792002528906
[12:38:56 UTC] INFO: incoming request
    reqId: "req-26"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:56 UTC] INFO: Request started
    reqId: "req-26"
    correlationId: "b2f0def4-16d7-4309-b3b3-806c7103fb4b"
    method: "OPTIONS"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:56 UTC] INFO: request completed
    reqId: "req-26"
    res: {
      "statusCode": 204
    }
    responseTime: 0.41187500208616257
[12:38:56 UTC] INFO: incoming request
    reqId: "req-27"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:56 UTC] INFO: Request started
    reqId: "req-27"
    correlationId: "703d9c96-412e-44b1-9bc5-521545a78b63"
    method: "OPTIONS"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:56 UTC] INFO: request completed
    reqId: "req-27"
    res: {
      "statusCode": 204
    }
    responseTime: 0.25920800119638443
[12:38:56 UTC] INFO: incoming request
    reqId: "req-28"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:56 UTC] INFO: Request started
    reqId: "req-28"
    correlationId: "6b26ec90-8851-4832-bf66-0f37d04e6277"
    method: "GET"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:56 UTC] INFO: incoming request
    reqId: "req-29"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:56 UTC] INFO: Request started
    reqId: "req-29"
    correlationId: "8ad9fe9f-5e61-4245-9e65-7b7ca0023a75"
    method: "GET"
    url: "/api/v1/test-suites/70008509-7c87-485f-ba5e-a47543a15e21/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:56 UTC] INFO: request completed
    reqId: "req-28"
    res: {
      "statusCode": 200
    }
    responseTime: 13.792750000953674
[12:38:56 UTC] INFO: Returning 1 test cases for suite 70008509-7c87-485f-ba5e-a47543a15e21
    reqId: "req-29"
[12:38:56 UTC] INFO: request completed
    reqId: "req-29"
    res: {
      "statusCode": 200
    }
    responseTime: 13.126582998782396
[12:38:57 UTC] INFO: incoming request
    reqId: "req-2a"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:57 UTC] INFO: Request started
    reqId: "req-2a"
    correlationId: "ffabdf31-f9bc-425f-ad46-986e199e92cf"
    method: "OPTIONS"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:57 UTC] INFO: request completed
    reqId: "req-2a"
    res: {
      "statusCode": 204
    }
    responseTime: 0.4185840003192425
[12:38:57 UTC] INFO: incoming request
    reqId: "req-2b"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:57 UTC] INFO: Request started
    reqId: "req-2b"
    correlationId: "6a3a164f-82e2-4b2c-8528-78f0529212ac"
    method: "OPTIONS"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:57 UTC] INFO: request completed
    reqId: "req-2b"
    res: {
      "statusCode": 204
    }
    responseTime: 0.1488329991698265
[12:38:57 UTC] INFO: incoming request
    reqId: "req-2c"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:57 UTC] INFO: Request started
    reqId: "req-2c"
    correlationId: "393e1408-d180-4eee-b7c6-376487449593"
    method: "GET"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:57 UTC] INFO: incoming request
    reqId: "req-2d"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:57 UTC] INFO: Request started
    reqId: "req-2d"
    correlationId: "83d53764-8d22-42ea-b1ef-121ad8a21c06"
    method: "GET"
    url: "/api/v1/test-suites/370e1ac0-834b-40ba-86c6-8e5ae0b46100/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:57 UTC] INFO: Returning 1 test cases for suite 370e1ac0-834b-40ba-86c6-8e5ae0b46100
    reqId: "req-2d"
[12:38:57 UTC] INFO: request completed
    reqId: "req-2d"
    res: {
      "statusCode": 200
    }
    responseTime: 11.121374998241663
[12:38:57 UTC] INFO: request completed
    reqId: "req-2c"
    res: {
      "statusCode": 200
    }
    responseTime: 15.091791998595
[12:38:58 UTC] INFO: incoming request
    reqId: "req-2e"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:58 UTC] INFO: Request started
    reqId: "req-2e"
    correlationId: "e69c2017-35c4-4de7-abf6-4538587986ae"
    method: "OPTIONS"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:58 UTC] INFO: request completed
    reqId: "req-2e"
    res: {
      "statusCode": 204
    }
    responseTime: 0.4091250002384186
[12:38:58 UTC] INFO: incoming request
    reqId: "req-2f"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:58 UTC] INFO: Request started
    reqId: "req-2f"
    correlationId: "b88672d6-2721-44f0-8921-a6c0d9031fa4"
    method: "OPTIONS"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:38:58 UTC] INFO: request completed
    reqId: "req-2f"
    res: {
      "statusCode": 204
    }
    responseTime: 0.17029200121760368
[12:38:58 UTC] INFO: incoming request
    reqId: "req-2g"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:38:58 UTC] INFO: Request started
    reqId: "req-2g"
    correlationId: "9cccadd7-0d7e-4bff-a77f-7d8e7f913778"
    method: "GET"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:58 UTC] INFO: incoming request
    reqId: "req-2h"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:38:58 UTC] INFO: Request started
    reqId: "req-2h"
    correlationId: "cc84d346-bcfe-4e36-a8bb-46cf9e894049"
    method: "GET"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:38:58 UTC] INFO: request completed
    reqId: "req-2g"
    res: {
      "statusCode": 200
    }
    responseTime: 15.097791999578476
[12:38:58 UTC] INFO: Returning 1 test cases for suite 2a3222ab-4ff0-4950-af5a-73d28c3ad81a
    reqId: "req-2h"
[12:38:58 UTC] INFO: request completed
    reqId: "req-2h"
    res: {
      "statusCode": 200
    }
    responseTime: 15.564584001898766
[12:39:01 UTC] INFO: incoming request
    reqId: "req-2i"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/priorities/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:39:01 UTC] INFO: Request started
    reqId: "req-2i"
    correlationId: "b4d8768c-58d5-4057-ba75-3f23e874f7b1"
    method: "OPTIONS"
    url: "/api/v1/priorities/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "options:priorities"
[12:39:01 UTC] INFO: request completed
    reqId: "req-2i"
    res: {
      "statusCode": 204
    }
    responseTime: 0.36112499982118607
[12:39:01 UTC] INFO: incoming request
    reqId: "req-2j"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/statuses/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:39:01 UTC] INFO: Request started
    reqId: "req-2j"
    correlationId: "f4c8ffc8-566b-44ce-ae3b-b89dd47f6815"
    method: "OPTIONS"
    url: "/api/v1/statuses/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "options:statuses"
[12:39:01 UTC] INFO: request completed
    reqId: "req-2j"
    res: {
      "statusCode": 204
    }
    responseTime: 0.13620800152420998
[12:39:01 UTC] INFO: incoming request
    reqId: "req-2k"
    req: {
      "method": "GET",
      "url": "/api/v1/priorities/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:39:01 UTC] INFO: Request started
    reqId: "req-2k"
    correlationId: "692211d2-db90-4e4d-a3ba-d11d114c32f4"
    method: "GET"
    url: "/api/v1/priorities/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "get:priorities"
[12:39:01 UTC] INFO: incoming request
    reqId: "req-2l"
    req: {
      "method": "GET",
      "url": "/api/v1/statuses/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:39:01 UTC] INFO: Request started
    reqId: "req-2l"
    correlationId: "79560e27-0be9-4c5f-9c00-e7b722afe548"
    method: "GET"
    url: "/api/v1/statuses/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "get:statuses"
[12:39:01 UTC] INFO: findMany operation on priority
    reqId: "req-2k"
    correlationId: "692211d2-db90-4e4d-a3ba-d11d114c32f4"
    operation: "priority:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:39:01 UTC] INFO: findMany operation on status
    reqId: "req-2l"
    correlationId: "79560e27-0be9-4c5f-9c00-e7b722afe548"
    operation: "status:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:39:01 UTC] INFO: request completed
    reqId: "req-2k"
    res: {
      "statusCode": 200
    }
    responseTime: 7.789041001349688
[12:39:01 UTC] INFO: request completed
    reqId: "req-2l"
    res: {
      "statusCode": 200
    }
    responseTime: 7.231125000864267
[12:39:05 UTC] INFO: incoming request
    reqId: "req-2m"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:39:05 UTC] INFO: Request started
    reqId: "req-2m"
    correlationId: "c229b9e1-dde2-4f09-bbc1-aad1ca99206c"
    method: "OPTIONS"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:39:05 UTC] INFO: request completed
    reqId: "req-2m"
    res: {
      "statusCode": 204
    }
    responseTime: 0.5228339992463589
[12:39:05 UTC] INFO: incoming request
    reqId: "req-2n"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:39:05 UTC] INFO: Request started
    reqId: "req-2n"
    correlationId: "e71b893b-5f9c-4eda-8758-24d6ddf06254"
    method: "GET"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:39:05 UTC] INFO: Returning 1 test cases for suite 2a3222ab-4ff0-4950-af5a-73d28c3ad81a
    reqId: "req-2n"
[12:39:05 UTC] INFO: request completed
    reqId: "req-2n"
    res: {
      "statusCode": 200
    }
    responseTime: 12.704874999821186
[12:39:50 UTC] INFO: incoming request
    reqId: "req-2o"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:39:50 UTC] INFO: Request started
    reqId: "req-2o"
    correlationId: "bb5badb5-dad0-454b-9cbe-2f4913e86a7b"
    method: "OPTIONS"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "options:role-assignments"
[12:39:50 UTC] INFO: request completed
    reqId: "req-2o"
    res: {
      "statusCode": 204
    }
    responseTime: 2.410624999552965
[12:39:50 UTC] INFO: incoming request
    reqId: "req-2p"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:39:50 UTC] INFO: Request started
    reqId: "req-2p"
    correlationId: "b4c835a5-04a2-49b5-989e-802eb11c02d5"
    method: "OPTIONS"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:39:50 UTC] INFO: request completed
    reqId: "req-2p"
    res: {
      "statusCode": 204
    }
    responseTime: 0.6845420002937317
[12:39:50 UTC] INFO: incoming request
    reqId: "req-2q"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:39:50 UTC] INFO: Request started
    reqId: "req-2q"
    correlationId: "6b9a259b-0808-4b9a-a5e3-76ee1d9c9793"
    method: "OPTIONS"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "options:workflow-transitions"
[12:39:50 UTC] INFO: request completed
    reqId: "req-2q"
    res: {
      "statusCode": 204
    }
    responseTime: 0.20462500303983688
[12:39:50 UTC] INFO: incoming request
    reqId: "req-2r"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:39:50 UTC] INFO: Request started
    reqId: "req-2r"
    correlationId: "9684556a-5747-4b6f-bb9c-4507ab6ab917"
    method: "OPTIONS"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:39:50 UTC] INFO: request completed
    reqId: "req-2r"
    res: {
      "statusCode": 204
    }
    responseTime: 0.3987499997019768
[12:39:50 UTC] INFO: incoming request
    reqId: "req-2s"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49938
    }
[12:39:50 UTC] INFO: Request started
    reqId: "req-2s"
    correlationId: "3d90edbb-99e1-4f24-bb8a-749d69db5b5a"
    method: "OPTIONS"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "options:test-plans"
[12:39:50 UTC] INFO: request completed
    reqId: "req-2s"
    res: {
      "statusCode": 204
    }
    responseTime: 0.8242090009152889
[12:39:50 UTC] INFO: incoming request
    reqId: "req-2t"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:39:50 UTC] INFO: Request started
    reqId: "req-2t"
    correlationId: "4cc9a70c-e9c3-40e8-ba00-0a1f0ee17bc3"
    method: "GET"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:39:50 UTC] INFO: incoming request
    reqId: "req-2u"
    req: {
      "method": "GET",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49451
    }
[12:39:50 UTC] INFO: Request started
    reqId: "req-2u"
    correlationId: "7664013c-666b-4854-9cf0-db58622787ba"
    method: "GET"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "get:role-assignments"
[12:39:50 UTC] INFO: incoming request
    reqId: "req-2v"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49941
    }
[12:39:50 UTC] INFO: Request started
    reqId: "req-2v"
    correlationId: "76774007-c6ea-47a6-93f7-a417f9bb9714"
    method: "OPTIONS"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:39:50 UTC] INFO: request completed
    reqId: "req-2v"
    res: {
      "statusCode": 204
    }
    responseTime: 1.1007919982075691
[12:39:50 UTC] INFO: incoming request
    reqId: "req-2w"
    req: {
      "method": "GET",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49938
    }
[12:39:50 UTC] INFO: Request started
    reqId: "req-2w"
    correlationId: "2895e525-6b08-4dca-a604-4dc8a8d0ff0a"
    method: "GET"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "get:workflow-transitions"
[12:39:50 UTC] INFO: incoming request
    reqId: "req-2x"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49943
    }
[12:39:50 UTC] INFO: Request started
    reqId: "req-2x"
    correlationId: "c44d1b7d-3167-4b98-9f09-8f3b7a0d1b1f"
    method: "OPTIONS"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "options:statuses"
[12:39:50 UTC] INFO: request completed
    reqId: "req-2x"
    res: {
      "statusCode": 204
    }
    responseTime: 6.051665998995304
[12:39:50 UTC] INFO: incoming request
    reqId: "req-2y"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49941
    }
[12:39:50 UTC] INFO: Request started
    reqId: "req-2y"
    correlationId: "8b12d4fd-5b33-4df1-ab5d-95dc68471027"
    method: "GET"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:39:50 UTC] INFO: incoming request
    reqId: "req-2z"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49944
    }
[12:39:50 UTC] INFO: Request started
    reqId: "req-2z"
    correlationId: "5e9348e1-e40b-41ca-b6f7-71c5c729915b"
    method: "OPTIONS"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "options:priorities"
[12:39:50 UTC] INFO: request completed
    reqId: "req-2z"
    res: {
      "statusCode": 204
    }
    responseTime: 0.17499999701976776
[12:39:50 UTC] INFO: incoming request
    reqId: "req-30"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49944
    }
[12:39:50 UTC] INFO: Request started
    reqId: "req-30"
    correlationId: "12fce93b-6a81-46c3-853a-9fa0de262881"
    method: "GET"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:39:50 UTC] INFO: incoming request
    reqId: "req-31"
    req: {
      "method": "GET",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49943
    }
[12:39:50 UTC] INFO: Request started
    reqId: "req-31"
    correlationId: "9f6b1a31-a705-44b1-bb3b-bd96c0cb8af2"
    method: "GET"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "get:test-plans"
[12:39:50 UTC] INFO: findMany operation on role-assignment
    reqId: "req-2u"
    correlationId: "7664013c-666b-4854-9cf0-db58622787ba"
    operation: "role-assignment:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:39:50 UTC] INFO: findMany operation on workflow-transition
    reqId: "req-2w"
    correlationId: "2895e525-6b08-4dca-a604-4dc8a8d0ff0a"
    operation: "workflow-transition:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:39:50 UTC] INFO: findMany operation on test suite
    reqId: "req-30"
    correlationId: "12fce93b-6a81-46c3-853a-9fa0de262881"
    operation: "test suite:findMany"
    page: 1
    limit: 100
    hasFilters: true
[12:39:50 UTC] INFO: findOne operation on test plan
    reqId: "req-31"
    correlationId: "9f6b1a31-a705-44b1-bb3b-bd96c0cb8af2"
    operation: "test plan:findOne"
    id: "9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
[12:39:50 UTC] INFO: Returning 1 test cases for suite 2a3222ab-4ff0-4950-af5a-73d28c3ad81a
    reqId: "req-2t"
[12:39:50 UTC] INFO: request completed
    reqId: "req-2t"
    res: {
      "statusCode": 200
    }
    responseTime: 61.03495899960399
[12:39:50 UTC] INFO: incoming request
    reqId: "req-32"
    req: {
      "method": "GET",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49443
    }
[12:39:50 UTC] INFO: Request started
    reqId: "req-32"
    correlationId: "509e13d0-9592-4927-bb0c-134a535a4923"
    method: "GET"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "get:statuses"
[12:39:50 UTC] INFO: request completed
    reqId: "req-31"
    res: {
      "statusCode": 200
    }
    responseTime: 16.455625001341105
[12:39:50 UTC] INFO: incoming request
    reqId: "req-33"
    req: {
      "method": "GET",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 49943
    }
[12:39:50 UTC] INFO: Request started
    reqId: "req-33"
    correlationId: "444effd5-c1ee-4534-b8bf-a07f2c5038dc"
    method: "GET"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "get:priorities"
[12:39:50 UTC] INFO: request completed
    reqId: "req-2y"
    res: {
      "statusCode": 200
    }
    responseTime: 27.094291001558304
[12:39:50 UTC] INFO: findMany operation on status
    reqId: "req-32"
    correlationId: "509e13d0-9592-4927-bb0c-134a535a4923"
    operation: "status:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:39:50 UTC] INFO: request completed
    reqId: "req-2w"
    res: {
      "statusCode": 200
    }
    responseTime: 40.48587499931455
[12:39:50 UTC] INFO: request completed
    reqId: "req-30"
    res: {
      "statusCode": 200
    }
    responseTime: 26.196042001247406
[12:39:50 UTC] INFO: findMany operation on priority
    reqId: "req-33"
    correlationId: "444effd5-c1ee-4534-b8bf-a07f2c5038dc"
    operation: "priority:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:39:50 UTC] INFO: request completed
    reqId: "req-33"
    res: {
      "statusCode": 200
    }
    responseTime: 12.827083002775908
[12:39:50 UTC] INFO: request completed
    reqId: "req-32"
    res: {
      "statusCode": 200
    }
    responseTime: 58.9414170011878
[12:39:50 UTC] INFO: request completed
    reqId: "req-2u"
    res: {
      "statusCode": 200
    }
    responseTime: 118.84849999845028
[12:41:44 UTC] INFO: incoming request
    reqId: "req-34"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 50583
    }
[12:41:44 UTC] INFO: Request started
    reqId: "req-34"
    correlationId: "f17049c4-84c5-4b98-b5dd-f6e55214daa1"
    method: "OPTIONS"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "options:role-assignments"
[12:41:44 UTC] INFO: request completed
    reqId: "req-34"
    res: {
      "statusCode": 204
    }
    responseTime: 1.8157080002129078
[12:41:44 UTC] INFO: incoming request
    reqId: "req-35"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 50585
    }
[12:41:44 UTC] INFO: Request started
    reqId: "req-35"
    correlationId: "95b46619-2102-47b4-ae2d-790606b9acbd"
    method: "OPTIONS"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "options:workflow-transitions"
[12:41:44 UTC] INFO: request completed
    reqId: "req-35"
    res: {
      "statusCode": 204
    }
    responseTime: 0.14054200053215027
[12:41:44 UTC] INFO: incoming request
    reqId: "req-36"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 50583
    }
[12:41:44 UTC] INFO: Request started
    reqId: "req-36"
    correlationId: "30e29329-8658-45b2-bc36-1d96e2a74077"
    method: "OPTIONS"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:41:44 UTC] INFO: request completed
    reqId: "req-36"
    res: {
      "statusCode": 204
    }
    responseTime: 0.4222079999744892
[12:41:44 UTC] INFO: incoming request
    reqId: "req-37"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 50587
    }
[12:41:44 UTC] INFO: Request started
    reqId: "req-37"
    correlationId: "9d229ff7-f60a-434f-ad1a-57579ba97366"
    method: "OPTIONS"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "options:test-plans"
[12:41:44 UTC] INFO: request completed
    reqId: "req-37"
    res: {
      "statusCode": 204
    }
    responseTime: 2.135292001068592
[12:41:44 UTC] INFO: incoming request
    reqId: "req-38"
    req: {
      "method": "GET",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 50583
    }
[12:41:44 UTC] INFO: Request started
    reqId: "req-38"
    correlationId: "ce09be45-6899-48b3-9208-4dc5bca0c116"
    method: "GET"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "get:role-assignments"
[12:41:44 UTC] INFO: incoming request
    reqId: "req-39"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 50585
    }
[12:41:44 UTC] INFO: Request started
    reqId: "req-39"
    correlationId: "e072c732-1aac-4686-95d0-36efcc9a0112"
    method: "OPTIONS"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:41:44 UTC] INFO: request completed
    reqId: "req-39"
    res: {
      "statusCode": 204
    }
    responseTime: 0.16975000128149986
[12:41:44 UTC] INFO: incoming request
    reqId: "req-3a"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 50589
    }
[12:41:44 UTC] INFO: Request started
    reqId: "req-3a"
    correlationId: "9a0453e4-6d8f-4af9-a37e-5b60b0c400c1"
    method: "OPTIONS"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:41:44 UTC] INFO: request completed
    reqId: "req-3a"
    res: {
      "statusCode": 204
    }
    responseTime: 0.1915000006556511
[12:41:44 UTC] INFO: incoming request
    reqId: "req-3b"
    req: {
      "method": "GET",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 50587
    }
[12:41:44 UTC] INFO: Request started
    reqId: "req-3b"
    correlationId: "6bbe379c-08fa-4101-8215-9b13c8763043"
    method: "GET"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "get:workflow-transitions"
[12:41:44 UTC] INFO: incoming request
    reqId: "req-3c"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 50591
    }
[12:41:44 UTC] INFO: Request started
    reqId: "req-3c"
    correlationId: "b4f29048-9473-41eb-8695-446a94bf92c9"
    method: "OPTIONS"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "options:statuses"
[12:41:44 UTC] INFO: request completed
    reqId: "req-3c"
    res: {
      "statusCode": 204
    }
    responseTime: 0.1514579989016056
[12:41:44 UTC] INFO: incoming request
    reqId: "req-3d"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 50585
    }
[12:41:44 UTC] INFO: Request started
    reqId: "req-3d"
    correlationId: "cd30ffc1-8ff6-4f65-a869-b4a413b0d9ba"
    method: "GET"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:41:44 UTC] INFO: incoming request
    reqId: "req-3e"
    req: {
      "method": "GET",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 50589
    }
[12:41:44 UTC] INFO: Request started
    reqId: "req-3e"
    correlationId: "08839b0b-f1bc-4fcf-84fb-0d26f3f20eba"
    method: "GET"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "get:test-plans"
[12:41:44 UTC] INFO: incoming request
    reqId: "req-3f"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 50592
    }
[12:41:44 UTC] INFO: Request started
    reqId: "req-3f"
    correlationId: "49e28f4c-b88a-4e97-ac5f-2b3e1e4ad1d2"
    method: "OPTIONS"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "options:priorities"
[12:41:44 UTC] INFO: request completed
    reqId: "req-3f"
    res: {
      "statusCode": 204
    }
    responseTime: 0.12124999985098839
[12:41:44 UTC] INFO: incoming request
    reqId: "req-3g"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 50591
    }
[12:41:44 UTC] INFO: Request started
    reqId: "req-3g"
    correlationId: "83c4e0bc-1727-4341-bf25-2534b8286bfd"
    method: "GET"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:41:44 UTC] INFO: incoming request
    reqId: "req-3h"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 50592
    }
[12:41:44 UTC] INFO: Request started
    reqId: "req-3h"
    correlationId: "7a04f289-89c0-44d4-b53f-be50b88daaf8"
    method: "GET"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:41:44 UTC] INFO: findMany operation on role-assignment
    reqId: "req-38"
    correlationId: "ce09be45-6899-48b3-9208-4dc5bca0c116"
    operation: "role-assignment:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:41:44 UTC] INFO: findMany operation on workflow-transition
    reqId: "req-3b"
    correlationId: "6bbe379c-08fa-4101-8215-9b13c8763043"
    operation: "workflow-transition:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:41:44 UTC] INFO: findOne operation on test plan
    reqId: "req-3e"
    correlationId: "08839b0b-f1bc-4fcf-84fb-0d26f3f20eba"
    operation: "test plan:findOne"
    id: "9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
[12:41:44 UTC] INFO: findMany operation on test suite
    reqId: "req-3h"
    correlationId: "7a04f289-89c0-44d4-b53f-be50b88daaf8"
    operation: "test suite:findMany"
    page: 1
    limit: 100
    hasFilters: true
[12:41:44 UTC] INFO: request completed
    reqId: "req-3e"
    res: {
      "statusCode": 200
    }
    responseTime: 14.308208998292685
[12:41:44 UTC] INFO: incoming request
    reqId: "req-3i"
    req: {
      "method": "GET",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 50589
    }
[12:41:44 UTC] INFO: Request started
    reqId: "req-3i"
    correlationId: "162db704-adf6-4100-b677-19bdb0327c74"
    method: "GET"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "get:statuses"
[12:41:44 UTC] INFO: Returning 1 test cases for suite 2a3222ab-4ff0-4950-af5a-73d28c3ad81a
    reqId: "req-3d"
[12:41:44 UTC] INFO: request completed
    reqId: "req-3d"
    res: {
      "statusCode": 200
    }
    responseTime: 19.762291997671127
[12:41:44 UTC] INFO: request completed
    reqId: "req-3b"
    res: {
      "statusCode": 200
    }
    responseTime: 24.717708002775908
[12:41:44 UTC] INFO: findMany operation on status
    reqId: "req-3i"
    correlationId: "162db704-adf6-4100-b677-19bdb0327c74"
    operation: "status:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:41:44 UTC] INFO: request completed
    reqId: "req-3g"
    res: {
      "statusCode": 200
    }
    responseTime: 20.59858299791813
[12:41:44 UTC] INFO: incoming request
    reqId: "req-3j"
    req: {
      "method": "GET",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 50585
    }
[12:41:44 UTC] INFO: Request started
    reqId: "req-3j"
    correlationId: "773f0c08-1b83-494d-b33b-a729c9392538"
    method: "GET"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "get:priorities"
[12:41:44 UTC] INFO: request completed
    reqId: "req-3h"
    res: {
      "statusCode": 200
    }
    responseTime: 21.193333998322487
[12:41:44 UTC] INFO: findMany operation on priority
    reqId: "req-3j"
    correlationId: "773f0c08-1b83-494d-b33b-a729c9392538"
    operation: "priority:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:41:44 UTC] INFO: request completed
    reqId: "req-38"
    res: {
      "statusCode": 200
    }
    responseTime: 54.859749998897314
[12:41:44 UTC] INFO: request completed
    reqId: "req-3i"
    res: {
      "statusCode": 200
    }
    responseTime: 13.023541998118162
[12:41:44 UTC] INFO: request completed
    reqId: "req-3j"
    res: {
      "statusCode": 200
    }
    responseTime: 8.105333000421524
[12:43:48 UTC] INFO: incoming request
    reqId: "req-3k"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51275
    }
[12:43:48 UTC] INFO: Request started
    reqId: "req-3k"
    correlationId: "3b5903a8-f2d5-46c3-bfd9-24f427367a29"
    method: "OPTIONS"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:43:48 UTC] INFO: request completed
    reqId: "req-3k"
    res: {
      "statusCode": 204
    }
    responseTime: 1.4828750006854534
[12:43:48 UTC] INFO: incoming request
    reqId: "req-3l"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51275
    }
[12:43:48 UTC] INFO: Request started
    reqId: "req-3l"
    correlationId: "2159a666-91df-4617-b804-a4a134c57f9d"
    method: "GET"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:43:48 UTC] INFO: Returning 1 test cases for suite 2a3222ab-4ff0-4950-af5a-73d28c3ad81a
    reqId: "req-3l"
[12:43:48 UTC] INFO: request completed
    reqId: "req-3l"
    res: {
      "statusCode": 200
    }
    responseTime: 21.336582999676466
[12:43:58 UTC] INFO: incoming request
    reqId: "req-3m"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51275
    }
[12:43:58 UTC] INFO: Request started
    reqId: "req-3m"
    correlationId: "9c0b80ab-6489-4bfc-b980-7cdb23a16ebd"
    method: "OPTIONS"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:43:58 UTC] INFO: request completed
    reqId: "req-3m"
    res: {
      "statusCode": 204
    }
    responseTime: 0.8179580010473728
[12:43:58 UTC] INFO: incoming request
    reqId: "req-3n"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51338
    }
[12:43:58 UTC] INFO: Request started
    reqId: "req-3n"
    correlationId: "4a010cf0-f5ac-49dc-82d3-f4c1246b77a7"
    method: "OPTIONS"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "options:statuses"
[12:43:58 UTC] INFO: request completed
    reqId: "req-3n"
    res: {
      "statusCode": 204
    }
    responseTime: 0.45170899853110313
[12:43:58 UTC] INFO: incoming request
    reqId: "req-3o"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51275
    }
[12:43:58 UTC] INFO: Request started
    reqId: "req-3o"
    correlationId: "3a8c3d69-37be-4804-b44b-05a6a29b7fdd"
    method: "GET"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/available-test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:43:58 UTC] INFO: incoming request
    reqId: "req-3p"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51341
    }
[12:43:58 UTC] INFO: Request started
    reqId: "req-3p"
    correlationId: "b71df2f8-182f-4d7f-9ea0-cd09400c18f3"
    method: "OPTIONS"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "options:priorities"
[12:43:58 UTC] INFO: request completed
    reqId: "req-3p"
    res: {
      "statusCode": 204
    }
    responseTime: 0.18891699984669685
[12:43:58 UTC] INFO: incoming request
    reqId: "req-3q"
    req: {
      "method": "GET",
      "url": "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51338
    }
[12:43:58 UTC] INFO: Request started
    reqId: "req-3q"
    correlationId: "2df94a9f-881b-4995-ae05-30cf5284bd9d"
    method: "GET"
    url: "/api/v1/statuses/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "get:statuses"
[12:43:58 UTC] INFO: incoming request
    reqId: "req-3r"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51343
    }
[12:43:58 UTC] INFO: Request started
    reqId: "req-3r"
    correlationId: "968feede-4812-468a-b4ea-fe0691d3bb05"
    method: "OPTIONS"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:43:58 UTC] INFO: request completed
    reqId: "req-3r"
    res: {
      "statusCode": 204
    }
    responseTime: 0.1762089990079403
[12:43:58 UTC] INFO: incoming request
    reqId: "req-3s"
    req: {
      "method": "GET",
      "url": "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51341
    }
[12:43:58 UTC] INFO: Request started
    reqId: "req-3s"
    correlationId: "493205ac-4b60-49dc-b9ce-71f418fe4191"
    method: "GET"
    url: "/api/v1/priorities/?filters=%7B%22isActive%22:%7B%22$eq%22:true%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "get:priorities"
[12:43:58 UTC] INFO: incoming request
    reqId: "req-3t"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/statuses/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51345
    }
[12:43:58 UTC] INFO: Request started
    reqId: "req-3t"
    correlationId: "dfd59863-64b7-4ed7-9207-ca03dbf12e9f"
    method: "OPTIONS"
    url: "/api/v1/statuses/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "options:statuses"
[12:43:58 UTC] INFO: request completed
    reqId: "req-3t"
    res: {
      "statusCode": 204
    }
    responseTime: 0.1229580007493496
[12:43:58 UTC] INFO: incoming request
    reqId: "req-3u"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51343
    }
[12:43:58 UTC] INFO: Request started
    reqId: "req-3u"
    correlationId: "df6bc5ac-a4a8-4be0-8a32-bb50301423f3"
    method: "GET"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:43:58 UTC] INFO: incoming request
    reqId: "req-3v"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/priorities/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51346
    }
[12:43:58 UTC] INFO: Request started
    reqId: "req-3v"
    correlationId: "12c29bc1-6fe7-41bb-b041-552274613312"
    method: "OPTIONS"
    url: "/api/v1/priorities/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "options:priorities"
[12:43:58 UTC] INFO: request completed
    reqId: "req-3v"
    res: {
      "statusCode": 204
    }
    responseTime: 0.16408300027251244
[12:43:58 UTC] INFO: incoming request
    reqId: "req-3w"
    req: {
      "method": "GET",
      "url": "/api/v1/statuses/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51345
    }
[12:43:58 UTC] INFO: Request started
    reqId: "req-3w"
    correlationId: "e66a6247-2c6f-479f-ac0f-a2881b4e5e2d"
    method: "GET"
    url: "/api/v1/statuses/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "get:statuses"
[12:43:58 UTC] INFO: incoming request
    reqId: "req-3x"
    req: {
      "method": "GET",
      "url": "/api/v1/priorities/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51346
    }
[12:43:58 UTC] INFO: Request started
    reqId: "req-3x"
    correlationId: "c313c483-9a36-46bc-a724-88f9a02de64e"
    method: "GET"
    url: "/api/v1/priorities/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "get:priorities"
[12:43:58 UTC] INFO: findMany operation on status
    reqId: "req-3q"
    correlationId: "2df94a9f-881b-4995-ae05-30cf5284bd9d"
    operation: "status:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:43:58 UTC] INFO: findMany operation on priority
    reqId: "req-3s"
    correlationId: "493205ac-4b60-49dc-b9ce-71f418fe4191"
    operation: "priority:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:43:58 UTC] INFO: findMany operation on status
    reqId: "req-3w"
    correlationId: "e66a6247-2c6f-479f-ac0f-a2881b4e5e2d"
    operation: "status:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:43:58 UTC] INFO: findMany operation on priority
    reqId: "req-3x"
    correlationId: "c313c483-9a36-46bc-a724-88f9a02de64e"
    operation: "priority:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:43:58 UTC] INFO: request completed
    reqId: "req-3q"
    res: {
      "statusCode": 200
    }
    responseTime: 41.73766599968076
[12:43:58 UTC] INFO: request completed
    reqId: "req-3w"
    res: {
      "statusCode": 200
    }
    responseTime: 35.14833300188184
[12:43:58 UTC] INFO: request completed
    reqId: "req-3o"
    res: {
      "statusCode": 200
    }
    responseTime: 51.946707997471094
[12:43:58 UTC] INFO: Returning 1 test cases for suite 2a3222ab-4ff0-4950-af5a-73d28c3ad81a
    reqId: "req-3u"
[12:43:58 UTC] INFO: request completed
    reqId: "req-3u"
    res: {
      "statusCode": 200
    }
    responseTime: 39.78345900028944
[12:43:58 UTC] INFO: request completed
    reqId: "req-3x"
    res: {
      "statusCode": 200
    }
    responseTime: 34.66320800036192
[12:43:58 UTC] INFO: request completed
    reqId: "req-3s"
    res: {
      "statusCode": 200
    }
    responseTime: 43.54345899820328
[12:44:19 UTC] INFO: incoming request
    reqId: "req-3y"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51341
    }
[12:44:19 UTC] INFO: Request started
    reqId: "req-3y"
    correlationId: "373a4c43-627b-4359-96c2-0168754efc0e"
    method: "OPTIONS"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:44:19 UTC] INFO: request completed
    reqId: "req-3y"
    res: {
      "statusCode": 204
    }
    responseTime: 1.703625001013279
[12:44:19 UTC] INFO: incoming request
    reqId: "req-3z"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/statuses/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51346
    }
[12:44:19 UTC] INFO: Request started
    reqId: "req-3z"
    correlationId: "fbe78b79-e831-4afe-87fd-7f009b36f771"
    method: "OPTIONS"
    url: "/api/v1/statuses/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "options:statuses"
[12:44:19 UTC] INFO: request completed
    reqId: "req-3z"
    res: {
      "statusCode": 204
    }
    responseTime: 0.16750000044703484
[12:44:19 UTC] INFO: incoming request
    reqId: "req-40"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/priorities/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51343
    }
[12:44:19 UTC] INFO: Request started
    reqId: "req-40"
    correlationId: "cb658a67-4e40-4775-b28f-994a22ab7d48"
    method: "OPTIONS"
    url: "/api/v1/priorities/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "options:priorities"
[12:44:19 UTC] INFO: request completed
    reqId: "req-40"
    res: {
      "statusCode": 204
    }
    responseTime: 0.13166600093245506
[12:44:19 UTC] INFO: incoming request
    reqId: "req-41"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51341
    }
[12:44:19 UTC] INFO: Request started
    reqId: "req-41"
    correlationId: "447fca89-9973-40ef-8e5c-c6b43f78172e"
    method: "GET"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:44:19 UTC] INFO: incoming request
    reqId: "req-42"
    req: {
      "method": "GET",
      "url": "/api/v1/statuses/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51346
    }
[12:44:19 UTC] INFO: Request started
    reqId: "req-42"
    correlationId: "57126630-a83b-4678-a7ac-1c9fc7ede289"
    method: "GET"
    url: "/api/v1/statuses/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "statuses"
    operation: "get:statuses"
[12:44:19 UTC] INFO: incoming request
    reqId: "req-43"
    req: {
      "method": "GET",
      "url": "/api/v1/priorities/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51343
    }
[12:44:19 UTC] INFO: Request started
    reqId: "req-43"
    correlationId: "2f04ba22-5ba6-4e8a-8f5f-bb76f262477c"
    method: "GET"
    url: "/api/v1/priorities/?filters=%7B%22organizationId%22:%7B%22$or%22:%7B%22$eq%22:%22cd22fe0c-0723-4ce5-9ed5-54cad0af8be9%22,%22$null%22:true%7D%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "priorities"
    operation: "get:priorities"
[12:44:19 UTC] INFO: findMany operation on status
    reqId: "req-42"
    correlationId: "57126630-a83b-4678-a7ac-1c9fc7ede289"
    operation: "status:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:44:19 UTC] INFO: findMany operation on priority
    reqId: "req-43"
    correlationId: "2f04ba22-5ba6-4e8a-8f5f-bb76f262477c"
    operation: "priority:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:44:19 UTC] INFO: request completed
    reqId: "req-42"
    res: {
      "statusCode": 200
    }
    responseTime: 16.290207996964455
[12:44:19 UTC] INFO: request completed
    reqId: "req-43"
    res: {
      "statusCode": 200
    }
    responseTime: 15.697707999497652
[12:44:19 UTC] INFO: Returning 1 test cases for suite 2a3222ab-4ff0-4950-af5a-73d28c3ad81a
    reqId: "req-41"
[12:44:19 UTC] INFO: request completed
    reqId: "req-41"
    res: {
      "statusCode": 200
    }
    responseTime: 28.12262499704957
[12:44:33 UTC] INFO: incoming request
    reqId: "req-44"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51341
    }
[12:44:33 UTC] INFO: Request started
    reqId: "req-44"
    correlationId: "48c54016-49d0-4dd3-9bc3-4a2b292125ad"
    method: "OPTIONS"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:44:33 UTC] INFO: request completed
    reqId: "req-44"
    res: {
      "statusCode": 204
    }
    responseTime: 0.9804579988121986
[12:44:33 UTC] INFO: incoming request
    reqId: "req-45"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51341
    }
[12:44:33 UTC] INFO: Request started
    reqId: "req-45"
    correlationId: "d418e456-c3de-4e75-a715-23f82fcd413d"
    method: "GET"
    url: "/api/v1/test-suites/2a3222ab-4ff0-4950-af5a-73d28c3ad81a/test-cases"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:44:33 UTC] INFO: Returning 1 test cases for suite 2a3222ab-4ff0-4950-af5a-73d28c3ad81a
    reqId: "req-45"
[12:44:33 UTC] INFO: request completed
    reqId: "req-45"
    res: {
      "statusCode": 200
    }
    responseTime: 33.48600000143051
[12:44:41 UTC] INFO: incoming request
    reqId: "req-46"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51341
    }
[12:44:41 UTC] INFO: Request started
    reqId: "req-46"
    correlationId: "304bdc19-3260-407a-9092-fe9a0d15a4cc"
    method: "OPTIONS"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "options:role-assignments"
[12:44:41 UTC] INFO: request completed
    reqId: "req-46"
    res: {
      "statusCode": 204
    }
    responseTime: 2.2975419983267784
[12:44:41 UTC] INFO: incoming request
    reqId: "req-47"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51343
    }
[12:44:41 UTC] INFO: Request started
    reqId: "req-47"
    correlationId: "a7bf37f8-d7ab-496a-89d6-ada0c56c7383"
    method: "OPTIONS"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "options:workflow-transitions"
[12:44:41 UTC] INFO: request completed
    reqId: "req-47"
    res: {
      "statusCode": 204
    }
    responseTime: 0.35374999791383743
[12:44:41 UTC] INFO: incoming request
    reqId: "req-48"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51346
    }
[12:44:41 UTC] INFO: Request started
    reqId: "req-48"
    correlationId: "dd714e7a-de17-4c1d-9655-cac14bf9db85"
    method: "OPTIONS"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "options:test-plans"
[12:44:41 UTC] INFO: request completed
    reqId: "req-48"
    res: {
      "statusCode": 204
    }
    responseTime: 0.3107920028269291
[12:44:41 UTC] INFO: incoming request
    reqId: "req-49"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51275
    }
[12:44:41 UTC] INFO: Request started
    reqId: "req-49"
    correlationId: "a5f8573b-b8b8-48a3-83a1-eeb63679a58e"
    method: "OPTIONS"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:44:41 UTC] INFO: request completed
    reqId: "req-49"
    res: {
      "statusCode": 204
    }
    responseTime: 0.3681659996509552
[12:44:41 UTC] INFO: incoming request
    reqId: "req-4a"
    req: {
      "method": "GET",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51343
    }
[12:44:41 UTC] INFO: Request started
    reqId: "req-4a"
    correlationId: "9490ef6d-9fdc-4d8b-9bf6-2ec98e566850"
    method: "GET"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "get:workflow-transitions"
[12:44:41 UTC] INFO: incoming request
    reqId: "req-4b"
    req: {
      "method": "GET",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51346
    }
[12:44:41 UTC] INFO: Request started
    reqId: "req-4b"
    correlationId: "4fef4a0f-4d6a-46b5-8df3-667f0d079770"
    method: "GET"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "get:test-plans"
[12:44:41 UTC] INFO: incoming request
    reqId: "req-4c"
    req: {
      "method": "GET",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51341
    }
[12:44:41 UTC] INFO: Request started
    reqId: "req-4c"
    correlationId: "88c26bc0-0671-49bf-8ade-d1a62a65a832"
    method: "GET"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "get:role-assignments"
[12:44:41 UTC] INFO: incoming request
    reqId: "req-4d"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51275
    }
[12:44:41 UTC] INFO: Request started
    reqId: "req-4d"
    correlationId: "52e68e97-aeb2-4ac7-8cd5-f8caefbaa8e0"
    method: "GET"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:44:41 UTC] INFO: incoming request
    reqId: "req-4e"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/user-fcm-tokens/",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51345
    }
[12:44:41 UTC] INFO: Request started
    reqId: "req-4e"
    correlationId: "e0dd0fb2-6ab3-4796-a46b-fece76e5a753"
    method: "OPTIONS"
    url: "/api/v1/user-fcm-tokens/"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "user-fcm-tokens"
    operation: "options:user-fcm-tokens"
[12:44:41 UTC] INFO: request completed
    reqId: "req-4e"
    res: {
      "statusCode": 204
    }
    responseTime: 0.17479200288653374
[12:44:41 UTC] INFO: incoming request
    reqId: "req-4f"
    req: {
      "method": "POST",
      "url": "/api/v1/user-fcm-tokens/",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 51345
    }
[12:44:41 UTC] INFO: Request started
    reqId: "req-4f"
    correlationId: "32027d72-68b6-415e-be6b-b11fa15bb95b"
    method: "POST"
    url: "/api/v1/user-fcm-tokens/"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "user-fcm-tokens"
    operation: "post:user-fcm-tokens"
[12:44:41 UTC] INFO: findMany operation on role-assignment
    reqId: "req-4c"
    correlationId: "88c26bc0-0671-49bf-8ade-d1a62a65a832"
    operation: "role-assignment:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:44:41 UTC] INFO: findMany operation on test suite
    reqId: "req-4d"
    correlationId: "52e68e97-aeb2-4ac7-8cd5-f8caefbaa8e0"
    operation: "test suite:findMany"
    page: 1
    limit: 100
    hasFilters: true
[12:44:41 UTC] INFO: findOne operation on test plan
    reqId: "req-4b"
    correlationId: "4fef4a0f-4d6a-46b5-8df3-667f0d079770"
    operation: "test plan:findOne"
    id: "9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
[12:44:41 UTC] INFO: findMany operation on workflow-transition
    reqId: "req-4a"
    correlationId: "9490ef6d-9fdc-4d8b-9bf6-2ec98e566850"
    operation: "workflow-transition:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:44:41 UTC] INFO: request completed
    reqId: "req-4b"
    res: {
      "statusCode": 200
    }
    responseTime: 14.689084000885487
[12:44:41 UTC] INFO: request completed
    reqId: "req-4d"
    res: {
      "statusCode": 200
    }
    responseTime: 15.246332999318838
[12:44:41 UTC] INFO: request completed
    reqId: "req-4a"
    res: {
      "statusCode": 200
    }
    responseTime: 23.686457999050617
[12:44:41 UTC] INFO: request completed
    reqId: "req-4c"
    res: {
      "statusCode": 200
    }
    responseTime: 20.61379100009799
[12:44:41 UTC] INFO: request completed
    reqId: "req-4f"
    res: {
      "statusCode": 200
    }
    responseTime: 22.655792001634836
[12:55:49 UTC] INFO: incoming request
    reqId: "req-4g"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 54665
    }
[12:55:49 UTC] INFO: Request started
    reqId: "req-4g"
    correlationId: "708653c5-3221-470d-be3d-0c1fc9bae56c"
    method: "OPTIONS"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "options:role-assignments"
[12:55:49 UTC] INFO: request completed
    reqId: "req-4g"
    res: {
      "statusCode": 204
    }
    responseTime: 4.886583000421524
[12:55:49 UTC] INFO: incoming request
    reqId: "req-4h"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 54667
    }
[12:55:49 UTC] INFO: Request started
    reqId: "req-4h"
    correlationId: "773ba230-7852-43ff-8983-66c2c280fa43"
    method: "OPTIONS"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "options:workflow-transitions"
[12:55:49 UTC] INFO: request completed
    reqId: "req-4h"
    res: {
      "statusCode": 204
    }
    responseTime: 0.16870800033211708
[12:55:49 UTC] INFO: incoming request
    reqId: "req-4i"
    req: {
      "method": "GET",
      "url": "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 54665
    }
[12:55:49 UTC] INFO: Request started
    reqId: "req-4i"
    correlationId: "b4eed1c5-6363-4437-8861-3d546302db22"
    method: "GET"
    url: "/api/v1/role-assignments/?filters=%7B%22userId%22:%7B%22$eq%22:%22e9397f12-ad2a-4f02-a93c-930e9ffd2983%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "role-assignments"
    operation: "get:role-assignments"
[12:55:49 UTC] INFO: incoming request
    reqId: "req-4j"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 54668
    }
[12:55:49 UTC] INFO: Request started
    reqId: "req-4j"
    correlationId: "4e1d04ec-624f-4a6b-a7a4-bd44eb3c6dec"
    method: "OPTIONS"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "options:test-plans"
[12:55:49 UTC] INFO: request completed
    reqId: "req-4j"
    res: {
      "statusCode": 204
    }
    responseTime: 0.5293330028653145
[12:55:49 UTC] INFO: incoming request
    reqId: "req-4k"
    req: {
      "method": "GET",
      "url": "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 54667
    }
[12:55:49 UTC] INFO: Request started
    reqId: "req-4k"
    correlationId: "0cf0a2e3-5e81-4db6-884f-e3588872e6f2"
    method: "GET"
    url: "/api/v1/workflow-transitions/?filters=%7B%22workflowId%22:%7B%22$eq%22:%2272d462d3-6e8f-475e-a7f9-893c706611df%22%7D%7D"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "workflow-transitions"
    operation: "get:workflow-transitions"
[12:55:49 UTC] INFO: incoming request
    reqId: "req-4l"
    req: {
      "method": "OPTIONS",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 54669
    }
[12:55:49 UTC] INFO: Request started
    reqId: "req-4l"
    correlationId: "fc04156c-0a8a-489d-a97c-de609451d26d"
    method: "OPTIONS"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "options:test-suites"
[12:55:49 UTC] INFO: request completed
    reqId: "req-4l"
    res: {
      "statusCode": 204
    }
    responseTime: 0.13808299973607063
[12:55:49 UTC] INFO: incoming request
    reqId: "req-4m"
    req: {
      "method": "GET",
      "url": "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 54668
    }
[12:55:49 UTC] INFO: Request started
    reqId: "req-4m"
    correlationId: "460854a0-657f-4e3d-af3a-1b105afa8f07"
    method: "GET"
    url: "/api/v1/test-plans/9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-plans"
    operation: "get:test-plans"
[12:55:49 UTC] INFO: incoming request
    reqId: "req-4n"
    req: {
      "method": "GET",
      "url": "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100",
      "host": "localhost:3001",
      "remoteAddress": "127.0.0.1",
      "remotePort": 54669
    }
[12:55:49 UTC] INFO: Request started
    reqId: "req-4n"
    correlationId: "d572936b-d920-410c-aa2c-048694babfd7"
    method: "GET"
    url: "/api/v1/test-suites/?filters=%7B%22testPlanId%22:%7B%22$eq%22:%229fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0%22%7D%7D&limit=100"
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ip: "127.0.0.1"
    resource: "test-suites"
    operation: "get:test-suites"
[12:55:49 UTC] INFO: findMany operation on test suite
    reqId: "req-4n"
    correlationId: "d572936b-d920-410c-aa2c-048694babfd7"
    operation: "test suite:findMany"
    page: 1
    limit: 100
    hasFilters: true
[12:55:49 UTC] INFO: findOne operation on test plan
    reqId: "req-4m"
    correlationId: "460854a0-657f-4e3d-af3a-1b105afa8f07"
    operation: "test plan:findOne"
    id: "9fc1b28c-aa18-4cc9-ac82-75bc57f5e9c0"
[12:55:49 UTC] INFO: findMany operation on role-assignment
    reqId: "req-4i"
    correlationId: "b4eed1c5-6363-4437-8861-3d546302db22"
    operation: "role-assignment:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:55:49 UTC] INFO: findMany operation on workflow-transition
    reqId: "req-4k"
    correlationId: "0cf0a2e3-5e81-4db6-884f-e3588872e6f2"
    operation: "workflow-transition:findMany"
    page: 1
    limit: 20
    hasFilters: true
[12:55:49 UTC] INFO: request completed
    reqId: "req-4m"
    res: {
      "statusCode": 200
    }
    responseTime: 175.288708999753
[12:55:49 UTC] INFO: request completed
    reqId: "req-4k"
    res: {
      "statusCode": 200
    }
    responseTime: 191.2403749972582
[12:55:49 UTC] INFO: request completed
    reqId: "req-4n"
    res: {
      "statusCode": 200
    }
    responseTime: 189.89970799908042
[12:55:49 UTC] INFO: request completed
    reqId: "req-4i"
    res: {
      "statusCode": 200
    }
    responseTime: 219.6418750025332
 ELIFECYCLE  Command failed.
