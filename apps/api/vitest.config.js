Object.defineProperty(exports, '__esModule', { value: true });
var node_path_1 = require('node:path');
var config_1 = require('vitest/config');
exports.default = (0, config_1.defineConfig)({
  test: {
    environment: 'node',
    globals: true,
    setupFiles: ['./src/__tests__/setup/vitest-setup.ts'],
    testTimeout: 30_000,
    hookTimeout: 30_000,
    fileParallelism: false, // Disable parallel execution to avoid database conflicts
    env: {
      NODE_ENV: 'test',
      DATABASE_URL: 'postgresql://postgres:postgres@localhost:5432/spark_test',
    },
  },
  resolve: {
    alias: {
      '@': node_path_1.default.resolve(__dirname, './src'),
    },
  },
  esbuild: {
    target: 'node16',
  },
});
