<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>You're Invited to Spark</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 40px auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background-color: #2c3e50;
            color: white;
            text-align: center;
            padding: 30px;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .logo {
            width: 40px;
            height: 40px;
            margin: 0 auto 15px;
            display: block;
        }
        .content {
            padding: 40px 30px;
        }
        .content h2 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 24px;
        }
        .content p {
            margin: 15px 0;
            color: #555;
        }
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        .cta-button {
            display: inline-block;
            background-color: #4F46E5;
            color: #ffffff !important;
            padding: 14px 32px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            transition: background-color 0.3s ease;
        }
        .cta-button:hover {
            background-color: #4338CA;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            font-size: 14px;
            color: #666;
        }
        .footer a {
            color: #4F46E5;
            text-decoration: none;
        }
        .alternative {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 6px;
            font-size: 14px;
            color: #666;
        }
        .alternative code {
            display: block;
            margin: 10px 0;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
        }
        .credentials {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #4F46E5;
            margin: 20px 0;
        }
        .feature-list {
            margin: 20px 0;
            padding-left: 20px;
        }
        .feature-list li {
            margin: 8px 0;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <svg class="logo" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" fill="currentColor"/>
            </svg>
            <h1>You're Invited to Join Spark</h1>
        </div>
        <div class="content">
            <h2>Hi {{inviteeName}},</h2>
            <p>{{inviterName}} has invited you to join <strong>{{organizationName}}</strong> on Spark. You'll be able to collaborate on projects and stay up-to-date with your team's progress.</p>
            
            <div class="credentials">
                <strong>Workspace:</strong> {{organizationName}}<br>
                <strong>Role:</strong> {{user_role}}<br>
                <strong>Invited by:</strong> {{inviterName}} ({{inviterEmail}})
            </div>
            
            <p>Click the button below to accept the invitation and create your account:</p>
            
            <div class="button-container">
                <a href="{{invitationUrl}}" class="cta-button">Accept Invitation</a>
            </div>
            
            <p>Spark helps teams stay organized with:</p>
            <ul class="feature-list">
                <li>Real-time project collaboration</li>
                <li>Task management and assignment</li>
                <li>Progress tracking and reporting</li>
            </ul>
            
            <p>This invitation will expire in 7 days. If you have any questions, feel free to reach out to {{inviterName}} or our support team.</p>
            
            <div class="alternative">
                <p><strong>Having trouble?</strong> If the button above doesn't work, copy and paste this link into your browser:</p>
                <code>{{invitationUrl}}</code>
            </div>
        </div>
        <div class="footer">
            <p>&copy; 2024 Spark. All rights reserved.</p>
            <p>Need help? <a href="{{appUrl}}/support">Contact our support team</a></p>
        </div>
    </div>
</body>
</html>