#!/bin/bash
# Ensure NODE_ENV is properly set and exported
echo "Current NODE_ENV: $NODE_ENV"

echo 'Starting database migration process...'
NODE_ENV=$NODE_ENV pnpm --filter @repo/db run db:migrate
echo 'Migration completed successfully!'
echo 'Starting API development server...'
echo "Current NODE_ENV: $NODE_ENV"
export NODE_ENV=$NODE_ENV
NODE_ENV=$NODE_ENV pnpm --filter=./apps/api run dev
echo 'API development server started successfully!'
