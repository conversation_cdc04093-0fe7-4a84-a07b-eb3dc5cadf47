{"permissions": {"allow": ["Bash(npx tsc:*)", "Bash(pnpm test:*)", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "Bash(grep:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(curl:*)", "Bash(pnpm dev:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(timeout 30 pnpm dev)", "Bash(rg:*)", "Bash(/Users/<USER>/.nvm/versions/node/v18.20.2/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 2 -B 2 \"selectSchema:\" src/routes/*.ts)", "Bash(npm run dev:*)", "Bash(find:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(timeout:*)", "Bash(node:*)", "Bash(npx tsx:*)"], "deny": []}}