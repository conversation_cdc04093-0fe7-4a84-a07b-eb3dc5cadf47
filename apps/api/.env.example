# Database Configuration
DB_HOST=localhost
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=spark_db
DB_PORT=5432
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/spark_db

# Server Configuration
NODE_ENV=development
PORT=3000
LOG_LEVEL=info

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-token-key-change-this-in-production
JWT_EXPIRES_IN=86400
JWT_ISSUER=spark-api
JWT_AUDIENCE=spark-app

# Email Configuration (Gmail example)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-specific-password
EMAIL_FROM=<EMAIL>
FRONT_END_URL=http://localhost:5173

# Optional Flags
DB_MIGRATING=false
DB_SEEDING=false