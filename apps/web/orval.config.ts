import { defineConfig } from 'orval';

const input = {
  target: 'http://127.0.0.1:3001/documentation/json',
  validation: false,
};
const hooks = { afterAllFilesWrite: 'prettier --write' };
const hookOverrides = {
  mutator: {
    path: './src/services/axiosClient.ts',
    name: 'axiosClient',
  },
};

export default defineConfig({
  hooksGen: {
    input,
    output: {
      mode: 'tags',
      target: 'src/services/hooks.ts',
      client: 'react-query',
      mock: false,
      override: hookOverrides,
      baseUrl: '/',
      // headers: true,
    },
    hooks,
  },
});
