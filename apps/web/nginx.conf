events {
}

http {
    include    /etc/nginx/mime.types;  # Explicitly point to the correct path
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_types text/plain text/css application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript application/javascript;
    gzip_min_length 256;
    client_max_body_size 100M;

    # HTTP server to redirect to HTTPS
   # server {
   #     listen       80;
   # 	server_name  localhost;  # Updated to match your domain
   #     return 301 https://$host$request_uri;  # Redirect all HTTP traffic to HTTPS
   # }

    # HTTPS server
    server {
        listen       443 ssl;
        server_name  localhost;  # Updated to match your domain

      	# SSL certificate and key (Certbot paths)
        ssl_certificate      /etc/nginx/certs/fullchain1.pem;
        ssl_certificate_key  /etc/nginx/certs/privkey1.pem;

        # SSL configuration for security
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_prefer_server_ciphers on;
        ssl_ciphers ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256;
        ssl_session_timeout 1d;
        ssl_session_cache shared:SSL:10m;
        ssl_session_tickets off;
        ssl_stapling on;
        ssl_stapling_verify on;

        location / {
            root   /usr/share/nginx/html;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html?$args;
        }

        location /api/v1 {
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Server $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Host $http_host;
            proxy_set_header X-Forwarded-Proto $scheme;  # Pass the scheme (https)
            proxy_read_timeout 5m;
            proxy_send_timeout 5m;
            proxy_pass http://spark-api-server:3000;
        }

         location /spark {
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Server $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Host $http_host;
            proxy_set_header X-Forwarded-Proto $scheme;  # Pass the scheme (https)
            proxy_read_timeout 5m;
            proxy_send_timeout 5m;
            proxy_pass http://spark-minio:9000;
        }

        location /socket.io {
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Server $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Host $http_host;
            proxy_set_header X-Forwarded-Proto $scheme;  # Pass the scheme (https)
            proxy_read_timeout 5m;
            proxy_send_timeout 5m;
            proxy_pass http://spark-api-server:3000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   /usr/share/nginx/html;
        }
    }
}