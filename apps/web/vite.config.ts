import path from 'node:path';
import { TanStackRouterVite } from '@tanstack/router-plugin/vite';
import react from '@vitejs/plugin-react-swc';
import { defineConfig } from 'vite';

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), TanStackRouterVite({ target: 'react', autoCodeSplitting: true })],
  server: {
    port: 5173,
    host: true,
  },
  resolve: {
    alias: {
      '@/web': path.resolve(__dirname, 'src'),
      '@/api': path.resolve(__dirname, '../../apps/api/src'),
      '@repo/ui': path.resolve(__dirname, '../../packages/ui/src'),
      '@repo/api-client': path.resolve(__dirname, '../../packages/api-client/src'),
      '@repo/api': path.resolve(__dirname, '../../apps/api/src'),
      '@repo/db': path.resolve(__dirname, '../../packages/db/src'),
    },
  },
});
