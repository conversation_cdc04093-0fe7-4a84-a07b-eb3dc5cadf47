/** biome-ignore-all lint/correctness/noUndeclaredVariables: <explanation> */
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js');

const firebaseConfig = {
  apiKey: 'AIzaSyAO2YC0kHpwLD_bx2RxzQ5La8njOwnTKh8',
  authDomain: 'spark-3690.firebaseapp.com',
  projectId: 'spark-3690',
  storageBucket: 'spark-3690.firebasestorage.app',
  messagingSenderId: '428877550406',
  appId: '1:428877550406:web:87e82dc8b66a9b12f6f5d2',
};

firebase.initializeApp(firebaseConfig);

const messaging = firebase.messaging();

// Background message handler
messaging.onBackgroundMessage((payload) => {
  // Handle logout notification specially
  if (payload.data?.id === 'logout') {
    // Send message to all clients to logout
    self.clients.matchAll({ type: 'window' }).then((clients) => {
      clients.forEach((client) => {
        client.postMessage({
          type: 'LOGOUT_FROM_FCM',
          data: payload.data,
        });
      });
    });
    return; // Don't show notification for logout
  }

  const notificationTitle = payload.notification?.title || 'New Notification';
  const notificationOptions = {
    body: payload.notification?.body || 'You have a new notification',
    icon: '/icon-192x192.png',
    badge: '/icon-192x192.png',
    data: payload.data,
    tag: payload.data?.workItemId || payload.data?.commentId || 'general',
    actions: [
      {
        action: 'view',
        title: 'View',
      },
      {
        action: 'dismiss',
        title: 'Dismiss',
      },
    ],
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});

// Notification click handler
self.addEventListener('notificationclick', (event) => {
  event.notification.close();

  if (event.action === 'dismiss') {
    return;
  }

  // Handle notification click - open the app
  const data = event.notification.data;
  let urlToOpen = '/';

  if (data?.workItemId) {
    urlToOpen = `/work-items/${data.workItemId}`;
  } else if (data?.commentId) {
    urlToOpen = `/work-items/${data.workItemId}#comment-${data.commentId}`;
  }

  event.waitUntil(
    self.clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
      // Check if there's already a window/tab open with the app
      for (const client of clientList) {
        if (client.url.includes(self.location.origin) && 'focus' in client) {
          // Navigate to the specific URL and focus the window
          client.navigate(urlToOpen);
          return client.focus();
        }
      }
      // If no window is open, open a new one
      if (self.clients.openWindow) {
        return self.clients.openWindow(urlToOpen);
      }
    }),
  );
});
