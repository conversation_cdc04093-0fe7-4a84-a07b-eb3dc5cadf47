# Stage 1: Build the React app
FROM node:20-slim AS build

# Set working directory
WORKDIR /app

# Install pnpm globally
RUN npm install -g pnpm@9.0.0

# Copy root-level configuration files for the monorepo
COPY package.json pnpm-workspace.yaml turbo.json pnpm-lock.yaml ./

# Copy only the necessary apps and packages
COPY apps/web ./apps/web
COPY packages ./packages

# Install dependencies for the entire workspace
RUN pnpm install --frozen-lockfile

# Build the React app
RUN pnpm run build --filter=./apps/web

# Stage 2: Serve the React app with Nginx
FROM nginx:1.25

# Remove default Nginx website
RUN rm -rf /usr/share/nginx/html/*

# Copy the build output from the first stage to Nginx's public directory
COPY --from=build /app/apps/web/dist /usr/share/nginx/html

# Copy SSL certificates
COPY apps/web/certs/fullchain1.pem /etc/nginx/certs/fullchain1.pem
COPY apps/web/certs/privkey1.pem /etc/nginx/certs/privkey1.pem

# Copy custom Nginx configuration
COPY apps/web/nginx.conf /etc/nginx/nginx.conf
COPY apps/web/mime.types /etc/nginx/mime.types
COPY apps/web/mime.types /etc/nginx/conf/mime.types

# Expose Nginx port
EXPOSE 80 443

# # Start Nginx
# CMD ["nginx", "-g", "daemon off;"]
