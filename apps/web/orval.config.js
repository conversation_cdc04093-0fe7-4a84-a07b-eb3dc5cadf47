Object.defineProperty(exports, '__esModule', { value: true });
var orval_1 = require('orval');
var input = {
  target: 'http://127.0.0.1:3001/documentation/json',
  validation: false,
};
var hooks = { afterAllFilesWrite: 'prettier --write' };
var hookOverrides = {
  mutator: {
    path: './src/services/axiosClient.ts',
    name: 'axiosClient',
  },
};
exports.default = (0, orval_1.defineConfig)({
  hooksGen: {
    input,
    output: {
      mode: 'tags',
      target: 'src/services/hooks.ts',
      client: 'react-query',
      mock: false,
      override: hookOverrides,
      baseUrl: '/',
      // headers: true,
    },
    hooks,
  },
});
