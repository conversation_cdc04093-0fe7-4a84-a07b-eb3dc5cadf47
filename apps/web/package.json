{"name": "@repo/web-backup", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "orval": "orval --config ./orval.config.ts", "test": "vitest", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "test:e2e:auth": "playwright test auth.spec.ts", "test:e2e:org": "playwright test organizations.spec.ts", "test:e2e:workspace": "playwright test workspaces-projects.spec.ts", "test:e2e:real": "./run-e2e-tests.sh"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@floating-ui/react": "^0.27.8", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-icons": "^1.3.2", "@repo/db": "workspace:*", "@repo/ui": "workspace:*", "@tanstack/react-form": "^1.2.4", "@tanstack/react-query": "^5.71.1", "@tanstack/react-query-devtools": "^5.71.2", "@tanstack/react-router": "^1.114.34", "@tanstack/react-table": "^8.21.2", "@tanstack/router-devtools": "^1.114.32", "@tanstack/zod-form-adapter": "^0.42.1", "@tiptap/extension-highlight": "^2.11.9", "@tiptap/extension-image": "^2.11.9", "@tiptap/extension-link": "^2.11.9", "@tiptap/extension-subscript": "^2.11.9", "@tiptap/extension-superscript": "^2.11.9", "@tiptap/extension-task-item": "^2.11.9", "@tiptap/extension-task-list": "^2.11.9", "@tiptap/extension-text-align": "^2.11.9", "@tiptap/extension-typography": "^2.11.9", "@tiptap/extension-underline": "^2.11.9", "@tiptap/pm": "^2.11.9", "@tiptap/react": "^2.11.9", "@tiptap/starter-kit": "^2.11.9", "axios": "^1.8.4", "date-fns": "^4.1.0", "firebase": "^11.9.1", "framer-motion": "^12.11.0", "hono": "^4.7.2", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "orval": "^7.7.0", "react": "^19.0.0", "react-beautiful-dnd": "^13.1.1", "react-confetti-explosion": "^3.0.3", "react-diff-viewer": "^3.1.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.55.0", "sonner": "^2.0.2", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tailwindcss/postcss": "^4.0.8", "@tanstack/react-router-devtools": "^1.114.34", "@tanstack/router-plugin": "^1.114.34", "@types/lodash": "^4.17.16", "@types/node": "^22.13.14", "@types/react": "^19.0.10", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "sass-embedded": "^1.87.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}