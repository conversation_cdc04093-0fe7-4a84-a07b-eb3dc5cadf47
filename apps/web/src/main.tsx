import '@repo/ui/styles/globals.css';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import './index.css';
import './styles/_keyframe-animations.css';
import './styles/_variables.css';

import { createRouter, RouterProvider } from '@tanstack/react-router';

// Import the generated route tree
import { z } from 'zod';
import { ThemeProvider } from './contexts/theme-context';
import { routeTree } from './routeTree.gen';

// Create a new router instance
const router = createRouter({ routeTree });

// Register the router instance for type safety
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router;
  }
}
z.setErrorMap((issue, ctx) => {
  const fieldName = issue?.path?.length > 0 ? `${issue.path.at(-1)}`?.replace('_', ' ') : 'Field';
  let message = ctx.defaultError;
  // Replace the data type with the field name
  message = message.replace(/String|Number|Boolean|Date|Array|Object/g, fieldName.toString());
  return { message };
});

// Create a client
const queryClient = new QueryClient();
// const TanStackRouterDevtools =
//   import.meta.env.MODE === "production"
//     ? () => null // Render nothing in production
//     : React.lazy(() =>
//         // Lazy load in development
//         import("@tanstack/router-devtools").then((res) => ({
//           default: res.TanStackRouterDevtools,
//           // For Embedded Mode
//           // default: res.TanStackRouterDevtoolsPanel
//         }))
//       );

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <ThemeProvider>
      <QueryClientProvider client={queryClient}>
        {/* <TanStackRouterDevtools /> */}
        <RouterProvider router={router} />
        <ReactQueryDevtools initialIsOpen={false} />
      </QueryClientProvider>
    </ThemeProvider>
  </StrictMode>,
);
