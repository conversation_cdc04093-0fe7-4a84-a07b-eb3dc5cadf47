import type React from 'react';
import { createContext, useEffect, useState } from 'react';
import { THEME_VARIANTS } from '../constants/theme-variants';

type Theme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  variant: string;
  isAutoRotating: boolean;
  toggleTheme: () => void;
  setVariant: (variant: string) => void;
  setIsAutoRotating: (value: boolean) => void;
}

export const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<Theme>(() => {
    const savedTheme = localStorage.getItem('theme') as Theme | null;
    if (savedTheme) {
      return savedTheme;
    }
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  });

  const [variant, setVariantState] = useState<string>(() => {
    const savedVariant = localStorage.getItem('themeVariant');
    return savedVariant || 'default';
  });

  const [isAutoRotating, setIsAutoRotatingState] = useState<boolean>(() => {
    const savedAutoRotate = localStorage.getItem('themeAutoRotate');
    return savedAutoRotate === 'true';
  });

  useEffect(() => {
    const root = document.documentElement;

    // Get all theme identifiers for efficient lookup
    const themeIdentifiers = new Set(THEME_VARIANTS.map((v) => v.identifier));

    // Remove all theme-related classes
    const classesToRemove: string[] = [];
    root.classList.forEach((className) => {
      // Check if it's a theme class
      if (
        className === 'dark' ||
        themeIdentifiers.has(className) ||
        themeIdentifiers.has(className.replace('-dark', ''))
      ) {
        classesToRemove.push(className);
      }
    });

    // Remove all identified theme classes
    classesToRemove.forEach((className) => root.classList.remove(className));

    // Apply appropriate theme class based on variant and mode
    if (variant === 'default') {
      if (theme === 'dark') {
        root.classList.add('dark');
      }
    } else {
      // For custom variants, use variant name and variant-dark pattern
      const className = theme === 'dark' ? `${variant}-dark` : variant;
      root.classList.add(className);
    }

    localStorage.setItem('theme', theme);
    localStorage.setItem('themeVariant', variant);
  }, [theme, variant]);

  // Add an effect to handle initial theme application on mount
  useEffect(() => {
    // Force initial theme application when component mounts
    const root = document.documentElement;
    const themeIdentifiers = new Set(THEME_VARIANTS.map((v) => v.identifier));

    // Clean up any existing theme classes first
    const classesToRemove: string[] = [];
    root.classList.forEach((className) => {
      if (
        className === 'dark' ||
        themeIdentifiers.has(className) ||
        themeIdentifiers.has(className.replace('-dark', ''))
      ) {
        classesToRemove.push(className);
      }
    });
    classesToRemove.forEach((className) => root.classList.remove(className));

    // Apply the current theme
    if (variant === 'default') {
      if (theme === 'dark') {
        root.classList.add('dark');
      }
    } else {
      const className = theme === 'dark' ? `${variant}-dark` : variant;
      root.classList.add(className);
    }
  }, [theme, variant]); // Run only on mount

  const toggleTheme = () => {
    setTheme((prevTheme) => (prevTheme === 'light' ? 'dark' : 'light'));
  };

  const setVariant = (newVariant: string) => {
    setVariantState(newVariant);
  };

  const setIsAutoRotating = (value: boolean) => {
    setIsAutoRotatingState(value);
    localStorage.setItem('themeAutoRotate', value.toString());
  };

  return (
    <ThemeContext.Provider
      value={{
        theme,
        variant,
        isAutoRotating,
        toggleTheme,
        setVariant,
        setIsAutoRotating,
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
}
