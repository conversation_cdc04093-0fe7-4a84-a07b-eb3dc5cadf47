import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { type AppDataSlice, createAppDataSlice } from './app-data';
import { type AuthSlice, createAuthSlice } from './auth';
import { type CurrentStatusSlice, createCurrentStatusSlice } from './currentStatus';

interface StoreState extends AuthSlice, CurrentStatusSlice, AppDataSlice {
  resetStore: () => void;
}

export const useRootStore = create<StoreState>()(
  devtools(
    persist(
      (set, get, store) => ({
        ...createAuthSlice(set, get, store),
        ...createCurrentStatusSlice(set, get, store),
        ...createAppDataSlice(set, get, store),
        resetStore: () => {
          get().clearAuth();
          get().clearAppData();
          set({
            currentOrganizationId: '',
            currentWorkspaceId: '',
            currentProjectId: '',
          });
        },
      }),
      {
        name: 'app-storage',
        //   partialize: (state) => ({
        //     userId: state.userId,
        //     accessToken: state.accessToken,
        //     refreshToken: state.refreshToken,
        //     currentOrganizationId: state.currentOrganizationId,
        //     currentWorkspaceId: state.currentWorkspaceId,
        //   }),
      },
    ),
  ),
);

// Centralized utility functions
export const getAccessToken = () => useRootStore.getState().accessToken;
export const getRefreshToken = () => useRootStore.getState().refreshToken;
export const getUserId = () => useRootStore.getState().userId;
export const isAuthenticated = () => !!useRootStore.getState().accessToken;

export const setAuthData = (userId: string, accessToken: string, refreshToken: string) => {
  const { setUserId, setTokens } = useRootStore.getState();
  setUserId(userId);
  setTokens(accessToken, refreshToken);
};

export const clearAuthData = () => useRootStore.getState().clearAuth();

export const getCurrentStatusPersistedState = () => {
  const { currentOrganizationId, currentWorkspaceId } = useRootStore.getState();
  return { currentOrganizationId, currentWorkspaceId };
};

export const resetEntireStore = () => useRootStore.getState().resetStore();
