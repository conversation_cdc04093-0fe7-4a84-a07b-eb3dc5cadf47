import type { StateCreator } from 'zustand';
import type {
  Organization,
  Project,
  RoleAssignment,
  WorkflowTransition,
  Workspace,
} from '../services/hooks.schemas';

export interface AppDataSlice {
  // Data
  organizations: Organization[];
  workspaces: Workspace[];
  projects: Project[];
  roleAssignments: RoleAssignment[];
  currentProjectWorkflowTransitions: WorkflowTransition[];
  currentWorkflowStatuses: string[]; // Assuming this is needed based on context

  // Loading states
  isLoadingOrganizations: boolean;
  isLoadingWorkspaces: boolean;
  isLoadingProjects: boolean;
  isLoadingRoleAssignments: boolean;

  // Error states
  organizationsError: string | null;
  workspacesError: string | null;
  projectsError: string | null;
  roleAssignmentsError: string | null;

  // Actions
  setOrganizations: (orgs: Organization[]) => void;
  setWorkspaces: (workspaces: Workspace[]) => void;
  setProjects: (projects: Project[]) => void;
  setRoleAssignments: (roleAssignments: RoleAssignment[]) => void;
  setCurrentProjectWorkflowTransitions: (transitions: WorkflowTransition[]) => void;

  // Populate data from role assignments
  populateDataFromRoleAssignments: () => void;

  setLoadingOrganizations: (loading: boolean) => void;
  setLoadingWorkspaces: (loading: boolean) => void;
  setLoadingProjects: (loading: boolean) => void;
  setLoadingRoleAssignments: (loading: boolean) => void;

  setOrganizationsError: (error: string | null) => void;
  setWorkspacesError: (error: string | null) => void;
  setProjectsError: (error: string | null) => void;
  setRoleAssignmentsError: (error: string | null) => void;

  // Computed getters
  getOrganizationById: (id: string) => Organization | undefined;
  getWorkspaceById: (id: string) => Workspace | undefined;
  getProjectById: (id: string) => Project | undefined;
  getWorkspacesByOrganizationId: (orgId: string) => Workspace[];
  getProjectsByWorkspaceId: (wsId: string) => Project[];
  getRoleAssignmentsByUserId: (userId: string) => RoleAssignment[];
  getRoleAssignmentsByScope: (scopeType: string, scopeId: string) => RoleAssignment[];

  // Role-based data getters
  getOrganizationsFromRoleAssignments: (userId: string) => Organization[];
  getWorkspacesFromRoleAssignments: (userId: string) => Workspace[];
  getProjectsFromRoleAssignments: (userId: string) => Project[];
  getUserRoleAssignmentsByScope: (
    userId: string,
    scopeType: 'organization' | 'workspace' | 'project',
  ) => RoleAssignment[];

  // Clear data
  clearAppData: () => void;
}

export const createAppDataSlice: StateCreator<AppDataSlice, [], [], AppDataSlice> = (set, get) => ({
  // Initial state
  organizations: [],
  workspaces: [],
  projects: [],
  roleAssignments: [],
  currentProjectWorkflowTransitions: [],
  currentWorkflowStatuses: [],

  isLoadingOrganizations: false,
  isLoadingWorkspaces: false,
  isLoadingProjects: false,
  isLoadingRoleAssignments: false,

  organizationsError: null,
  workspacesError: null,
  projectsError: null,
  roleAssignmentsError: null,

  // Actions
  setOrganizations: (orgs) => set({ organizations: orgs }),
  setWorkspaces: (workspaces) => set({ workspaces }),
  setProjects: (projects) => set({ projects }),
  setRoleAssignments: (roleAssignments) => set({ roleAssignments }),
  setCurrentProjectWorkflowTransitions: (transitions) =>
    set({ currentProjectWorkflowTransitions: transitions }),

  setLoadingOrganizations: (loading) => set({ isLoadingOrganizations: loading }),
  setLoadingWorkspaces: (loading) => set({ isLoadingWorkspaces: loading }),
  setLoadingProjects: (loading) => set({ isLoadingProjects: loading }),
  setLoadingRoleAssignments: (loading) => set({ isLoadingRoleAssignments: loading }),

  setOrganizationsError: (error) => set({ organizationsError: error }),
  setWorkspacesError: (error) => set({ workspacesError: error }),
  setProjectsError: (error) => set({ projectsError: error }),
  setRoleAssignmentsError: (error) => set({ roleAssignmentsError: error }),

  // Populate data from role assignments
  populateDataFromRoleAssignments: () => {
    const roleAssignments = get().roleAssignments;

    // Extract unique organizations
    const uniqueOrgs = new Map<string, Organization>();
    const uniqueWorkspaces = new Map<string, Workspace>();
    const uniqueProjects = new Map<string, Project>();

    roleAssignments.forEach((ra) => {
      if (ra.organization) {
        uniqueOrgs.set(ra.organization.id, ra.organization);
      }
      if (ra.workspace) {
        uniqueWorkspaces.set(ra.workspace.id, ra.workspace);
      }
      if (ra.project) {
        uniqueProjects.set(ra.project.id, ra.project);
      }
    });

    // Update store with extracted data
    set({
      organizations: Array.from(uniqueOrgs.values()),
      workspaces: Array.from(uniqueWorkspaces.values()),
      projects: Array.from(uniqueProjects.values()),
    });
  },

  // Computed getters
  getOrganizationById: (id) => {
    return get().organizations.find((org) => org.id === id);
  },

  getWorkspaceById: (id) => {
    return get().workspaces.find((ws) => ws.id === id);
  },

  getProjectById: (id) => {
    return get().projects.find((proj) => proj.id === id);
  },

  getWorkspacesByOrganizationId: (orgId) => {
    return get().workspaces.filter((ws) => ws.organizationId === orgId);
  },

  getProjectsByWorkspaceId: (wsId) => {
    return get().projects.filter((proj) => proj.workspaceId === wsId);
  },

  getRoleAssignmentsByUserId: (userId) => {
    return get().roleAssignments.filter((ra) => ra.userId === userId);
  },

  getRoleAssignmentsByScope: (scopeType, scopeId) => {
    return get().roleAssignments.filter(
      (ra) => ra.scopeType === scopeType && ra.scopeId === scopeId,
    );
  },

  // Role-based data getters
  getOrganizationsFromRoleAssignments: (userId) => {
    const roleAssignments = get().roleAssignments.filter(
      (ra) => ra.userId === userId && ra.scopeType === 'organization',
    );
    const uniqueOrgs = new Map<string, Organization>();
    roleAssignments.forEach((ra) => {
      if (ra.organization) {
        uniqueOrgs.set(ra.organization.id, ra.organization);
      }
    });
    return Array.from(uniqueOrgs.values());
  },

  getWorkspacesFromRoleAssignments: (userId) => {
    const roleAssignments = get().roleAssignments.filter(
      (ra) =>
        ra.userId === userId && (ra.scopeType === 'workspace' || ra.scopeType === 'organization'),
    );
    const uniqueWorkspaces = new Map<string, Workspace>();
    roleAssignments.forEach((ra) => {
      if (ra.workspace) {
        uniqueWorkspaces.set(ra.workspace.id, ra.workspace);
      }
      // If user has organization role, they have access to all workspaces in that org
      if (ra.scopeType === 'organization' && ra.organization) {
        const orgWorkspaces = get().workspaces.filter(
          (ws) => ws.organizationId === ra.organization?.id,
        );
        orgWorkspaces.forEach((ws) => {
          uniqueWorkspaces.set(ws.id, ws);
        });
      }
    });
    return Array.from(uniqueWorkspaces.values());
  },

  getProjectsFromRoleAssignments: (userId) => {
    const roleAssignments = get().roleAssignments.filter((ra) => ra.userId === userId);
    const uniqueProjects = new Map<string, Project>();
    roleAssignments.forEach((ra) => {
      if (ra.project) {
        uniqueProjects.set(ra.project.id, ra.project);
      }
      // If user has workspace role, they have access to all projects in that workspace
      if (ra.scopeType === 'workspace' && ra.workspace) {
        const wsProjects = get().projects.filter((proj) => proj.workspaceId === ra.workspace?.id);
        wsProjects.forEach((proj) => {
          uniqueProjects.set(proj.id, proj);
        });
      }
      // If user has organization role, they have access to all projects in all workspaces
      if (ra.scopeType === 'organization' && ra.organization) {
        const orgWorkspaces = get().workspaces.filter(
          (ws) => ws.organizationId === ra.organization?.id,
        );
        orgWorkspaces.forEach((ws) => {
          const wsProjects = get().projects.filter((proj) => proj.workspaceId === ws.id);
          wsProjects.forEach((proj) => {
            uniqueProjects.set(proj.id, proj);
          });
        });
      }
    });
    return Array.from(uniqueProjects.values());
  },

  getUserRoleAssignmentsByScope: (userId, scopeType) => {
    return get().roleAssignments.filter((ra) => ra.userId === userId && ra.scopeType === scopeType);
  },

  // Clear all data
  clearAppData: () =>
    set({
      organizations: [],
      workspaces: [],
      projects: [],
      roleAssignments: [],
      currentProjectWorkflowTransitions: [],
      isLoadingOrganizations: false,
      isLoadingWorkspaces: false,
      isLoadingProjects: false,
      isLoadingRoleAssignments: false,
      organizationsError: null,
      workspacesError: null,
      projectsError: null,
      roleAssignmentsError: null,
    }),
});
