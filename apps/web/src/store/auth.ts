import type { StateCreator } from 'zustand';

export interface AuthSlice {
  userId: string;
  accessToken: string | null;
  refreshToken: string | null;
  setUserId: (userId: string) => void;
  setTokens: (accessToken: string | null, refreshToken: string | null) => void;
  clearAuth: () => void;
}

export const createAuthSlice: StateCreator<AuthSlice, [], [], AuthSlice> = (set) => ({
  userId: '',
  accessToken: null,
  refreshToken: null,
  setUserId: (userId) => set({ userId }),
  setTokens: (accessToken, refreshToken) => set({ accessToken, refreshToken }),
  clearAuth: () => set({ userId: '', accessToken: null, refreshToken: null }),
});
