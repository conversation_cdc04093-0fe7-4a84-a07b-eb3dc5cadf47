import type { StateCreator } from 'zustand';
import type { User } from '../services/hooks.schemas';

export interface CurrentStatusSlice {
  currentOrganizationId: string;
  currentWorkspaceId: string;
  currentProjectId: string;

  currentUser: User | null;

  setCurrentOrganizationId: (orgId: string) => void;
  setCurrentWorkspaceId: (workspaceId: string) => void;
  setCurrentProjectId: (projectId: string) => void;
  setCurrentUser: (user: User) => void;
}

export const createCurrentStatusSlice: StateCreator<
  CurrentStatusSlice,
  [],
  [],
  CurrentStatusSlice
> = (set) => ({
  currentOrganizationId: '',
  currentWorkspaceId: '',
  currentProjectId: '',

  currentUser: null,

  setCurrentOrganizationId: (orgId) => set({ currentOrganizationId: orgId }),
  setCurrentWorkspaceId: (workspaceId) => set({ currentWorkspaceId: workspaceId }),
  setCurrentProjectId: (projectId) => set({ currentProjectId: projectId }),
  setCurrentUser: (user) => set({ currentUser: user }),
});

// Utility function to get the persisted state
export const getCurrentStatusPersistedState = (state: CurrentStatusSlice) => ({
  currentOrganizationId: state.currentOrganizationId,
  currentWorkspaceId: state.currentWorkspaceId,
});
