import { z } from 'zod';

const EnvSchema = z.object({
  VITE_API_URL: z.string().url().default('http://localhost:3001'),
  VITE_API_KEY: z.string().default('test'),
});

export type EnvSchema = z.infer<typeof EnvSchema>;

try {
  EnvSchema.parse(import.meta.env);
} catch (error) {
  if (error instanceof z.ZodError) {
    let message = 'Missing required values in @repo/web .env:\n';
    error.issues.forEach((issue) => {
      message += `${issue.path[0]}\n`;
    });
    const e = new Error(message);
    e.stack = '';
    throw e;
  }
}

export default EnvSchema.parse(import.meta.env);
