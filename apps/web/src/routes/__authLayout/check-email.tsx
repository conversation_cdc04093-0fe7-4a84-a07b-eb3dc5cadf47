import { Toaster } from '@repo/ui/components/toaster';
import { useToast } from '@repo/ui/components/use-toast';
import { createFileRoute, useSearch } from '@tanstack/react-router';
import { z } from 'zod';
import AuthLeftPanel from '@/web/components/auth/auth-left-pannel';
import { CheckEmail } from '@/web/components/auth/check-email';
import { usePostApiV1AuthResendVerification } from '@/web/services/auth';

const checkEmailSearchSchema = z.object({
  email: z.string().email().optional(),
});

const CheckEmailPage = () => {
  const { email } = useSearch({ from: '/__authLayout/check-email' });
  const { toast } = useToast();

  const resendVerification = usePostApiV1AuthResendVerification({
    mutation: {
      onError: (error) => {
        toast({
          title: 'Failed to resend email',
          description: error.message || 'Please try again later.',
          variant: 'destructive',
        });
      },
    },
  });

  const handleResendEmail = async () => {
    if (!email) {
      return;
    }

    await resendVerification.mutateAsync({
      data: { email },
    });
  };

  return (
    <div className="w-screen">
      <AuthLeftPanel>
        <CheckEmail email={email} onResendEmail={email ? handleResendEmail : undefined} />
      </AuthLeftPanel>
      <Toaster />
    </div>
  );
};

export const Route = createFileRoute('/__authLayout/check-email')({
  component: CheckEmailPage,
  validateSearch: checkEmailSearchSchema,
});
