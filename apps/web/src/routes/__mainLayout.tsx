import { SidebarInset, SidebarProvider } from '@repo/ui/components/sidebar';
import { createFileRoute, Outlet, redirect, useLocation } from '@tanstack/react-router';
import { AnimatePresence, motion } from 'framer-motion';
import { FCMTokenHandler } from '../components/auth/fcm-token-handler';
import { DataInitializer } from '../components/data-initializer';
import { AppSidebar } from '../components/nav/app-sidebar';
import { isAuthenticated } from '../utils';

export const Route = createFileRoute('/__mainLayout')({
  beforeLoad: () => {
    if (!isAuthenticated()) {
      throw redirect({
        to: '/login',
      });
    }
  },
  component: RouteComponent,
});

function RouteComponent() {
  const location = useLocation(); // Get current location to trigger animations on route change

  // Extract the main route segment (e.g., '/settings' from '/settings/priority')
  const getMainRoute = (pathname: string) => {
    const segments = pathname.split('/').filter(Boolean);
    return segments[0] || '/';
  };

  // Only animate when changing between main routes, not within settings
  const animationKey = getMainRoute(location.pathname);

  return (
    <>
      {/* Data initializer - fetches and stores app data */}
      <DataInitializer />

      {/* FCM Token Handler for push notifications */}
      <FCMTokenHandler enabled={true} />

      <div className="flex h-screen w-full">
        <SidebarProvider>
          <AppSidebar />
          <SidebarInset className="w-[calc(var(--sidebar-width-icon)_+_1px)]">
            <AnimatePresence mode="wait">
              <motion.div
                animate={{ opacity: 1, x: 0 }} // Use main route as key to prevent animation within settings
                exit={{ opacity: 0, x: -150 }} // Start state (fades out, slides right)
                initial={{ opacity: 0, x: 50 }} // End state (fades in, slides to position)
                key={animationKey} // Exit state (fades out, slides left)
                style={{ width: '100%', height: '100%' }} // Animation duration
                transition={{ duration: 0.1 }}
              >
                <Outlet />
              </motion.div>
            </AnimatePresence>
          </SidebarInset>
        </SidebarProvider>
      </div>
    </>
  );
}
