import { createFileRoute, Outlet, redirect } from '@tanstack/react-router';
import type React from 'react';
import { isAuthenticated } from '../utils';

const AuthLayout: React.FC = () => {
  return (
    <div className="flex max-h-screen bg-background">
      <Outlet />
    </div>
  );
};
export const Route = createFileRoute('/__authLayout')({
  beforeLoad: () => {
    if (isAuthenticated()) {
      throw redirect({
        to: '/',
      });
    }
  },
  component: AuthLayout,
});
