import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/avatar';
import { Badge } from '@repo/ui/components/badge';
import { Button } from '@repo/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/ui/components/card';
import { Progress } from '@repo/ui/components/progress';
import { ScrollArea } from '@repo/ui/components/scroll-area';
import { Separator } from '@repo/ui/components/separator';
import { createFileRoute, redirect } from '@tanstack/react-router';
import { Activity, Building2, Clock, FolderOpen, Plus, TrendingUp, Users, Zap } from 'lucide-react';
import { useRootStore } from '@/web/store/store';
import { isAuthenticated } from '@/web/utils';

// Resizable Widget Wrapper
const ResizableWidget = ({
  children,
  className = '',
  minHeight = '200px',
  // defaultHeight = "350px",
  // id,
}: {
  children: React.ReactNode;
  className?: string;
  minHeight?: string;
  defaultHeight?: string;
  id: string;
}) => {
  return (
    <div
      className={`resizable-widget ${className}`}
      style={{
        resize: 'vertical',
        overflow: 'auto',
        minHeight,
        height: 'auto',
        maxHeight: 'none',
      }}
    >
      {children}
    </div>
  );
};

// Widget Components
const OrganizationsWidget = () => {
  const organizations = useRootStore((state) => state.organizations);

  return (
    <Card className="group h-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="font-medium text-lg">My Organizations</CardTitle>
          <CardDescription>Organizations you're part of</CardDescription>
        </div>
        <Building2 className="h-5 w-5 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[200px]">
          <div className="space-y-4">
            {organizations.map((org) => (
              <div
                className="flex items-center space-x-4 rounded-lg border p-3 transition-colors hover:bg-muted/50"
                key={org.id}
              >
                <Avatar className="h-10 w-10">
                  <AvatarImage alt={org.name} src={org.logoUrl || undefined} />
                  <AvatarFallback>{org.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <div className="flex-1 space-y-1">
                  <div className="flex items-center justify-between">
                    <p className="font-medium text-sm leading-none">{org.name}</p>
                    <Badge variant={org.isActive ? 'default' : 'secondary'}>
                      {org.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                  <p className="text-muted-foreground text-xs">{org.description}</p>
                  {/* <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                    <Users className="h-3 w-3" />
                    <span>{org. || 0} members</span>
                  </div> */}
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
        <Separator className="my-4" />
        <Button className="w-full" variant="outline">
          <Plus className="mr-2 h-4 w-4" />
          Create Organization
        </Button>
      </CardContent>
    </Card>
  );
};

const WorkspacesWidget = () => {
  const currentOrganizationId = useRootStore((state) => state.currentOrganizationId);
  const workspaces = useRootStore((state) => state.workspaces);
  const filteredWorkspaces = currentOrganizationId
    ? workspaces.filter((ws) => ws.organizationId === currentOrganizationId)
    : workspaces;

  return (
    <Card className="group h-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="font-medium text-lg">My Workspaces</CardTitle>
          <CardDescription>Recent and active workspaces</CardDescription>
        </div>
        <FolderOpen className="h-5 w-5 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[200px]">
          <div className="space-y-3">
            {filteredWorkspaces.map((workspace) => (
              <div
                className="flex cursor-pointer items-center space-x-3 rounded-lg border p-3 transition-colors hover:bg-muted/50"
                key={workspace.id}
              >
                <div className="flex h-8 w-8 items-center justify-center rounded-md font-medium text-sm">
                  <Avatar className="h-10 w-10">
                    <AvatarImage alt={workspace.name} src={workspace.icon || undefined} />
                    <AvatarFallback>{workspace.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                </div>
                <div className="flex-1">
                  <p className="font-medium text-sm leading-none">{workspace.name}</p>
                  <p className="mt-1 text-muted-foreground text-xs">
                    {workspace.organization?.name}
                  </p>
                  {workspace.description && (
                    <p className="text-muted-foreground text-xs">{workspace.description}</p>
                  )}
                </div>
                <Badge className="text-xs" variant="outline">
                  {workspace.status === 'active' ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            ))}
          </div>
        </ScrollArea>
        <Separator className="my-4" />
        <Button className="w-full" variant="outline">
          <Plus className="mr-2 h-4 w-4" />
          Create Workspace
        </Button>
      </CardContent>
    </Card>
  );
};

const RecentActivityWidget = () => {
  const activities = [
    {
      type: 'organization',
      name: 'Project created',
      action: 'created',
      time: '2 hours ago',
      icon: FolderOpen,
    },
    {
      type: 'workspace',
      name: 'Team member invited',
      action: 'invited',
      time: '4 hours ago',
      icon: Users,
    },
    {
      type: 'workspace',
      name: 'Sprint started',
      action: 'started',
      time: '1 day ago',
      icon: Zap,
    },
  ];

  return (
    <Card className="group h-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="font-medium text-lg">Recent Activity</CardTitle>
          <CardDescription>Your latest actions</CardDescription>
        </div>
        <Activity className="h-5 w-5 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[200px]">
          <div className="space-y-3">
            {activities.map((activity, index) => {
              const IconComponent = activity.icon;
              return (
                <div className="flex items-center space-x-3" key={index}>
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted">
                    <IconComponent className="h-4 w-4" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm">
                      <span className="font-medium">{activity.action}</span> {activity.name}
                    </p>
                    <p className="flex items-center text-muted-foreground text-xs">
                      <Clock className="mr-1 h-3 w-3" />
                      {activity.time}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};

const StatsOverviewWidget = () => {
  const organizations = useRootStore((state) => state.organizations);
  const workspaces = useRootStore((state) => state.workspaces);
  const projects = useRootStore((state) => state.projects);

  const stats = [
    {
      title: 'Total Organizations',
      value: organizations.length,
      change: '+1 this month',
      icon: Building2,
      color: 'text-primary',
    },
    {
      title: 'Active Workspaces',
      value: workspaces.length,
      change: '+2 this week',
      icon: FolderOpen,
      color: 'text-emerald-600 dark:text-emerald-400',
    },
    {
      title: 'Projects',
      value: projects.length,
      change: '+3 this month',
      icon: FolderOpen,
      color: 'text-purple-600 dark:text-purple-400',
    },
  ];

  return (
    <Card className="group h-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="font-medium text-lg">Overview</CardTitle>
          <CardDescription>Your current statistics</CardDescription>
        </div>
        <TrendingUp className="h-5 w-5 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {stats.map((stat, index) => {
            const IconComponent = stat.icon;
            return (
              <div className="flex items-center space-x-3" key={index}>
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-muted">
                  <IconComponent className={`h-5 w-5 ${stat.color}`} />
                </div>
                <div className="flex-1">
                  <p className="font-bold text-2xl">{stat.value}</p>
                  <p className="text-muted-foreground text-sm">{stat.title}</p>
                  <p className="text-emerald-600 text-xs dark:text-emerald-400">{stat.change}</p>
                </div>
              </div>
            );
          })}
        </div>
        <Separator className="my-4" />
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Monthly Goal</span>
            <span>75%</span>
          </div>
          <Progress className="h-2" value={75} />
        </div>
      </CardContent>
    </Card>
  );
};

// Main Dashboard Component
const Dashboard = () => {
  const currentUser = useRootStore((state) => state.currentUser);

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="mb-6">
        <div className="font-semibold text-2xl">Dashboard</div>
        <p className="text-muted-foreground">
          Welcome back, {currentUser?.displayName || currentUser?.firstName}! Here's what's
          happening with your organizations and workspaces.
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        <div className="md:col-span-2 lg:col-span-1">
          <ResizableWidget defaultHeight="400px" id="organizations">
            <OrganizationsWidget />
          </ResizableWidget>
        </div>

        <div className="md:col-span-2 lg:col-span-1">
          <ResizableWidget defaultHeight="400px" id="workspaces">
            <WorkspacesWidget />
          </ResizableWidget>
        </div>

        <div className="md:col-span-1 lg:col-span-1">
          <ResizableWidget defaultHeight="350px" id="stats">
            <StatsOverviewWidget />
          </ResizableWidget>
        </div>

        <div className="md:col-span-1 lg:col-span-1">
          <ResizableWidget defaultHeight="350px" id="activity">
            <RecentActivityWidget />
          </ResizableWidget>
        </div>
      </div>

      <style>{`
        .resizable-widget {
          border: 1px solid transparent;
          transition: border-color 0.2s ease;
        }

        .resizable-widget:hover {
          border-color: hsl(var(--border));
        }

        .resizable-widget::-webkit-resizer {
          background: linear-gradient(
            -45deg,
            transparent 0%,
            transparent 40%,
            hsl(var(--border)) 40%,
            hsl(var(--border)) 60%,
            transparent 60%
          );
        }
      `}</style>
    </div>
  );
};

export const Route = createFileRoute('/__mainLayout/dashboard')({
  beforeLoad: () => {
    if (!isAuthenticated()) {
      throw redirect({
        to: '/login',
      });
    }
  },
  component: Dashboard,
});
