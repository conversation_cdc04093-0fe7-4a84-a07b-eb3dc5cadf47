import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/ui/components/card';
import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { OrganizationForm } from '@/web/components/organization/organization-form';

function OrganizationCreatePage() {
  const navigate = useNavigate();

  const handleSuccess = () => {
    navigate({ to: '/' });
  };

  const handleCancel = () => {
    navigate({ to: '/' });
  };

  return (
    <div className="container mx-auto p-10">
      <Card>
        <CardHeader>
          <CardTitle>Create Your Organization</CardTitle>
          <CardDescription>
            Let's set up your organization. This will only take a minute.
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <OrganizationForm onCancel={handleCancel} onSuccess={handleSuccess} />
        </CardContent>
      </Card>
    </div>
  );
}

export const Route = createFileRoute('/__mainLayout/organization-create')({
  component: OrganizationCreatePage,
});
