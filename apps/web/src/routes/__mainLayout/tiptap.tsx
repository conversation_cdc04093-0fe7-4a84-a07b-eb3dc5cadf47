// import Tiptap from "@/web/components/Tiptap";
import { Alert, AlertDescription } from '@repo/ui/components/alert';
import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/avatar';
import { Badge } from '@repo/ui/components/badge';
import { Button } from '@repo/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@repo/ui/components/card';
import { Input } from '@repo/ui/components/input';
import { Label } from '@repo/ui/components/label';
import { Progress } from '@repo/ui/components/progress';
import { RadioGroup, RadioGroupItem } from '@repo/ui/components/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { Separator } from '@repo/ui/components/separator';
import { Textarea } from '@repo/ui/components/textarea';
import { createFileRoute } from '@tanstack/react-router';
import {
  AlertCircle,
  ArrowLeft,
  ArrowRight,
  AtSign,
  Briefcase,
  Building,
  Building2,
  Calendar,
  Check,
  CheckCheck,
  CheckCircle2,
  Clock,
  Code,
  ExternalLink,
  Eye,
  FileText,
  Flag,
  FolderKanban,
  FolderOpen,
  Globe,
  Globe2,
  Hash,
  Inbox,
  Info,
  Layers,
  Lightbulb,
  Link2,
  Lock,
  Mail,
  Megaphone,
  MoreHorizontal,
  Palette,
  PartyPopper,
  Plus,
  Rocket,
  Send,
  Settings,
  Shield,
  Smile,
  Sparkles,
  Target,
  TrendingUp,
  Upload,
  UserCheck,
  UserPlus,
  Users,
  X,
  Zap,
} from 'lucide-react';
import { useState } from 'react';

// Types for invitations
interface Invitation {
  id: string;
  type: 'organization' | 'workspace' | 'project';
  entityName: string;
  entityDescription?: string;
  invitedBy: string;
  invitedByAvatar?: string;
  role: string;
  expiresAt: string;
  entityLogo?: string;
  memberCount?: number;
}

const Onboarding = () => {
  const [currentView, setCurrentView] = useState<'welcome' | 'invitations' | 'creation'>('welcome');
  const [currentStep, setCurrentStep] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  // const [showPassword, setShowPassword] = useState(false);
  const [inviteEmails, setInviteEmails] = useState(['']);
  const [uploadedLogo, setUploadedLogo] = useState<string | null>(null);
  const [animateConfetti, setAnimateConfetti] = useState(false);
  const [acceptedInvitations, setAcceptedInvitations] = useState<string[]>([]);
  const [declinedInvitations, setDeclinedInvitations] = useState<string[]>([]);

  // Mock user data
  const currentUser = {
    name: 'John Doe',
    email: '<EMAIL>',
  };

  // Mock invitations - in real app, this would come from API
  const [invitations] = useState<Invitation[]>([
    {
      id: '1',
      type: 'organization',
      entityName: 'Acme Corporation',
      entityDescription: 'Building the future of work',
      invitedBy: 'Sarah Chen',
      invitedByAvatar: '/api/placeholder/40/40',
      role: 'Member',
      expiresAt: '2024-02-01',
      entityLogo: '/api/placeholder/48/48',
      memberCount: 127,
    },
    {
      id: '2',
      type: 'workspace',
      entityName: 'Product Team',
      entityDescription: 'Where we build amazing products',
      invitedBy: 'Mike Johnson',
      invitedByAvatar: '/api/placeholder/40/40',
      role: 'Contributor',
      expiresAt: '2024-02-05',
      memberCount: 24,
    },
    // Note: Project invitations are only sent to users who already have workspace access
  ]);

  const [formData, setFormData] = useState({
    organization: {
      name: '',
      slug: '',
      description: '',
      website: '',
      logoUrl: '',
      billingEmail: '',
      billingAddress: '',
      size: '',
    },
    workspace: {
      name: '',
      slug: '',
      description: '',
      color: '#6366f1',
      visibility: 'private',
      type: '',
    },
    project: {
      name: '',
      key: '',
      description: '',
      color: '#10b981',
      visibility: 'private',
      status: 'planning',
      startDate: '',
      targetDate: '',
      methodology: '',
    },
  });

  const organizationSizes = [
    {
      value: '1-10',
      label: 'Just me',
      icon: Smile,
      description: 'Personal use',
    },
    {
      value: '11-50',
      label: 'Small team',
      icon: Users,
      description: '2-50 people',
    },
    {
      value: '51-200',
      label: 'Growing company',
      icon: TrendingUp,
      description: '51-200 people',
    },
    {
      value: '200+',
      label: 'Large organization',
      icon: Building,
      description: '200+ people',
    },
  ];

  const workspaceTypes = [
    {
      value: 'engineering',
      label: 'Engineering',
      icon: Code,
      description: 'Code, deploy, iterate',
    },
    {
      value: 'design',
      label: 'Design',
      icon: Palette,
      description: 'Create, prototype, ship',
    },
    {
      value: 'marketing',
      label: 'Marketing',
      icon: Megaphone,
      description: 'Plan, execute, measure',
    },
    {
      value: 'operations',
      label: 'Operations',
      icon: Settings,
      description: 'Optimize, scale, grow',
    },
    {
      value: 'other',
      label: 'Other',
      icon: MoreHorizontal,
      description: 'Custom workspace',
    },
  ];

  const methodologies = [
    {
      value: 'agile',
      label: 'Agile',
      icon: Zap,
      description: 'Iterative development',
    },
    {
      value: 'waterfall',
      label: 'Waterfall',
      icon: TrendingUp,
      description: 'Sequential phases',
    },
    {
      value: 'kanban',
      label: 'Kanban',
      icon: FolderKanban,
      description: 'Continuous flow',
    },
    {
      value: 'custom',
      label: 'Custom',
      icon: Sparkles,
      description: 'Your own way',
    },
  ];

  const colors = [
    { value: '#6366f1', label: 'Indigo', class: 'bg-indigo-500' },
    { value: '#8b5cf6', label: 'Purple', class: 'bg-purple-600' },
    { value: '#ec4899', label: 'Pink', class: 'bg-pink-500' },
    { value: '#f43f5e', label: 'Rose', class: 'bg-rose-500' },
    { value: '#3b82f6', label: 'Blue', class: 'bg-primary' },
    { value: '#06b6d4', label: 'Cyan', class: 'bg-cyan-500' },
    { value: '#10b981', label: 'Emerald', class: 'bg-emerald-500' },
    { value: '#f59e0b', label: 'Amber', class: 'bg-amber-500' },
  ];

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  };

  const handleInputChange = (section: keyof typeof formData, field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
        ...(field === 'name' && { slug: generateSlug(value) }),
      },
    }));
  };

  const generateProjectKey = (name: string) => {
    const words = name.split(' ');
    if (words.length === 1) {
      return name.substring(0, 3).toUpperCase();
    }
    return words
      .map((word) => word[0])
      .join('')
      .toUpperCase()
      .substring(0, 4);
  };

  const handleProjectNameChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      project: {
        ...prev.project,
        name: value,
        key: generateProjectKey(value),
      },
    }));
  };

  const handleAcceptInvitation = (invitationId: string) => {
    setAcceptedInvitations([...acceptedInvitations, invitationId]);
    // In real app, make API call here
  };

  const handleDeclineInvitation = (invitationId: string) => {
    setDeclinedInvitations([...declinedInvitations, invitationId]);
    // In real app, make API call here
  };

  const isInvitationProcessed = (invitationId: string) => {
    return acceptedInvitations.includes(invitationId) || declinedInvitations.includes(invitationId);
  };

  const allInvitationsProcessed = () => {
    return invitations.every((inv) => isInvitationProcessed(inv.id));
  };

  const hasAnyInvitations = invitations.length > 0;

  const addInviteEmail = () => {
    setInviteEmails([...inviteEmails, '']);
  };

  const updateInviteEmail = (index: number, value: string) => {
    const newEmails = [...inviteEmails];
    newEmails[index] = value;
    setInviteEmails(newEmails);
  };

  const removeInviteEmail = (index: number) => {
    setInviteEmails(inviteEmails.filter((_, i) => i !== index));
  };

  const nextStep = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      if (currentView === 'creation' && currentStep === 2) {
        // Skip project creation
        setCurrentStep(4);
      } else {
        setCurrentStep((prev) => prev + 1);
      }
      setIsTransitioning(false);
      if (currentStep === 4) {
        setTimeout(() => setAnimateConfetti(true), 300);
      }
    }, 300);
  };

  const prevStep = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      if (currentStep === 4 && formData.project.name === '') {
        // If skipped project, go back to workspace
        setCurrentStep(1);
      } else {
        setCurrentStep((prev) => prev - 1);
      }
      setIsTransitioning(false);
    }, 300);
  };

  const skipProjectCreation = () => {
    setCurrentStep(4); // Go to invite team step
  };

  const completeOnboarding = () => {
    alert('Welcome to WorkHub! 🎉');
  };

  const skipOnboarding = () => {
    alert('You can complete setup anytime from settings');
  };

  const getEntityIcon = (type: string) => {
    switch (type) {
      case 'organization':
        return Building2;
      case 'workspace':
        return Briefcase;
      case 'project':
        return FolderOpen;
      default:
        return Layers;
    }
  };

  const getEntityColor = (type: string) => {
    switch (type) {
      case 'organization':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/30';
      case 'workspace':
        return 'text-purple-600 bg-purple-100 dark:bg-purple-900/30';
      case 'project':
        return 'text-emerald-600 bg-emerald-100 dark:bg-emerald-900/30';
      default:
        return 'text-muted-foreground bg-muted';
    }
  };

  const progressPercentage = currentView === 'creation' ? ((currentStep + 1) / 5) * 100 : 0;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800">
      {/* Progress Bar */}
      {currentView === 'creation' && (
        <div className="fixed top-0 right-0 left-0 z-50 border-b bg-white/80 backdrop-blur-sm dark:bg-muted/80">
          <Progress className="h-1" value={progressPercentage} />
          <div className="mx-auto flex max-w-4xl items-center justify-between px-4 py-3">
            <div className="flex items-center gap-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-indigo-500 to-purple-500">
                <Layers className="h-5 w-5 text-white" />
              </div>
              <span className="text-muted-foreground text-sm">Step {currentStep + 1} of 5</span>
            </div>
            <div className="flex items-center gap-2">
              {currentStep > 0 && (
                <Button onClick={prevStep} size="sm" variant="ghost">
                  <ArrowLeft className="mr-1 h-4 w-4" />
                  Back
                </Button>
              )}
              <Button onClick={skipOnboarding} size="sm" variant="ghost">
                Skip setup
              </Button>
            </div>
          </div>
        </div>
      )}

      <div className="flex min-h-screen items-center justify-center p-4 pt-20">
        <div
          className={`w-full transition-all duration-500 ${
            currentView === 'welcome'
              ? 'max-w-4xl'
              : currentView === 'invitations'
                ? 'max-w-4xl'
                : currentStep === 5
                  ? 'max-w-3xl'
                  : 'max-w-2xl'
          }`}
        >
          {/* Welcome Screen */}
          {currentView === 'welcome' && (
            <div
              className={`transition-all duration-500 ${
                isTransitioning ? 'scale-95 opacity-0' : 'scale-100 opacity-100'
              }`}
            >
              <div className="mb-12 text-center">
                <div className="mb-6 inline-flex h-20 w-20 animate-pulse items-center justify-center rounded-2xl bg-gradient-to-br from-indigo-500 to-purple-500">
                  <Layers className="h-10 w-10 text-white" />
                </div>
                <h1 className="mb-4 bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text font-semibold text-2xl text-transparent">
                  Welcome to WorkHub, {currentUser.name.split(' ')[0]}!
                </h1>
                <p className="mx-auto max-w-2xl text-base text-muted-foreground">
                  {hasAnyInvitations
                    ? "You have some pending invitations waiting for you. Let's get you set up!"
                    : "Let's create your workspace and get your team onboard in just a few minutes."}
                </p>
              </div>

              {/* Quick Stats */}
              {hasAnyInvitations && (
                <div className="mx-auto mb-8 grid max-w-2xl gap-4 md:grid-cols-3">
                  <Card className="border-2 border-blue-200 dark:border-blue-900">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                          <Inbox className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                          <p className="font-bold text-2xl">{invitations.length}</p>
                          <p className="text-muted-foreground text-sm">Pending invites</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-2 border-purple-200 dark:border-purple-900">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-900/30">
                          <Users className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                        </div>
                        <div>
                          <p className="font-bold text-2xl">
                            {invitations.reduce((acc, inv) => acc + (inv.memberCount || 0), 0)}
                          </p>
                          <p className="text-muted-foreground text-sm">Total members</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-2 border-emerald-200 dark:border-emerald-900">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-emerald-100 dark:bg-emerald-900/30">
                          <Clock className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
                        </div>
                        <div>
                          <p className="font-bold text-2xl">5 min</p>
                          <p className="text-muted-foreground text-sm">To complete</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              <div className="flex flex-col justify-center gap-4 sm:flex-row">
                {hasAnyInvitations && (
                  <Button
                    className="group bg-gradient-to-r from-indigo-500 to-purple-500 px-8 text-white hover:from-indigo-600 hover:to-purple-600"
                    onClick={() => setCurrentView('invitations')}
                    size="lg"
                  >
                    View Invitations
                    <Badge className="ml-2 bg-white/20 text-white" variant="secondary">
                      {invitations.length}
                    </Badge>
                    <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                  </Button>
                )}
                <Button
                  className={
                    hasAnyInvitations
                      ? 'px-8'
                      : 'group bg-gradient-to-r from-indigo-500 to-purple-500 px-8 text-white hover:from-indigo-600 hover:to-purple-600'
                  }
                  onClick={() => setCurrentView('creation')}
                  size="lg"
                  variant={hasAnyInvitations ? 'outline' : 'default'}
                >
                  Create New Organization
                  {!hasAnyInvitations && (
                    <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                  )}
                </Button>
                {!hasAnyInvitations && (
                  <Button className="px-8" onClick={skipOnboarding} size="lg" variant="ghost">
                    I'll do this later
                  </Button>
                )}
              </div>
            </div>
          )}

          {/* Invitations View */}
          {currentView === 'invitations' && (
            <div
              className={`transition-all duration-500 ${
                isTransitioning ? 'translate-y-4 opacity-0' : 'translate-y-0 opacity-100'
              }`}
            >
              <div className="mb-8 text-center">
                <h2 className="mb-2 font-bold text-3xl">Your Invitations</h2>
                <p className="text-muted-foreground">
                  Accept invitations to join teams or create your own workspace
                </p>
              </div>

              <Alert className="mb-6 border-blue-200">
                <Info className="h-4 w-4" />
                <AlertDescription>
                  <strong>Access hierarchy:</strong> Organization → Workspace → Projects. Accepting
                  a workspace invitation automatically grants organization access.
                </AlertDescription>
              </Alert>

              <div className="mb-8 space-y-4">
                {invitations.map((invitation) => {
                  const Icon = getEntityIcon(invitation.type);
                  const isProcessed = isInvitationProcessed(invitation.id);
                  const isAccepted = acceptedInvitations.includes(invitation.id);
                  // const isDeclined = declinedInvitations.includes(invitation.id);

                  return (
                    <Card
                      className={`transition-all duration-300 ${
                        isProcessed ? 'opacity-60' : 'hover:-translate-y-1 hover:shadow-lg'
                      }`}
                      key={invitation.id}
                    >
                      <CardContent className="p-6">
                        <div className="flex items-start gap-4">
                          {/* Entity Logo/Icon */}
                          <div className="flex-shrink-0">
                            {invitation.entityLogo ? (
                              <img
                                alt={invitation.entityName}
                                className="h-12 w-12 rounded-lg object-cover"
                                src={invitation.entityLogo}
                              />
                            ) : (
                              <div
                                className={`flex h-12 w-12 items-center justify-center rounded-lg ${getEntityColor(invitation.type)}`}
                              >
                                <Icon className="h-6 w-6" />
                              </div>
                            )}
                          </div>

                          {/* Invitation Details */}
                          <div className="flex-1">
                            <div className="flex items-start justify-between">
                              <div>
                                <h3 className="flex items-center gap-2 font-semibold text-lg">
                                  {invitation.entityName}
                                  <Badge className="text-xs" variant="secondary">
                                    {invitation.type}
                                  </Badge>
                                </h3>
                                {invitation.entityDescription && (
                                  <p className="mt-1 text-muted-foreground text-sm">
                                    {invitation.entityDescription}
                                  </p>
                                )}
                                <div className="mt-3 flex items-center gap-4 text-muted-foreground text-sm">
                                  <div className="flex items-center gap-2">
                                    <Avatar className="h-5 w-5">
                                      <AvatarImage src={invitation.invitedByAvatar} />
                                      <AvatarFallback>{invitation.invitedBy[0]}</AvatarFallback>
                                    </Avatar>
                                    <span>Invited by {invitation.invitedBy}</span>
                                  </div>
                                  <Separator className="h-4" orientation="vertical" />
                                  <span>Role: {invitation.role}</span>
                                  {invitation.memberCount && (
                                    <>
                                      <Separator className="h-4" orientation="vertical" />
                                      <span>{invitation.memberCount} members</span>
                                    </>
                                  )}
                                </div>
                              </div>

                              {/* Action Buttons */}
                              <div className="flex items-center gap-2">
                                {isProcessed ? (
                                  <div className="flex items-center gap-2">
                                    {isAccepted ? (
                                      <Badge className="bg-green-500" variant="default">
                                        <CheckCircle2 className="mr-1 h-3 w-3" />
                                        Accepted
                                      </Badge>
                                    ) : (
                                      <Badge variant="secondary">
                                        <X className="mr-1 h-3 w-3" />
                                        Declined
                                      </Badge>
                                    )}
                                  </div>
                                ) : (
                                  <>
                                    <Button
                                      onClick={() => handleDeclineInvitation(invitation.id)}
                                      size="sm"
                                      variant="ghost"
                                    >
                                      Decline
                                    </Button>
                                    <Button
                                      className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white hover:from-indigo-600 hover:to-purple-600"
                                      onClick={() => handleAcceptInvitation(invitation.id)}
                                      size="sm"
                                    >
                                      Accept
                                    </Button>
                                  </>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>

              {/* Bottom Actions */}
              <div className="flex items-center justify-between">
                <Button onClick={() => setCurrentView('welcome')} variant="ghost">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>

                <div className="flex items-center gap-4">
                  {allInvitationsProcessed() && acceptedInvitations.length > 0 && (
                    <Button onClick={completeOnboarding} variant="outline">
                      Go to Dashboard
                      <ExternalLink className="ml-2 h-4 w-4" />
                    </Button>
                  )}
                  <Button className="group" onClick={() => setCurrentView('creation')}>
                    Create Organization
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Creation Flow - Organization */}
          {currentView === 'creation' && currentStep === 0 && (
            <Card
              className={`transition-all duration-500 ${
                isTransitioning ? 'translate-y-4 opacity-0' : 'translate-y-0 opacity-100'
              }`}
            >
              <CardHeader className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-indigo-500">
                    <Building2 className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl">Create your organization</CardTitle>
                    <CardDescription>This is your company's home in WorkHub</CardDescription>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                <div className="space-y-6">
                  {/* Logo Upload */}
                  <div className="flex items-center gap-6">
                    <div className="group relative">
                      <div className="flex h-24 w-24 items-center justify-center overflow-hidden rounded-2xl bg-gradient-to-br from-gray-100 to-gray-200 transition-all group-hover:shadow-lg dark:from-gray-800 dark:to-gray-700">
                        {uploadedLogo ? (
                          <img
                            alt="Logo"
                            className="h-full w-full object-cover"
                            src={uploadedLogo}
                          />
                        ) : (
                          <Upload className="h-8 w-8 text-muted-foreground" />
                        )}
                      </div>
                      <Button
                        className="-bottom-2 -right-2 absolute h-8 w-8 rounded-full p-0 opacity-0 transition-opacity group-hover:opacity-100"
                        onClick={() => setUploadedLogo('/api/placeholder/96/96')}
                        size="sm"
                        variant="secondary"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="flex-1">
                      <Label className="font-medium text-base">Organization logo</Label>
                      <p className="mt-1 text-muted-foreground text-sm">
                        Upload your company logo or choose one later
                      </p>
                    </div>
                  </div>

                  {/* Organization Name */}
                  <div className="space-y-3">
                    <Label
                      className="flex items-center gap-2 font-medium text-base"
                      htmlFor="org-name"
                    >
                      Organization name
                      <Badge className="text-xs" variant="secondary">
                        Required
                      </Badge>
                    </Label>
                    <div className="relative">
                      <Building className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        className="h-12 pl-10 text-base"
                        id="org-name"
                        onChange={(e) => handleInputChange('organization', 'name', e.target.value)}
                        placeholder="Acme Corporation"
                        value={formData.organization.name}
                      />
                    </div>
                    {formData.organization.name && (
                      <p className="slide-in-from-bottom-2 flex animate-in items-center gap-1 text-muted-foreground text-sm">
                        <Link2 className="h-3 w-3" />
                        workhub.com/{formData.organization.slug}
                      </p>
                    )}
                  </div>

                  {/* Organization Size */}
                  <div className="space-y-3">
                    <Label className="font-medium text-base">How big is your organization?</Label>
                    <div className="grid grid-cols-2 gap-3">
                      {organizationSizes.map((size) => {
                        const Icon = size.icon;
                        return (
                          <button
                            className={`rounded-xl border-2 p-4 text-left transition-all ${
                              formData.organization.size === size.value
                                ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-950/30'
                                : 'border-border hover:border-border dark:border-border'
                            }`}
                            key={size.value}
                            onClick={() => handleInputChange('organization', 'size', size.value)}
                            type="button"
                          >
                            <Icon
                              className={`mb-2 h-5 w-5 ${
                                formData.organization.size === size.value
                                  ? 'text-indigo-600 dark:text-indigo-400'
                                  : 'text-muted-foreground'
                              }`}
                            />
                            <div className="font-medium text-sm">{size.label}</div>
                            <div className="text-muted-foreground text-xs">{size.description}</div>
                          </button>
                        );
                      })}
                    </div>
                  </div>

                  {/* Additional Details */}
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label className="flex items-center gap-1" htmlFor="org-website">
                          <Globe className="h-4 w-4" />
                          Website
                        </Label>
                        <Input
                          id="org-website"
                          onChange={(e) =>
                            handleInputChange('organization', 'website', e.target.value)
                          }
                          placeholder="https://example.com"
                          type="url"
                          value={formData.organization.website}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label className="flex items-center gap-1" htmlFor="org-email">
                          <Mail className="h-4 w-4" />
                          Billing email
                        </Label>
                        <Input
                          id="org-email"
                          onChange={(e) =>
                            handleInputChange('organization', 'billingEmail', e.target.value)
                          }
                          placeholder="<EMAIL>"
                          type="email"
                          value={formData.organization.billingEmail}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label className="flex items-center gap-1" htmlFor="org-description">
                        <FileText className="h-4 w-4" />
                        Description
                      </Label>
                      <Textarea
                        id="org-description"
                        onChange={(e) =>
                          handleInputChange('organization', 'description', e.target.value)
                        }
                        placeholder="What does your organization do?"
                        rows={3}
                        value={formData.organization.description}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>

              <CardFooter className="flex justify-between border-t pt-6">
                <div className="flex items-center gap-2 text-muted-foreground text-sm">
                  <Lightbulb className="h-4 w-4" />
                  You can always change these settings later
                </div>
                <Button
                  className="group"
                  disabled={!(formData.organization.name && formData.organization.size)}
                  onClick={nextStep}
                >
                  Continue
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Button>
              </CardFooter>
            </Card>
          )}

          {/* Creation Flow - Workspace */}
          {currentView === 'creation' && currentStep === 1 && (
            <Card
              className={`transition-all duration-500 ${
                isTransitioning ? 'translate-y-4 opacity-0' : 'translate-y-0 opacity-100'
              }`}
            >
              <CardHeader className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-purple-500 to-pink-500">
                    <Briefcase className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl">Create your first workspace</CardTitle>
                    <CardDescription>Workspaces help teams stay organized</CardDescription>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                {/* Workspace Type */}
                <div className="space-y-3">
                  <Label className="font-medium text-base">What type of workspace is this?</Label>
                  <div className="grid gap-3">
                    {workspaceTypes.map((type) => {
                      const Icon = type.icon;
                      return (
                        <button
                          className={`flex items-start gap-3 rounded-xl border-2 p-4 text-left transition-all ${
                            formData.workspace.type === type.value
                              ? 'border-purple-500 bg-purple-50 dark:bg-purple-950/30'
                              : 'border-border hover:border-border dark:border-border'
                          }`}
                          key={type.value}
                          onClick={() => handleInputChange('workspace', 'type', type.value)}
                          type="button"
                        >
                          <Icon
                            className={`mt-0.5 h-5 w-5 flex-shrink-0 ${
                              formData.workspace.type === type.value
                                ? 'text-purple-600 dark:text-purple-400'
                                : 'text-muted-foreground'
                            }`}
                          />
                          <div>
                            <div className="mb-1 font-medium">{type.label}</div>
                            <div className="text-muted-foreground text-sm">{type.description}</div>
                          </div>
                        </button>
                      );
                    })}
                  </div>
                </div>

                {/* Workspace Name */}
                <div className="space-y-3">
                  <Label
                    className="flex items-center gap-2 font-medium text-base"
                    htmlFor="ws-name"
                  >
                    Workspace name
                    <Badge className="text-xs" variant="secondary">
                      Required
                    </Badge>
                  </Label>
                  <div className="relative">
                    <Hash className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      className="h-12 pl-10 text-base"
                      id="ws-name"
                      onChange={(e) => handleInputChange('workspace', 'name', e.target.value)}
                      placeholder={
                        formData.workspace.type === 'engineering'
                          ? 'Engineering Team'
                          : 'My Workspace'
                      }
                      value={formData.workspace.name}
                    />
                  </div>
                  {formData.workspace.name && (
                    <p className="slide-in-from-bottom-2 flex animate-in items-center gap-1 text-muted-foreground text-sm">
                      <Link2 className="h-3 w-3" />
                      {formData.organization.slug}/{formData.workspace.slug}
                    </p>
                  )}
                </div>

                {/* Color Selection */}
                <div className="space-y-3">
                  <Label className="flex items-center gap-2 font-medium text-base">
                    <Palette className="h-4 w-4" />
                    Choose a color theme
                  </Label>
                  <div className="flex flex-wrap gap-3">
                    {colors.map((color) => (
                      <button
                        className={`group relative h-12 w-12 rounded-xl transition-all ${color.class} ${
                          formData.workspace.color === color.value
                            ? 'scale-110 ring-2 ring-offset-2 ring-offset-background'
                            : 'hover:scale-110'
                        }`}
                        key={color.value}
                        onClick={() => handleInputChange('workspace', 'color', color.value)}
                        title={color.label}
                        type="button"
                      >
                        {formData.workspace.color === color.value && (
                          <Check className="absolute inset-0 m-auto h-5 w-5 text-white" />
                        )}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Visibility */}
                <div className="space-y-3">
                  <Label className="flex items-center gap-2 font-medium text-base">
                    <Eye className="h-4 w-4" />
                    Workspace visibility
                  </Label>
                  <RadioGroup
                    onValueChange={(value) => handleInputChange('workspace', 'visibility', value)}
                    value={formData.workspace.visibility}
                  >
                    <div className="grid gap-3">
                      <label
                        className={`flex cursor-pointer items-start gap-3 rounded-xl border-2 p-4 transition-all ${
                          formData.workspace.visibility === 'private'
                            ? 'border-purple-500 bg-purple-50 dark:bg-purple-950/30'
                            : 'border-border hover:border-border dark:border-border'
                        }`}
                        htmlFor="ws-private"
                      >
                        <RadioGroupItem className="mt-1" id="ws-private" value="private" />
                        <div className="flex-1">
                          <div className="mb-1 flex items-center gap-2 font-medium">
                            <Lock className="h-4 w-4" />
                            Private workspace
                          </div>
                          <div className="text-muted-foreground text-sm">
                            Only invited members can view and access
                          </div>
                        </div>
                      </label>

                      <label
                        className={`flex cursor-pointer items-start gap-3 rounded-xl border-2 p-4 transition-all ${
                          formData.workspace.visibility === 'public'
                            ? 'border-purple-500 bg-purple-50 dark:bg-purple-950/30'
                            : 'border-border hover:border-border dark:border-border'
                        }`}
                        htmlFor="ws-public"
                      >
                        <RadioGroupItem className="mt-1" id="ws-public" value="public" />
                        <div className="flex-1">
                          <div className="mb-1 flex items-center gap-2 font-medium">
                            <Globe2 className="h-4 w-4" />
                            Public to organization
                          </div>
                          <div className="text-muted-foreground text-sm">
                            Anyone in {formData.organization.name || 'your organization'} can
                            discover and join
                          </div>
                        </div>
                      </label>
                    </div>
                  </RadioGroup>
                </div>

                {/* Description */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-1" htmlFor="ws-description">
                    <FileText className="h-4 w-4" />
                    Description <span className="text-muted-foreground text-sm">(optional)</span>
                  </Label>
                  <Textarea
                    id="ws-description"
                    onChange={(e) => handleInputChange('workspace', 'description', e.target.value)}
                    placeholder="What's this workspace for?"
                    rows={3}
                    value={formData.workspace.description}
                  />
                </div>
              </CardContent>

              <CardFooter className="flex justify-between border-t pt-6">
                <div className="flex items-center gap-2 text-muted-foreground text-sm">
                  <Info className="h-4 w-4" />
                  Projects are optional
                </div>
                <Button
                  className="group"
                  disabled={!(formData.workspace.name && formData.workspace.type)}
                  onClick={nextStep}
                >
                  Continue
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Button>
              </CardFooter>
            </Card>
          )}

          {/* Creation Flow - Project (Optional) */}
          {currentView === 'creation' && currentStep === 2 && (
            <Card
              className={`transition-all duration-500 ${
                isTransitioning ? 'translate-y-4 opacity-0' : 'translate-y-0 opacity-100'
              }`}
            >
              <CardHeader className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-teal-500">
                    <FolderOpen className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl">Create your first project</CardTitle>
                    <CardDescription>
                      Projects help you organize work into focused initiatives
                    </CardDescription>
                  </div>
                </div>

                <Alert className="border-amber-200 bg-amber-50 dark:bg-amber-950/20">
                  <AlertCircle className="h-4 w-4 text-amber-600" />
                  <AlertDescription className="text-amber-800 dark:text-amber-200">
                    This step is optional. You can create projects later from your workspace.
                  </AlertDescription>
                </Alert>
              </CardHeader>

              <CardContent className="space-y-6">
                {/* Project Name and Key */}
                <div className="space-y-4">
                  <div className="grid grid-cols-3 gap-4">
                    <div className="col-span-2 space-y-2">
                      <Label className="font-medium text-base" htmlFor="proj-name">
                        Project name
                      </Label>
                      <Input
                        className="h-12 text-base"
                        id="proj-name"
                        onChange={(e) => handleProjectNameChange(e.target.value)}
                        placeholder="Website Redesign"
                        value={formData.project.name}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="font-medium text-base" htmlFor="proj-key">
                        Key
                      </Label>
                      <div className="relative">
                        <AtSign className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          className="h-12 pl-10 text-base uppercase"
                          id="proj-key"
                          maxLength={4}
                          onChange={(e) =>
                            handleInputChange('project', 'key', e.target.value.toUpperCase())
                          }
                          placeholder="WEB"
                          value={formData.project.key}
                        />
                      </div>
                    </div>
                  </div>
                  {formData.project.name && (
                    <p className="text-muted-foreground text-sm">
                      Work items will be labeled: {formData.project.key || 'KEY'}-1,{' '}
                      {formData.project.key || 'KEY'}-2, etc.
                    </p>
                  )}
                </div>

                {/* Methodology */}
                <div className="space-y-3">
                  <Label className="font-medium text-base">Project methodology</Label>
                  <div className="grid grid-cols-2 gap-3">
                    {methodologies.map((method) => {
                      const Icon = method.icon;
                      return (
                        <button
                          className={`rounded-xl border-2 p-4 text-left transition-all ${
                            formData.project.methodology === method.value
                              ? 'border-emerald-500 bg-emerald-50 dark:bg-emerald-950/30'
                              : 'border-border hover:border-border dark:border-border'
                          }`}
                          key={method.value}
                          onClick={() => handleInputChange('project', 'methodology', method.value)}
                          type="button"
                        >
                          <Icon
                            className={`mb-2 h-5 w-5 ${
                              formData.project.methodology === method.value
                                ? 'text-emerald-600 dark:text-emerald-400'
                                : 'text-muted-foreground'
                            }`}
                          />
                          <div className="font-medium text-sm">{method.label}</div>
                          <div className="text-muted-foreground text-xs">{method.description}</div>
                        </button>
                      );
                    })}
                  </div>
                </div>

                {/* Timeline */}
                <div className="space-y-3">
                  <Label className="flex items-center gap-2 font-medium text-base">
                    <Calendar className="h-4 w-4" />
                    Project timeline{' '}
                    <span className="text-muted-foreground text-sm">(optional)</span>
                  </Label>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm" htmlFor="proj-start">
                        Start date
                      </Label>
                      <div className="relative">
                        <Flag className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          className="pl-10"
                          id="proj-start"
                          onChange={(e) =>
                            handleInputChange('project', 'startDate', e.target.value)
                          }
                          type="date"
                          value={formData.project.startDate}
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm" htmlFor="proj-target">
                        Target date
                      </Label>
                      <div className="relative">
                        <Target className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          className="pl-10"
                          id="proj-target"
                          onChange={(e) =>
                            handleInputChange('project', 'targetDate', e.target.value)
                          }
                          type="date"
                          value={formData.project.targetDate}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Project Settings */}
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <Label className="font-medium text-base">Project color</Label>
                    <div className="flex flex-wrap gap-2">
                      {colors.slice(0, 4).map((color) => (
                        <button
                          className={`h-10 w-10 rounded-lg transition-all ${color.class} ${
                            formData.project.color === color.value
                              ? 'scale-110 ring-2 ring-offset-2 ring-offset-background'
                              : 'hover:scale-110'
                          }`}
                          key={color.value}
                          onClick={() => handleInputChange('project', 'color', color.value)}
                          type="button"
                        >
                          {formData.project.color === color.value && (
                            <Check className="mx-auto h-4 w-4 text-white" />
                          )}
                        </button>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label className="font-medium text-base">Visibility</Label>
                    <Select
                      onValueChange={(value) => handleInputChange('project', 'visibility', value)}
                      value={formData.project.visibility}
                    >
                      <SelectTrigger className="h-10">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="private">
                          <div className="flex items-center gap-2">
                            <Lock className="h-4 w-4" />
                            Private
                          </div>
                        </SelectItem>
                        <SelectItem value="public">
                          <div className="flex items-center gap-2">
                            <Globe2 className="h-4 w-4" />
                            Workspace visible
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Description */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-1" htmlFor="proj-description">
                    <FileText className="h-4 w-4" />
                    Description <span className="text-muted-foreground text-sm">(optional)</span>
                  </Label>
                  <Textarea
                    id="proj-description"
                    onChange={(e) => handleInputChange('project', 'description', e.target.value)}
                    placeholder="What's this project about?"
                    rows={3}
                    value={formData.project.description}
                  />
                </div>
              </CardContent>

              <CardFooter className="flex justify-between border-t pt-6">
                <Button
                  className="text-muted-foreground"
                  onClick={skipProjectCreation}
                  variant="ghost"
                >
                  Skip this step
                </Button>
                <Button
                  className="group"
                  disabled={
                    !(
                      formData.project.name ||
                      (formData.project.name && formData.project.methodology)
                    )
                  }
                  onClick={nextStep}
                >
                  Continue
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Button>
              </CardFooter>
            </Card>
          )}

          {/* Skip placeholder for step 3 - it's handled by the skip logic */}

          {/* Creation Flow - Invite Team */}
          {currentView === 'creation' && currentStep === 4 && (
            <Card
              className={`transition-all duration-500 ${
                isTransitioning ? 'translate-y-4 opacity-0' : 'translate-y-0 opacity-100'
              }`}
            >
              <CardHeader className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-amber-500 to-orange-500">
                    <Users className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl">Invite your team</CardTitle>
                    <CardDescription>Great work happens together</CardDescription>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <Label className="font-medium text-base">
                    Invite people to {formData.workspace.name || 'your workspace'}
                  </Label>

                  {inviteEmails.map((email, index) => (
                    <div className="slide-in-from-bottom-2 flex animate-in gap-2" key={index}>
                      <div className="relative flex-1">
                        <UserPlus className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          className="h-12 pl-10"
                          onChange={(e) => updateInviteEmail(index, e.target.value)}
                          placeholder="<EMAIL>"
                          type="email"
                          value={email}
                        />
                      </div>
                      {inviteEmails.length > 1 && (
                        <Button
                          className="h-12 w-12"
                          onClick={() => removeInviteEmail(index)}
                          size="icon"
                          variant="ghost"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  ))}

                  <Button className="w-full" onClick={addInviteEmail} variant="outline">
                    <Plus className="mr-2 h-4 w-4" />
                    Add another
                  </Button>
                </div>

                <div className="space-y-4">
                  <div className="space-y-3 rounded-xl bg-blue-50 p-4 dark:bg-blue-950/20">
                    <div className="flex items-start gap-3">
                      <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/50">
                        <Send className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium text-sm">Workspace invitations</p>
                        <p className="text-muted-foreground text-sm">
                          Team members will join {formData.workspace.name} and automatically get
                          access to {formData.organization.name}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3 rounded-xl bg-purple-50 p-4 dark:bg-purple-950/20">
                    <div className="flex items-start gap-3">
                      <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-900/50">
                        <Shield className="h-4 w-4 text-purple-600" />
                      </div>
                      <div>
                        <p className="font-medium text-sm">Access hierarchy</p>
                        <p className="text-muted-foreground text-sm">
                          Organization → Workspace → Projects. Users need workspace access before
                          joining projects.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3 rounded-xl bg-emerald-50 p-4 dark:bg-emerald-950/20">
                    <div className="flex items-start gap-3">
                      <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-lg bg-emerald-100 dark:bg-emerald-900/50">
                        <UserCheck className="h-4 w-4 text-emerald-600" />
                      </div>
                      <div>
                        <p className="font-medium text-sm">Default role</p>
                        <p className="text-muted-foreground text-sm">
                          Invited members will join as Contributors. You can change roles later.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>

              <CardFooter className="flex justify-between border-t pt-6">
                <Button onClick={nextStep} variant="ghost">
                  Skip for now
                </Button>
                <Button className="group" onClick={nextStep}>
                  {inviteEmails.filter((e) => e).length > 0 ? 'Send invites' : 'Continue'}
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Button>
              </CardFooter>
            </Card>
          )}

          {/* Success Screen */}
          {currentView === 'creation' && currentStep === 5 && (
            <div
              className={`space-y-8 text-center transition-all duration-500 ${
                isTransitioning ? 'scale-95 opacity-0' : 'scale-100 opacity-100'
              }`}
            >
              {animateConfetti && (
                <div className="pointer-events-none fixed inset-0">
                  {[...new Array(50)].map((_, i) => (
                    <div
                      className="absolute animate-bounce"
                      key={i}
                      style={{
                        left: `${Math.random() * 100}%`,
                        top: '-20px',
                        animationDelay: `${Math.random() * 2}s`,
                        animationDuration: `${2 + Math.random() * 2}s`,
                      }}
                    >
                      <PartyPopper
                        className={`h-6 w-6 ${
                          [
                            'text-indigo-500',
                            'text-purple-500',
                            'text-pink-500',
                            'text-amber-500',
                            'text-emerald-500',
                          ][Math.floor(Math.random() * 5)]
                        }`}
                      />
                    </div>
                  ))}
                </div>
              )}

              <div className="space-y-6">
                <div className="inline-flex h-24 w-24 animate-pulse items-center justify-center rounded-full bg-gradient-to-br from-green-500 to-emerald-500">
                  <CheckCircle2 className="h-12 w-12 text-white" />
                </div>

                <div className="space-y-3">
                  <h1 className="font-semibold text-2xl">Welcome to WorkHub! 🎉</h1>
                  <p className="mx-auto max-w-md text-base text-muted-foreground">
                    {formData.organization.name} is all set up. Let's start building something
                    amazing.
                  </p>
                </div>
              </div>

              <Card className="mx-auto max-w-md text-left">
                <CardHeader>
                  <CardTitle className="text-lg">What we created</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                        <Building2 className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <p className="font-medium">{formData.organization.name}</p>
                        <p className="text-muted-foreground text-sm">
                          /{formData.organization.slug}
                        </p>
                      </div>
                      <CheckCheck className="ml-auto h-5 w-5 text-green-500" />
                    </div>

                    <div className="flex items-center gap-3">
                      <div
                        className="flex h-10 w-10 items-center justify-center rounded-lg"
                        style={{
                          backgroundColor: `${formData.workspace.color}20`,
                        }}
                      >
                        <Briefcase
                          className="h-5 w-5"
                          style={{ color: formData.workspace.color }}
                        />
                      </div>
                      <div>
                        <p className="font-medium">{formData.workspace.name}</p>
                        <p className="text-muted-foreground text-sm">
                          {formData.workspace.type} workspace
                        </p>
                      </div>
                      <CheckCheck className="ml-auto h-5 w-5 text-green-500" />
                    </div>

                    {formData.project.name && (
                      <div className="flex items-center gap-3">
                        <div
                          className="flex h-10 w-10 items-center justify-center rounded-lg"
                          style={{
                            backgroundColor: `${formData.project.color}20`,
                          }}
                        >
                          <FolderOpen
                            className="h-5 w-5"
                            style={{ color: formData.project.color }}
                          />
                        </div>
                        <div>
                          <p className="font-medium">{formData.project.name}</p>
                          <p className="text-muted-foreground text-sm">
                            {formData.project.key} • {formData.project.methodology}
                          </p>
                        </div>
                        <CheckCheck className="ml-auto h-5 w-5 text-green-500" />
                      </div>
                    )}

                    {acceptedInvitations.length > 0 && (
                      <>
                        <Separator />
                        <div>
                          <p className="mb-2 text-muted-foreground text-sm">Also joined:</p>
                          <div className="space-y-2">
                            {invitations
                              .filter((inv) => acceptedInvitations.includes(inv.id))
                              .map((inv) => (
                                <div className="flex items-center gap-2 text-sm" key={inv.id}>
                                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                                  <span>{inv.entityName}</span>
                                  <Badge className="text-xs" variant="secondary">
                                    {inv.type}
                                  </Badge>
                                </div>
                              ))}
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>

              <div className="space-y-4">
                <Button
                  className="group bg-gradient-to-r from-indigo-500 to-purple-500 px-8 text-white hover:from-indigo-600 hover:to-purple-600"
                  onClick={completeOnboarding}
                  size="lg"
                >
                  <Rocket className="mr-2 h-5 w-5" />
                  Go to Dashboard
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Button>

                <p className="text-muted-foreground text-sm">
                  Press <kbd className="rounded bg-muted px-2 py-1 text-xs">⌘K</kbd> anytime for
                  quick actions
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Onboarding;
export const Route = createFileRoute('/__mainLayout/tiptap')({
  component: Onboarding,
});
