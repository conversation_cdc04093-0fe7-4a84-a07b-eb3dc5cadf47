'use client';

import { But<PERSON> } from '@repo/ui/components/button';
import { ScrollArea } from '@repo/ui/components/scroll-area';
import { cn } from '@repo/ui/lib/utils';
import { createFileRoute, Link, Outlet, useRouter } from '@tanstack/react-router';
import { Activity, Flag, Layers, Settings, Tag, Users, Workflow } from 'lucide-react';
import { useEffect, useState } from 'react';

export const Route = createFileRoute('/__mainLayout/settings')({
  component: SettingsLayout,
});

const sidebarNavItems = [
  {
    title: 'General',
    href: '/settings',
    icon: Settings,
    // description: "Basic application settings",
  },
  {
    title: 'Status Categories',
    href: '/settings/status-categories',
    icon: Tag,
    // description: "High-level status groupings",
  },
  {
    title: 'Priorities',
    href: '/settings/priority',
    icon: Flag,
    // description: "Priority levels for work items",
  },
  {
    title: 'Work Item Types',
    href: '/settings/work-item-type',
    icon: Layers,
    // description: "Different types of work items",
  },
  {
    title: 'Statuses',
    href: '/settings/status',
    icon: Activity,
    // description: "Workflow statuses",
  },
  // {
  //   title: "Status Types",
  //   href: "/settings/status-type",
  //   icon: Grid3X3,
  //   // description: "Status type categories",
  // },
  {
    title: 'Roles',
    href: '/settings/role',
    icon: Users,
    // description: "User roles and permissions",
  },
  {
    title: 'Workflows',
    href: '/settings/work-flow',
    icon: Workflow,
    // description: "Workflow configurations",
  },
];

function SettingsLayout() {
  return (
    <div className="flex h-screen bg-background">
      {/* Enhanced Sidebar */}
      <div className="w-64 border-r bg-card">
        <div className="p-6 pb-0">
          <h1 className="font-semibold text-2xl">Settings</h1>
          <p className="text-muted-foreground text-sm">Manage your organization</p>
        </div>
        <ScrollArea className="h-[calc(100vh-120px)]">
          <div className="space-y-1 p-4">
            <SidebarNav items={sidebarNavItems} />
          </div>
        </ScrollArea>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-6">
            <Outlet />
          </div>
        </ScrollArea>
      </div>
    </div>
  );
}

function SidebarNav({ items }: { items: typeof sidebarNavItems }) {
  const router = useRouter();
  const [currentUrl, setCurrentUrl] = useState(router.state.location.pathname);

  useEffect(() => {
    const unsubscribe = router.subscribe('onLoad', (state) => {
      setCurrentUrl(state.toLocation.pathname);
    });
    return () => unsubscribe();
  }, [router]);

  return (
    <>
      {items.map((item) => {
        const Icon = item.icon;
        const isActive = currentUrl === item.href;

        return (
          <Link key={item.href} to={item.href}>
            <Button
              className={cn(
                'h-auto w-full justify-start p-3',
                isActive ? 'bg-muted hover:bg-muted' : 'hover:bg-transparent hover:underline',
              )}
              variant="ghost"
            >
              <Icon className="mr-3 h-4 w-4 flex-shrink-0" />
              <div className="text-left">
                <div className="font-medium text-sm">{item.title}</div>
                <div className="text-muted-foreground text-xs">{/* {item.description} */}</div>
              </div>
            </Button>
          </Link>
        );
      })}
    </>
  );
}
