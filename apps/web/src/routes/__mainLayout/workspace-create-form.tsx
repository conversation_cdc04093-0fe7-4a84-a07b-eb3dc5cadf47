import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/ui/components/card';
import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { WorkspaceForm } from '@/web/components/workspace/workspace-form';

function WorkspaceCreatePage() {
  const navigate = useNavigate();

  const handleSubmit = () => {
    navigate({ to: '/' });
  };

  const handleCancel = () => {
    navigate({ to: '/' });
  };

  return (
    <div className="container mx-auto p-10">
      <Card>
        <CardHeader>
          <CardTitle>Create Your Workspace</CardTitle>
          <CardDescription>
            Let's set up your workspace. This will only take a minute.
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <WorkspaceForm onCancel={handleCancel} onSubmit={handleSubmit} />
        </CardContent>
      </Card>
    </div>
  );
}

export const Route = createFileRoute('/__mainLayout/workspace-create-form')({
  component: WorkspaceCreatePage,
});
