import { createFileRoute, redirect } from '@tanstack/react-router';
// import { Onboarding } from "@/web/components/onboarding";
import SparkOnboardingFlow from '@/web/components/onboarding/spark-onboarding-flow';
import { isAuthenticated } from '@/web/utils';

export const Route = createFileRoute('/__mainLayout/')({
  beforeLoad: () => {
    if (!isAuthenticated()) {
      throw redirect({
        to: '/login',
      });
    }
  },
  component: SparkOnboardingFlow,
});
