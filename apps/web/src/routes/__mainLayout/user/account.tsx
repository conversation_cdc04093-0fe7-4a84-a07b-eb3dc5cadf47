import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/avatar';
import { Button } from '@repo/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/ui/components/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
import { PhoneInput } from '@repo/ui/components/phone-input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { Textarea } from '@repo/ui/components/textarea';
import { Toaster } from '@repo/ui/components/toaster';
import { useToast } from '@repo/ui/components/use-toast';
import { useForm } from '@tanstack/react-form';
import { createFileRoute } from '@tanstack/react-router';
import { Camera, Save } from 'lucide-react';
import { useEffect, useState } from 'react';
import { usePostApiV1AuthChangePassword } from '@/web/services/auth';
import axiosInstance from '@/web/services/axiosClient';
import { useGetApiV1UsersId, usePatchApiV1UsersId } from '@/web/services/users';
import { useRootStore } from '@/web/store/store';
import Man from '../../../assets/man.png';
import { ThemeVariantSelector } from '../../../components/theme-variant-selector';

// Common languages
const LANGUAGES = [
  { value: 'en', label: 'English' },
  { value: 'es', label: 'Spanish' },
  { value: 'fr', label: 'French' },
  { value: 'de', label: 'German' },
  { value: 'pt', label: 'Portuguese' },
  { value: 'it', label: 'Italian' },
  { value: 'zh', label: 'Chinese' },
  { value: 'ja', label: 'Japanese' },
];

// Common timezones
const TIMEZONES = [
  { value: 'UTC', label: 'UTC' },
  { value: 'America/New_York', label: 'Eastern Time (US & Canada)' },
  { value: 'America/Chicago', label: 'Central Time (US & Canada)' },
  { value: 'America/Denver', label: 'Mountain Time (US & Canada)' },
  { value: 'America/Los_Angeles', label: 'Pacific Time (US & Canada)' },
  { value: 'Europe/London', label: 'London' },
  { value: 'Europe/Paris', label: 'Paris' },
  { value: 'Asia/Tokyo', label: 'Tokyo' },
  { value: 'Asia/Shanghai', label: 'Beijing, Shanghai' },
  { value: 'Asia/Kolkata', label: 'India Standard Time' },
  { value: 'Australia/Sydney', label: 'Sydney' },
];

// Date formats
const DATE_FORMATS = [
  { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY' },
  { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY' },
  { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD' },
  { value: 'DD.MM.YYYY', label: 'DD.MM.YYYY' },
];

// Time formats
const TIME_FORMATS = [
  { value: '12h', label: '12 Hour (AM/PM)' },
  { value: '24h', label: '24 Hour' },
];

export const Route = createFileRoute('/__mainLayout/user/account')({
  component: RouteComponent,
});

function RouteComponent() {
  const [isUploading, setIsUploading] = useState(false);
  const [profileImageUrl, setProfileImageUrl] = useState<string | null>(null);
  const { toast } = useToast();

  // Get current user from store
  const currentUser = useRootStore((state) => state.currentUser);
  const currentUserId = currentUser?.id;

  // Fetch current user data
  const { data, isLoading } = useGetApiV1UsersId(currentUserId || '', {
    query: {
      enabled: !!currentUserId,
    },
  });

  // User update mutation
  const updateUserMutation = usePatchApiV1UsersId();

  // Password change mutation
  const changePasswordMutation = usePostApiV1AuthChangePassword();

  // Get the user from the API response
  const user = data?.data;

  // Initialize profile image URL from user data
  useEffect(() => {
    if (user?.avatarUrl) {
      setProfileImageUrl(user.avatarUrl);
    }
  }, [user?.avatarUrl]);

  // Initialize form with dynamic default values
  const personalInfoForm = useForm({
    defaultValues: user
      ? {
          displayName: user.displayName || '',
          email: user.email || '',
          firstName: user.firstName || '',
          lastName: user.lastName || '',
          language: user.language || 'en',
          phone: user.phone || '',
          timezone: user.timezone || 'UTC',
          jobTitle: user.jobTitle || '',
          department: user.department || '',
          bio: user.bio || '',
          dateFormat: user.dateFormat || 'MM/DD/YYYY',
          timeFormat: user.timeFormat || '12h',
        }
      : {
          displayName: '',
          email: '',
          firstName: '',
          lastName: '',
          language: 'en',
          phone: '',
          timezone: 'UTC',
          jobTitle: '',
          department: '',
          bio: '',
          dateFormat: 'MM/DD/YYYY',
          timeFormat: '12h',
        },
    onSubmit: async ({ value }) => {
      if (!currentUserId) {
        return;
      }

      try {
        await updateUserMutation.mutateAsync({
          id: currentUserId,
          data: value,
        });
        toast({
          title: 'Profile updated',
          description: 'Your profile has been updated successfully.',
        });
      } catch {
        toast({
          title: 'Update failed',
          description: 'Failed to update profile. Please try again.',
          variant: 'destructive',
        });
      }
    },
  });

  const passwordResetForm = useForm({
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
    onSubmit: async ({ value }) => {
      // Validate passwords match
      if (value.newPassword !== value.confirmPassword) {
        toast({
          title: 'Validation error',
          description: "Passwords don't match",
          variant: 'destructive',
        });
        return;
      }

      // Validate password length
      if (value.newPassword.length < 8) {
        toast({
          title: 'Validation error',
          description: 'Password must be at least 8 characters',
          variant: 'destructive',
        });
        return;
      }

      try {
        await changePasswordMutation.mutateAsync({
          data: {
            currentPassword: value.currentPassword,
            newPassword: value.newPassword,
          },
        });

        toast({
          title: 'Password changed',
          description: 'Your password has been changed successfully.',
        });

        // Reset form
        passwordResetForm.reset();
      } catch (error) {
        let errorMessage: string | undefined;
        if (error && typeof error === 'object' && 'response' in error) {
          const err = error as { response?: { data?: { message?: string } } };
          errorMessage = err.response?.data?.message;
        }
        toast({
          title: 'Password change failed',
          description:
            errorMessage ||
            'Failed to change password. Please check your current password and try again.',
          variant: 'destructive',
        });
      }
    },
  });

  const requiredFields = [
    'displayName',
    'email',
    'firstName',
    'lastName',
    'language',
    'timezone',
    'dateFormat',
    'timeFormat',
  ];

  // Handle profile image upload
  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!(file && currentUserId)) {
      return;
    }

    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await axiosInstance.post('/api/v1/attachments/upload', formData, {
        params: {
          entityType: 'user',
          entityId: currentUserId,
        },
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data?.url) {
        setProfileImageUrl(response.data.url);

        // Update user's avatar URL
        await updateUserMutation.mutateAsync({
          id: currentUserId,
          data: { avatarUrl: response.data.url },
        });

        toast({
          title: 'Profile image updated',
          description: 'Your profile image has been updated successfully.',
        });
      }
    } catch {
      toast({
        title: 'Upload failed',
        description: 'Failed to upload profile image. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
    }
  };

  // Get user initials for avatar fallback
  const getUserInitials = () => {
    const firstName = personalInfoForm.state.values.firstName || user?.firstName || '';
    const lastName = personalInfoForm.state.values.lastName || user?.lastName || '';
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase() || 'U';
  };

  // Handle loading and error states
  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-muted py-8">
        <div>Loading...</div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-muted py-8">
        <div>Error loading user data. Please try again.</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-muted py-8">
      <div className="mx-auto max-w-4xl px-4">
        <div className="mb-8">
          <h1 className="font-semibold text-2xl text-foreground">Edit Profile</h1>
          <p className="mt-2 text-muted-foreground">
            Update your personal information and preferences
          </p>
        </div>

        <Form>
          <div className="space-y-6">
            {/* Profile Picture Section */}
            <Card>
              <CardHeader>
                <CardTitle>Profile Picture</CardTitle>
                <CardDescription>Click on your photo to update it</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-6">
                  <div className="group relative">
                    <label className="cursor-pointer" htmlFor="profile-upload">
                      <Avatar className="h-24 w-24">
                        <AvatarImage
                          alt="Profile"
                          src={profileImageUrl || user?.avatarUrl || Man}
                        />
                        <AvatarFallback className="text-lg">{getUserInitials()}</AvatarFallback>
                      </Avatar>
                      <div className="absolute inset-0 flex items-center justify-center rounded-full bg-primary bg-opacity-50 opacity-0 transition-opacity group-hover:opacity-100">
                        <Camera className="h-6 w-6 text-primary-foreground" />
                      </div>
                    </label>
                    <input
                      accept="image/*"
                      className="hidden"
                      disabled={isUploading}
                      id="profile-upload"
                      onChange={handleImageUpload}
                      type="file"
                    />
                  </div>
                  <div className="space-y-1">
                    <h3 className="font-semibold text-foreground text-lg">
                      {personalInfoForm.state.values.displayName || user.displayName}
                    </h3>
                    <p className="text-muted-foreground text-sm">{user.email}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
                <CardDescription>Your personal details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <personalInfoForm.Field name="firstName">
                    {(field) => (
                      <FormField field={field} required={requiredFields.includes('firstName')}>
                        <FormItem>
                          <FormLabel>First Name</FormLabel>
                          <FormControl>
                            <Input
                              onBlur={field.handleBlur}
                              onChange={(e) => field.handleChange(e.target.value)}
                              placeholder="John"
                              value={field.state.value}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      </FormField>
                    )}
                  </personalInfoForm.Field>

                  <personalInfoForm.Field name="lastName">
                    {(field) => (
                      <FormField field={field} required={requiredFields.includes('lastName')}>
                        <FormItem>
                          <FormLabel>Last Name</FormLabel>
                          <FormControl>
                            <Input
                              onBlur={field.handleBlur}
                              onChange={(e) => field.handleChange(e.target.value)}
                              placeholder="Doe"
                              value={field.state.value}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      </FormField>
                    )}
                  </personalInfoForm.Field>
                </div>

                <personalInfoForm.Field name="displayName">
                  {(field) => (
                    <FormField field={field} required={requiredFields.includes('displayName')}>
                      <FormItem>
                        <FormLabel>Display Name</FormLabel>
                        <FormControl>
                          <Input
                            onBlur={field.handleBlur}
                            onChange={(e) => field.handleChange(e.target.value)}
                            placeholder="John Doe"
                            value={field.state.value}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>
                  )}
                </personalInfoForm.Field>

                <personalInfoForm.Field name="email">
                  {(field) => (
                    <FormField field={field} required={requiredFields.includes('email')}>
                      <FormItem>
                        <FormLabel>Email Address</FormLabel>
                        <FormControl>
                          <Input
                            disabled
                            onBlur={field.handleBlur}
                            onChange={(e) => field.handleChange(e.target.value)}
                            placeholder="<EMAIL>"
                            type="email"
                            value={field.state.value}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>
                  )}
                </personalInfoForm.Field>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
                <CardDescription>How people can reach you</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <personalInfoForm.Field
                  name="phone"
                  validators={{
                    onChange: ({ value }) => {
                      if (value && value.length > 0 && value.length < 10) {
                        return 'Phone number must be at least 10 characters';
                      }
                      return;
                    },
                  }}
                >
                  {(field) => (
                    <FormField field={field} required={requiredFields.includes('phone')}>
                      <FormItem>
                        <FormLabel>Phone Number</FormLabel>
                        <FormControl>
                          <PhoneInput
                            defaultCountry="US"
                            onBlur={field.handleBlur}
                            onChange={(value) => field.handleChange(value || '')}
                            placeholder="+****************"
                            value={field.state.value || ''}
                          />
                        </FormControl>
                        <FormMessage />
                        {field.state.meta.errors && field.state.meta.errors.length > 0 && (
                          <p className="text-red-500 text-sm">{field.state.meta.errors[0]}</p>
                        )}
                      </FormItem>
                    </FormField>
                  )}
                </personalInfoForm.Field>
              </CardContent>
            </Card>

            {/* Professional Information */}
            <Card>
              <CardHeader>
                <CardTitle>Professional Information</CardTitle>
                <CardDescription>Your work details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <personalInfoForm.Field name="jobTitle">
                    {(field) => (
                      <FormField field={field} required={requiredFields.includes('jobTitle')}>
                        <FormItem>
                          <FormLabel>Job Title</FormLabel>
                          <FormControl>
                            <Input
                              onBlur={field.handleBlur}
                              onChange={(e) => field.handleChange(e.target.value)}
                              placeholder="Software Engineer"
                              value={field.state.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      </FormField>
                    )}
                  </personalInfoForm.Field>

                  <personalInfoForm.Field name="department">
                    {(field) => (
                      <FormField field={field} required={requiredFields.includes('department')}>
                        <FormItem>
                          <FormLabel>Department</FormLabel>
                          <FormControl>
                            <Input
                              onBlur={field.handleBlur}
                              onChange={(e) => field.handleChange(e.target.value)}
                              placeholder="Engineering"
                              value={field.state.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      </FormField>
                    )}
                  </personalInfoForm.Field>
                </div>

                <personalInfoForm.Field name="bio">
                  {(field) => (
                    <FormField field={field} required={requiredFields.includes('bio')}>
                      <FormItem>
                        <FormLabel>Bio</FormLabel>
                        <FormControl>
                          <Textarea
                            className="min-h-[100px]"
                            onBlur={field.handleBlur}
                            onChange={(e) => field.handleChange(e.target.value)}
                            placeholder="Tell us about yourself..."
                            value={field.state.value || ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>
                  )}
                </personalInfoForm.Field>
              </CardContent>
            </Card>

            {/* Preferences */}
            <Card>
              <CardHeader>
                <CardTitle>Preferences</CardTitle>
                <CardDescription>Customize your experience</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <personalInfoForm.Field name="language">
                    {(field) => (
                      <FormField field={field} required={requiredFields.includes('language')}>
                        <FormItem>
                          <FormLabel>Language</FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={(value) => field.handleChange(value)}
                              value={field.state.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select language" />
                              </SelectTrigger>
                              <SelectContent>
                                {LANGUAGES.map((lang) => (
                                  <SelectItem key={lang.value} value={lang.value}>
                                    {lang.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      </FormField>
                    )}
                  </personalInfoForm.Field>

                  <personalInfoForm.Field name="timezone">
                    {(field) => (
                      <FormField field={field} required={requiredFields.includes('timezone')}>
                        <FormItem>
                          <FormLabel>Timezone</FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={(value) => field.handleChange(value)}
                              value={field.state.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select timezone" />
                              </SelectTrigger>
                              <SelectContent>
                                {TIMEZONES.map((tz) => (
                                  <SelectItem key={tz.value} value={tz.value}>
                                    {tz.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      </FormField>
                    )}
                  </personalInfoForm.Field>
                </div>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <personalInfoForm.Field name="dateFormat">
                    {(field) => (
                      <FormField field={field} required={requiredFields.includes('dateFormat')}>
                        <FormItem>
                          <FormLabel>Date Format</FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={(value) => field.handleChange(value)}
                              value={field.state.value}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {DATE_FORMATS.map((format) => (
                                  <SelectItem key={format.value} value={format.value}>
                                    {format.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      </FormField>
                    )}
                  </personalInfoForm.Field>

                  <personalInfoForm.Field name="timeFormat">
                    {(field) => (
                      <FormField field={field} required={requiredFields.includes('timeFormat')}>
                        <FormItem>
                          <FormLabel>Time Format</FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={(value) => field.handleChange(value)}
                              value={field.state.value}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {TIME_FORMATS.map((format) => (
                                  <SelectItem key={format.value} value={format.value}>
                                    {format.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      </FormField>
                    )}
                  </personalInfoForm.Field>
                </div>

                <ThemeVariantSelector />
              </CardContent>
            </Card>

            {/* Security */}
            <Card>
              <CardHeader>
                <CardTitle>Security</CardTitle>
                <CardDescription>Manage your account security</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <passwordResetForm.Field name="currentPassword">
                  {(field) => (
                    <FormField field={field} required={true}>
                      <FormItem>
                        <FormLabel>Current Password</FormLabel>
                        <FormControl>
                          <Input
                            onBlur={field.handleBlur}
                            onChange={(e) => field.handleChange(e.target.value)}
                            placeholder="Enter current password"
                            type="password"
                            value={field.state.value}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>
                  )}
                </passwordResetForm.Field>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <passwordResetForm.Field name="newPassword">
                    {(field) => (
                      <FormField field={field} required={true}>
                        <FormItem>
                          <FormLabel>New Password</FormLabel>
                          <FormControl>
                            <Input
                              onBlur={field.handleBlur}
                              onChange={(e) => field.handleChange(e.target.value)}
                              placeholder="Enter new password"
                              type="password"
                              value={field.state.value}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      </FormField>
                    )}
                  </passwordResetForm.Field>

                  <passwordResetForm.Field name="confirmPassword">
                    {(field) => (
                      <FormField field={field} required={true}>
                        <FormItem>
                          <FormLabel>Confirm New Password</FormLabel>
                          <FormControl>
                            <Input
                              onBlur={field.handleBlur}
                              onChange={(e) => field.handleChange(e.target.value)}
                              placeholder="Confirm new password"
                              type="password"
                              value={field.state.value}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      </FormField>
                    )}
                  </passwordResetForm.Field>
                </div>

                <div className="pt-2">
                  <Button
                    disabled={changePasswordMutation.isPending}
                    onClick={() => passwordResetForm.handleSubmit()}
                    size="sm"
                    type="button"
                    variant="outline"
                  >
                    {changePasswordMutation.isPending ? 'Updating Password...' : 'Update Password'}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="flex justify-end pt-6">
              <Button
                className="flex items-center space-x-2"
                disabled={updateUserMutation.isPending}
                onClick={() => personalInfoForm.handleSubmit()}
                type="button"
              >
                <Save className="h-4 w-4" />
                <span>{updateUserMutation.isPending ? 'Saving...' : 'Save Changes'}</span>
              </Button>
            </div>
          </div>
        </Form>
      </div>
      <Toaster />
    </div>
  );
}
