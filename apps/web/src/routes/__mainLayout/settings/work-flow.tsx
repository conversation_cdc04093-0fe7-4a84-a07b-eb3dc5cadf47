import { Dialog, DialogContent } from '@repo/ui/components/dialog';
import { createFileRoute, useLocation, useNavigate } from '@tanstack/react-router';
import { useEffect, useState } from 'react';
import Workflows from '@/web/components/organization/work-flow/work-flow-list';
import JiraWorkflowEditor from '@/web/components/workfloweditor';

export const Route = createFileRoute('/__mainLayout/settings/work-flow')({
  component: WorkflowLayout,
});

function WorkflowLayout() {
  const location = useLocation();
  const navigate = useNavigate();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'design'>('create');
  const [workflowId, setWorkflowId] = useState<string | undefined>();

  // Parse the current route to determine if modal should be open
  useEffect(() => {
    const path = location.pathname;

    if (path === '/settings/work-flow/create') {
      setModalMode('create');
      setWorkflowId(undefined);
      setIsModalOpen(true);
    } else if (path.includes('/design')) {
      const id = path
        .split('/')
        .find((_, index, arr) => arr[index - 1] === 'work-flow' && arr[index + 1] === 'design');
      setModalMode('design');
      setWorkflowId(id);
      setIsModalOpen(true);
    } else if (path.includes('/edit')) {
      const id = path
        .split('/')
        .find((_, index, arr) => arr[index - 1] === 'work-flow' && arr[index + 1] === 'edit');
      setModalMode('edit');
      setWorkflowId(id);
      setIsModalOpen(true);
    } else {
      setIsModalOpen(false);
    }
  }, [location.pathname]);

  const handleCloseModal = () => {
    setIsModalOpen(false);
    navigate({ to: '/settings/work-flow' });
  };

  return (
    <>
      <Workflows />
      <Dialog
        onOpenChange={(open) => {
          if (!open) {
            handleCloseModal();
          }
        }}
        open={isModalOpen}
      >
        <DialogContent className="max-h-[calc(100dvh)] max-w-screen ">
          {isModalOpen && (
            <JiraWorkflowEditor
              mode={modalMode === 'create' ? 'create' : 'edit'}
              workflowId={workflowId}
            />
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
