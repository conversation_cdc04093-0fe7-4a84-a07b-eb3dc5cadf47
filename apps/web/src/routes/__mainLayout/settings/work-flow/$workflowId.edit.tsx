import { Button } from '@repo/ui/components/button';
import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { ArrowLeft } from 'lucide-react';
import JiraWorkflowEditor from '@/web/components/workfloweditor';

export const Route = createFileRoute('/__mainLayout/settings/work-flow/$workflowId/edit')({
  component: EditWorkflow,
});

function EditWorkflow() {
  const { workflowId } = Route.useParams();
  const navigate = useNavigate();

  return (
    <div className="flex h-screen flex-col bg-muted">
      {/* Header with back button */}
      <div className="border-b bg-white px-6 py-4 shadow-sm">
        <div className="flex items-center gap-4">
          <Button
            className="flex items-center gap-2"
            onClick={() => navigate({ to: '/settings/work-flow' })}
            size="sm"
            variant="ghost"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Workflows
          </Button>
          <div className="h-6 w-px bg-muted" />
          <h1 className="font-semibold text-foreground text-lg">Edit Workflow</h1>
        </div>
      </div>

      {/* Full page workflow editor */}
      <div className="flex-1 overflow-hidden">
        <JiraWorkflowEditor mode="edit" workflowId={workflowId} />
      </div>
    </div>
  );
}
