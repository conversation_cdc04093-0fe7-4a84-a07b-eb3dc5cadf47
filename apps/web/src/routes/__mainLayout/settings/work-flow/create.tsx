import { createFileRoute } from '@tanstack/react-router';
import { Loader2 } from 'lucide-react';
import { Suspense } from 'react';
import JiraWorkflowEditor from '@/web/components/workfloweditor';

export const Route = createFileRoute('/__mainLayout/settings/work-flow/create')({
  component: CreateWorkflow,
});

function CreateWorkflow() {
  return (
    <Suspense
      fallback={
        <div className="flex h-screen items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        </div>
      }
    >
      <JiraWorkflowEditor mode="create" />
    </Suspense>
  );
}
