'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/avatar';
import { Badge } from '@repo/ui/components/badge';
import { Button } from '@repo/ui/components/button';
import { Card, CardContent, CardFooter } from '@repo/ui/components/card';
import { Input } from '@repo/ui/components/input';
import { Label } from '@repo/ui/components/label';
import { Textarea } from '@repo/ui/components/textarea';
import { useQueryClient } from '@tanstack/react-query';
import { createFileRoute } from '@tanstack/react-router';
import { motion } from 'framer-motion';
import { Camera, Globe, Palette, Users } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { ColorPicker } from '@/web/components/onboarding/components/color-picker';
import { TeamSizeSelector } from '@/web/components/onboarding/components/team-size-selector';
import { useAppDataRefresh } from '@/web/hooks/use-app-data';
import {
  useGetApiV1Organizations,
  useGetApiV1OrganizationsId,
  usePatchApiV1OrganizationsId,
  usePostApiV1Organizations,
} from '@/web/services/organizations';
import { useRootStore } from '@/web/store/store';

export const Route = createFileRoute('/__mainLayout/settings/')({
  component: GeneralSettings,
});

interface OrganizationFormData {
  name: string;
  slug: string;
  description: string;
  size: string;
  color: string;
  website: string;
  logoUrl?: string;
}

function GeneralSettings() {
  const queryClient = useQueryClient();
  const { refetchOrganizations, refetchRoleAssignments } = useAppDataRefresh();

  const currentUser = useRootStore((state) => state.currentUser);
  const currentOrganizationId = useRootStore((state) => state.currentOrganizationId);
  const setCurrentOrganizationId = useRootStore((state) => state.setCurrentOrganizationId);
  const organizations = useRootStore((state) => state.organizations);

  const [formData, setFormData] = useState<OrganizationFormData>({
    name: '',
    slug: '',
    description: '',
    size: '',
    color: '#6366f1',
    website: '',
    logoUrl: '',
  });
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Fetch user's organizations
  const { data: orgsData } = useGetApiV1Organizations(
    currentUser?.id
      ? {
          filters: JSON.stringify({ createdBy: { $eq: currentUser.id } }),
        }
      : undefined,
  );

  // Fetch current organization details if exists
  const { data: currentOrgData } = useGetApiV1OrganizationsId(currentOrganizationId || '', {
    query: {
      enabled: !!currentOrganizationId,
    },
  });

  const createOrgMutation = usePostApiV1Organizations({
    mutation: {
      onSuccess: async (data) => {
        // Set the newly created org as current
        setCurrentOrganizationId(data.data.id);
        // Refresh organizations and role assignments
        await refetchOrganizations();
        await refetchRoleAssignments();
      },
    },
  });

  const updateOrgMutation = usePatchApiV1OrganizationsId({
    mutation: {
      onSuccess: async () => {
        // Refresh organizations list
        await refetchOrganizations();
        // Invalidate the specific organization query
        if (currentOrganizationId) {
          await queryClient.invalidateQueries({
            queryKey: [`/api/v1/organizations/${currentOrganizationId}`],
          });
        }
      },
    },
  });

  // Check if user has any organizations
  const hasOrganizations = organizations.length > 0 || (orgsData?.data && orgsData.data.length > 0);
  const isEditMode = hasOrganizations && currentOrganizationId;

  // Initialize form with current org data if in edit mode
  useEffect(() => {
    if (currentOrgData?.data) {
      const org = currentOrgData.data;
      setFormData({
        name: org.name || '',
        slug: org.slug || '',
        description: org.description || '',
        size: (org.settings?.teamSize || '') as string,
        color: (org.settings?.brandColor || '#6366f1') as string,
        website: org.website || '',
        logoUrl: org.logoUrl || '',
      });
    }
  }, [currentOrgData]);

  const colors = [
    { value: '#6366f1', label: 'Indigo', class: 'bg-indigo-500' },
    { value: '#8b5cf6', label: 'Purple', class: 'bg-purple-600' },
    { value: '#ec4899', label: 'Pink', class: 'bg-pink-500' },
    { value: '#f43f5e', label: 'Rose', class: 'bg-rose-500' },
    { value: '#3b82f6', label: 'Blue', class: 'bg-primary' },
    { value: '#06b6d4', label: 'Cyan', class: 'bg-cyan-500' },
    { value: '#10b981', label: 'Emerald', class: 'bg-emerald-500' },
    { value: '#f59e0b', label: 'Amber', class: 'bg-amber-500' },
  ];

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  };

  const handleInputChange = (field: string, value: string) => {
    const newData = { ...formData, [field]: value };
    if (field === 'name' && !isEditMode) {
      // Only auto-generate slug in create mode
      newData.slug = generateSlug(value);
    }
    setFormData(newData);
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      return;
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('Image size should be less than 5MB');
      return;
    }

    setIsUploadingImage(true);
    try {
      // Convert to base64
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result as string;
        setFormData({ ...formData, logoUrl: base64String });
        setIsUploadingImage(false);
      };
      reader.readAsDataURL(file);
    } catch (_error) {
      setIsUploadingImage(false);
    }
  };

  const getOrgInitials = () => {
    return formData.name
      ? formData.name
          .split(' ')
          .map((word) => word[0])
          .join('')
          .toUpperCase()
          .slice(0, 2)
      : 'ORG';
  };

  const handleSubmit = async () => {
    if (!(formData.name && formData.size)) {
      return;
    }

    const organizationData = {
      name: formData.name,
      slug: formData.slug,
      description: formData.description || null,
      website: formData.website || null,
      logoUrl: formData.logoUrl || null,
      createdBy: currentUser?.id || null,
      settings: {
        teamSize: formData.size,
        brandColor: formData.color,
      },
    };

    if (isEditMode && currentOrganizationId) {
      await updateOrgMutation.mutateAsync({
        id: currentOrganizationId,
        data: organizationData,
      });
    } else {
      await createOrgMutation.mutateAsync({
        data: organizationData,
      });
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="font-semibold text-2xl tracking-tight">General Settings</h2>
        <p className="text-muted-foreground">
          {isEditMode ? 'Manage your organization settings' : 'Create your organization'}
        </p>
      </div>

      <Card className="w-full rounded-none border-none">
        {/* <CardHeader className="p-4 bg-muted/50 dark:bg-muted/50">
          <StepHeader
            icon={Building2}
            iconColor="bg-primary/10"
            title={isEditMode ? "Organization Settings" : "Create your organization"}
            description={
              isEditMode
                ? "Update your organization details and preferences"
                : "Your organization is the top-level workspace where all your teams collaborate"
            }
          />
        </CardHeader> */}

        <CardContent className="space-y-6 p-6">
          {/* Organization Logo and Name */}
          <div className="flex flex-wrap items-center gap-6">
            {/* Logo Upload */}
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className="flex-shrink-0"
              initial={{ opacity: 0, y: 20 }}
              transition={{ delay: 0.05 }}
            >
              <div className="group relative">
                <label className="cursor-pointer" htmlFor="org-logo-upload">
                  <Avatar className="h-20 w-20 rounded-lg border-2 border-border">
                    <AvatarImage alt="Organization Logo" src={formData.logoUrl} />
                    <AvatarFallback
                      className="rounded-lg bg-muted text-lg"
                      style={{ backgroundColor: formData.color }}
                    >
                      {getOrgInitials()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="absolute inset-0 flex items-center justify-center rounded-lg bg-black/50 opacity-0 transition-opacity group-hover:opacity-100">
                    <Camera className="h-6 w-6 text-white" />
                  </div>
                </label>
                <input
                  accept="image/*"
                  className="hidden"
                  disabled={isUploadingImage}
                  id="org-logo-upload"
                  onChange={handleImageUpload}
                  ref={fileInputRef}
                  type="file"
                />
              </div>
            </motion.div>

            {/* Organization Name */}
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className="flex-1 space-y-3"
              initial={{ opacity: 0, y: 20 }}
              transition={{ delay: 0.1 }}
            >
              <Label className="flex items-center gap-2" htmlFor="org-name">
                Organization name
                <Badge className="text-xs" variant="secondary">
                  Required
                </Badge>
              </Label>
              <div className="relative">
                {/* <Hash className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground" /> */}
                <Input
                  className=""
                  id="org-name"
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Acme Corporation"
                  value={formData.name}
                />
              </div>
              {/* {formData.name && (
                <p className="text-sm text-muted-foreground flex items-center gap-1 animate-in slide-in-from-bottom-2">
                  <Link2 className="w-3 h-3" />
                  spark.app/{formData.slug}
                </p>
              )} */}
            </motion.div>
          </div>

          {/* Team Size */}
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            className="space-y-3"
            initial={{ opacity: 0, y: 20 }}
            transition={{ delay: 0.2 }}
          >
            <Label className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              How big is your team?
              <Badge className="text-xs" variant="secondary">
                Required
              </Badge>
            </Label>
            <TeamSizeSelector
              onChange={(value) => handleInputChange('size', value)}
              value={formData.size}
            />
          </motion.div>

          {/* Color Selection */}
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            className="space-y-3"
            initial={{ opacity: 0, y: 20 }}
            transition={{ delay: 0.3 }}
          >
            <Label className="flex items-center gap-2">
              <Palette className="h-4 w-4" />
              Choose a color theme
            </Label>
            <ColorPicker
              colors={colors}
              onChange={(value) => handleInputChange('color', value)}
              value={formData.color}
            />
          </motion.div>

          {/* Description */}
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            className="space-y-3"
            initial={{ opacity: 0, y: 20 }}
            transition={{ delay: 0.4 }}
          >
            <Label htmlFor="org-description">Description (optional)</Label>
            <Textarea
              className="h-24 resize-none"
              id="org-description"
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Tell us about your organization..."
              value={formData.description}
            />
          </motion.div>

          {/* Website */}
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            className="space-y-3"
            initial={{ opacity: 0, y: 20 }}
            transition={{ delay: 0.5 }}
          >
            <Label className="flex items-center gap-2" htmlFor="org-website">
              <Globe className="h-4 w-4" />
              Website (optional)
            </Label>
            <Input
              id="org-website"
              onChange={(e) => handleInputChange('website', e.target.value)}
              placeholder="https://acme.com"
              value={formData.website}
            />
          </motion.div>
        </CardContent>

        <CardFooter className="flex justify-end bg-muted/50 p-4 dark:bg-muted/50">
          <Button
            className="group bg-gradient-to-r from-primary to-primary/80 hover:from-primary/80 hover:to-primary/60"
            disabled={
              !(formData.name && formData.size) ||
              createOrgMutation.isPending ||
              updateOrgMutation.isPending
            }
            onClick={handleSubmit}
          >
            {createOrgMutation.isPending || updateOrgMutation.isPending
              ? isEditMode
                ? 'Updating...'
                : 'Creating...'
              : isEditMode
                ? 'Update Organization'
                : 'Create Organization'}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
