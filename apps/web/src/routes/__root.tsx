import { ToastProvider } from '@repo/ui/components/toast';
import { Toaster } from '@repo/ui/components/toaster';
import { createRootRoute, Outlet } from '@tanstack/react-router';
import { ThemeAutoRotator } from '../components/theme-auto-rotator';

export const Route = createRootRoute({
  component: RootComponent,
});

function RootComponent() {
  return (
    <>
      <ToastProvider />
      <ThemeAutoRotator />
      <Outlet />
      <Toaster />
    </>
  );
}
