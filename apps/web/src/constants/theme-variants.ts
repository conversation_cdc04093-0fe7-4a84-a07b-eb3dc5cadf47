export interface ThemeVariantConfig {
  label: string;
  identifier: string;
  font?: string;
}

export const THEME_VARIANTS: ThemeVariantConfig[] = [
  { label: 'Default', identifier: 'default' },
  { label: 'Amber Minimal ', identifier: 'amber-minimal' },
  { label: 'Amethyst Haze', identifier: 'amethyst-haze' },
  { label: 'Bold Tech', identifier: 'bold-tech' },
  { label: 'Bubblegum', identifier: 'bubblegum' },
  { label: 'Caffeine', identifier: 'caffeine' },
  { label: 'Candyland', identifier: 'candyland' },
  { label: 'Catppuccin', identifier: 'catppuccin' },
  { label: 'claude', identifier: 'claude' },
  { label: 'claymorphism', identifier: 'claymorphism' },
  { label: 'Clean Slate', identifier: 'clean-slate' },
  { label: 'Cosmic Night', identifier: 'cosmic-night' },
  { label: 'Cyberpunk', identifier: 'cyberpunk' },
  { label: 'Doom 64', identifier: 'doom-64' },
  { label: 'Elegant Luxury', identifier: 'elegant-luxury' },
  { label: 'Graphite', identifier: 'graphite' },
  { label: 'Kodama Grove', identifier: 'kodama-grove' },
  { label: 'Midnight Bloom', identifier: 'midnight-bloom' },
  { label: 'Mocha Mousse', identifier: 'mocha-mousse' },
  { label: 'Modern Minimal', identifier: 'modern-minimal' },
  { label: 'Mono', identifier: 'mono' },
  { label: 'Nature', identifier: 'nature' },
  { label: 'Neo Brutalism', identifier: 'neo-brutalism' },
  { label: 'Northern Lights', identifier: 'northern-lights' },
  { label: 'Notebook', identifier: 'notebook' },
  { label: 'Ocean Breeze', identifier: 'ocean-breeze' },
  { label: 'Pastel Dreams', identifier: 'pastel-dreams' },
  { label: 'Perpetuity', identifier: 'perpetuity' },
  { label: 'Quantum Rose', identifier: 'quantum-rose' },
  { label: 'Retro Arcade', identifier: 'retro-arcade' },
  { label: 'Solar Dusk', identifier: 'solar-dusk' },
  { label: 'Starry Night', identifier: 'starry-night' },
  { label: 'Sunset Horizon', identifier: 'sunset-horizon' },
  { label: 'Supabase', identifier: 'supabase' },
  { label: 'T3 Chat', identifier: 't3-chat' },
  { label: 'Tangerine', identifier: 'tangerine' },
  { label: 'Twitter', identifier: 'twitter' },
  { label: 'Vercel', identifier: 'vercel' },
  { label: 'Vintage Paper', identifier: 'vintage-paper' },
  { label: 'Violet Bloom', identifier: 'violet-bloom' },
];

// You can add more themes by adding them to this array and creating corresponding CSS classes:
// - Add entry here: { label: "Purple", identifier: "purple" }
// - Add CSS classes: .purple { ... } and .purple-dark { ... } in globals.css
