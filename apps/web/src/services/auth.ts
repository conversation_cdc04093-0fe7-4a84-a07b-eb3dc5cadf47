/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  ErrorResponse,
  GetApiV1AuthMe200,
  PostApiV1AuthChangePassword200,
  PostApiV1AuthChangePasswordBody,
  PostApiV1AuthForgotPassword200,
  PostApiV1AuthForgotPasswordBody,
  PostApiV1AuthLogin200,
  PostApiV1AuthLoginBody,
  PostApiV1AuthLogout200,
  PostApiV1AuthLogoutBody,
  PostApiV1AuthRefresh200,
  PostApiV1AuthRefreshBody,
  PostApiV1AuthRegister200,
  PostApiV1AuthRegisterBody,
  PostApiV1AuthResendVerification200,
  PostApiV1AuthResendVerificationBody,
  PostApiV1AuthResetPassword200,
  PostApiV1AuthResetPasswordBody,
  PostApiV1AuthVerifyEmail200,
  PostApiV1AuthVerifyEmailBody,
} from './hooks.schemas';

/**
 * Create a new user account with email and password
 * @summary Register a new user
 */
export const postApiV1AuthRegister = (
  postApiV1AuthRegisterBody: BodyType<PostApiV1AuthRegisterBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1AuthRegister200>({
    url: '/api/v1/auth/register',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1AuthRegisterBody,
    signal,
  });
};

export const getPostApiV1AuthRegisterMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1AuthRegister>>,
    TError,
    { data: BodyType<PostApiV1AuthRegisterBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1AuthRegister>>,
  TError,
  { data: BodyType<PostApiV1AuthRegisterBody> },
  TContext
> => {
  const mutationKey = ['postApiV1AuthRegister'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1AuthRegister>>,
    { data: BodyType<PostApiV1AuthRegisterBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1AuthRegister(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1AuthRegisterMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1AuthRegister>>
>;
export type PostApiV1AuthRegisterMutationBody = BodyType<PostApiV1AuthRegisterBody>;
export type PostApiV1AuthRegisterMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Register a new user
 */
export const usePostApiV1AuthRegister = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1AuthRegister>>,
    TError,
    { data: BodyType<PostApiV1AuthRegisterBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1AuthRegister>>,
  TError,
  { data: BodyType<PostApiV1AuthRegisterBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1AuthRegisterMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Authenticate user with email and password
 * @summary User login
 */
export const postApiV1AuthLogin = (
  postApiV1AuthLoginBody: BodyType<PostApiV1AuthLoginBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1AuthLogin200>({
    url: '/api/v1/auth/login',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1AuthLoginBody,
    signal,
  });
};

export const getPostApiV1AuthLoginMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1AuthLogin>>,
    TError,
    { data: BodyType<PostApiV1AuthLoginBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1AuthLogin>>,
  TError,
  { data: BodyType<PostApiV1AuthLoginBody> },
  TContext
> => {
  const mutationKey = ['postApiV1AuthLogin'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1AuthLogin>>,
    { data: BodyType<PostApiV1AuthLoginBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1AuthLogin(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1AuthLoginMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1AuthLogin>>
>;
export type PostApiV1AuthLoginMutationBody = BodyType<PostApiV1AuthLoginBody>;
export type PostApiV1AuthLoginMutationError = ErrorType<ErrorResponse>;

/**
 * @summary User login
 */
export const usePostApiV1AuthLogin = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1AuthLogin>>,
    TError,
    { data: BodyType<PostApiV1AuthLoginBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1AuthLogin>>,
  TError,
  { data: BodyType<PostApiV1AuthLoginBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1AuthLoginMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Get a new access token using a refresh token
 * @summary Refresh access token
 */
export const postApiV1AuthRefresh = (
  postApiV1AuthRefreshBody: BodyType<PostApiV1AuthRefreshBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1AuthRefresh200>({
    url: '/api/v1/auth/refresh',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1AuthRefreshBody,
    signal,
  });
};

export const getPostApiV1AuthRefreshMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1AuthRefresh>>,
    TError,
    { data: BodyType<PostApiV1AuthRefreshBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1AuthRefresh>>,
  TError,
  { data: BodyType<PostApiV1AuthRefreshBody> },
  TContext
> => {
  const mutationKey = ['postApiV1AuthRefresh'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1AuthRefresh>>,
    { data: BodyType<PostApiV1AuthRefreshBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1AuthRefresh(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1AuthRefreshMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1AuthRefresh>>
>;
export type PostApiV1AuthRefreshMutationBody = BodyType<PostApiV1AuthRefreshBody>;
export type PostApiV1AuthRefreshMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Refresh access token
 */
export const usePostApiV1AuthRefresh = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1AuthRefresh>>,
    TError,
    { data: BodyType<PostApiV1AuthRefreshBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1AuthRefresh>>,
  TError,
  { data: BodyType<PostApiV1AuthRefreshBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1AuthRefreshMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Send password reset email to user
 * @summary Request password reset
 */
export const postApiV1AuthForgotPassword = (
  postApiV1AuthForgotPasswordBody: BodyType<PostApiV1AuthForgotPasswordBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1AuthForgotPassword200>({
    url: '/api/v1/auth/forgot-password',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1AuthForgotPasswordBody,
    signal,
  });
};

export const getPostApiV1AuthForgotPasswordMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1AuthForgotPassword>>,
    TError,
    { data: BodyType<PostApiV1AuthForgotPasswordBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1AuthForgotPassword>>,
  TError,
  { data: BodyType<PostApiV1AuthForgotPasswordBody> },
  TContext
> => {
  const mutationKey = ['postApiV1AuthForgotPassword'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1AuthForgotPassword>>,
    { data: BodyType<PostApiV1AuthForgotPasswordBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1AuthForgotPassword(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1AuthForgotPasswordMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1AuthForgotPassword>>
>;
export type PostApiV1AuthForgotPasswordMutationBody = BodyType<PostApiV1AuthForgotPasswordBody>;
export type PostApiV1AuthForgotPasswordMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Request password reset
 */
export const usePostApiV1AuthForgotPassword = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1AuthForgotPassword>>,
    TError,
    { data: BodyType<PostApiV1AuthForgotPasswordBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1AuthForgotPassword>>,
  TError,
  { data: BodyType<PostApiV1AuthForgotPasswordBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1AuthForgotPasswordMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Reset user password using reset token
 * @summary Reset password
 */
export const postApiV1AuthResetPassword = (
  postApiV1AuthResetPasswordBody: BodyType<PostApiV1AuthResetPasswordBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1AuthResetPassword200>({
    url: '/api/v1/auth/reset-password',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1AuthResetPasswordBody,
    signal,
  });
};

export const getPostApiV1AuthResetPasswordMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1AuthResetPassword>>,
    TError,
    { data: BodyType<PostApiV1AuthResetPasswordBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1AuthResetPassword>>,
  TError,
  { data: BodyType<PostApiV1AuthResetPasswordBody> },
  TContext
> => {
  const mutationKey = ['postApiV1AuthResetPassword'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1AuthResetPassword>>,
    { data: BodyType<PostApiV1AuthResetPasswordBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1AuthResetPassword(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1AuthResetPasswordMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1AuthResetPassword>>
>;
export type PostApiV1AuthResetPasswordMutationBody = BodyType<PostApiV1AuthResetPasswordBody>;
export type PostApiV1AuthResetPasswordMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Reset password
 */
export const usePostApiV1AuthResetPassword = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1AuthResetPassword>>,
    TError,
    { data: BodyType<PostApiV1AuthResetPasswordBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1AuthResetPassword>>,
  TError,
  { data: BodyType<PostApiV1AuthResetPasswordBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1AuthResetPasswordMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Verify user email using verification token
 * @summary Verify email address
 */
export const postApiV1AuthVerifyEmail = (
  postApiV1AuthVerifyEmailBody: BodyType<PostApiV1AuthVerifyEmailBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1AuthVerifyEmail200>({
    url: '/api/v1/auth/verify-email',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1AuthVerifyEmailBody,
    signal,
  });
};

export const getPostApiV1AuthVerifyEmailMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1AuthVerifyEmail>>,
    TError,
    { data: BodyType<PostApiV1AuthVerifyEmailBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1AuthVerifyEmail>>,
  TError,
  { data: BodyType<PostApiV1AuthVerifyEmailBody> },
  TContext
> => {
  const mutationKey = ['postApiV1AuthVerifyEmail'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1AuthVerifyEmail>>,
    { data: BodyType<PostApiV1AuthVerifyEmailBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1AuthVerifyEmail(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1AuthVerifyEmailMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1AuthVerifyEmail>>
>;
export type PostApiV1AuthVerifyEmailMutationBody = BodyType<PostApiV1AuthVerifyEmailBody>;
export type PostApiV1AuthVerifyEmailMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Verify email address
 */
export const usePostApiV1AuthVerifyEmail = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1AuthVerifyEmail>>,
    TError,
    { data: BodyType<PostApiV1AuthVerifyEmailBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1AuthVerifyEmail>>,
  TError,
  { data: BodyType<PostApiV1AuthVerifyEmailBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1AuthVerifyEmailMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Change user password (requires authentication)
 * @summary Change password
 */
export const postApiV1AuthChangePassword = (
  postApiV1AuthChangePasswordBody: BodyType<PostApiV1AuthChangePasswordBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1AuthChangePassword200>({
    url: '/api/v1/auth/change-password',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1AuthChangePasswordBody,
    signal,
  });
};

export const getPostApiV1AuthChangePasswordMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1AuthChangePassword>>,
    TError,
    { data: BodyType<PostApiV1AuthChangePasswordBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1AuthChangePassword>>,
  TError,
  { data: BodyType<PostApiV1AuthChangePasswordBody> },
  TContext
> => {
  const mutationKey = ['postApiV1AuthChangePassword'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1AuthChangePassword>>,
    { data: BodyType<PostApiV1AuthChangePasswordBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1AuthChangePassword(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1AuthChangePasswordMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1AuthChangePassword>>
>;
export type PostApiV1AuthChangePasswordMutationBody = BodyType<PostApiV1AuthChangePasswordBody>;
export type PostApiV1AuthChangePasswordMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Change password
 */
export const usePostApiV1AuthChangePassword = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1AuthChangePassword>>,
    TError,
    { data: BodyType<PostApiV1AuthChangePasswordBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1AuthChangePassword>>,
  TError,
  { data: BodyType<PostApiV1AuthChangePasswordBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1AuthChangePasswordMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Resend email verification link
 * @summary Resend verification email
 */
export const postApiV1AuthResendVerification = (
  postApiV1AuthResendVerificationBody: BodyType<PostApiV1AuthResendVerificationBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1AuthResendVerification200>({
    url: '/api/v1/auth/resend-verification',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1AuthResendVerificationBody,
    signal,
  });
};

export const getPostApiV1AuthResendVerificationMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1AuthResendVerification>>,
    TError,
    { data: BodyType<PostApiV1AuthResendVerificationBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1AuthResendVerification>>,
  TError,
  { data: BodyType<PostApiV1AuthResendVerificationBody> },
  TContext
> => {
  const mutationKey = ['postApiV1AuthResendVerification'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1AuthResendVerification>>,
    { data: BodyType<PostApiV1AuthResendVerificationBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1AuthResendVerification(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1AuthResendVerificationMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1AuthResendVerification>>
>;
export type PostApiV1AuthResendVerificationMutationBody =
  BodyType<PostApiV1AuthResendVerificationBody>;
export type PostApiV1AuthResendVerificationMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Resend verification email
 */
export const usePostApiV1AuthResendVerification = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1AuthResendVerification>>,
    TError,
    { data: BodyType<PostApiV1AuthResendVerificationBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1AuthResendVerification>>,
  TError,
  { data: BodyType<PostApiV1AuthResendVerificationBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1AuthResendVerificationMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Get authenticated user information
 * @summary Get current user
 */
export const getApiV1AuthMe = (signal?: AbortSignal) => {
  return axiosClient<GetApiV1AuthMe200>({
    url: '/api/v1/auth/me',
    method: 'GET',
    signal,
  });
};

export const getGetApiV1AuthMeQueryKey = () => {
  return ['/api/v1/auth/me'] as const;
};

export const getGetApiV1AuthMeQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1AuthMe>>,
  TError = ErrorType<ErrorResponse>,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1AuthMe>>, TError, TData>>;
}) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1AuthMeQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1AuthMe>>> = ({ signal }) =>
    getApiV1AuthMe(signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1AuthMe>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1AuthMeQueryResult = NonNullable<Awaited<ReturnType<typeof getApiV1AuthMe>>>;
export type GetApiV1AuthMeQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1AuthMe<
  TData = Awaited<ReturnType<typeof getApiV1AuthMe>>,
  TError = ErrorType<ErrorResponse>,
>(options: {
  query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1AuthMe>>, TError, TData>> &
    Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getApiV1AuthMe>>,
        TError,
        Awaited<ReturnType<typeof getApiV1AuthMe>>
      >,
      'initialData'
    >;
}): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1AuthMe<
  TData = Awaited<ReturnType<typeof getApiV1AuthMe>>,
  TError = ErrorType<ErrorResponse>,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1AuthMe>>, TError, TData>> &
    Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getApiV1AuthMe>>,
        TError,
        Awaited<ReturnType<typeof getApiV1AuthMe>>
      >,
      'initialData'
    >;
}): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1AuthMe<
  TData = Awaited<ReturnType<typeof getApiV1AuthMe>>,
  TError = ErrorType<ErrorResponse>,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1AuthMe>>, TError, TData>>;
}): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get current user
 */

export function useGetApiV1AuthMe<
  TData = Awaited<ReturnType<typeof getApiV1AuthMe>>,
  TError = ErrorType<ErrorResponse>,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1AuthMe>>, TError, TData>>;
}): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1AuthMeQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Logout authenticated user
 * @summary Logout user
 */
export const postApiV1AuthLogout = (
  postApiV1AuthLogoutBody: BodyType<PostApiV1AuthLogoutBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1AuthLogout200>({
    url: '/api/v1/auth/logout',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1AuthLogoutBody,
    signal,
  });
};

export const getPostApiV1AuthLogoutMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1AuthLogout>>,
    TError,
    { data: BodyType<PostApiV1AuthLogoutBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1AuthLogout>>,
  TError,
  { data: BodyType<PostApiV1AuthLogoutBody> },
  TContext
> => {
  const mutationKey = ['postApiV1AuthLogout'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1AuthLogout>>,
    { data: BodyType<PostApiV1AuthLogoutBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1AuthLogout(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1AuthLogoutMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1AuthLogout>>
>;
export type PostApiV1AuthLogoutMutationBody = BodyType<PostApiV1AuthLogoutBody>;
export type PostApiV1AuthLogoutMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Logout user
 */
export const usePostApiV1AuthLogout = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1AuthLogout>>,
    TError,
    { data: BodyType<PostApiV1AuthLogoutBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1AuthLogout>>,
  TError,
  { data: BodyType<PostApiV1AuthLogoutBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1AuthLogoutMutationOptions(options);

  return useMutation(mutationOptions);
};
