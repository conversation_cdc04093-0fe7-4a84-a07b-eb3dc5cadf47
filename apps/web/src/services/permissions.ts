/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1ApiV1PermissionsId204,
  ErrorResponse,
  GetApiV1ApiV1Permissions200,
  GetApiV1ApiV1PermissionsId200,
  GetApiV1ApiV1PermissionsParams,
  PatchApiV1ApiV1PermissionsId200,
  PatchApiV1ApiV1PermissionsIdBody,
  PostApiV1ApiV1Permissions201,
  PostApiV1ApiV1PermissionsBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of permissions resources with optional filtering and sorting
 * @summary Get permissions list
 */
export const getApiV1ApiV1Permissions = (
  params?: GetApiV1ApiV1PermissionsParams,
  signal?: AbortSignal,
) => {
  return axiosClient<GetApiV1ApiV1Permissions200>({
    url: '/api/v1/api/v1/permissions/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1ApiV1PermissionsQueryKey = (params?: GetApiV1ApiV1PermissionsParams) => {
  return ['/api/v1/api/v1/permissions/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1ApiV1PermissionsQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1ApiV1Permissions>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1ApiV1PermissionsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ApiV1Permissions>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1ApiV1PermissionsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1ApiV1Permissions>>> = ({
    signal,
  }) => getApiV1ApiV1Permissions(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1ApiV1Permissions>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1ApiV1PermissionsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1ApiV1Permissions>>
>;
export type GetApiV1ApiV1PermissionsQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1ApiV1Permissions<
  TData = Awaited<ReturnType<typeof getApiV1ApiV1Permissions>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1ApiV1PermissionsParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ApiV1Permissions>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1ApiV1Permissions>>,
          TError,
          Awaited<ReturnType<typeof getApiV1ApiV1Permissions>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1ApiV1Permissions<
  TData = Awaited<ReturnType<typeof getApiV1ApiV1Permissions>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1ApiV1PermissionsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ApiV1Permissions>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1ApiV1Permissions>>,
          TError,
          Awaited<ReturnType<typeof getApiV1ApiV1Permissions>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1ApiV1Permissions<
  TData = Awaited<ReturnType<typeof getApiV1ApiV1Permissions>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1ApiV1PermissionsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ApiV1Permissions>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get permissions list
 */

export function useGetApiV1ApiV1Permissions<
  TData = Awaited<ReturnType<typeof getApiV1ApiV1Permissions>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1ApiV1PermissionsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ApiV1Permissions>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1ApiV1PermissionsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new permission resource
 * @summary Create permission
 */
export const postApiV1ApiV1Permissions = (
  postApiV1ApiV1PermissionsBody: BodyType<PostApiV1ApiV1PermissionsBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1ApiV1Permissions201>({
    url: '/api/v1/api/v1/permissions/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1ApiV1PermissionsBody,
    signal,
  });
};

export const getPostApiV1ApiV1PermissionsMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1ApiV1Permissions>>,
    TError,
    { data: BodyType<PostApiV1ApiV1PermissionsBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1ApiV1Permissions>>,
  TError,
  { data: BodyType<PostApiV1ApiV1PermissionsBody> },
  TContext
> => {
  const mutationKey = ['postApiV1ApiV1Permissions'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1ApiV1Permissions>>,
    { data: BodyType<PostApiV1ApiV1PermissionsBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1ApiV1Permissions(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1ApiV1PermissionsMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1ApiV1Permissions>>
>;
export type PostApiV1ApiV1PermissionsMutationBody = BodyType<PostApiV1ApiV1PermissionsBody>;
export type PostApiV1ApiV1PermissionsMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create permission
 */
export const usePostApiV1ApiV1Permissions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1ApiV1Permissions>>,
    TError,
    { data: BodyType<PostApiV1ApiV1PermissionsBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1ApiV1Permissions>>,
  TError,
  { data: BodyType<PostApiV1ApiV1PermissionsBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1ApiV1PermissionsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific permission resource by its unique identifier
 * @summary Get permission by ID
 */
export const getApiV1ApiV1PermissionsId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1ApiV1PermissionsId200>({
    url: `/api/v1/api/v1/permissions/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1ApiV1PermissionsIdQueryKey = (id: string) => {
  return [`/api/v1/api/v1/permissions/${id}`] as const;
};

export const getGetApiV1ApiV1PermissionsIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1ApiV1PermissionsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ApiV1PermissionsId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1ApiV1PermissionsIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1ApiV1PermissionsId>>> = ({
    signal,
  }) => getApiV1ApiV1PermissionsId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1ApiV1PermissionsId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1ApiV1PermissionsIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1ApiV1PermissionsId>>
>;
export type GetApiV1ApiV1PermissionsIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1ApiV1PermissionsId<
  TData = Awaited<ReturnType<typeof getApiV1ApiV1PermissionsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ApiV1PermissionsId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1ApiV1PermissionsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1ApiV1PermissionsId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1ApiV1PermissionsId<
  TData = Awaited<ReturnType<typeof getApiV1ApiV1PermissionsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ApiV1PermissionsId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1ApiV1PermissionsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1ApiV1PermissionsId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1ApiV1PermissionsId<
  TData = Awaited<ReturnType<typeof getApiV1ApiV1PermissionsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ApiV1PermissionsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get permission by ID
 */

export function useGetApiV1ApiV1PermissionsId<
  TData = Awaited<ReturnType<typeof getApiV1ApiV1PermissionsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ApiV1PermissionsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1ApiV1PermissionsIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing permission resource
 * @summary Update permission
 */
export const patchApiV1ApiV1PermissionsId = (
  id: string,
  patchApiV1ApiV1PermissionsIdBody: BodyType<PatchApiV1ApiV1PermissionsIdBody>,
) => {
  return axiosClient<PatchApiV1ApiV1PermissionsId200>({
    url: `/api/v1/api/v1/permissions/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1ApiV1PermissionsIdBody,
  });
};

export const getPatchApiV1ApiV1PermissionsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1ApiV1PermissionsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1ApiV1PermissionsIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1ApiV1PermissionsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1ApiV1PermissionsIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1ApiV1PermissionsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1ApiV1PermissionsId>>,
    { id: string; data: BodyType<PatchApiV1ApiV1PermissionsIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1ApiV1PermissionsId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1ApiV1PermissionsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1ApiV1PermissionsId>>
>;
export type PatchApiV1ApiV1PermissionsIdMutationBody = BodyType<PatchApiV1ApiV1PermissionsIdBody>;
export type PatchApiV1ApiV1PermissionsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update permission
 */
export const usePatchApiV1ApiV1PermissionsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1ApiV1PermissionsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1ApiV1PermissionsIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1ApiV1PermissionsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1ApiV1PermissionsIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1ApiV1PermissionsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing permission resource
 * @summary Delete permission
 */
export const deleteApiV1ApiV1PermissionsId = (id: string) => {
  return axiosClient<DeleteApiV1ApiV1PermissionsId204>({
    url: `/api/v1/api/v1/permissions/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1ApiV1PermissionsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1ApiV1PermissionsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1ApiV1PermissionsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1ApiV1PermissionsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1ApiV1PermissionsId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1ApiV1PermissionsId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1ApiV1PermissionsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1ApiV1PermissionsId>>
>;

export type DeleteApiV1ApiV1PermissionsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete permission
 */
export const useDeleteApiV1ApiV1PermissionsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1ApiV1PermissionsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1ApiV1PermissionsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1ApiV1PermissionsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
