/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1ApiV1RolePermissionsIdPermissions200,
  DeleteApiV1ApiV1RolePermissionsIdPermissionsBody,
  DeleteApiV1AttachmentsId204,
  DeleteApiV1TestSuitesIdTestCasesTestCaseId204,
  GetApiV1ApiV1RolePermissions200Item,
  GetApiV1ApiV1RolePermissionsParams,
  GetApiV1AttachmentsByEntity200Item,
  GetApiV1AttachmentsByEntityParams,
  GetApiV1AttachmentsIdSignedUrl200,
  GetApiV1AttachmentsIdSignedUrlParams,
  GetApiV1TestSuitesAllTestCases200,
  GetApiV1TestSuitesAllTestCasesParams,
  GetApiV1TestSuitesAllTestSuites200,
  GetApiV1TestSuitesAllTestSuitesParams,
  GetApiV1TestSuitesIdAvailableTestCases200Item,
  GetApiV1TestSuitesIdAvailableTestCasesParams,
  GetApiV1TestSuitesIdTestCases200Item,
  PatchApiV1TestSuitesIdTestCasesTestCaseId200,
  PatchApiV1TestSuitesIdTestCasesTestCaseIdBody,
  PostApiV1ApiV1RolePermissionsIdPermissions201,
  PostApiV1ApiV1RolePermissionsIdPermissionsBody,
  PostApiV1AttachmentsUpload200,
  PostApiV1AttachmentsUploadParams,
  PostApiV1TestSuitesIdTestCasesLink201,
  PostApiV1TestSuitesIdTestCasesLinkBody,
  PutApiV1ApiV1RolePermissionsIdPermissions200,
  PutApiV1ApiV1RolePermissionsIdPermissionsBody,
} from './hooks.schemas';

export const postApiV1AttachmentsUpload = (
  params: PostApiV1AttachmentsUploadParams,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1AttachmentsUpload200>({
    url: '/api/v1/attachments/upload',
    method: 'POST',
    params,
    signal,
  });
};

export const getPostApiV1AttachmentsUploadMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1AttachmentsUpload>>,
    TError,
    { params: PostApiV1AttachmentsUploadParams },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1AttachmentsUpload>>,
  TError,
  { params: PostApiV1AttachmentsUploadParams },
  TContext
> => {
  const mutationKey = ['postApiV1AttachmentsUpload'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1AttachmentsUpload>>,
    { params: PostApiV1AttachmentsUploadParams }
  > = (props) => {
    const { params } = props ?? {};

    return postApiV1AttachmentsUpload(params);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1AttachmentsUploadMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1AttachmentsUpload>>
>;

export type PostApiV1AttachmentsUploadMutationError = ErrorType<unknown>;

export const usePostApiV1AttachmentsUpload = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1AttachmentsUpload>>,
    TError,
    { params: PostApiV1AttachmentsUploadParams },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1AttachmentsUpload>>,
  TError,
  { params: PostApiV1AttachmentsUploadParams },
  TContext
> => {
  const mutationOptions = getPostApiV1AttachmentsUploadMutationOptions(options);

  return useMutation(mutationOptions);
};
export const getApiV1AttachmentsIdSignedUrl = (
  id: string,
  params?: GetApiV1AttachmentsIdSignedUrlParams,
  signal?: AbortSignal,
) => {
  return axiosClient<GetApiV1AttachmentsIdSignedUrl200>({
    url: `/api/v1/attachments/${id}/signed-url`,
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1AttachmentsIdSignedUrlQueryKey = (
  id: string,
  params?: GetApiV1AttachmentsIdSignedUrlParams,
) => {
  return [`/api/v1/attachments/${id}/signed-url`, ...(params ? [params] : [])] as const;
};

export const getGetApiV1AttachmentsIdSignedUrlQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1AttachmentsIdSignedUrl>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  params?: GetApiV1AttachmentsIdSignedUrlParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1AttachmentsIdSignedUrl>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1AttachmentsIdSignedUrlQueryKey(id, params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1AttachmentsIdSignedUrl>>> = ({
    signal,
  }) => getApiV1AttachmentsIdSignedUrl(id, params, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1AttachmentsIdSignedUrl>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1AttachmentsIdSignedUrlQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1AttachmentsIdSignedUrl>>
>;
export type GetApiV1AttachmentsIdSignedUrlQueryError = ErrorType<unknown>;

export function useGetApiV1AttachmentsIdSignedUrl<
  TData = Awaited<ReturnType<typeof getApiV1AttachmentsIdSignedUrl>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  params: undefined | GetApiV1AttachmentsIdSignedUrlParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1AttachmentsIdSignedUrl>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1AttachmentsIdSignedUrl>>,
          TError,
          Awaited<ReturnType<typeof getApiV1AttachmentsIdSignedUrl>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1AttachmentsIdSignedUrl<
  TData = Awaited<ReturnType<typeof getApiV1AttachmentsIdSignedUrl>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  params?: GetApiV1AttachmentsIdSignedUrlParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1AttachmentsIdSignedUrl>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1AttachmentsIdSignedUrl>>,
          TError,
          Awaited<ReturnType<typeof getApiV1AttachmentsIdSignedUrl>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1AttachmentsIdSignedUrl<
  TData = Awaited<ReturnType<typeof getApiV1AttachmentsIdSignedUrl>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  params?: GetApiV1AttachmentsIdSignedUrlParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1AttachmentsIdSignedUrl>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};

export function useGetApiV1AttachmentsIdSignedUrl<
  TData = Awaited<ReturnType<typeof getApiV1AttachmentsIdSignedUrl>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  params?: GetApiV1AttachmentsIdSignedUrlParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1AttachmentsIdSignedUrl>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1AttachmentsIdSignedUrlQueryOptions(id, params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getApiV1AttachmentsByEntity = (
  params: GetApiV1AttachmentsByEntityParams,
  signal?: AbortSignal,
) => {
  return axiosClient<GetApiV1AttachmentsByEntity200Item[]>({
    url: '/api/v1/attachments/by-entity',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1AttachmentsByEntityQueryKey = (
  params: GetApiV1AttachmentsByEntityParams,
) => {
  return ['/api/v1/attachments/by-entity', ...(params ? [params] : [])] as const;
};

export const getGetApiV1AttachmentsByEntityQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1AttachmentsByEntity>>,
  TError = ErrorType<unknown>,
>(
  params: GetApiV1AttachmentsByEntityParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1AttachmentsByEntity>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1AttachmentsByEntityQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1AttachmentsByEntity>>> = ({
    signal,
  }) => getApiV1AttachmentsByEntity(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1AttachmentsByEntity>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1AttachmentsByEntityQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1AttachmentsByEntity>>
>;
export type GetApiV1AttachmentsByEntityQueryError = ErrorType<unknown>;

export function useGetApiV1AttachmentsByEntity<
  TData = Awaited<ReturnType<typeof getApiV1AttachmentsByEntity>>,
  TError = ErrorType<unknown>,
>(
  params: GetApiV1AttachmentsByEntityParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1AttachmentsByEntity>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1AttachmentsByEntity>>,
          TError,
          Awaited<ReturnType<typeof getApiV1AttachmentsByEntity>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1AttachmentsByEntity<
  TData = Awaited<ReturnType<typeof getApiV1AttachmentsByEntity>>,
  TError = ErrorType<unknown>,
>(
  params: GetApiV1AttachmentsByEntityParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1AttachmentsByEntity>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1AttachmentsByEntity>>,
          TError,
          Awaited<ReturnType<typeof getApiV1AttachmentsByEntity>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1AttachmentsByEntity<
  TData = Awaited<ReturnType<typeof getApiV1AttachmentsByEntity>>,
  TError = ErrorType<unknown>,
>(
  params: GetApiV1AttachmentsByEntityParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1AttachmentsByEntity>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};

export function useGetApiV1AttachmentsByEntity<
  TData = Awaited<ReturnType<typeof getApiV1AttachmentsByEntity>>,
  TError = ErrorType<unknown>,
>(
  params: GetApiV1AttachmentsByEntityParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1AttachmentsByEntity>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1AttachmentsByEntityQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const deleteApiV1AttachmentsId = (id: string) => {
  return axiosClient<DeleteApiV1AttachmentsId204>({
    url: `/api/v1/attachments/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1AttachmentsIdMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1AttachmentsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1AttachmentsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1AttachmentsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1AttachmentsId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1AttachmentsId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1AttachmentsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1AttachmentsId>>
>;

export type DeleteApiV1AttachmentsIdMutationError = ErrorType<unknown>;

export const useDeleteApiV1AttachmentsId = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1AttachmentsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1AttachmentsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1AttachmentsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
export const getApiV1ApiV1RolePermissions = (
  params: GetApiV1ApiV1RolePermissionsParams,
  signal?: AbortSignal,
) => {
  return axiosClient<GetApiV1ApiV1RolePermissions200Item[]>({
    url: '/api/v1/api/v1/role-permissions/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1ApiV1RolePermissionsQueryKey = (
  params: GetApiV1ApiV1RolePermissionsParams,
) => {
  return ['/api/v1/api/v1/role-permissions/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1ApiV1RolePermissionsQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1ApiV1RolePermissions>>,
  TError = ErrorType<unknown>,
>(
  params: GetApiV1ApiV1RolePermissionsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ApiV1RolePermissions>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1ApiV1RolePermissionsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1ApiV1RolePermissions>>> = ({
    signal,
  }) => getApiV1ApiV1RolePermissions(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1ApiV1RolePermissions>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1ApiV1RolePermissionsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1ApiV1RolePermissions>>
>;
export type GetApiV1ApiV1RolePermissionsQueryError = ErrorType<unknown>;

export function useGetApiV1ApiV1RolePermissions<
  TData = Awaited<ReturnType<typeof getApiV1ApiV1RolePermissions>>,
  TError = ErrorType<unknown>,
>(
  params: GetApiV1ApiV1RolePermissionsParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ApiV1RolePermissions>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1ApiV1RolePermissions>>,
          TError,
          Awaited<ReturnType<typeof getApiV1ApiV1RolePermissions>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1ApiV1RolePermissions<
  TData = Awaited<ReturnType<typeof getApiV1ApiV1RolePermissions>>,
  TError = ErrorType<unknown>,
>(
  params: GetApiV1ApiV1RolePermissionsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ApiV1RolePermissions>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1ApiV1RolePermissions>>,
          TError,
          Awaited<ReturnType<typeof getApiV1ApiV1RolePermissions>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1ApiV1RolePermissions<
  TData = Awaited<ReturnType<typeof getApiV1ApiV1RolePermissions>>,
  TError = ErrorType<unknown>,
>(
  params: GetApiV1ApiV1RolePermissionsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ApiV1RolePermissions>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};

export function useGetApiV1ApiV1RolePermissions<
  TData = Awaited<ReturnType<typeof getApiV1ApiV1RolePermissions>>,
  TError = ErrorType<unknown>,
>(
  params: GetApiV1ApiV1RolePermissionsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ApiV1RolePermissions>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1ApiV1RolePermissionsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const putApiV1ApiV1RolePermissionsIdPermissions = (
  id: string,
  putApiV1ApiV1RolePermissionsIdPermissionsBody: BodyType<PutApiV1ApiV1RolePermissionsIdPermissionsBody>,
) => {
  return axiosClient<PutApiV1ApiV1RolePermissionsIdPermissions200>({
    url: `/api/v1/api/v1/role-permissions/${id}/permissions`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: putApiV1ApiV1RolePermissionsIdPermissionsBody,
  });
};

export const getPutApiV1ApiV1RolePermissionsIdPermissionsMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof putApiV1ApiV1RolePermissionsIdPermissions>>,
    TError,
    {
      id: string;
      data: BodyType<PutApiV1ApiV1RolePermissionsIdPermissionsBody>;
    },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof putApiV1ApiV1RolePermissionsIdPermissions>>,
  TError,
  { id: string; data: BodyType<PutApiV1ApiV1RolePermissionsIdPermissionsBody> },
  TContext
> => {
  const mutationKey = ['putApiV1ApiV1RolePermissionsIdPermissions'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof putApiV1ApiV1RolePermissionsIdPermissions>>,
    {
      id: string;
      data: BodyType<PutApiV1ApiV1RolePermissionsIdPermissionsBody>;
    }
  > = (props) => {
    const { id, data } = props ?? {};

    return putApiV1ApiV1RolePermissionsIdPermissions(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PutApiV1ApiV1RolePermissionsIdPermissionsMutationResult = NonNullable<
  Awaited<ReturnType<typeof putApiV1ApiV1RolePermissionsIdPermissions>>
>;
export type PutApiV1ApiV1RolePermissionsIdPermissionsMutationBody =
  BodyType<PutApiV1ApiV1RolePermissionsIdPermissionsBody>;
export type PutApiV1ApiV1RolePermissionsIdPermissionsMutationError = ErrorType<unknown>;

export const usePutApiV1ApiV1RolePermissionsIdPermissions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof putApiV1ApiV1RolePermissionsIdPermissions>>,
    TError,
    {
      id: string;
      data: BodyType<PutApiV1ApiV1RolePermissionsIdPermissionsBody>;
    },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof putApiV1ApiV1RolePermissionsIdPermissions>>,
  TError,
  { id: string; data: BodyType<PutApiV1ApiV1RolePermissionsIdPermissionsBody> },
  TContext
> => {
  const mutationOptions = getPutApiV1ApiV1RolePermissionsIdPermissionsMutationOptions(options);

  return useMutation(mutationOptions);
};
export const postApiV1ApiV1RolePermissionsIdPermissions = (
  id: string,
  postApiV1ApiV1RolePermissionsIdPermissionsBody: BodyType<PostApiV1ApiV1RolePermissionsIdPermissionsBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1ApiV1RolePermissionsIdPermissions201>({
    url: `/api/v1/api/v1/role-permissions/${id}/permissions`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1ApiV1RolePermissionsIdPermissionsBody,
    signal,
  });
};

export const getPostApiV1ApiV1RolePermissionsIdPermissionsMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1ApiV1RolePermissionsIdPermissions>>,
    TError,
    {
      id: string;
      data: BodyType<PostApiV1ApiV1RolePermissionsIdPermissionsBody>;
    },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1ApiV1RolePermissionsIdPermissions>>,
  TError,
  {
    id: string;
    data: BodyType<PostApiV1ApiV1RolePermissionsIdPermissionsBody>;
  },
  TContext
> => {
  const mutationKey = ['postApiV1ApiV1RolePermissionsIdPermissions'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1ApiV1RolePermissionsIdPermissions>>,
    {
      id: string;
      data: BodyType<PostApiV1ApiV1RolePermissionsIdPermissionsBody>;
    }
  > = (props) => {
    const { id, data } = props ?? {};

    return postApiV1ApiV1RolePermissionsIdPermissions(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1ApiV1RolePermissionsIdPermissionsMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1ApiV1RolePermissionsIdPermissions>>
>;
export type PostApiV1ApiV1RolePermissionsIdPermissionsMutationBody =
  BodyType<PostApiV1ApiV1RolePermissionsIdPermissionsBody>;
export type PostApiV1ApiV1RolePermissionsIdPermissionsMutationError = ErrorType<unknown>;

export const usePostApiV1ApiV1RolePermissionsIdPermissions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1ApiV1RolePermissionsIdPermissions>>,
    TError,
    {
      id: string;
      data: BodyType<PostApiV1ApiV1RolePermissionsIdPermissionsBody>;
    },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1ApiV1RolePermissionsIdPermissions>>,
  TError,
  {
    id: string;
    data: BodyType<PostApiV1ApiV1RolePermissionsIdPermissionsBody>;
  },
  TContext
> => {
  const mutationOptions = getPostApiV1ApiV1RolePermissionsIdPermissionsMutationOptions(options);

  return useMutation(mutationOptions);
};
export const deleteApiV1ApiV1RolePermissionsIdPermissions = (
  id: string,
  deleteApiV1ApiV1RolePermissionsIdPermissionsBody: BodyType<DeleteApiV1ApiV1RolePermissionsIdPermissionsBody>,
) => {
  return axiosClient<DeleteApiV1ApiV1RolePermissionsIdPermissions200>({
    url: `/api/v1/api/v1/role-permissions/${id}/permissions`,
    method: 'DELETE',
    headers: { 'Content-Type': 'application/json' },
    data: deleteApiV1ApiV1RolePermissionsIdPermissionsBody,
  });
};

export const getDeleteApiV1ApiV1RolePermissionsIdPermissionsMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1ApiV1RolePermissionsIdPermissions>>,
    TError,
    {
      id: string;
      data: BodyType<DeleteApiV1ApiV1RolePermissionsIdPermissionsBody>;
    },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1ApiV1RolePermissionsIdPermissions>>,
  TError,
  {
    id: string;
    data: BodyType<DeleteApiV1ApiV1RolePermissionsIdPermissionsBody>;
  },
  TContext
> => {
  const mutationKey = ['deleteApiV1ApiV1RolePermissionsIdPermissions'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1ApiV1RolePermissionsIdPermissions>>,
    {
      id: string;
      data: BodyType<DeleteApiV1ApiV1RolePermissionsIdPermissionsBody>;
    }
  > = (props) => {
    const { id, data } = props ?? {};

    return deleteApiV1ApiV1RolePermissionsIdPermissions(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1ApiV1RolePermissionsIdPermissionsMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1ApiV1RolePermissionsIdPermissions>>
>;
export type DeleteApiV1ApiV1RolePermissionsIdPermissionsMutationBody =
  BodyType<DeleteApiV1ApiV1RolePermissionsIdPermissionsBody>;
export type DeleteApiV1ApiV1RolePermissionsIdPermissionsMutationError = ErrorType<unknown>;

export const useDeleteApiV1ApiV1RolePermissionsIdPermissions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1ApiV1RolePermissionsIdPermissions>>,
    TError,
    {
      id: string;
      data: BodyType<DeleteApiV1ApiV1RolePermissionsIdPermissionsBody>;
    },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1ApiV1RolePermissionsIdPermissions>>,
  TError,
  {
    id: string;
    data: BodyType<DeleteApiV1ApiV1RolePermissionsIdPermissionsBody>;
  },
  TContext
> => {
  const mutationOptions = getDeleteApiV1ApiV1RolePermissionsIdPermissionsMutationOptions(options);

  return useMutation(mutationOptions);
};
export const getApiV1TestSuitesIdTestCases = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1TestSuitesIdTestCases200Item[]>({
    url: `/api/v1/test-suites/${id}/test-cases`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1TestSuitesIdTestCasesQueryKey = (id: string) => {
  return [`/api/v1/test-suites/${id}/test-cases`] as const;
};

export const getGetApiV1TestSuitesIdTestCasesQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesIdTestCases>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesIdTestCases>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1TestSuitesIdTestCasesQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1TestSuitesIdTestCases>>> = ({
    signal,
  }) => getApiV1TestSuitesIdTestCases(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesIdTestCases>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1TestSuitesIdTestCasesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1TestSuitesIdTestCases>>
>;
export type GetApiV1TestSuitesIdTestCasesQueryError = ErrorType<unknown>;

export function useGetApiV1TestSuitesIdTestCases<
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesIdTestCases>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesIdTestCases>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestSuitesIdTestCases>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestSuitesIdTestCases>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestSuitesIdTestCases<
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesIdTestCases>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesIdTestCases>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestSuitesIdTestCases>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestSuitesIdTestCases>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestSuitesIdTestCases<
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesIdTestCases>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesIdTestCases>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};

export function useGetApiV1TestSuitesIdTestCases<
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesIdTestCases>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesIdTestCases>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1TestSuitesIdTestCasesQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const postApiV1TestSuitesIdTestCasesLink = (
  id: string,
  postApiV1TestSuitesIdTestCasesLinkBody: BodyType<PostApiV1TestSuitesIdTestCasesLinkBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1TestSuitesIdTestCasesLink201>({
    url: `/api/v1/test-suites/${id}/test-cases/link`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1TestSuitesIdTestCasesLinkBody,
    signal,
  });
};

export const getPostApiV1TestSuitesIdTestCasesLinkMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1TestSuitesIdTestCasesLink>>,
    TError,
    { id: string; data: BodyType<PostApiV1TestSuitesIdTestCasesLinkBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1TestSuitesIdTestCasesLink>>,
  TError,
  { id: string; data: BodyType<PostApiV1TestSuitesIdTestCasesLinkBody> },
  TContext
> => {
  const mutationKey = ['postApiV1TestSuitesIdTestCasesLink'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1TestSuitesIdTestCasesLink>>,
    { id: string; data: BodyType<PostApiV1TestSuitesIdTestCasesLinkBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return postApiV1TestSuitesIdTestCasesLink(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1TestSuitesIdTestCasesLinkMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1TestSuitesIdTestCasesLink>>
>;
export type PostApiV1TestSuitesIdTestCasesLinkMutationBody =
  BodyType<PostApiV1TestSuitesIdTestCasesLinkBody>;
export type PostApiV1TestSuitesIdTestCasesLinkMutationError = ErrorType<unknown>;

export const usePostApiV1TestSuitesIdTestCasesLink = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1TestSuitesIdTestCasesLink>>,
    TError,
    { id: string; data: BodyType<PostApiV1TestSuitesIdTestCasesLinkBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1TestSuitesIdTestCasesLink>>,
  TError,
  { id: string; data: BodyType<PostApiV1TestSuitesIdTestCasesLinkBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1TestSuitesIdTestCasesLinkMutationOptions(options);

  return useMutation(mutationOptions);
};
export const deleteApiV1TestSuitesIdTestCasesTestCaseId = (id: string, testCaseId: string) => {
  return axiosClient<DeleteApiV1TestSuitesIdTestCasesTestCaseId204>({
    url: `/api/v1/test-suites/${id}/test-cases/${testCaseId}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1TestSuitesIdTestCasesTestCaseIdMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1TestSuitesIdTestCasesTestCaseId>>,
    TError,
    { id: string; testCaseId: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1TestSuitesIdTestCasesTestCaseId>>,
  TError,
  { id: string; testCaseId: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1TestSuitesIdTestCasesTestCaseId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1TestSuitesIdTestCasesTestCaseId>>,
    { id: string; testCaseId: string }
  > = (props) => {
    const { id, testCaseId } = props ?? {};

    return deleteApiV1TestSuitesIdTestCasesTestCaseId(id, testCaseId);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1TestSuitesIdTestCasesTestCaseIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1TestSuitesIdTestCasesTestCaseId>>
>;

export type DeleteApiV1TestSuitesIdTestCasesTestCaseIdMutationError = ErrorType<unknown>;

export const useDeleteApiV1TestSuitesIdTestCasesTestCaseId = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1TestSuitesIdTestCasesTestCaseId>>,
    TError,
    { id: string; testCaseId: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1TestSuitesIdTestCasesTestCaseId>>,
  TError,
  { id: string; testCaseId: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1TestSuitesIdTestCasesTestCaseIdMutationOptions(options);

  return useMutation(mutationOptions);
};
export const patchApiV1TestSuitesIdTestCasesTestCaseId = (
  id: string,
  testCaseId: string,
  patchApiV1TestSuitesIdTestCasesTestCaseIdBody: BodyType<PatchApiV1TestSuitesIdTestCasesTestCaseIdBody>,
) => {
  return axiosClient<PatchApiV1TestSuitesIdTestCasesTestCaseId200>({
    url: `/api/v1/test-suites/${id}/test-cases/${testCaseId}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1TestSuitesIdTestCasesTestCaseIdBody,
  });
};

export const getPatchApiV1TestSuitesIdTestCasesTestCaseIdMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1TestSuitesIdTestCasesTestCaseId>>,
    TError,
    {
      id: string;
      testCaseId: string;
      data: BodyType<PatchApiV1TestSuitesIdTestCasesTestCaseIdBody>;
    },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1TestSuitesIdTestCasesTestCaseId>>,
  TError,
  {
    id: string;
    testCaseId: string;
    data: BodyType<PatchApiV1TestSuitesIdTestCasesTestCaseIdBody>;
  },
  TContext
> => {
  const mutationKey = ['patchApiV1TestSuitesIdTestCasesTestCaseId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1TestSuitesIdTestCasesTestCaseId>>,
    {
      id: string;
      testCaseId: string;
      data: BodyType<PatchApiV1TestSuitesIdTestCasesTestCaseIdBody>;
    }
  > = (props) => {
    const { id, testCaseId, data } = props ?? {};

    return patchApiV1TestSuitesIdTestCasesTestCaseId(id, testCaseId, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1TestSuitesIdTestCasesTestCaseIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1TestSuitesIdTestCasesTestCaseId>>
>;
export type PatchApiV1TestSuitesIdTestCasesTestCaseIdMutationBody =
  BodyType<PatchApiV1TestSuitesIdTestCasesTestCaseIdBody>;
export type PatchApiV1TestSuitesIdTestCasesTestCaseIdMutationError = ErrorType<unknown>;

export const usePatchApiV1TestSuitesIdTestCasesTestCaseId = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1TestSuitesIdTestCasesTestCaseId>>,
    TError,
    {
      id: string;
      testCaseId: string;
      data: BodyType<PatchApiV1TestSuitesIdTestCasesTestCaseIdBody>;
    },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1TestSuitesIdTestCasesTestCaseId>>,
  TError,
  {
    id: string;
    testCaseId: string;
    data: BodyType<PatchApiV1TestSuitesIdTestCasesTestCaseIdBody>;
  },
  TContext
> => {
  const mutationOptions = getPatchApiV1TestSuitesIdTestCasesTestCaseIdMutationOptions(options);

  return useMutation(mutationOptions);
};
export const getApiV1TestSuitesIdAvailableTestCases = (
  id: string,
  params?: GetApiV1TestSuitesIdAvailableTestCasesParams,
  signal?: AbortSignal,
) => {
  return axiosClient<GetApiV1TestSuitesIdAvailableTestCases200Item[]>({
    url: `/api/v1/test-suites/${id}/available-test-cases`,
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1TestSuitesIdAvailableTestCasesQueryKey = (
  id: string,
  params?: GetApiV1TestSuitesIdAvailableTestCasesParams,
) => {
  return [`/api/v1/test-suites/${id}/available-test-cases`, ...(params ? [params] : [])] as const;
};

export const getGetApiV1TestSuitesIdAvailableTestCasesQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesIdAvailableTestCases>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  params?: GetApiV1TestSuitesIdAvailableTestCasesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getApiV1TestSuitesIdAvailableTestCases>>,
        TError,
        TData
      >
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetApiV1TestSuitesIdAvailableTestCasesQueryKey(id, params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getApiV1TestSuitesIdAvailableTestCases>>
  > = ({ signal }) => getApiV1TestSuitesIdAvailableTestCases(id, params, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1TestSuitesIdAvailableTestCases>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1TestSuitesIdAvailableTestCasesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1TestSuitesIdAvailableTestCases>>
>;
export type GetApiV1TestSuitesIdAvailableTestCasesQueryError = ErrorType<unknown>;

export function useGetApiV1TestSuitesIdAvailableTestCases<
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesIdAvailableTestCases>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  params: undefined | GetApiV1TestSuitesIdAvailableTestCasesParams,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getApiV1TestSuitesIdAvailableTestCases>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestSuitesIdAvailableTestCases>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestSuitesIdAvailableTestCases>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestSuitesIdAvailableTestCases<
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesIdAvailableTestCases>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  params?: GetApiV1TestSuitesIdAvailableTestCasesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getApiV1TestSuitesIdAvailableTestCases>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestSuitesIdAvailableTestCases>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestSuitesIdAvailableTestCases>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestSuitesIdAvailableTestCases<
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesIdAvailableTestCases>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  params?: GetApiV1TestSuitesIdAvailableTestCasesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getApiV1TestSuitesIdAvailableTestCases>>,
        TError,
        TData
      >
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};

export function useGetApiV1TestSuitesIdAvailableTestCases<
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesIdAvailableTestCases>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  params?: GetApiV1TestSuitesIdAvailableTestCasesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getApiV1TestSuitesIdAvailableTestCases>>,
        TError,
        TData
      >
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1TestSuitesIdAvailableTestCasesQueryOptions(id, params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getApiV1TestSuitesAllTestCases = (
  params?: GetApiV1TestSuitesAllTestCasesParams,
  signal?: AbortSignal,
) => {
  return axiosClient<GetApiV1TestSuitesAllTestCases200>({
    url: '/api/v1/test-suites/all-test-cases',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1TestSuitesAllTestCasesQueryKey = (
  params?: GetApiV1TestSuitesAllTestCasesParams,
) => {
  return ['/api/v1/test-suites/all-test-cases', ...(params ? [params] : [])] as const;
};

export const getGetApiV1TestSuitesAllTestCasesQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesAllTestCases>>,
  TError = ErrorType<unknown>,
>(
  params?: GetApiV1TestSuitesAllTestCasesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesAllTestCases>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1TestSuitesAllTestCasesQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1TestSuitesAllTestCases>>> = ({
    signal,
  }) => getApiV1TestSuitesAllTestCases(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1TestSuitesAllTestCases>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1TestSuitesAllTestCasesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1TestSuitesAllTestCases>>
>;
export type GetApiV1TestSuitesAllTestCasesQueryError = ErrorType<unknown>;

export function useGetApiV1TestSuitesAllTestCases<
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesAllTestCases>>,
  TError = ErrorType<unknown>,
>(
  params: undefined | GetApiV1TestSuitesAllTestCasesParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesAllTestCases>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestSuitesAllTestCases>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestSuitesAllTestCases>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestSuitesAllTestCases<
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesAllTestCases>>,
  TError = ErrorType<unknown>,
>(
  params?: GetApiV1TestSuitesAllTestCasesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesAllTestCases>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestSuitesAllTestCases>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestSuitesAllTestCases>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestSuitesAllTestCases<
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesAllTestCases>>,
  TError = ErrorType<unknown>,
>(
  params?: GetApiV1TestSuitesAllTestCasesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesAllTestCases>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};

export function useGetApiV1TestSuitesAllTestCases<
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesAllTestCases>>,
  TError = ErrorType<unknown>,
>(
  params?: GetApiV1TestSuitesAllTestCasesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesAllTestCases>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1TestSuitesAllTestCasesQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getApiV1TestSuitesAllTestSuites = (
  params?: GetApiV1TestSuitesAllTestSuitesParams,
  signal?: AbortSignal,
) => {
  return axiosClient<GetApiV1TestSuitesAllTestSuites200>({
    url: '/api/v1/test-suites/all-test-suites',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1TestSuitesAllTestSuitesQueryKey = (
  params?: GetApiV1TestSuitesAllTestSuitesParams,
) => {
  return ['/api/v1/test-suites/all-test-suites', ...(params ? [params] : [])] as const;
};

export const getGetApiV1TestSuitesAllTestSuitesQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesAllTestSuites>>,
  TError = ErrorType<unknown>,
>(
  params?: GetApiV1TestSuitesAllTestSuitesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesAllTestSuites>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1TestSuitesAllTestSuitesQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1TestSuitesAllTestSuites>>> = ({
    signal,
  }) => getApiV1TestSuitesAllTestSuites(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1TestSuitesAllTestSuites>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1TestSuitesAllTestSuitesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1TestSuitesAllTestSuites>>
>;
export type GetApiV1TestSuitesAllTestSuitesQueryError = ErrorType<unknown>;

export function useGetApiV1TestSuitesAllTestSuites<
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesAllTestSuites>>,
  TError = ErrorType<unknown>,
>(
  params: undefined | GetApiV1TestSuitesAllTestSuitesParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesAllTestSuites>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestSuitesAllTestSuites>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestSuitesAllTestSuites>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestSuitesAllTestSuites<
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesAllTestSuites>>,
  TError = ErrorType<unknown>,
>(
  params?: GetApiV1TestSuitesAllTestSuitesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesAllTestSuites>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestSuitesAllTestSuites>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestSuitesAllTestSuites>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestSuitesAllTestSuites<
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesAllTestSuites>>,
  TError = ErrorType<unknown>,
>(
  params?: GetApiV1TestSuitesAllTestSuitesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesAllTestSuites>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};

export function useGetApiV1TestSuitesAllTestSuites<
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesAllTestSuites>>,
  TError = ErrorType<unknown>,
>(
  params?: GetApiV1TestSuitesAllTestSuitesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesAllTestSuites>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1TestSuitesAllTestSuitesQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getDocs = (signal?: AbortSignal) => {
  return axiosClient<void>({ url: '/docs', method: 'GET', signal });
};

export const getGetDocsQueryKey = () => {
  return ['/docs'] as const;
};

export const getGetDocsQueryOptions = <
  TData = Awaited<ReturnType<typeof getDocs>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocs>>, TError, TData>>;
}) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetDocsQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getDocs>>> = ({ signal }) =>
    getDocs(signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getDocs>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetDocsQueryResult = NonNullable<Awaited<ReturnType<typeof getDocs>>>;
export type GetDocsQueryError = ErrorType<unknown>;

export function useGetDocs<
  TData = Awaited<ReturnType<typeof getDocs>>,
  TError = ErrorType<unknown>,
>(options: {
  query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocs>>, TError, TData>> &
    Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getDocs>>,
        TError,
        Awaited<ReturnType<typeof getDocs>>
      >,
      'initialData'
    >;
}): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetDocs<
  TData = Awaited<ReturnType<typeof getDocs>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocs>>, TError, TData>> &
    Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getDocs>>,
        TError,
        Awaited<ReturnType<typeof getDocs>>
      >,
      'initialData'
    >;
}): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetDocs<
  TData = Awaited<ReturnType<typeof getDocs>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocs>>, TError, TData>>;
}): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};

export function useGetDocs<
  TData = Awaited<ReturnType<typeof getDocs>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocs>>, TError, TData>>;
}): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetDocsQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}
