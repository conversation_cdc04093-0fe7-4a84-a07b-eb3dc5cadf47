/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1PrioritiesId204,
  ErrorResponse,
  GetApiV1Priorities200,
  GetApiV1PrioritiesId200,
  GetApiV1PrioritiesParams,
  PatchApiV1PrioritiesId200,
  PatchApiV1PrioritiesIdBody,
  PostApiV1Priorities201,
  PostApiV1PrioritiesBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of priorities resources with optional filtering and sorting
 * @summary Get priorities list
 */
export const getApiV1Priorities = (params?: GetApiV1PrioritiesParams, signal?: AbortSignal) => {
  return axiosClient<GetApiV1Priorities200>({
    url: '/api/v1/priorities/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1PrioritiesQueryKey = (params?: GetApiV1PrioritiesParams) => {
  return ['/api/v1/priorities/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1PrioritiesQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1Priorities>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1PrioritiesParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Priorities>>, TError, TData>>;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1PrioritiesQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1Priorities>>> = ({ signal }) =>
    getApiV1Priorities(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1Priorities>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1PrioritiesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1Priorities>>
>;
export type GetApiV1PrioritiesQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1Priorities<
  TData = Awaited<ReturnType<typeof getApiV1Priorities>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1PrioritiesParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Priorities>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Priorities>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Priorities>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Priorities<
  TData = Awaited<ReturnType<typeof getApiV1Priorities>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1PrioritiesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1Priorities>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Priorities>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Priorities>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Priorities<
  TData = Awaited<ReturnType<typeof getApiV1Priorities>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1PrioritiesParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Priorities>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get priorities list
 */

export function useGetApiV1Priorities<
  TData = Awaited<ReturnType<typeof getApiV1Priorities>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1PrioritiesParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Priorities>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1PrioritiesQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new priority resource
 * @summary Create priority
 */
export const postApiV1Priorities = (
  postApiV1PrioritiesBody: BodyType<PostApiV1PrioritiesBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1Priorities201>({
    url: '/api/v1/priorities/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1PrioritiesBody,
    signal,
  });
};

export const getPostApiV1PrioritiesMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1Priorities>>,
    TError,
    { data: BodyType<PostApiV1PrioritiesBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1Priorities>>,
  TError,
  { data: BodyType<PostApiV1PrioritiesBody> },
  TContext
> => {
  const mutationKey = ['postApiV1Priorities'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1Priorities>>,
    { data: BodyType<PostApiV1PrioritiesBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1Priorities(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1PrioritiesMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1Priorities>>
>;
export type PostApiV1PrioritiesMutationBody = BodyType<PostApiV1PrioritiesBody>;
export type PostApiV1PrioritiesMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create priority
 */
export const usePostApiV1Priorities = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1Priorities>>,
    TError,
    { data: BodyType<PostApiV1PrioritiesBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1Priorities>>,
  TError,
  { data: BodyType<PostApiV1PrioritiesBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1PrioritiesMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific priority resource by its unique identifier
 * @summary Get priority by ID
 */
export const getApiV1PrioritiesId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1PrioritiesId200>({
    url: `/api/v1/priorities/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1PrioritiesIdQueryKey = (id: string) => {
  return [`/api/v1/priorities/${id}`] as const;
};

export const getGetApiV1PrioritiesIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1PrioritiesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1PrioritiesId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1PrioritiesIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1PrioritiesId>>> = ({ signal }) =>
    getApiV1PrioritiesId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1PrioritiesId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1PrioritiesIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1PrioritiesId>>
>;
export type GetApiV1PrioritiesIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1PrioritiesId<
  TData = Awaited<ReturnType<typeof getApiV1PrioritiesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1PrioritiesId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1PrioritiesId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1PrioritiesId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1PrioritiesId<
  TData = Awaited<ReturnType<typeof getApiV1PrioritiesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1PrioritiesId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1PrioritiesId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1PrioritiesId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1PrioritiesId<
  TData = Awaited<ReturnType<typeof getApiV1PrioritiesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1PrioritiesId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get priority by ID
 */

export function useGetApiV1PrioritiesId<
  TData = Awaited<ReturnType<typeof getApiV1PrioritiesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1PrioritiesId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1PrioritiesIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing priority resource
 * @summary Update priority
 */
export const patchApiV1PrioritiesId = (
  id: string,
  patchApiV1PrioritiesIdBody: BodyType<PatchApiV1PrioritiesIdBody>,
) => {
  return axiosClient<PatchApiV1PrioritiesId200>({
    url: `/api/v1/priorities/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1PrioritiesIdBody,
  });
};

export const getPatchApiV1PrioritiesIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1PrioritiesId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1PrioritiesIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1PrioritiesId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1PrioritiesIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1PrioritiesId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1PrioritiesId>>,
    { id: string; data: BodyType<PatchApiV1PrioritiesIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1PrioritiesId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1PrioritiesIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1PrioritiesId>>
>;
export type PatchApiV1PrioritiesIdMutationBody = BodyType<PatchApiV1PrioritiesIdBody>;
export type PatchApiV1PrioritiesIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update priority
 */
export const usePatchApiV1PrioritiesId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1PrioritiesId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1PrioritiesIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1PrioritiesId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1PrioritiesIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1PrioritiesIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing priority resource
 * @summary Delete priority
 */
export const deleteApiV1PrioritiesId = (id: string) => {
  return axiosClient<DeleteApiV1PrioritiesId204>({
    url: `/api/v1/priorities/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1PrioritiesIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1PrioritiesId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1PrioritiesId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1PrioritiesId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1PrioritiesId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1PrioritiesId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1PrioritiesIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1PrioritiesId>>
>;

export type DeleteApiV1PrioritiesIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete priority
 */
export const useDeleteApiV1PrioritiesId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1PrioritiesId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1PrioritiesId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1PrioritiesIdMutationOptions(options);

  return useMutation(mutationOptions);
};
