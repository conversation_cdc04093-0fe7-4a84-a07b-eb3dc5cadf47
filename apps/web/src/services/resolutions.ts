/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1ResolutionsId204,
  ErrorResponse,
  GetApiV1Resolutions200,
  GetApiV1ResolutionsId200,
  GetApiV1ResolutionsParams,
  PatchApiV1ResolutionsId200,
  PatchApiV1ResolutionsIdBody,
  PostApiV1Resolutions201,
  PostApiV1ResolutionsBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of resolutions resources with optional filtering and sorting
 * @summary Get resolutions list
 */
export const getApiV1Resolutions = (params?: GetApiV1ResolutionsParams, signal?: AbortSignal) => {
  return axiosClient<GetApiV1Resolutions200>({
    url: '/api/v1/resolutions/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1ResolutionsQueryKey = (params?: GetApiV1ResolutionsParams) => {
  return ['/api/v1/resolutions/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1ResolutionsQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1Resolutions>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1ResolutionsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1Resolutions>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1ResolutionsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1Resolutions>>> = ({ signal }) =>
    getApiV1Resolutions(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1Resolutions>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1ResolutionsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1Resolutions>>
>;
export type GetApiV1ResolutionsQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1Resolutions<
  TData = Awaited<ReturnType<typeof getApiV1Resolutions>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1ResolutionsParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1Resolutions>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Resolutions>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Resolutions>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Resolutions<
  TData = Awaited<ReturnType<typeof getApiV1Resolutions>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1ResolutionsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1Resolutions>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Resolutions>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Resolutions>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Resolutions<
  TData = Awaited<ReturnType<typeof getApiV1Resolutions>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1ResolutionsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1Resolutions>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get resolutions list
 */

export function useGetApiV1Resolutions<
  TData = Awaited<ReturnType<typeof getApiV1Resolutions>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1ResolutionsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1Resolutions>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1ResolutionsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new resolution resource
 * @summary Create resolution
 */
export const postApiV1Resolutions = (
  postApiV1ResolutionsBody: BodyType<PostApiV1ResolutionsBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1Resolutions201>({
    url: '/api/v1/resolutions/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1ResolutionsBody,
    signal,
  });
};

export const getPostApiV1ResolutionsMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1Resolutions>>,
    TError,
    { data: BodyType<PostApiV1ResolutionsBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1Resolutions>>,
  TError,
  { data: BodyType<PostApiV1ResolutionsBody> },
  TContext
> => {
  const mutationKey = ['postApiV1Resolutions'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1Resolutions>>,
    { data: BodyType<PostApiV1ResolutionsBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1Resolutions(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1ResolutionsMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1Resolutions>>
>;
export type PostApiV1ResolutionsMutationBody = BodyType<PostApiV1ResolutionsBody>;
export type PostApiV1ResolutionsMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create resolution
 */
export const usePostApiV1Resolutions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1Resolutions>>,
    TError,
    { data: BodyType<PostApiV1ResolutionsBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1Resolutions>>,
  TError,
  { data: BodyType<PostApiV1ResolutionsBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1ResolutionsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific resolution resource by its unique identifier
 * @summary Get resolution by ID
 */
export const getApiV1ResolutionsId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1ResolutionsId200>({
    url: `/api/v1/resolutions/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1ResolutionsIdQueryKey = (id: string) => {
  return [`/api/v1/resolutions/${id}`] as const;
};

export const getGetApiV1ResolutionsIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1ResolutionsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ResolutionsId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1ResolutionsIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1ResolutionsId>>> = ({ signal }) =>
    getApiV1ResolutionsId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1ResolutionsId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1ResolutionsIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1ResolutionsId>>
>;
export type GetApiV1ResolutionsIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1ResolutionsId<
  TData = Awaited<ReturnType<typeof getApiV1ResolutionsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ResolutionsId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1ResolutionsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1ResolutionsId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1ResolutionsId<
  TData = Awaited<ReturnType<typeof getApiV1ResolutionsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ResolutionsId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1ResolutionsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1ResolutionsId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1ResolutionsId<
  TData = Awaited<ReturnType<typeof getApiV1ResolutionsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ResolutionsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get resolution by ID
 */

export function useGetApiV1ResolutionsId<
  TData = Awaited<ReturnType<typeof getApiV1ResolutionsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ResolutionsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1ResolutionsIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing resolution resource
 * @summary Update resolution
 */
export const patchApiV1ResolutionsId = (
  id: string,
  patchApiV1ResolutionsIdBody: BodyType<PatchApiV1ResolutionsIdBody>,
) => {
  return axiosClient<PatchApiV1ResolutionsId200>({
    url: `/api/v1/resolutions/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1ResolutionsIdBody,
  });
};

export const getPatchApiV1ResolutionsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1ResolutionsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1ResolutionsIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1ResolutionsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1ResolutionsIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1ResolutionsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1ResolutionsId>>,
    { id: string; data: BodyType<PatchApiV1ResolutionsIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1ResolutionsId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1ResolutionsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1ResolutionsId>>
>;
export type PatchApiV1ResolutionsIdMutationBody = BodyType<PatchApiV1ResolutionsIdBody>;
export type PatchApiV1ResolutionsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update resolution
 */
export const usePatchApiV1ResolutionsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1ResolutionsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1ResolutionsIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1ResolutionsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1ResolutionsIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1ResolutionsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing resolution resource
 * @summary Delete resolution
 */
export const deleteApiV1ResolutionsId = (id: string) => {
  return axiosClient<DeleteApiV1ResolutionsId204>({
    url: `/api/v1/resolutions/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1ResolutionsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1ResolutionsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1ResolutionsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1ResolutionsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1ResolutionsId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1ResolutionsId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1ResolutionsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1ResolutionsId>>
>;

export type DeleteApiV1ResolutionsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete resolution
 */
export const useDeleteApiV1ResolutionsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1ResolutionsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1ResolutionsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1ResolutionsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
