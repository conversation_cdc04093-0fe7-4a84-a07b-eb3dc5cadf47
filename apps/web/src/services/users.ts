/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1UsersId204,
  ErrorResponse,
  GetApiV1Users200,
  GetApiV1UsersId200,
  GetApiV1UsersParams,
  PatchApiV1UsersId200,
  PatchApiV1UsersIdBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of users resources with optional filtering and sorting
 * @summary Get users list
 */
export const getApiV1Users = (params?: GetApiV1UsersParams, signal?: AbortSignal) => {
  return axiosClient<GetApiV1Users200>({
    url: '/api/v1/users/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1UsersQueryKey = (params?: GetApiV1UsersParams) => {
  return ['/api/v1/users/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1UsersQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1Users>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1UsersParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Users>>, TError, TData>>;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1UsersQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1Users>>> = ({ signal }) =>
    getApiV1Users(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1Users>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1UsersQueryResult = NonNullable<Awaited<ReturnType<typeof getApiV1Users>>>;
export type GetApiV1UsersQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1Users<
  TData = Awaited<ReturnType<typeof getApiV1Users>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1UsersParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Users>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Users>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Users>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Users<
  TData = Awaited<ReturnType<typeof getApiV1Users>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1UsersParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Users>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Users>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Users>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Users<
  TData = Awaited<ReturnType<typeof getApiV1Users>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1UsersParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Users>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get users list
 */

export function useGetApiV1Users<
  TData = Awaited<ReturnType<typeof getApiV1Users>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1UsersParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Users>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1UsersQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Retrieve a specific user resource by its unique identifier
 * @summary Get user by ID
 */
export const getApiV1UsersId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1UsersId200>({
    url: `/api/v1/users/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1UsersIdQueryKey = (id: string) => {
  return [`/api/v1/users/${id}`] as const;
};

export const getGetApiV1UsersIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1UsersId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1UsersId>>, TError, TData>>;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1UsersIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1UsersId>>> = ({ signal }) =>
    getApiV1UsersId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1UsersId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1UsersIdQueryResult = NonNullable<Awaited<ReturnType<typeof getApiV1UsersId>>>;
export type GetApiV1UsersIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1UsersId<
  TData = Awaited<ReturnType<typeof getApiV1UsersId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1UsersId>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1UsersId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1UsersId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1UsersId<
  TData = Awaited<ReturnType<typeof getApiV1UsersId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1UsersId>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1UsersId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1UsersId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1UsersId<
  TData = Awaited<ReturnType<typeof getApiV1UsersId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1UsersId>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get user by ID
 */

export function useGetApiV1UsersId<
  TData = Awaited<ReturnType<typeof getApiV1UsersId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1UsersId>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1UsersIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing user resource
 * @summary Update user
 */
export const patchApiV1UsersId = (
  id: string,
  patchApiV1UsersIdBody: BodyType<PatchApiV1UsersIdBody>,
) => {
  return axiosClient<PatchApiV1UsersId200>({
    url: `/api/v1/users/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1UsersIdBody,
  });
};

export const getPatchApiV1UsersIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1UsersId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1UsersIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1UsersId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1UsersIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1UsersId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1UsersId>>,
    { id: string; data: BodyType<PatchApiV1UsersIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1UsersId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1UsersIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1UsersId>>
>;
export type PatchApiV1UsersIdMutationBody = BodyType<PatchApiV1UsersIdBody>;
export type PatchApiV1UsersIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update user
 */
export const usePatchApiV1UsersId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1UsersId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1UsersIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1UsersId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1UsersIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1UsersIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing user resource
 * @summary Delete user
 */
export const deleteApiV1UsersId = (id: string) => {
  return axiosClient<DeleteApiV1UsersId204>({
    url: `/api/v1/users/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1UsersIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1UsersId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1UsersId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1UsersId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1UsersId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1UsersId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1UsersIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1UsersId>>
>;

export type DeleteApiV1UsersIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete user
 */
export const useDeleteApiV1UsersId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1UsersId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1UsersId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1UsersIdMutationOptions(options);

  return useMutation(mutationOptions);
};
