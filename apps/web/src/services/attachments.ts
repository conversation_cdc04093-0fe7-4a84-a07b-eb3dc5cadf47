/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  ErrorResponse,
  GetApiV1Attachments200,
  GetApiV1AttachmentsId200,
  GetApiV1AttachmentsParams,
  PatchApiV1AttachmentsId200,
  PatchApiV1AttachmentsIdBody,
} from './hooks.schemas';

/**
 * Retrieve a specific attachment resource by its unique identifier
 * @summary Get attachment by ID
 */
export const getApiV1AttachmentsId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1AttachmentsId200>({
    url: `/api/v1/attachments/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1AttachmentsIdQueryKey = (id: string) => {
  return [`/api/v1/attachments/${id}`] as const;
};

export const getGetApiV1AttachmentsIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1AttachmentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1AttachmentsId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1AttachmentsIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1AttachmentsId>>> = ({ signal }) =>
    getApiV1AttachmentsId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1AttachmentsId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1AttachmentsIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1AttachmentsId>>
>;
export type GetApiV1AttachmentsIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1AttachmentsId<
  TData = Awaited<ReturnType<typeof getApiV1AttachmentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1AttachmentsId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1AttachmentsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1AttachmentsId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1AttachmentsId<
  TData = Awaited<ReturnType<typeof getApiV1AttachmentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1AttachmentsId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1AttachmentsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1AttachmentsId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1AttachmentsId<
  TData = Awaited<ReturnType<typeof getApiV1AttachmentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1AttachmentsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get attachment by ID
 */

export function useGetApiV1AttachmentsId<
  TData = Awaited<ReturnType<typeof getApiV1AttachmentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1AttachmentsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1AttachmentsIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing attachment resource
 * @summary Update attachment
 */
export const patchApiV1AttachmentsId = (
  id: string,
  patchApiV1AttachmentsIdBody: BodyType<PatchApiV1AttachmentsIdBody>,
) => {
  return axiosClient<PatchApiV1AttachmentsId200>({
    url: `/api/v1/attachments/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1AttachmentsIdBody,
  });
};

export const getPatchApiV1AttachmentsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1AttachmentsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1AttachmentsIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1AttachmentsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1AttachmentsIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1AttachmentsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1AttachmentsId>>,
    { id: string; data: BodyType<PatchApiV1AttachmentsIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1AttachmentsId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1AttachmentsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1AttachmentsId>>
>;
export type PatchApiV1AttachmentsIdMutationBody = BodyType<PatchApiV1AttachmentsIdBody>;
export type PatchApiV1AttachmentsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update attachment
 */
export const usePatchApiV1AttachmentsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1AttachmentsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1AttachmentsIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1AttachmentsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1AttachmentsIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1AttachmentsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a paginated list of attachments resources with optional filtering and sorting
 * @summary Get attachments list
 */
export const getApiV1Attachments = (params?: GetApiV1AttachmentsParams, signal?: AbortSignal) => {
  return axiosClient<GetApiV1Attachments200>({
    url: '/api/v1/attachments/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1AttachmentsQueryKey = (params?: GetApiV1AttachmentsParams) => {
  return ['/api/v1/attachments/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1AttachmentsQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1Attachments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1AttachmentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1Attachments>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1AttachmentsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1Attachments>>> = ({ signal }) =>
    getApiV1Attachments(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1Attachments>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1AttachmentsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1Attachments>>
>;
export type GetApiV1AttachmentsQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1Attachments<
  TData = Awaited<ReturnType<typeof getApiV1Attachments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1AttachmentsParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1Attachments>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Attachments>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Attachments>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Attachments<
  TData = Awaited<ReturnType<typeof getApiV1Attachments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1AttachmentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1Attachments>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Attachments>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Attachments>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Attachments<
  TData = Awaited<ReturnType<typeof getApiV1Attachments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1AttachmentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1Attachments>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get attachments list
 */

export function useGetApiV1Attachments<
  TData = Awaited<ReturnType<typeof getApiV1Attachments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1AttachmentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1Attachments>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1AttachmentsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}
