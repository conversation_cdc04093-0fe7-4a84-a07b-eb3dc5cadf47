/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1RolesId204,
  ErrorResponse,
  GetApiV1Roles200,
  GetApiV1RolesId200,
  GetApiV1RolesParams,
  PatchApiV1RolesId200,
  PatchApiV1RolesIdBody,
  PostApiV1Roles201,
  PostApiV1RolesBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of roles resources with optional filtering and sorting
 * @summary Get roles list
 */
export const getApiV1Roles = (params?: GetApiV1RolesParams, signal?: AbortSignal) => {
  return axiosClient<GetApiV1Roles200>({
    url: '/api/v1/roles/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1RolesQueryKey = (params?: GetApiV1RolesParams) => {
  return ['/api/v1/roles/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1RolesQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1Roles>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1RolesParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Roles>>, TError, TData>>;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1RolesQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1Roles>>> = ({ signal }) =>
    getApiV1Roles(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1Roles>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1RolesQueryResult = NonNullable<Awaited<ReturnType<typeof getApiV1Roles>>>;
export type GetApiV1RolesQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1Roles<
  TData = Awaited<ReturnType<typeof getApiV1Roles>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1RolesParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Roles>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Roles>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Roles>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Roles<
  TData = Awaited<ReturnType<typeof getApiV1Roles>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1RolesParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Roles>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Roles>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Roles>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Roles<
  TData = Awaited<ReturnType<typeof getApiV1Roles>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1RolesParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Roles>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get roles list
 */

export function useGetApiV1Roles<
  TData = Awaited<ReturnType<typeof getApiV1Roles>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1RolesParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Roles>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1RolesQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new role resource
 * @summary Create role
 */
export const postApiV1Roles = (
  postApiV1RolesBody: BodyType<PostApiV1RolesBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1Roles201>({
    url: '/api/v1/roles/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1RolesBody,
    signal,
  });
};

export const getPostApiV1RolesMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1Roles>>,
    TError,
    { data: BodyType<PostApiV1RolesBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1Roles>>,
  TError,
  { data: BodyType<PostApiV1RolesBody> },
  TContext
> => {
  const mutationKey = ['postApiV1Roles'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1Roles>>,
    { data: BodyType<PostApiV1RolesBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1Roles(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1RolesMutationResult = NonNullable<Awaited<ReturnType<typeof postApiV1Roles>>>;
export type PostApiV1RolesMutationBody = BodyType<PostApiV1RolesBody>;
export type PostApiV1RolesMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create role
 */
export const usePostApiV1Roles = <TError = ErrorType<ErrorResponse>, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1Roles>>,
    TError,
    { data: BodyType<PostApiV1RolesBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1Roles>>,
  TError,
  { data: BodyType<PostApiV1RolesBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1RolesMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific role resource by its unique identifier
 * @summary Get role by ID
 */
export const getApiV1RolesId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1RolesId200>({
    url: `/api/v1/roles/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1RolesIdQueryKey = (id: string) => {
  return [`/api/v1/roles/${id}`] as const;
};

export const getGetApiV1RolesIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1RolesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1RolesId>>, TError, TData>>;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1RolesIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1RolesId>>> = ({ signal }) =>
    getApiV1RolesId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1RolesId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1RolesIdQueryResult = NonNullable<Awaited<ReturnType<typeof getApiV1RolesId>>>;
export type GetApiV1RolesIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1RolesId<
  TData = Awaited<ReturnType<typeof getApiV1RolesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1RolesId>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1RolesId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1RolesId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1RolesId<
  TData = Awaited<ReturnType<typeof getApiV1RolesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1RolesId>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1RolesId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1RolesId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1RolesId<
  TData = Awaited<ReturnType<typeof getApiV1RolesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1RolesId>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get role by ID
 */

export function useGetApiV1RolesId<
  TData = Awaited<ReturnType<typeof getApiV1RolesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1RolesId>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1RolesIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing role resource
 * @summary Update role
 */
export const patchApiV1RolesId = (
  id: string,
  patchApiV1RolesIdBody: BodyType<PatchApiV1RolesIdBody>,
) => {
  return axiosClient<PatchApiV1RolesId200>({
    url: `/api/v1/roles/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1RolesIdBody,
  });
};

export const getPatchApiV1RolesIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1RolesId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1RolesIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1RolesId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1RolesIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1RolesId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1RolesId>>,
    { id: string; data: BodyType<PatchApiV1RolesIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1RolesId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1RolesIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1RolesId>>
>;
export type PatchApiV1RolesIdMutationBody = BodyType<PatchApiV1RolesIdBody>;
export type PatchApiV1RolesIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update role
 */
export const usePatchApiV1RolesId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1RolesId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1RolesIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1RolesId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1RolesIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1RolesIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing role resource
 * @summary Delete role
 */
export const deleteApiV1RolesId = (id: string) => {
  return axiosClient<DeleteApiV1RolesId204>({
    url: `/api/v1/roles/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1RolesIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1RolesId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1RolesId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1RolesId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1RolesId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1RolesId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1RolesIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1RolesId>>
>;

export type DeleteApiV1RolesIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete role
 */
export const useDeleteApiV1RolesId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1RolesId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1RolesId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1RolesIdMutationOptions(options);

  return useMutation(mutationOptions);
};
