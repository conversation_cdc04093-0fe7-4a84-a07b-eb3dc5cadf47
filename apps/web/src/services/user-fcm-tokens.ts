/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  ErrorResponse,
  GetApiV1UserFcmTokens200Item,
  PostApiV1UserFcmTokens201,
  PostApiV1UserFcmTokensBody,
} from './hooks.schemas';

/**
 * Register a new FCM token for the authenticated user
 * @summary Register FCM token
 */
export const postApiV1UserFcmTokens = (
  postApiV1UserFcmTokensBody: BodyType<PostApiV1UserFcmTokensBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1UserFcmTokens201>({
    url: '/api/v1/user-fcm-tokens/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1UserFcmTokensBody,
    signal,
  });
};

export const getPostApiV1UserFcmTokensMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1UserFcmTokens>>,
    TError,
    { data: BodyType<PostApiV1UserFcmTokensBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1UserFcmTokens>>,
  TError,
  { data: BodyType<PostApiV1UserFcmTokensBody> },
  TContext
> => {
  const mutationKey = ['postApiV1UserFcmTokens'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1UserFcmTokens>>,
    { data: BodyType<PostApiV1UserFcmTokensBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1UserFcmTokens(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1UserFcmTokensMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1UserFcmTokens>>
>;
export type PostApiV1UserFcmTokensMutationBody = BodyType<PostApiV1UserFcmTokensBody>;
export type PostApiV1UserFcmTokensMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Register FCM token
 */
export const usePostApiV1UserFcmTokens = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1UserFcmTokens>>,
    TError,
    { data: BodyType<PostApiV1UserFcmTokensBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1UserFcmTokens>>,
  TError,
  { data: BodyType<PostApiV1UserFcmTokensBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1UserFcmTokensMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Get all FCM tokens for the authenticated user
 * @summary Get user FCM tokens
 */
export const getApiV1UserFcmTokens = (signal?: AbortSignal) => {
  return axiosClient<GetApiV1UserFcmTokens200Item[]>({
    url: '/api/v1/user-fcm-tokens/',
    method: 'GET',
    signal,
  });
};

export const getGetApiV1UserFcmTokensQueryKey = () => {
  return ['/api/v1/user-fcm-tokens/'] as const;
};

export const getGetApiV1UserFcmTokensQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1UserFcmTokens>>,
  TError = ErrorType<ErrorResponse>,
>(options?: {
  query?: Partial<
    UseQueryOptions<Awaited<ReturnType<typeof getApiV1UserFcmTokens>>, TError, TData>
  >;
}) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1UserFcmTokensQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1UserFcmTokens>>> = ({ signal }) =>
    getApiV1UserFcmTokens(signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1UserFcmTokens>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1UserFcmTokensQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1UserFcmTokens>>
>;
export type GetApiV1UserFcmTokensQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1UserFcmTokens<
  TData = Awaited<ReturnType<typeof getApiV1UserFcmTokens>>,
  TError = ErrorType<ErrorResponse>,
>(options: {
  query: Partial<
    UseQueryOptions<Awaited<ReturnType<typeof getApiV1UserFcmTokens>>, TError, TData>
  > &
    Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getApiV1UserFcmTokens>>,
        TError,
        Awaited<ReturnType<typeof getApiV1UserFcmTokens>>
      >,
      'initialData'
    >;
}): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1UserFcmTokens<
  TData = Awaited<ReturnType<typeof getApiV1UserFcmTokens>>,
  TError = ErrorType<ErrorResponse>,
>(options?: {
  query?: Partial<
    UseQueryOptions<Awaited<ReturnType<typeof getApiV1UserFcmTokens>>, TError, TData>
  > &
    Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getApiV1UserFcmTokens>>,
        TError,
        Awaited<ReturnType<typeof getApiV1UserFcmTokens>>
      >,
      'initialData'
    >;
}): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1UserFcmTokens<
  TData = Awaited<ReturnType<typeof getApiV1UserFcmTokens>>,
  TError = ErrorType<ErrorResponse>,
>(options?: {
  query?: Partial<
    UseQueryOptions<Awaited<ReturnType<typeof getApiV1UserFcmTokens>>, TError, TData>
  >;
}): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get user FCM tokens
 */

export function useGetApiV1UserFcmTokens<
  TData = Awaited<ReturnType<typeof getApiV1UserFcmTokens>>,
  TError = ErrorType<ErrorResponse>,
>(options?: {
  query?: Partial<
    UseQueryOptions<Awaited<ReturnType<typeof getApiV1UserFcmTokens>>, TError, TData>
  >;
}): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1UserFcmTokensQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Delete an FCM token
 * @summary Delete FCM token
 */
export const deleteApiV1UserFcmTokensId = (id: string) => {
  return axiosClient<unknown>({
    url: `/api/v1/user-fcm-tokens/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1UserFcmTokensIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1UserFcmTokensId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1UserFcmTokensId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1UserFcmTokensId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1UserFcmTokensId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1UserFcmTokensId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1UserFcmTokensIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1UserFcmTokensId>>
>;

export type DeleteApiV1UserFcmTokensIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete FCM token
 */
export const useDeleteApiV1UserFcmTokensId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1UserFcmTokensId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1UserFcmTokensId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1UserFcmTokensIdMutationOptions(options);

  return useMutation(mutationOptions);
};
