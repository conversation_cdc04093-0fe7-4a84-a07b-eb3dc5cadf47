/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1ProjectsId204,
  ErrorResponse,
  GetApiV1Projects200,
  GetApiV1ProjectsId200,
  GetApiV1ProjectsParams,
  PatchApiV1ProjectsId200,
  PatchApiV1ProjectsIdBody,
  PostApiV1Projects201,
  PostApiV1ProjectsBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of projects resources with optional filtering and sorting
 * @summary Get projects list
 */
export const getApiV1Projects = (params?: GetApiV1ProjectsParams, signal?: AbortSignal) => {
  return axiosClient<GetApiV1Projects200>({
    url: '/api/v1/projects/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1ProjectsQueryKey = (params?: GetApiV1ProjectsParams) => {
  return ['/api/v1/projects/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1ProjectsQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1Projects>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1ProjectsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Projects>>, TError, TData>>;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1ProjectsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1Projects>>> = ({ signal }) =>
    getApiV1Projects(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1Projects>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1ProjectsQueryResult = NonNullable<Awaited<ReturnType<typeof getApiV1Projects>>>;
export type GetApiV1ProjectsQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1Projects<
  TData = Awaited<ReturnType<typeof getApiV1Projects>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1ProjectsParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Projects>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Projects>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Projects>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Projects<
  TData = Awaited<ReturnType<typeof getApiV1Projects>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1ProjectsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Projects>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Projects>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Projects>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Projects<
  TData = Awaited<ReturnType<typeof getApiV1Projects>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1ProjectsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Projects>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get projects list
 */

export function useGetApiV1Projects<
  TData = Awaited<ReturnType<typeof getApiV1Projects>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1ProjectsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Projects>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1ProjectsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new project resource
 * @summary Create project
 */
export const postApiV1Projects = (
  postApiV1ProjectsBody: BodyType<PostApiV1ProjectsBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1Projects201>({
    url: '/api/v1/projects/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1ProjectsBody,
    signal,
  });
};

export const getPostApiV1ProjectsMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1Projects>>,
    TError,
    { data: BodyType<PostApiV1ProjectsBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1Projects>>,
  TError,
  { data: BodyType<PostApiV1ProjectsBody> },
  TContext
> => {
  const mutationKey = ['postApiV1Projects'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1Projects>>,
    { data: BodyType<PostApiV1ProjectsBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1Projects(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1ProjectsMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1Projects>>
>;
export type PostApiV1ProjectsMutationBody = BodyType<PostApiV1ProjectsBody>;
export type PostApiV1ProjectsMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create project
 */
export const usePostApiV1Projects = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1Projects>>,
    TError,
    { data: BodyType<PostApiV1ProjectsBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1Projects>>,
  TError,
  { data: BodyType<PostApiV1ProjectsBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1ProjectsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific project resource by its unique identifier
 * @summary Get project by ID
 */
export const getApiV1ProjectsId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1ProjectsId200>({
    url: `/api/v1/projects/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1ProjectsIdQueryKey = (id: string) => {
  return [`/api/v1/projects/${id}`] as const;
};

export const getGetApiV1ProjectsIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1ProjectsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1ProjectsId>>, TError, TData>>;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1ProjectsIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1ProjectsId>>> = ({ signal }) =>
    getApiV1ProjectsId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1ProjectsId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1ProjectsIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1ProjectsId>>
>;
export type GetApiV1ProjectsIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1ProjectsId<
  TData = Awaited<ReturnType<typeof getApiV1ProjectsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1ProjectsId>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1ProjectsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1ProjectsId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1ProjectsId<
  TData = Awaited<ReturnType<typeof getApiV1ProjectsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ProjectsId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1ProjectsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1ProjectsId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1ProjectsId<
  TData = Awaited<ReturnType<typeof getApiV1ProjectsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1ProjectsId>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get project by ID
 */

export function useGetApiV1ProjectsId<
  TData = Awaited<ReturnType<typeof getApiV1ProjectsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1ProjectsId>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1ProjectsIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing project resource
 * @summary Update project
 */
export const patchApiV1ProjectsId = (
  id: string,
  patchApiV1ProjectsIdBody: BodyType<PatchApiV1ProjectsIdBody>,
) => {
  return axiosClient<PatchApiV1ProjectsId200>({
    url: `/api/v1/projects/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1ProjectsIdBody,
  });
};

export const getPatchApiV1ProjectsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1ProjectsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1ProjectsIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1ProjectsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1ProjectsIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1ProjectsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1ProjectsId>>,
    { id: string; data: BodyType<PatchApiV1ProjectsIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1ProjectsId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1ProjectsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1ProjectsId>>
>;
export type PatchApiV1ProjectsIdMutationBody = BodyType<PatchApiV1ProjectsIdBody>;
export type PatchApiV1ProjectsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update project
 */
export const usePatchApiV1ProjectsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1ProjectsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1ProjectsIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1ProjectsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1ProjectsIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1ProjectsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing project resource
 * @summary Delete project
 */
export const deleteApiV1ProjectsId = (id: string) => {
  return axiosClient<DeleteApiV1ProjectsId204>({
    url: `/api/v1/projects/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1ProjectsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1ProjectsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1ProjectsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1ProjectsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1ProjectsId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1ProjectsId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1ProjectsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1ProjectsId>>
>;

export type DeleteApiV1ProjectsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete project
 */
export const useDeleteApiV1ProjectsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1ProjectsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1ProjectsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1ProjectsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
