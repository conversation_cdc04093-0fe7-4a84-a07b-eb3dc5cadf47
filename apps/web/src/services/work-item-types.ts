/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1WorkItemTypesId204,
  ErrorResponse,
  GetApiV1WorkItemTypes200,
  GetApiV1WorkItemTypesId200,
  GetApiV1WorkItemTypesParams,
  PatchApiV1WorkItemTypesId200,
  PatchApiV1WorkItemTypesIdBody,
  PostApiV1WorkItemTypes201,
  PostApiV1WorkItemTypesBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of work-item-types resources with optional filtering and sorting
 * @summary Get work-item-types list
 */
export const getApiV1WorkItemTypes = (
  params?: GetApiV1WorkItemTypesParams,
  signal?: AbortSignal,
) => {
  return axiosClient<GetApiV1WorkItemTypes200>({
    url: '/api/v1/work-item-types/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1WorkItemTypesQueryKey = (params?: GetApiV1WorkItemTypesParams) => {
  return ['/api/v1/work-item-types/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1WorkItemTypesQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1WorkItemTypes>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemTypesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemTypes>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1WorkItemTypesQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1WorkItemTypes>>> = ({ signal }) =>
    getApiV1WorkItemTypes(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1WorkItemTypes>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1WorkItemTypesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1WorkItemTypes>>
>;
export type GetApiV1WorkItemTypesQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1WorkItemTypes<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemTypes>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1WorkItemTypesParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemTypes>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItemTypes>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItemTypes>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItemTypes<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemTypes>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemTypesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemTypes>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItemTypes>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItemTypes>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItemTypes<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemTypes>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemTypesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemTypes>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get work-item-types list
 */

export function useGetApiV1WorkItemTypes<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemTypes>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemTypesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemTypes>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1WorkItemTypesQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new work-item-type resource
 * @summary Create work-item-type
 */
export const postApiV1WorkItemTypes = (
  postApiV1WorkItemTypesBody: BodyType<PostApiV1WorkItemTypesBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1WorkItemTypes201>({
    url: '/api/v1/work-item-types/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1WorkItemTypesBody,
    signal,
  });
};

export const getPostApiV1WorkItemTypesMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1WorkItemTypes>>,
    TError,
    { data: BodyType<PostApiV1WorkItemTypesBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1WorkItemTypes>>,
  TError,
  { data: BodyType<PostApiV1WorkItemTypesBody> },
  TContext
> => {
  const mutationKey = ['postApiV1WorkItemTypes'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1WorkItemTypes>>,
    { data: BodyType<PostApiV1WorkItemTypesBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1WorkItemTypes(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1WorkItemTypesMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1WorkItemTypes>>
>;
export type PostApiV1WorkItemTypesMutationBody = BodyType<PostApiV1WorkItemTypesBody>;
export type PostApiV1WorkItemTypesMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create work-item-type
 */
export const usePostApiV1WorkItemTypes = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1WorkItemTypes>>,
    TError,
    { data: BodyType<PostApiV1WorkItemTypesBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1WorkItemTypes>>,
  TError,
  { data: BodyType<PostApiV1WorkItemTypesBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1WorkItemTypesMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific work-item-type resource by its unique identifier
 * @summary Get work-item-type by ID
 */
export const getApiV1WorkItemTypesId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1WorkItemTypesId200>({
    url: `/api/v1/work-item-types/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1WorkItemTypesIdQueryKey = (id: string) => {
  return [`/api/v1/work-item-types/${id}`] as const;
};

export const getGetApiV1WorkItemTypesIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1WorkItemTypesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemTypesId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1WorkItemTypesIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1WorkItemTypesId>>> = ({
    signal,
  }) => getApiV1WorkItemTypesId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemTypesId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1WorkItemTypesIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1WorkItemTypesId>>
>;
export type GetApiV1WorkItemTypesIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1WorkItemTypesId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemTypesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemTypesId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItemTypesId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItemTypesId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItemTypesId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemTypesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemTypesId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItemTypesId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItemTypesId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItemTypesId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemTypesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemTypesId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get work-item-type by ID
 */

export function useGetApiV1WorkItemTypesId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemTypesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemTypesId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1WorkItemTypesIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing work-item-type resource
 * @summary Update work-item-type
 */
export const patchApiV1WorkItemTypesId = (
  id: string,
  patchApiV1WorkItemTypesIdBody: BodyType<PatchApiV1WorkItemTypesIdBody>,
) => {
  return axiosClient<PatchApiV1WorkItemTypesId200>({
    url: `/api/v1/work-item-types/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1WorkItemTypesIdBody,
  });
};

export const getPatchApiV1WorkItemTypesIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1WorkItemTypesId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1WorkItemTypesIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1WorkItemTypesId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1WorkItemTypesIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1WorkItemTypesId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1WorkItemTypesId>>,
    { id: string; data: BodyType<PatchApiV1WorkItemTypesIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1WorkItemTypesId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1WorkItemTypesIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1WorkItemTypesId>>
>;
export type PatchApiV1WorkItemTypesIdMutationBody = BodyType<PatchApiV1WorkItemTypesIdBody>;
export type PatchApiV1WorkItemTypesIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update work-item-type
 */
export const usePatchApiV1WorkItemTypesId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1WorkItemTypesId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1WorkItemTypesIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1WorkItemTypesId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1WorkItemTypesIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1WorkItemTypesIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing work-item-type resource
 * @summary Delete work-item-type
 */
export const deleteApiV1WorkItemTypesId = (id: string) => {
  return axiosClient<DeleteApiV1WorkItemTypesId204>({
    url: `/api/v1/work-item-types/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1WorkItemTypesIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1WorkItemTypesId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1WorkItemTypesId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1WorkItemTypesId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1WorkItemTypesId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1WorkItemTypesId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1WorkItemTypesIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1WorkItemTypesId>>
>;

export type DeleteApiV1WorkItemTypesIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete work-item-type
 */
export const useDeleteApiV1WorkItemTypesId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1WorkItemTypesId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1WorkItemTypesId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1WorkItemTypesIdMutationOptions(options);

  return useMutation(mutationOptions);
};
