/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1WorkflowsId204,
  ErrorResponse,
  GetApiV1Workflows200,
  GetApiV1WorkflowsId200,
  GetApiV1WorkflowsParams,
  PatchApiV1WorkflowsId200,
  PatchApiV1WorkflowsIdBody,
  PostApiV1Workflows201,
  PostApiV1WorkflowsBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of workflows resources with optional filtering and sorting
 * @summary Get workflows list
 */
export const getApiV1Workflows = (params?: GetApiV1WorkflowsParams, signal?: AbortSignal) => {
  return axiosClient<GetApiV1Workflows200>({
    url: '/api/v1/workflows/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1WorkflowsQueryKey = (params?: GetApiV1WorkflowsParams) => {
  return ['/api/v1/workflows/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1WorkflowsQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1Workflows>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkflowsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Workflows>>, TError, TData>>;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1WorkflowsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1Workflows>>> = ({ signal }) =>
    getApiV1Workflows(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1Workflows>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1WorkflowsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1Workflows>>
>;
export type GetApiV1WorkflowsQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1Workflows<
  TData = Awaited<ReturnType<typeof getApiV1Workflows>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1WorkflowsParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Workflows>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Workflows>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Workflows>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Workflows<
  TData = Awaited<ReturnType<typeof getApiV1Workflows>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkflowsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Workflows>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Workflows>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Workflows>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Workflows<
  TData = Awaited<ReturnType<typeof getApiV1Workflows>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkflowsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Workflows>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get workflows list
 */

export function useGetApiV1Workflows<
  TData = Awaited<ReturnType<typeof getApiV1Workflows>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkflowsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Workflows>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1WorkflowsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new workflow resource
 * @summary Create workflow
 */
export const postApiV1Workflows = (
  postApiV1WorkflowsBody: BodyType<PostApiV1WorkflowsBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1Workflows201>({
    url: '/api/v1/workflows/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1WorkflowsBody,
    signal,
  });
};

export const getPostApiV1WorkflowsMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1Workflows>>,
    TError,
    { data: BodyType<PostApiV1WorkflowsBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1Workflows>>,
  TError,
  { data: BodyType<PostApiV1WorkflowsBody> },
  TContext
> => {
  const mutationKey = ['postApiV1Workflows'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1Workflows>>,
    { data: BodyType<PostApiV1WorkflowsBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1Workflows(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1WorkflowsMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1Workflows>>
>;
export type PostApiV1WorkflowsMutationBody = BodyType<PostApiV1WorkflowsBody>;
export type PostApiV1WorkflowsMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create workflow
 */
export const usePostApiV1Workflows = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1Workflows>>,
    TError,
    { data: BodyType<PostApiV1WorkflowsBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1Workflows>>,
  TError,
  { data: BodyType<PostApiV1WorkflowsBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1WorkflowsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific workflow resource by its unique identifier
 * @summary Get workflow by ID
 */
export const getApiV1WorkflowsId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1WorkflowsId200>({
    url: `/api/v1/workflows/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1WorkflowsIdQueryKey = (id: string) => {
  return [`/api/v1/workflows/${id}`] as const;
};

export const getGetApiV1WorkflowsIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1WorkflowsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowsId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1WorkflowsIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1WorkflowsId>>> = ({ signal }) =>
    getApiV1WorkflowsId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowsId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1WorkflowsIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1WorkflowsId>>
>;
export type GetApiV1WorkflowsIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1WorkflowsId<
  TData = Awaited<ReturnType<typeof getApiV1WorkflowsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowsId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkflowsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkflowsId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkflowsId<
  TData = Awaited<ReturnType<typeof getApiV1WorkflowsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowsId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkflowsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkflowsId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkflowsId<
  TData = Awaited<ReturnType<typeof getApiV1WorkflowsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get workflow by ID
 */

export function useGetApiV1WorkflowsId<
  TData = Awaited<ReturnType<typeof getApiV1WorkflowsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1WorkflowsIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing workflow resource
 * @summary Update workflow
 */
export const patchApiV1WorkflowsId = (
  id: string,
  patchApiV1WorkflowsIdBody: BodyType<PatchApiV1WorkflowsIdBody>,
) => {
  return axiosClient<PatchApiV1WorkflowsId200>({
    url: `/api/v1/workflows/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1WorkflowsIdBody,
  });
};

export const getPatchApiV1WorkflowsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1WorkflowsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1WorkflowsIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1WorkflowsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1WorkflowsIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1WorkflowsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1WorkflowsId>>,
    { id: string; data: BodyType<PatchApiV1WorkflowsIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1WorkflowsId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1WorkflowsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1WorkflowsId>>
>;
export type PatchApiV1WorkflowsIdMutationBody = BodyType<PatchApiV1WorkflowsIdBody>;
export type PatchApiV1WorkflowsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update workflow
 */
export const usePatchApiV1WorkflowsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1WorkflowsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1WorkflowsIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1WorkflowsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1WorkflowsIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1WorkflowsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing workflow resource
 * @summary Delete workflow
 */
export const deleteApiV1WorkflowsId = (id: string) => {
  return axiosClient<DeleteApiV1WorkflowsId204>({
    url: `/api/v1/workflows/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1WorkflowsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1WorkflowsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1WorkflowsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1WorkflowsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1WorkflowsId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1WorkflowsId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1WorkflowsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1WorkflowsId>>
>;

export type DeleteApiV1WorkflowsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete workflow
 */
export const useDeleteApiV1WorkflowsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1WorkflowsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1WorkflowsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1WorkflowsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
