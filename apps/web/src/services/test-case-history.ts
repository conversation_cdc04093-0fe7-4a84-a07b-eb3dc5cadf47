/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';
import type { ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  ErrorResponse,
  GetApiV1TestCaseHistory200,
  GetApiV1TestCaseHistoryId200,
  GetApiV1TestCaseHistoryParams,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of test case history entries resources with optional filtering and sorting
 * @summary Get test case history entries list
 */
export const getApiV1TestCaseHistory = (
  params?: GetApiV1TestCaseHistoryParams,
  signal?: AbortSignal,
) => {
  return axiosClient<GetApiV1TestCaseHistory200>({
    url: '/api/v1/test-case-history/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1TestCaseHistoryQueryKey = (params?: GetApiV1TestCaseHistoryParams) => {
  return ['/api/v1/test-case-history/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1TestCaseHistoryQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1TestCaseHistory>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestCaseHistoryParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseHistory>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1TestCaseHistoryQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1TestCaseHistory>>> = ({
    signal,
  }) => getApiV1TestCaseHistory(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1TestCaseHistory>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1TestCaseHistoryQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1TestCaseHistory>>
>;
export type GetApiV1TestCaseHistoryQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1TestCaseHistory<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseHistory>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1TestCaseHistoryParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseHistory>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestCaseHistory>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestCaseHistory>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestCaseHistory<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseHistory>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestCaseHistoryParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseHistory>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestCaseHistory>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestCaseHistory>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestCaseHistory<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseHistory>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestCaseHistoryParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseHistory>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get test case history entries list
 */

export function useGetApiV1TestCaseHistory<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseHistory>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestCaseHistoryParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseHistory>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1TestCaseHistoryQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Retrieve a specific test case history resource by its unique identifier
 * @summary Get test case history by ID
 */
export const getApiV1TestCaseHistoryId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1TestCaseHistoryId200>({
    url: `/api/v1/test-case-history/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1TestCaseHistoryIdQueryKey = (id: string) => {
  return [`/api/v1/test-case-history/${id}`] as const;
};

export const getGetApiV1TestCaseHistoryIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1TestCaseHistoryId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseHistoryId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1TestCaseHistoryIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1TestCaseHistoryId>>> = ({
    signal,
  }) => getApiV1TestCaseHistoryId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseHistoryId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1TestCaseHistoryIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1TestCaseHistoryId>>
>;
export type GetApiV1TestCaseHistoryIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1TestCaseHistoryId<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseHistoryId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseHistoryId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestCaseHistoryId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestCaseHistoryId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestCaseHistoryId<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseHistoryId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseHistoryId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestCaseHistoryId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestCaseHistoryId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestCaseHistoryId<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseHistoryId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseHistoryId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get test case history by ID
 */

export function useGetApiV1TestCaseHistoryId<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseHistoryId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseHistoryId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1TestCaseHistoryIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}
