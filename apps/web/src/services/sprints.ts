/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1SprintsId204,
  ErrorResponse,
  GetApiV1Sprints200,
  GetApiV1SprintsId200,
  GetApiV1SprintsParams,
  PatchApiV1SprintsId200,
  PatchApiV1SprintsIdBody,
  PostApiV1Sprints201,
  PostApiV1SprintsBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of sprints resources with optional filtering and sorting
 * @summary Get sprints list
 */
export const getApiV1Sprints = (params?: GetApiV1SprintsParams, signal?: AbortSignal) => {
  return axiosClient<GetApiV1Sprints200>({
    url: '/api/v1/sprints/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1SprintsQueryKey = (params?: GetApiV1SprintsParams) => {
  return ['/api/v1/sprints/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1SprintsQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1Sprints>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1SprintsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Sprints>>, TError, TData>>;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1SprintsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1Sprints>>> = ({ signal }) =>
    getApiV1Sprints(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1Sprints>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1SprintsQueryResult = NonNullable<Awaited<ReturnType<typeof getApiV1Sprints>>>;
export type GetApiV1SprintsQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1Sprints<
  TData = Awaited<ReturnType<typeof getApiV1Sprints>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1SprintsParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Sprints>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Sprints>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Sprints>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Sprints<
  TData = Awaited<ReturnType<typeof getApiV1Sprints>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1SprintsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Sprints>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Sprints>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Sprints>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Sprints<
  TData = Awaited<ReturnType<typeof getApiV1Sprints>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1SprintsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Sprints>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get sprints list
 */

export function useGetApiV1Sprints<
  TData = Awaited<ReturnType<typeof getApiV1Sprints>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1SprintsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Sprints>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1SprintsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new sprint resource
 * @summary Create sprint
 */
export const postApiV1Sprints = (
  postApiV1SprintsBody: BodyType<PostApiV1SprintsBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1Sprints201>({
    url: '/api/v1/sprints/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1SprintsBody,
    signal,
  });
};

export const getPostApiV1SprintsMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1Sprints>>,
    TError,
    { data: BodyType<PostApiV1SprintsBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1Sprints>>,
  TError,
  { data: BodyType<PostApiV1SprintsBody> },
  TContext
> => {
  const mutationKey = ['postApiV1Sprints'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1Sprints>>,
    { data: BodyType<PostApiV1SprintsBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1Sprints(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1SprintsMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1Sprints>>
>;
export type PostApiV1SprintsMutationBody = BodyType<PostApiV1SprintsBody>;
export type PostApiV1SprintsMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create sprint
 */
export const usePostApiV1Sprints = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1Sprints>>,
    TError,
    { data: BodyType<PostApiV1SprintsBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1Sprints>>,
  TError,
  { data: BodyType<PostApiV1SprintsBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1SprintsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific sprint resource by its unique identifier
 * @summary Get sprint by ID
 */
export const getApiV1SprintsId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1SprintsId200>({
    url: `/api/v1/sprints/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1SprintsIdQueryKey = (id: string) => {
  return [`/api/v1/sprints/${id}`] as const;
};

export const getGetApiV1SprintsIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1SprintsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1SprintsId>>, TError, TData>>;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1SprintsIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1SprintsId>>> = ({ signal }) =>
    getApiV1SprintsId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1SprintsId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1SprintsIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1SprintsId>>
>;
export type GetApiV1SprintsIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1SprintsId<
  TData = Awaited<ReturnType<typeof getApiV1SprintsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1SprintsId>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1SprintsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1SprintsId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1SprintsId<
  TData = Awaited<ReturnType<typeof getApiV1SprintsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1SprintsId>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1SprintsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1SprintsId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1SprintsId<
  TData = Awaited<ReturnType<typeof getApiV1SprintsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1SprintsId>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get sprint by ID
 */

export function useGetApiV1SprintsId<
  TData = Awaited<ReturnType<typeof getApiV1SprintsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1SprintsId>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1SprintsIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing sprint resource
 * @summary Update sprint
 */
export const patchApiV1SprintsId = (
  id: string,
  patchApiV1SprintsIdBody: BodyType<PatchApiV1SprintsIdBody>,
) => {
  return axiosClient<PatchApiV1SprintsId200>({
    url: `/api/v1/sprints/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1SprintsIdBody,
  });
};

export const getPatchApiV1SprintsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1SprintsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1SprintsIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1SprintsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1SprintsIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1SprintsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1SprintsId>>,
    { id: string; data: BodyType<PatchApiV1SprintsIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1SprintsId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1SprintsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1SprintsId>>
>;
export type PatchApiV1SprintsIdMutationBody = BodyType<PatchApiV1SprintsIdBody>;
export type PatchApiV1SprintsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update sprint
 */
export const usePatchApiV1SprintsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1SprintsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1SprintsIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1SprintsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1SprintsIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1SprintsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing sprint resource
 * @summary Delete sprint
 */
export const deleteApiV1SprintsId = (id: string) => {
  return axiosClient<DeleteApiV1SprintsId204>({
    url: `/api/v1/sprints/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1SprintsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1SprintsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1SprintsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1SprintsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1SprintsId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1SprintsId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1SprintsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1SprintsId>>
>;

export type DeleteApiV1SprintsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete sprint
 */
export const useDeleteApiV1SprintsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1SprintsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1SprintsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1SprintsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
