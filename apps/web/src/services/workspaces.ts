/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1WorkspacesId204,
  ErrorResponse,
  GetApiV1Workspaces200,
  GetApiV1WorkspacesId200,
  GetApiV1WorkspacesParams,
  PatchApiV1WorkspacesId200,
  PatchApiV1WorkspacesIdBody,
  PostApiV1Workspaces201,
  PostApiV1WorkspacesBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of workspaces resources with optional filtering and sorting
 * @summary Get workspaces list
 */
export const getApiV1Workspaces = (params?: GetApiV1WorkspacesParams, signal?: AbortSignal) => {
  return axiosClient<GetApiV1Workspaces200>({
    url: '/api/v1/workspaces/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1WorkspacesQueryKey = (params?: GetApiV1WorkspacesParams) => {
  return ['/api/v1/workspaces/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1WorkspacesQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1Workspaces>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkspacesParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Workspaces>>, TError, TData>>;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1WorkspacesQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1Workspaces>>> = ({ signal }) =>
    getApiV1Workspaces(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1Workspaces>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1WorkspacesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1Workspaces>>
>;
export type GetApiV1WorkspacesQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1Workspaces<
  TData = Awaited<ReturnType<typeof getApiV1Workspaces>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1WorkspacesParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Workspaces>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Workspaces>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Workspaces>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Workspaces<
  TData = Awaited<ReturnType<typeof getApiV1Workspaces>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkspacesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1Workspaces>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Workspaces>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Workspaces>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Workspaces<
  TData = Awaited<ReturnType<typeof getApiV1Workspaces>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkspacesParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Workspaces>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get workspaces list
 */

export function useGetApiV1Workspaces<
  TData = Awaited<ReturnType<typeof getApiV1Workspaces>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkspacesParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Workspaces>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1WorkspacesQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new workspace resource
 * @summary Create workspace
 */
export const postApiV1Workspaces = (
  postApiV1WorkspacesBody: BodyType<PostApiV1WorkspacesBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1Workspaces201>({
    url: '/api/v1/workspaces/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1WorkspacesBody,
    signal,
  });
};

export const getPostApiV1WorkspacesMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1Workspaces>>,
    TError,
    { data: BodyType<PostApiV1WorkspacesBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1Workspaces>>,
  TError,
  { data: BodyType<PostApiV1WorkspacesBody> },
  TContext
> => {
  const mutationKey = ['postApiV1Workspaces'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1Workspaces>>,
    { data: BodyType<PostApiV1WorkspacesBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1Workspaces(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1WorkspacesMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1Workspaces>>
>;
export type PostApiV1WorkspacesMutationBody = BodyType<PostApiV1WorkspacesBody>;
export type PostApiV1WorkspacesMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create workspace
 */
export const usePostApiV1Workspaces = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1Workspaces>>,
    TError,
    { data: BodyType<PostApiV1WorkspacesBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1Workspaces>>,
  TError,
  { data: BodyType<PostApiV1WorkspacesBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1WorkspacesMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific workspace resource by its unique identifier
 * @summary Get workspace by ID
 */
export const getApiV1WorkspacesId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1WorkspacesId200>({
    url: `/api/v1/workspaces/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1WorkspacesIdQueryKey = (id: string) => {
  return [`/api/v1/workspaces/${id}`] as const;
};

export const getGetApiV1WorkspacesIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1WorkspacesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkspacesId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1WorkspacesIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1WorkspacesId>>> = ({ signal }) =>
    getApiV1WorkspacesId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkspacesId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1WorkspacesIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1WorkspacesId>>
>;
export type GetApiV1WorkspacesIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1WorkspacesId<
  TData = Awaited<ReturnType<typeof getApiV1WorkspacesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkspacesId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkspacesId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkspacesId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkspacesId<
  TData = Awaited<ReturnType<typeof getApiV1WorkspacesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkspacesId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkspacesId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkspacesId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkspacesId<
  TData = Awaited<ReturnType<typeof getApiV1WorkspacesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkspacesId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get workspace by ID
 */

export function useGetApiV1WorkspacesId<
  TData = Awaited<ReturnType<typeof getApiV1WorkspacesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkspacesId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1WorkspacesIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing workspace resource
 * @summary Update workspace
 */
export const patchApiV1WorkspacesId = (
  id: string,
  patchApiV1WorkspacesIdBody: BodyType<PatchApiV1WorkspacesIdBody>,
) => {
  return axiosClient<PatchApiV1WorkspacesId200>({
    url: `/api/v1/workspaces/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1WorkspacesIdBody,
  });
};

export const getPatchApiV1WorkspacesIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1WorkspacesId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1WorkspacesIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1WorkspacesId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1WorkspacesIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1WorkspacesId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1WorkspacesId>>,
    { id: string; data: BodyType<PatchApiV1WorkspacesIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1WorkspacesId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1WorkspacesIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1WorkspacesId>>
>;
export type PatchApiV1WorkspacesIdMutationBody = BodyType<PatchApiV1WorkspacesIdBody>;
export type PatchApiV1WorkspacesIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update workspace
 */
export const usePatchApiV1WorkspacesId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1WorkspacesId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1WorkspacesIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1WorkspacesId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1WorkspacesIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1WorkspacesIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing workspace resource
 * @summary Delete workspace
 */
export const deleteApiV1WorkspacesId = (id: string) => {
  return axiosClient<DeleteApiV1WorkspacesId204>({
    url: `/api/v1/workspaces/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1WorkspacesIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1WorkspacesId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1WorkspacesId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1WorkspacesId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1WorkspacesId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1WorkspacesId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1WorkspacesIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1WorkspacesId>>
>;

export type DeleteApiV1WorkspacesIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete workspace
 */
export const useDeleteApiV1WorkspacesId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1WorkspacesId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1WorkspacesId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1WorkspacesIdMutationOptions(options);

  return useMutation(mutationOptions);
};
