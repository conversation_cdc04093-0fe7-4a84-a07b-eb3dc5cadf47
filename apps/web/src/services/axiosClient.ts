import axios, {
  type AxiosError,
  type AxiosRequestConfig,
  type AxiosResponse,
  CanceledError,
  type InternalAxiosRequestConfig,
} from 'axios';
import { getAuthToken } from '@/web/utils';

const axiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
});

axiosInstance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = getAuthToken();
    const client = localStorage.getItem('selected-client') || '{}';
    if (token) {
      config.headers = config.headers || {};
      config.headers.Authorization = `Bearer ${token}`;
      const clientArr = JSON.parse(client);
      config.headers['X-Client-Key'] =
        Array.isArray(clientArr) && clientArr.length ? `${clientArr.join(',')}` : '';
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

axiosInstance.interceptors.response.use(
  (response) => response,
  async (error: AxiosError | CanceledError<unknown>) => {
    const handle401Error = () => {};

    if (axios.isAxiosError(error) && error.response) {
      if (error.response.status === 401) {
        handle401Error();
      }
    } else if (error instanceof CanceledError) {
      if ('response' in error && error.response && error.response.status === 401) {
        handle401Error();
      }
    } else {
    }

    if (error.response) {
      return Promise.reject(error.response.data);
    }
    return Promise.reject(error);
  },
);

export const axiosClient = <T>(config: AxiosRequestConfig): Promise<T> => {
  return axiosInstance(config).then((response: AxiosResponse<T>) => response.data);
};

export default axiosInstance;

export type ErrorType<ErrorData> = ErrorData;
export type BodyType<BodyData> = BodyData;
