/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1TestCaseWorkItemLinksId204,
  ErrorResponse,
  GetApiV1TestCaseWorkItemLinks200,
  GetApiV1TestCaseWorkItemLinksId200,
  GetApiV1TestCaseWorkItemLinksParams,
  PostApiV1TestCaseWorkItemLinks201,
  PostApiV1TestCaseWorkItemLinksBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of test case work item links resources with optional filtering and sorting
 * @summary Get test case work item links list
 */
export const getApiV1TestCaseWorkItemLinks = (
  params?: GetApiV1TestCaseWorkItemLinksParams,
  signal?: AbortSignal,
) => {
  return axiosClient<GetApiV1TestCaseWorkItemLinks200>({
    url: '/api/v1/test-case-work-item-links/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1TestCaseWorkItemLinksQueryKey = (
  params?: GetApiV1TestCaseWorkItemLinksParams,
) => {
  return ['/api/v1/test-case-work-item-links/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1TestCaseWorkItemLinksQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinks>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestCaseWorkItemLinksParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinks>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1TestCaseWorkItemLinksQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinks>>> = ({
    signal,
  }) => getApiV1TestCaseWorkItemLinks(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinks>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1TestCaseWorkItemLinksQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinks>>
>;
export type GetApiV1TestCaseWorkItemLinksQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1TestCaseWorkItemLinks<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinks>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1TestCaseWorkItemLinksParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinks>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinks>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinks>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestCaseWorkItemLinks<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinks>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestCaseWorkItemLinksParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinks>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinks>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinks>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestCaseWorkItemLinks<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinks>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestCaseWorkItemLinksParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinks>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get test case work item links list
 */

export function useGetApiV1TestCaseWorkItemLinks<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinks>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestCaseWorkItemLinksParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinks>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1TestCaseWorkItemLinksQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new test case work item link resource
 * @summary Create test case work item link
 */
export const postApiV1TestCaseWorkItemLinks = (
  postApiV1TestCaseWorkItemLinksBody: BodyType<PostApiV1TestCaseWorkItemLinksBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1TestCaseWorkItemLinks201>({
    url: '/api/v1/test-case-work-item-links/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1TestCaseWorkItemLinksBody,
    signal,
  });
};

export const getPostApiV1TestCaseWorkItemLinksMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1TestCaseWorkItemLinks>>,
    TError,
    { data: BodyType<PostApiV1TestCaseWorkItemLinksBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1TestCaseWorkItemLinks>>,
  TError,
  { data: BodyType<PostApiV1TestCaseWorkItemLinksBody> },
  TContext
> => {
  const mutationKey = ['postApiV1TestCaseWorkItemLinks'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1TestCaseWorkItemLinks>>,
    { data: BodyType<PostApiV1TestCaseWorkItemLinksBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1TestCaseWorkItemLinks(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1TestCaseWorkItemLinksMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1TestCaseWorkItemLinks>>
>;
export type PostApiV1TestCaseWorkItemLinksMutationBody =
  BodyType<PostApiV1TestCaseWorkItemLinksBody>;
export type PostApiV1TestCaseWorkItemLinksMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create test case work item link
 */
export const usePostApiV1TestCaseWorkItemLinks = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1TestCaseWorkItemLinks>>,
    TError,
    { data: BodyType<PostApiV1TestCaseWorkItemLinksBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1TestCaseWorkItemLinks>>,
  TError,
  { data: BodyType<PostApiV1TestCaseWorkItemLinksBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1TestCaseWorkItemLinksMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific test case work item link resource by its unique identifier
 * @summary Get test case work item link by ID
 */
export const getApiV1TestCaseWorkItemLinksId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1TestCaseWorkItemLinksId200>({
    url: `/api/v1/test-case-work-item-links/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1TestCaseWorkItemLinksIdQueryKey = (id: string) => {
  return [`/api/v1/test-case-work-item-links/${id}`] as const;
};

export const getGetApiV1TestCaseWorkItemLinksIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinksId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinksId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1TestCaseWorkItemLinksIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinksId>>> = ({
    signal,
  }) => getApiV1TestCaseWorkItemLinksId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinksId>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1TestCaseWorkItemLinksIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinksId>>
>;
export type GetApiV1TestCaseWorkItemLinksIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1TestCaseWorkItemLinksId<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinksId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinksId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinksId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinksId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestCaseWorkItemLinksId<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinksId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinksId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinksId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinksId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestCaseWorkItemLinksId<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinksId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinksId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get test case work item link by ID
 */

export function useGetApiV1TestCaseWorkItemLinksId<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinksId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseWorkItemLinksId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1TestCaseWorkItemLinksIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Delete an existing test case work item link resource
 * @summary Delete test case work item link
 */
export const deleteApiV1TestCaseWorkItemLinksId = (id: string) => {
  return axiosClient<DeleteApiV1TestCaseWorkItemLinksId204>({
    url: `/api/v1/test-case-work-item-links/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1TestCaseWorkItemLinksIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1TestCaseWorkItemLinksId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1TestCaseWorkItemLinksId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1TestCaseWorkItemLinksId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1TestCaseWorkItemLinksId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1TestCaseWorkItemLinksId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1TestCaseWorkItemLinksIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1TestCaseWorkItemLinksId>>
>;

export type DeleteApiV1TestCaseWorkItemLinksIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete test case work item link
 */
export const useDeleteApiV1TestCaseWorkItemLinksId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1TestCaseWorkItemLinksId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1TestCaseWorkItemLinksId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1TestCaseWorkItemLinksIdMutationOptions(options);

  return useMutation(mutationOptions);
};
