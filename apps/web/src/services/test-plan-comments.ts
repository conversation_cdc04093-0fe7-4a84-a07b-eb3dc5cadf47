/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1TestPlanCommentsId204,
  ErrorResponse,
  GetApiV1TestPlanComments200,
  GetApiV1TestPlanCommentsId200,
  GetApiV1TestPlanCommentsParams,
  PatchApiV1TestPlanCommentsId200,
  PatchApiV1TestPlanCommentsIdBody,
  PostApiV1TestPlanComments201,
  PostApiV1TestPlanCommentsBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of test plan comments resources with optional filtering and sorting
 * @summary Get test plan comments list
 */
export const getApiV1TestPlanComments = (
  params?: GetApiV1TestPlanCommentsParams,
  signal?: AbortSignal,
) => {
  return axiosClient<GetApiV1TestPlanComments200>({
    url: '/api/v1/test-plan-comments/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1TestPlanCommentsQueryKey = (params?: GetApiV1TestPlanCommentsParams) => {
  return ['/api/v1/test-plan-comments/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1TestPlanCommentsQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1TestPlanComments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestPlanCommentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestPlanComments>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1TestPlanCommentsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1TestPlanComments>>> = ({
    signal,
  }) => getApiV1TestPlanComments(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1TestPlanComments>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1TestPlanCommentsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1TestPlanComments>>
>;
export type GetApiV1TestPlanCommentsQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1TestPlanComments<
  TData = Awaited<ReturnType<typeof getApiV1TestPlanComments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1TestPlanCommentsParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestPlanComments>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestPlanComments>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestPlanComments>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestPlanComments<
  TData = Awaited<ReturnType<typeof getApiV1TestPlanComments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestPlanCommentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestPlanComments>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestPlanComments>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestPlanComments>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestPlanComments<
  TData = Awaited<ReturnType<typeof getApiV1TestPlanComments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestPlanCommentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestPlanComments>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get test plan comments list
 */

export function useGetApiV1TestPlanComments<
  TData = Awaited<ReturnType<typeof getApiV1TestPlanComments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestPlanCommentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestPlanComments>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1TestPlanCommentsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new test plan comment resource
 * @summary Create test plan comment
 */
export const postApiV1TestPlanComments = (
  postApiV1TestPlanCommentsBody: BodyType<PostApiV1TestPlanCommentsBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1TestPlanComments201>({
    url: '/api/v1/test-plan-comments/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1TestPlanCommentsBody,
    signal,
  });
};

export const getPostApiV1TestPlanCommentsMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1TestPlanComments>>,
    TError,
    { data: BodyType<PostApiV1TestPlanCommentsBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1TestPlanComments>>,
  TError,
  { data: BodyType<PostApiV1TestPlanCommentsBody> },
  TContext
> => {
  const mutationKey = ['postApiV1TestPlanComments'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1TestPlanComments>>,
    { data: BodyType<PostApiV1TestPlanCommentsBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1TestPlanComments(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1TestPlanCommentsMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1TestPlanComments>>
>;
export type PostApiV1TestPlanCommentsMutationBody = BodyType<PostApiV1TestPlanCommentsBody>;
export type PostApiV1TestPlanCommentsMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create test plan comment
 */
export const usePostApiV1TestPlanComments = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1TestPlanComments>>,
    TError,
    { data: BodyType<PostApiV1TestPlanCommentsBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1TestPlanComments>>,
  TError,
  { data: BodyType<PostApiV1TestPlanCommentsBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1TestPlanCommentsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific test plan comment resource by its unique identifier
 * @summary Get test plan comment by ID
 */
export const getApiV1TestPlanCommentsId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1TestPlanCommentsId200>({
    url: `/api/v1/test-plan-comments/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1TestPlanCommentsIdQueryKey = (id: string) => {
  return [`/api/v1/test-plan-comments/${id}`] as const;
};

export const getGetApiV1TestPlanCommentsIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1TestPlanCommentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestPlanCommentsId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1TestPlanCommentsIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1TestPlanCommentsId>>> = ({
    signal,
  }) => getApiV1TestPlanCommentsId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestPlanCommentsId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1TestPlanCommentsIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1TestPlanCommentsId>>
>;
export type GetApiV1TestPlanCommentsIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1TestPlanCommentsId<
  TData = Awaited<ReturnType<typeof getApiV1TestPlanCommentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestPlanCommentsId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestPlanCommentsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestPlanCommentsId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestPlanCommentsId<
  TData = Awaited<ReturnType<typeof getApiV1TestPlanCommentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestPlanCommentsId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestPlanCommentsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestPlanCommentsId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestPlanCommentsId<
  TData = Awaited<ReturnType<typeof getApiV1TestPlanCommentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestPlanCommentsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get test plan comment by ID
 */

export function useGetApiV1TestPlanCommentsId<
  TData = Awaited<ReturnType<typeof getApiV1TestPlanCommentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestPlanCommentsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1TestPlanCommentsIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing test plan comment resource
 * @summary Update test plan comment
 */
export const patchApiV1TestPlanCommentsId = (
  id: string,
  patchApiV1TestPlanCommentsIdBody: BodyType<PatchApiV1TestPlanCommentsIdBody>,
) => {
  return axiosClient<PatchApiV1TestPlanCommentsId200>({
    url: `/api/v1/test-plan-comments/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1TestPlanCommentsIdBody,
  });
};

export const getPatchApiV1TestPlanCommentsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1TestPlanCommentsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1TestPlanCommentsIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1TestPlanCommentsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1TestPlanCommentsIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1TestPlanCommentsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1TestPlanCommentsId>>,
    { id: string; data: BodyType<PatchApiV1TestPlanCommentsIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1TestPlanCommentsId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1TestPlanCommentsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1TestPlanCommentsId>>
>;
export type PatchApiV1TestPlanCommentsIdMutationBody = BodyType<PatchApiV1TestPlanCommentsIdBody>;
export type PatchApiV1TestPlanCommentsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update test plan comment
 */
export const usePatchApiV1TestPlanCommentsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1TestPlanCommentsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1TestPlanCommentsIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1TestPlanCommentsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1TestPlanCommentsIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1TestPlanCommentsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing test plan comment resource
 * @summary Delete test plan comment
 */
export const deleteApiV1TestPlanCommentsId = (id: string) => {
  return axiosClient<DeleteApiV1TestPlanCommentsId204>({
    url: `/api/v1/test-plan-comments/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1TestPlanCommentsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1TestPlanCommentsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1TestPlanCommentsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1TestPlanCommentsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1TestPlanCommentsId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1TestPlanCommentsId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1TestPlanCommentsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1TestPlanCommentsId>>
>;

export type DeleteApiV1TestPlanCommentsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete test plan comment
 */
export const useDeleteApiV1TestPlanCommentsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1TestPlanCommentsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1TestPlanCommentsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1TestPlanCommentsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
