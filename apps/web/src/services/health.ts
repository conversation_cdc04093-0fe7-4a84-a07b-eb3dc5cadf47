/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';
import type { ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type { GetApiV1AdminHealth200 } from './hooks.schemas';

/**
 * @summary Health check endpoint
 */
export const getApiV1AdminHealth = (signal?: AbortSignal) => {
  return axiosClient<GetApiV1AdminHealth200>({
    url: '/api/v1/admin/health',
    method: 'GET',
    signal,
  });
};

export const getGetApiV1AdminHealthQueryKey = () => {
  return ['/api/v1/admin/health'] as const;
};

export const getGetApiV1AdminHealthQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1AdminHealth>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1AdminHealth>>, TError, TData>>;
}) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1AdminHealthQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1AdminHealth>>> = ({ signal }) =>
    getApiV1AdminHealth(signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1AdminHealth>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1AdminHealthQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1AdminHealth>>
>;
export type GetApiV1AdminHealthQueryError = ErrorType<unknown>;

export function useGetApiV1AdminHealth<
  TData = Awaited<ReturnType<typeof getApiV1AdminHealth>>,
  TError = ErrorType<unknown>,
>(options: {
  query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1AdminHealth>>, TError, TData>> &
    Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getApiV1AdminHealth>>,
        TError,
        Awaited<ReturnType<typeof getApiV1AdminHealth>>
      >,
      'initialData'
    >;
}): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1AdminHealth<
  TData = Awaited<ReturnType<typeof getApiV1AdminHealth>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1AdminHealth>>, TError, TData>> &
    Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getApiV1AdminHealth>>,
        TError,
        Awaited<ReturnType<typeof getApiV1AdminHealth>>
      >,
      'initialData'
    >;
}): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1AdminHealth<
  TData = Awaited<ReturnType<typeof getApiV1AdminHealth>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1AdminHealth>>, TError, TData>>;
}): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Health check endpoint
 */

export function useGetApiV1AdminHealth<
  TData = Awaited<ReturnType<typeof getApiV1AdminHealth>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1AdminHealth>>, TError, TData>>;
}): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1AdminHealthQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Detailed performance metrics
 */
export const getApiV1AdminMetrics = (signal?: AbortSignal) => {
  return axiosClient<void>({
    url: '/api/v1/admin/metrics',
    method: 'GET',
    signal,
  });
};

export const getGetApiV1AdminMetricsQueryKey = () => {
  return ['/api/v1/admin/metrics'] as const;
};

export const getGetApiV1AdminMetricsQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1AdminMetrics>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1AdminMetrics>>, TError, TData>>;
}) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1AdminMetricsQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1AdminMetrics>>> = ({ signal }) =>
    getApiV1AdminMetrics(signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1AdminMetrics>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1AdminMetricsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1AdminMetrics>>
>;
export type GetApiV1AdminMetricsQueryError = ErrorType<unknown>;

export function useGetApiV1AdminMetrics<
  TData = Awaited<ReturnType<typeof getApiV1AdminMetrics>>,
  TError = ErrorType<unknown>,
>(options: {
  query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1AdminMetrics>>, TError, TData>> &
    Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getApiV1AdminMetrics>>,
        TError,
        Awaited<ReturnType<typeof getApiV1AdminMetrics>>
      >,
      'initialData'
    >;
}): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1AdminMetrics<
  TData = Awaited<ReturnType<typeof getApiV1AdminMetrics>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<
    UseQueryOptions<Awaited<ReturnType<typeof getApiV1AdminMetrics>>, TError, TData>
  > &
    Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getApiV1AdminMetrics>>,
        TError,
        Awaited<ReturnType<typeof getApiV1AdminMetrics>>
      >,
      'initialData'
    >;
}): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1AdminMetrics<
  TData = Awaited<ReturnType<typeof getApiV1AdminMetrics>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1AdminMetrics>>, TError, TData>>;
}): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Detailed performance metrics
 */

export function useGetApiV1AdminMetrics<
  TData = Awaited<ReturnType<typeof getApiV1AdminMetrics>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1AdminMetrics>>, TError, TData>>;
}): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1AdminMetricsQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Live metrics data
 */
export const getApiV1AdminMetricsLive = (signal?: AbortSignal) => {
  return axiosClient<void>({
    url: '/api/v1/admin/metrics/live',
    method: 'GET',
    signal,
  });
};

export const getGetApiV1AdminMetricsLiveQueryKey = () => {
  return ['/api/v1/admin/metrics/live'] as const;
};

export const getGetApiV1AdminMetricsLiveQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1AdminMetricsLive>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<
    UseQueryOptions<Awaited<ReturnType<typeof getApiV1AdminMetricsLive>>, TError, TData>
  >;
}) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1AdminMetricsLiveQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1AdminMetricsLive>>> = ({
    signal,
  }) => getApiV1AdminMetricsLive(signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1AdminMetricsLive>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1AdminMetricsLiveQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1AdminMetricsLive>>
>;
export type GetApiV1AdminMetricsLiveQueryError = ErrorType<unknown>;

export function useGetApiV1AdminMetricsLive<
  TData = Awaited<ReturnType<typeof getApiV1AdminMetricsLive>>,
  TError = ErrorType<unknown>,
>(options: {
  query: Partial<
    UseQueryOptions<Awaited<ReturnType<typeof getApiV1AdminMetricsLive>>, TError, TData>
  > &
    Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getApiV1AdminMetricsLive>>,
        TError,
        Awaited<ReturnType<typeof getApiV1AdminMetricsLive>>
      >,
      'initialData'
    >;
}): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1AdminMetricsLive<
  TData = Awaited<ReturnType<typeof getApiV1AdminMetricsLive>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<
    UseQueryOptions<Awaited<ReturnType<typeof getApiV1AdminMetricsLive>>, TError, TData>
  > &
    Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getApiV1AdminMetricsLive>>,
        TError,
        Awaited<ReturnType<typeof getApiV1AdminMetricsLive>>
      >,
      'initialData'
    >;
}): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1AdminMetricsLive<
  TData = Awaited<ReturnType<typeof getApiV1AdminMetricsLive>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<
    UseQueryOptions<Awaited<ReturnType<typeof getApiV1AdminMetricsLive>>, TError, TData>
  >;
}): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Live metrics data
 */

export function useGetApiV1AdminMetricsLive<
  TData = Awaited<ReturnType<typeof getApiV1AdminMetricsLive>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<
    UseQueryOptions<Awaited<ReturnType<typeof getApiV1AdminMetricsLive>>, TError, TData>
  >;
}): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1AdminMetricsLiveQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}
