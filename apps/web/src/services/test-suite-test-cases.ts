/**
 * Custom hooks for test suite test case associations
 * These endpoints manage the many-to-many relationship between test suites and test cases
 */

import type {
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import { axiosClient } from './axiosClient';
import type { ErrorResponse } from './hooks.schemas';

// Types for the new endpoints
export interface TestCaseWithSuiteProperties {
  id: string;
  title: string;
  description: string | null;
  preconditions: string | null;
  steps: Array<{ step: string; expectedResult: string }>;
  tags: string[];
  estimate: Record<string, any>;
  order: number;
  isActive: boolean;
  createdBy: string | null;
  createdAt: string;
  updatedAt: string;
  projectId: string;
  assigneeId: string | null;
  reporterId: string | null;
  workItemId: string | null;
  copiedFromId: string | null;
  // Suite-specific properties
  suiteStatus: {
    id: string;
    name: string;
  } | null;
  suitePriority: {
    id: string;
    name: string;
  } | null;
  suiteOrder: number;
  // Relations
  project?: any;
  assignee?: any;
  reporter?: any;
  creator?: any;
  workItem?: any;
}

export interface LinkTestCasesBody {
  testCaseIds: string[];
  statusId?: string | null;
  priorityId?: string | null;
}

export interface UpdateTestCaseInSuiteBody {
  statusId?: string | null;
  priorityId?: string | null;
  order?: number;
}

export interface AvailableTestCase {
  id: string;
  title: string;
  description: string | null;
  projectId: string;
  project?: {
    id: string;
    name: string;
    key: string;
  };
  assignee?: {
    id: string;
    email: string;
    displayName: string;
  } | null;
}

// Get test cases for a specific suite
export const getApiV1TestSuitesIdTestCases = (id: string, signal?: AbortSignal) => {
  return axiosClient<TestCaseWithSuiteProperties[]>({
    url: `/api/v1/test-suites/${id}/test-cases`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1TestSuitesIdTestCasesQueryKey = (id: string) => {
  return [`/api/v1/test-suites/${id}/test-cases`] as const;
};

export const getGetApiV1TestSuitesIdTestCasesQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesIdTestCases>>,
  TError = ErrorResponse,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesIdTestCases>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1TestSuitesIdTestCasesQueryKey(id);

  const queryFn = () => getApiV1TestSuitesIdTestCases(id);

  return { queryKey, queryFn, enabled: !!id, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1TestSuitesIdTestCases>>,
    TError,
    TData
  >;
};

export const useGetApiV1TestSuitesIdTestCases = <
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesIdTestCases>>,
  TError = ErrorResponse,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesIdTestCases>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> => {
  const queryOptions = getGetApiV1TestSuitesIdTestCasesQueryOptions(id, options);
  const query = useQuery(queryOptions) as UseQueryResult<TData, TError>;
  return query;
};

// Link test cases to a suite
export const postApiV1TestSuitesIdTestCasesLink = (id: string, data: LinkTestCasesBody) => {
  return axiosClient<{ message: string }>({
    url: `/api/v1/test-suites/${id}/test-cases/link`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data,
  });
};

export const getPostApiV1TestSuitesIdTestCasesLinkMutationOptions = <
  TError = ErrorResponse,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1TestSuitesIdTestCasesLink>>,
    TError,
    { id: string; data: LinkTestCasesBody },
    TContext
  >;
}) => {
  const { mutation: mutationOptions } = options ?? {};

  const mutationFn = ({ id, data }: { id: string; data: LinkTestCasesBody }) =>
    postApiV1TestSuitesIdTestCasesLink(id, data);

  return { mutationFn, ...mutationOptions };
};

export const usePostApiV1TestSuitesIdTestCasesLink = <
  TError = ErrorResponse,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1TestSuitesIdTestCasesLink>>,
    TError,
    { id: string; data: LinkTestCasesBody },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1TestSuitesIdTestCasesLink>>,
  TError,
  { id: string; data: LinkTestCasesBody },
  TContext
> => {
  const mutationOptions = getPostApiV1TestSuitesIdTestCasesLinkMutationOptions(options);
  return useMutation(mutationOptions);
};

// Remove test case from suite
export const deleteApiV1TestSuitesIdTestCasesTestCaseId = (id: string, testCaseId: string) => {
  return axiosClient<void>({
    url: `/api/v1/test-suites/${id}/test-cases/${testCaseId}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1TestSuitesIdTestCasesTestCaseIdMutationOptions = <
  TError = ErrorResponse,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1TestSuitesIdTestCasesTestCaseId>>,
    TError,
    { id: string; testCaseId: string },
    TContext
  >;
}) => {
  const { mutation: mutationOptions } = options ?? {};

  const mutationFn = ({ id, testCaseId }: { id: string; testCaseId: string }) =>
    deleteApiV1TestSuitesIdTestCasesTestCaseId(id, testCaseId);

  return { mutationFn, ...mutationOptions };
};

export const useDeleteApiV1TestSuitesIdTestCasesTestCaseId = <
  TError = ErrorResponse,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1TestSuitesIdTestCasesTestCaseId>>,
    TError,
    { id: string; testCaseId: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1TestSuitesIdTestCasesTestCaseId>>,
  TError,
  { id: string; testCaseId: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1TestSuitesIdTestCasesTestCaseIdMutationOptions(options);
  return useMutation(mutationOptions);
};

// Update test case in suite
export const patchApiV1TestSuitesIdTestCasesTestCaseId = (
  id: string,
  testCaseId: string,
  data: UpdateTestCaseInSuiteBody,
) => {
  return axiosClient<{ message: string }>({
    url: `/api/v1/test-suites/${id}/test-cases/${testCaseId}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data,
  });
};

export const getPatchApiV1TestSuitesIdTestCasesTestCaseIdMutationOptions = <
  TError = ErrorResponse,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1TestSuitesIdTestCasesTestCaseId>>,
    TError,
    { id: string; testCaseId: string; data: UpdateTestCaseInSuiteBody },
    TContext
  >;
}) => {
  const { mutation: mutationOptions } = options ?? {};

  const mutationFn = ({
    id,
    testCaseId,
    data,
  }: {
    id: string;
    testCaseId: string;
    data: UpdateTestCaseInSuiteBody;
  }) => patchApiV1TestSuitesIdTestCasesTestCaseId(id, testCaseId, data);

  return { mutationFn, ...mutationOptions };
};

export const usePatchApiV1TestSuitesIdTestCasesTestCaseId = <
  TError = ErrorResponse,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1TestSuitesIdTestCasesTestCaseId>>,
    TError,
    { id: string; testCaseId: string; data: UpdateTestCaseInSuiteBody },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1TestSuitesIdTestCasesTestCaseId>>,
  TError,
  { id: string; testCaseId: string; data: UpdateTestCaseInSuiteBody },
  TContext
> => {
  const mutationOptions = getPatchApiV1TestSuitesIdTestCasesTestCaseIdMutationOptions(options);
  return useMutation(mutationOptions);
};

// Get available test cases
export const getApiV1TestSuitesIdAvailableTestCases = (
  id: string,
  projectId?: string,
  signal?: AbortSignal,
) => {
  return axiosClient<AvailableTestCase[]>({
    url: `/api/v1/test-suites/${id}/available-test-cases`,
    method: 'GET',
    params: projectId ? { projectId } : undefined,
    signal,
  });
};

export const getGetApiV1TestSuitesIdAvailableTestCasesQueryKey = (
  id: string,
  projectId?: string,
) => {
  return [
    `/api/v1/test-suites/${id}/available-test-cases`,
    ...(projectId ? [projectId] : []),
  ] as const;
};

export const getGetApiV1TestSuitesIdAvailableTestCasesQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesIdAvailableTestCases>>,
  TError = ErrorResponse,
>(
  id: string,
  projectId?: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getApiV1TestSuitesIdAvailableTestCases>>,
        TError,
        TData
      >
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetApiV1TestSuitesIdAvailableTestCasesQueryKey(id, projectId);

  const queryFn = () => getApiV1TestSuitesIdAvailableTestCases(id, projectId);

  return { queryKey, queryFn, enabled: !!id, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1TestSuitesIdAvailableTestCases>>,
    TError,
    TData
  >;
};

export const useGetApiV1TestSuitesIdAvailableTestCases = <
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesIdAvailableTestCases>>,
  TError = ErrorResponse,
>(
  id: string,
  projectId?: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getApiV1TestSuitesIdAvailableTestCases>>,
        TError,
        TData
      >
    >;
  },
): UseQueryResult<TData, TError> => {
  const queryOptions = getGetApiV1TestSuitesIdAvailableTestCasesQueryOptions(
    id,
    projectId,
    options,
  );
  const query = useQuery(queryOptions) as UseQueryResult<TData, TError>;
  return query;
};
