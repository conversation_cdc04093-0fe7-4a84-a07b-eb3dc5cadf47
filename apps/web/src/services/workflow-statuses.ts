/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1WorkflowStatusesId204,
  ErrorResponse,
  GetApiV1WorkflowStatuses200,
  GetApiV1WorkflowStatusesId200,
  GetApiV1WorkflowStatusesParams,
  PatchApiV1WorkflowStatusesId200,
  PatchApiV1WorkflowStatusesIdBody,
  PostApiV1WorkflowStatuses201,
  PostApiV1WorkflowStatusesBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of workflow-statuses resources with optional filtering and sorting
 * @summary Get workflow-statuses list
 */
export const getApiV1WorkflowStatuses = (
  params?: GetApiV1WorkflowStatusesParams,
  signal?: AbortSignal,
) => {
  return axiosClient<GetApiV1WorkflowStatuses200>({
    url: '/api/v1/workflow-statuses/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1WorkflowStatusesQueryKey = (params?: GetApiV1WorkflowStatusesParams) => {
  return ['/api/v1/workflow-statuses/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1WorkflowStatusesQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1WorkflowStatuses>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkflowStatusesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowStatuses>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1WorkflowStatusesQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1WorkflowStatuses>>> = ({
    signal,
  }) => getApiV1WorkflowStatuses(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1WorkflowStatuses>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1WorkflowStatusesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1WorkflowStatuses>>
>;
export type GetApiV1WorkflowStatusesQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1WorkflowStatuses<
  TData = Awaited<ReturnType<typeof getApiV1WorkflowStatuses>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1WorkflowStatusesParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowStatuses>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkflowStatuses>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkflowStatuses>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkflowStatuses<
  TData = Awaited<ReturnType<typeof getApiV1WorkflowStatuses>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkflowStatusesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowStatuses>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkflowStatuses>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkflowStatuses>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkflowStatuses<
  TData = Awaited<ReturnType<typeof getApiV1WorkflowStatuses>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkflowStatusesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowStatuses>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get workflow-statuses list
 */

export function useGetApiV1WorkflowStatuses<
  TData = Awaited<ReturnType<typeof getApiV1WorkflowStatuses>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkflowStatusesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowStatuses>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1WorkflowStatusesQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new workflow-status resource
 * @summary Create workflow-status
 */
export const postApiV1WorkflowStatuses = (
  postApiV1WorkflowStatusesBody: BodyType<PostApiV1WorkflowStatusesBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1WorkflowStatuses201>({
    url: '/api/v1/workflow-statuses/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1WorkflowStatusesBody,
    signal,
  });
};

export const getPostApiV1WorkflowStatusesMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1WorkflowStatuses>>,
    TError,
    { data: BodyType<PostApiV1WorkflowStatusesBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1WorkflowStatuses>>,
  TError,
  { data: BodyType<PostApiV1WorkflowStatusesBody> },
  TContext
> => {
  const mutationKey = ['postApiV1WorkflowStatuses'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1WorkflowStatuses>>,
    { data: BodyType<PostApiV1WorkflowStatusesBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1WorkflowStatuses(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1WorkflowStatusesMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1WorkflowStatuses>>
>;
export type PostApiV1WorkflowStatusesMutationBody = BodyType<PostApiV1WorkflowStatusesBody>;
export type PostApiV1WorkflowStatusesMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create workflow-status
 */
export const usePostApiV1WorkflowStatuses = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1WorkflowStatuses>>,
    TError,
    { data: BodyType<PostApiV1WorkflowStatusesBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1WorkflowStatuses>>,
  TError,
  { data: BodyType<PostApiV1WorkflowStatusesBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1WorkflowStatusesMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific workflow-status resource by its unique identifier
 * @summary Get workflow-status by ID
 */
export const getApiV1WorkflowStatusesId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1WorkflowStatusesId200>({
    url: `/api/v1/workflow-statuses/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1WorkflowStatusesIdQueryKey = (id: string) => {
  return [`/api/v1/workflow-statuses/${id}`] as const;
};

export const getGetApiV1WorkflowStatusesIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1WorkflowStatusesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowStatusesId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1WorkflowStatusesIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1WorkflowStatusesId>>> = ({
    signal,
  }) => getApiV1WorkflowStatusesId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowStatusesId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1WorkflowStatusesIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1WorkflowStatusesId>>
>;
export type GetApiV1WorkflowStatusesIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1WorkflowStatusesId<
  TData = Awaited<ReturnType<typeof getApiV1WorkflowStatusesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowStatusesId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkflowStatusesId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkflowStatusesId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkflowStatusesId<
  TData = Awaited<ReturnType<typeof getApiV1WorkflowStatusesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowStatusesId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkflowStatusesId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkflowStatusesId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkflowStatusesId<
  TData = Awaited<ReturnType<typeof getApiV1WorkflowStatusesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowStatusesId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get workflow-status by ID
 */

export function useGetApiV1WorkflowStatusesId<
  TData = Awaited<ReturnType<typeof getApiV1WorkflowStatusesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowStatusesId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1WorkflowStatusesIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing workflow-status resource
 * @summary Update workflow-status
 */
export const patchApiV1WorkflowStatusesId = (
  id: string,
  patchApiV1WorkflowStatusesIdBody: BodyType<PatchApiV1WorkflowStatusesIdBody>,
) => {
  return axiosClient<PatchApiV1WorkflowStatusesId200>({
    url: `/api/v1/workflow-statuses/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1WorkflowStatusesIdBody,
  });
};

export const getPatchApiV1WorkflowStatusesIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1WorkflowStatusesId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1WorkflowStatusesIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1WorkflowStatusesId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1WorkflowStatusesIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1WorkflowStatusesId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1WorkflowStatusesId>>,
    { id: string; data: BodyType<PatchApiV1WorkflowStatusesIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1WorkflowStatusesId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1WorkflowStatusesIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1WorkflowStatusesId>>
>;
export type PatchApiV1WorkflowStatusesIdMutationBody = BodyType<PatchApiV1WorkflowStatusesIdBody>;
export type PatchApiV1WorkflowStatusesIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update workflow-status
 */
export const usePatchApiV1WorkflowStatusesId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1WorkflowStatusesId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1WorkflowStatusesIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1WorkflowStatusesId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1WorkflowStatusesIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1WorkflowStatusesIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing workflow-status resource
 * @summary Delete workflow-status
 */
export const deleteApiV1WorkflowStatusesId = (id: string) => {
  return axiosClient<DeleteApiV1WorkflowStatusesId204>({
    url: `/api/v1/workflow-statuses/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1WorkflowStatusesIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1WorkflowStatusesId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1WorkflowStatusesId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1WorkflowStatusesId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1WorkflowStatusesId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1WorkflowStatusesId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1WorkflowStatusesIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1WorkflowStatusesId>>
>;

export type DeleteApiV1WorkflowStatusesIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete workflow-status
 */
export const useDeleteApiV1WorkflowStatusesId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1WorkflowStatusesId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1WorkflowStatusesId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1WorkflowStatusesIdMutationOptions(options);

  return useMutation(mutationOptions);
};
