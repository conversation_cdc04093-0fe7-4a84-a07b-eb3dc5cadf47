/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1WorkItemHistoryId204,
  ErrorResponse,
  GetApiV1WorkItemHistory200,
  GetApiV1WorkItemHistoryId200,
  GetApiV1WorkItemHistoryParams,
  PatchApiV1WorkItemHistoryId200,
  PatchApiV1WorkItemHistoryIdBody,
  PostApiV1WorkItemHistory201,
  PostApiV1WorkItemHistoryBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of work-item-history resources with optional filtering and sorting
 * @summary Get work-item-history list
 */
export const getApiV1WorkItemHistory = (
  params?: GetApiV1WorkItemHistoryParams,
  signal?: AbortSignal,
) => {
  return axiosClient<GetApiV1WorkItemHistory200>({
    url: '/api/v1/work-item-history/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1WorkItemHistoryQueryKey = (params?: GetApiV1WorkItemHistoryParams) => {
  return ['/api/v1/work-item-history/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1WorkItemHistoryQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1WorkItemHistory>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemHistoryParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemHistory>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1WorkItemHistoryQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1WorkItemHistory>>> = ({
    signal,
  }) => getApiV1WorkItemHistory(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1WorkItemHistory>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1WorkItemHistoryQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1WorkItemHistory>>
>;
export type GetApiV1WorkItemHistoryQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1WorkItemHistory<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemHistory>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1WorkItemHistoryParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemHistory>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItemHistory>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItemHistory>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItemHistory<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemHistory>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemHistoryParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemHistory>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItemHistory>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItemHistory>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItemHistory<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemHistory>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemHistoryParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemHistory>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get work-item-history list
 */

export function useGetApiV1WorkItemHistory<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemHistory>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemHistoryParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemHistory>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1WorkItemHistoryQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new work-item-history resource
 * @summary Create work-item-history
 */
export const postApiV1WorkItemHistory = (
  postApiV1WorkItemHistoryBody: BodyType<PostApiV1WorkItemHistoryBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1WorkItemHistory201>({
    url: '/api/v1/work-item-history/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1WorkItemHistoryBody,
    signal,
  });
};

export const getPostApiV1WorkItemHistoryMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1WorkItemHistory>>,
    TError,
    { data: BodyType<PostApiV1WorkItemHistoryBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1WorkItemHistory>>,
  TError,
  { data: BodyType<PostApiV1WorkItemHistoryBody> },
  TContext
> => {
  const mutationKey = ['postApiV1WorkItemHistory'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1WorkItemHistory>>,
    { data: BodyType<PostApiV1WorkItemHistoryBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1WorkItemHistory(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1WorkItemHistoryMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1WorkItemHistory>>
>;
export type PostApiV1WorkItemHistoryMutationBody = BodyType<PostApiV1WorkItemHistoryBody>;
export type PostApiV1WorkItemHistoryMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create work-item-history
 */
export const usePostApiV1WorkItemHistory = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1WorkItemHistory>>,
    TError,
    { data: BodyType<PostApiV1WorkItemHistoryBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1WorkItemHistory>>,
  TError,
  { data: BodyType<PostApiV1WorkItemHistoryBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1WorkItemHistoryMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific work-item-history resource by its unique identifier
 * @summary Get work-item-history by ID
 */
export const getApiV1WorkItemHistoryId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1WorkItemHistoryId200>({
    url: `/api/v1/work-item-history/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1WorkItemHistoryIdQueryKey = (id: string) => {
  return [`/api/v1/work-item-history/${id}`] as const;
};

export const getGetApiV1WorkItemHistoryIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1WorkItemHistoryId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemHistoryId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1WorkItemHistoryIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1WorkItemHistoryId>>> = ({
    signal,
  }) => getApiV1WorkItemHistoryId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemHistoryId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1WorkItemHistoryIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1WorkItemHistoryId>>
>;
export type GetApiV1WorkItemHistoryIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1WorkItemHistoryId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemHistoryId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemHistoryId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItemHistoryId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItemHistoryId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItemHistoryId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemHistoryId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemHistoryId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItemHistoryId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItemHistoryId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItemHistoryId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemHistoryId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemHistoryId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get work-item-history by ID
 */

export function useGetApiV1WorkItemHistoryId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemHistoryId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemHistoryId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1WorkItemHistoryIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing work-item-history resource
 * @summary Update work-item-history
 */
export const patchApiV1WorkItemHistoryId = (
  id: string,
  patchApiV1WorkItemHistoryIdBody: BodyType<PatchApiV1WorkItemHistoryIdBody>,
) => {
  return axiosClient<PatchApiV1WorkItemHistoryId200>({
    url: `/api/v1/work-item-history/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1WorkItemHistoryIdBody,
  });
};

export const getPatchApiV1WorkItemHistoryIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1WorkItemHistoryId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1WorkItemHistoryIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1WorkItemHistoryId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1WorkItemHistoryIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1WorkItemHistoryId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1WorkItemHistoryId>>,
    { id: string; data: BodyType<PatchApiV1WorkItemHistoryIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1WorkItemHistoryId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1WorkItemHistoryIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1WorkItemHistoryId>>
>;
export type PatchApiV1WorkItemHistoryIdMutationBody = BodyType<PatchApiV1WorkItemHistoryIdBody>;
export type PatchApiV1WorkItemHistoryIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update work-item-history
 */
export const usePatchApiV1WorkItemHistoryId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1WorkItemHistoryId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1WorkItemHistoryIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1WorkItemHistoryId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1WorkItemHistoryIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1WorkItemHistoryIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing work-item-history resource
 * @summary Delete work-item-history
 */
export const deleteApiV1WorkItemHistoryId = (id: string) => {
  return axiosClient<DeleteApiV1WorkItemHistoryId204>({
    url: `/api/v1/work-item-history/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1WorkItemHistoryIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1WorkItemHistoryId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1WorkItemHistoryId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1WorkItemHistoryId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1WorkItemHistoryId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1WorkItemHistoryId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1WorkItemHistoryIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1WorkItemHistoryId>>
>;

export type DeleteApiV1WorkItemHistoryIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete work-item-history
 */
export const useDeleteApiV1WorkItemHistoryId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1WorkItemHistoryId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1WorkItemHistoryId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1WorkItemHistoryIdMutationOptions(options);

  return useMutation(mutationOptions);
};
