/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1StatusesId204,
  ErrorResponse,
  GetApiV1Statuses200,
  GetApiV1StatusesId200,
  GetApiV1StatusesParams,
  PatchApiV1StatusesId200,
  PatchApiV1StatusesIdBody,
  PostApiV1Statuses201,
  PostApiV1StatusesBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of statuses resources with optional filtering and sorting
 * @summary Get statuses list
 */
export const getApiV1Statuses = (params?: GetApiV1StatusesParams, signal?: AbortSignal) => {
  return axiosClient<GetApiV1Statuses200>({
    url: '/api/v1/statuses/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1StatusesQueryKey = (params?: GetApiV1StatusesParams) => {
  return ['/api/v1/statuses/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1StatusesQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1Statuses>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1StatusesParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Statuses>>, TError, TData>>;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1StatusesQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1Statuses>>> = ({ signal }) =>
    getApiV1Statuses(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1Statuses>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1StatusesQueryResult = NonNullable<Awaited<ReturnType<typeof getApiV1Statuses>>>;
export type GetApiV1StatusesQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1Statuses<
  TData = Awaited<ReturnType<typeof getApiV1Statuses>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1StatusesParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Statuses>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Statuses>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Statuses>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Statuses<
  TData = Awaited<ReturnType<typeof getApiV1Statuses>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1StatusesParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Statuses>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Statuses>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Statuses>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Statuses<
  TData = Awaited<ReturnType<typeof getApiV1Statuses>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1StatusesParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Statuses>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get statuses list
 */

export function useGetApiV1Statuses<
  TData = Awaited<ReturnType<typeof getApiV1Statuses>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1StatusesParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1Statuses>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1StatusesQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new status resource
 * @summary Create status
 */
export const postApiV1Statuses = (
  postApiV1StatusesBody: BodyType<PostApiV1StatusesBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1Statuses201>({
    url: '/api/v1/statuses/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1StatusesBody,
    signal,
  });
};

export const getPostApiV1StatusesMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1Statuses>>,
    TError,
    { data: BodyType<PostApiV1StatusesBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1Statuses>>,
  TError,
  { data: BodyType<PostApiV1StatusesBody> },
  TContext
> => {
  const mutationKey = ['postApiV1Statuses'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1Statuses>>,
    { data: BodyType<PostApiV1StatusesBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1Statuses(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1StatusesMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1Statuses>>
>;
export type PostApiV1StatusesMutationBody = BodyType<PostApiV1StatusesBody>;
export type PostApiV1StatusesMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create status
 */
export const usePostApiV1Statuses = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1Statuses>>,
    TError,
    { data: BodyType<PostApiV1StatusesBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1Statuses>>,
  TError,
  { data: BodyType<PostApiV1StatusesBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1StatusesMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific status resource by its unique identifier
 * @summary Get status by ID
 */
export const getApiV1StatusesId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1StatusesId200>({
    url: `/api/v1/statuses/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1StatusesIdQueryKey = (id: string) => {
  return [`/api/v1/statuses/${id}`] as const;
};

export const getGetApiV1StatusesIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1StatusesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1StatusesId>>, TError, TData>>;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1StatusesIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1StatusesId>>> = ({ signal }) =>
    getApiV1StatusesId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1StatusesId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1StatusesIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1StatusesId>>
>;
export type GetApiV1StatusesIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1StatusesId<
  TData = Awaited<ReturnType<typeof getApiV1StatusesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1StatusesId>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1StatusesId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1StatusesId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1StatusesId<
  TData = Awaited<ReturnType<typeof getApiV1StatusesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1StatusesId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1StatusesId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1StatusesId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1StatusesId<
  TData = Awaited<ReturnType<typeof getApiV1StatusesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1StatusesId>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get status by ID
 */

export function useGetApiV1StatusesId<
  TData = Awaited<ReturnType<typeof getApiV1StatusesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1StatusesId>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1StatusesIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing status resource
 * @summary Update status
 */
export const patchApiV1StatusesId = (
  id: string,
  patchApiV1StatusesIdBody: BodyType<PatchApiV1StatusesIdBody>,
) => {
  return axiosClient<PatchApiV1StatusesId200>({
    url: `/api/v1/statuses/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1StatusesIdBody,
  });
};

export const getPatchApiV1StatusesIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1StatusesId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1StatusesIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1StatusesId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1StatusesIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1StatusesId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1StatusesId>>,
    { id: string; data: BodyType<PatchApiV1StatusesIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1StatusesId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1StatusesIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1StatusesId>>
>;
export type PatchApiV1StatusesIdMutationBody = BodyType<PatchApiV1StatusesIdBody>;
export type PatchApiV1StatusesIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update status
 */
export const usePatchApiV1StatusesId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1StatusesId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1StatusesIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1StatusesId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1StatusesIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1StatusesIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing status resource
 * @summary Delete status
 */
export const deleteApiV1StatusesId = (id: string) => {
  return axiosClient<DeleteApiV1StatusesId204>({
    url: `/api/v1/statuses/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1StatusesIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1StatusesId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1StatusesId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1StatusesId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1StatusesId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1StatusesId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1StatusesIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1StatusesId>>
>;

export type DeleteApiV1StatusesIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete status
 */
export const useDeleteApiV1StatusesId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1StatusesId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1StatusesId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1StatusesIdMutationOptions(options);

  return useMutation(mutationOptions);
};
