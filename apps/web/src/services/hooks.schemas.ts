/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */
export type ErrorResponseErrorsItem = {
  field: string;
  message: string;
  code: string;
  type: string;
  path: string[];
};

export interface ErrorResponse {
  statusCode: number;
  errorCode: string;
  message: string;
  errors?: ErrorResponseErrorsItem[];
  data?: unknown;
}

export type ValidationErrorStatusCode =
  (typeof ValidationErrorStatusCode)[keyof typeof ValidationErrorStatusCode];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const ValidationErrorStatusCode = {
  NUMBER_400: 400,
} as const;

export type ValidationErrorErrorsItem = {
  field: string;
  message: string;
  code: string;
  type: string;
  path: string[];
};

export interface ValidationError {
  statusCode: ValidationErrorStatusCode;
  errorCode: string;
  message: string;
  errors?: ValidationErrorErrorsItem[];
  data?: unknown;
}

export type UnauthorizedErrorStatusCode =
  (typeof UnauthorizedErrorStatusCode)[keyof typeof UnauthorizedErrorStatusCode];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UnauthorizedErrorStatusCode = {
  NUMBER_401: 401,
} as const;

export type UnauthorizedErrorErrorsItem = {
  field: string;
  message: string;
  code: string;
  type: string;
  path: string[];
};

export interface UnauthorizedError {
  statusCode: UnauthorizedErrorStatusCode;
  errorCode: string;
  message: string;
  errors?: UnauthorizedErrorErrorsItem[];
  data?: unknown;
}

export type ForbiddenErrorStatusCode =
  (typeof ForbiddenErrorStatusCode)[keyof typeof ForbiddenErrorStatusCode];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const ForbiddenErrorStatusCode = {
  NUMBER_403: 403,
} as const;

export type ForbiddenErrorErrorsItem = {
  field: string;
  message: string;
  code: string;
  type: string;
  path: string[];
};

export interface ForbiddenError {
  statusCode: ForbiddenErrorStatusCode;
  errorCode: string;
  message: string;
  errors?: ForbiddenErrorErrorsItem[];
  data?: unknown;
}

export type NotFoundErrorStatusCode =
  (typeof NotFoundErrorStatusCode)[keyof typeof NotFoundErrorStatusCode];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const NotFoundErrorStatusCode = {
  NUMBER_404: 404,
} as const;

export type NotFoundErrorErrorsItem = {
  field: string;
  message: string;
  code: string;
  type: string;
  path: string[];
};

export interface NotFoundError {
  statusCode: NotFoundErrorStatusCode;
  errorCode: string;
  message: string;
  errors?: NotFoundErrorErrorsItem[];
  data?: unknown;
}

export type ConflictErrorStatusCode =
  (typeof ConflictErrorStatusCode)[keyof typeof ConflictErrorStatusCode];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const ConflictErrorStatusCode = {
  NUMBER_409: 409,
} as const;

export type ConflictErrorErrorsItem = {
  field: string;
  message: string;
  code: string;
  type: string;
  path: string[];
};

export interface ConflictError {
  statusCode: ConflictErrorStatusCode;
  errorCode: string;
  message: string;
  errors?: ConflictErrorErrorsItem[];
  data?: unknown;
}

export type InternalServerErrorStatusCode =
  (typeof InternalServerErrorStatusCode)[keyof typeof InternalServerErrorStatusCode];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const InternalServerErrorStatusCode = {
  NUMBER_500: 500,
} as const;

export type InternalServerErrorErrorsItem = {
  field: string;
  message: string;
  code: string;
  type: string;
  path: string[];
};

export interface InternalServerError {
  statusCode: InternalServerErrorStatusCode;
  errorCode: string;
  message: string;
  errors?: InternalServerErrorErrorsItem[];
  data?: unknown;
}

export interface IdParam {
  id: string;
}

export interface QueryString {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
}

export interface User {
  id: string;
  /** @maxLength 255 */
  email: string;
  /** @maxLength 255 */
  firstName: string;
  /** @maxLength 255 */
  lastName: string;
  /** @maxLength 255 */
  displayName: string;
  /** @maxLength 255 */
  password: string;
  isActive: boolean;
  isStaff: boolean;
  isEmailVerified: boolean;
  /** @nullable */
  lastLogin: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  avatarUrl: string | null;
  /** @nullable */
  bio: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  jobTitle: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  department: string | null;
  /**
   * @maxLength 50
   * @nullable
   */
  phone: string | null;
  /** @maxLength 50 */
  timezone: string;
  /** @maxLength 10 */
  language: string;
  /** @maxLength 20 */
  dateFormat: string;
  /** @maxLength 10 */
  timeFormat: string;
  createdAt: string;
  updatedAt: string;
  /**
   * @maxLength 512
   * @nullable
   */
  passwordResetToken: string | null;
  /** @nullable */
  passwordResetExpires: string | null;
  /**
   * @maxLength 512
   * @nullable
   */
  emailVerificationToken: string | null;
  loginAttempts: number;
  /** @nullable */
  lockedUntil: string | null;
}

export type AttachmentEntityType = (typeof AttachmentEntityType)[keyof typeof AttachmentEntityType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const AttachmentEntityType = {
  work_item: 'work_item',
  project: 'project',
  workspace: 'workspace',
  organization: 'organization',
  comment: 'comment',
  test_case: 'test_case',
  test_plan: 'test_plan',
  test_suite: 'test_suite',
  test_run: 'test_run',
  user: 'user',
} as const;

export interface Attachment {
  id: string;
  entityType: AttachmentEntityType;
  entityId: string;
  /** @maxLength 255 */
  fileName: string;
  /** @maxLength 500 */
  url: string;
  /**
   * @maxLength 100
   * @nullable
   */
  fileType: string | null;
  /** @nullable */
  size: number | null;
  isActive: boolean;
  /** @nullable */
  uploadedBy: string | null;
  uploadedAt: string;
  uploader: User;
  organization: Organization;
  workspace: Workspace;
  project: Project;
  workItem: WorkItem;
}

export type CreateAttachmentInputEntityType =
  (typeof CreateAttachmentInputEntityType)[keyof typeof CreateAttachmentInputEntityType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CreateAttachmentInputEntityType = {
  work_item: 'work_item',
  project: 'project',
  workspace: 'workspace',
  organization: 'organization',
  comment: 'comment',
  test_case: 'test_case',
  test_plan: 'test_plan',
  test_suite: 'test_suite',
  test_run: 'test_run',
  user: 'user',
} as const;

export interface CreateAttachmentInput {
  entityType: CreateAttachmentInputEntityType;
  entityId: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  fileName: string;
  /**
   * @minLength 1
   * @maxLength 500
   */
  url: string;
  /**
   * @maxLength 100
   * @nullable
   */
  fileType?: string | null;
  /** @nullable */
  size?: number | null;
  isActive?: boolean;
  /** @nullable */
  uploadedBy?: string | null;
}

export type UpdateAttachmentInputEntityType =
  (typeof UpdateAttachmentInputEntityType)[keyof typeof UpdateAttachmentInputEntityType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UpdateAttachmentInputEntityType = {
  work_item: 'work_item',
  project: 'project',
  workspace: 'workspace',
  organization: 'organization',
  comment: 'comment',
  test_case: 'test_case',
  test_plan: 'test_plan',
  test_suite: 'test_suite',
  test_run: 'test_run',
  user: 'user',
} as const;

export interface UpdateAttachmentInput {
  entityType?: UpdateAttachmentInputEntityType;
  entityId?: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  fileName?: string;
  /**
   * @minLength 1
   * @maxLength 500
   */
  url?: string;
  /**
   * @maxLength 100
   * @nullable
   */
  fileType?: string | null;
  /** @nullable */
  size?: number | null;
  isActive?: boolean;
  /** @nullable */
  uploadedBy?: string | null;
}

export type AuditLogAction = (typeof AuditLogAction)[keyof typeof AuditLogAction];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const AuditLogAction = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  ARCHIVE: 'ARCHIVE',
  RESTORE: 'RESTORE',
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  PASSWORD_CHANGE: 'PASSWORD_CHANGE',
  PROFILE_UPDATE: 'PROFILE_UPDATE',
  ROLE_ASSIGNED: 'ROLE_ASSIGNED',
  ROLE_REVOKED: 'ROLE_REVOKED',
  PERMISSION_UPDATED: 'PERMISSION_UPDATED',
  STATUS_CHANGE: 'STATUS_CHANGE',
  SPRINT_TRANSITION: 'SPRINT_TRANSITION',
  COMMENT_ADDED: 'COMMENT_ADDED',
  ATTACHMENT_UPLOADED: 'ATTACHMENT_UPLOADED',
  NOTIFICATION_SENT: 'NOTIFICATION_SENT',
  SYSTEM_START: 'SYSTEM_START',
  SYSTEM_ERROR: 'SYSTEM_ERROR',
  SCHEDULED_TASK_RUN: 'SCHEDULED_TASK_RUN',
  DATA_IMPORT: 'DATA_IMPORT',
  DATA_EXPORT: 'DATA_EXPORT',
} as const;

export type AuditLogDetails = { [key: string]: unknown };

export interface AuditLog {
  id: string;
  /** @maxLength 100 */
  entityType: string;
  entityId: string;
  action: AuditLogAction;
  /** @nullable */
  changedBy: string | null;
  timestamp: string;
  details?: AuditLogDetails;
  /**
   * @maxLength 100
   * @nullable
   */
  relatedEntityType: string | null;
  /** @nullable */
  relatedEntityId: string | null;
  user: User;
  organization: Organization;
  workspace: Workspace;
  project: Project;
  workItem: WorkItem;
  role: Role;
  sprint: Sprint;
  workflow: Workflow;
  status: Status;
  priority: Priority;
}

export type CreateAuditLogInputAction =
  (typeof CreateAuditLogInputAction)[keyof typeof CreateAuditLogInputAction];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CreateAuditLogInputAction = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  ARCHIVE: 'ARCHIVE',
  RESTORE: 'RESTORE',
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  PASSWORD_CHANGE: 'PASSWORD_CHANGE',
  PROFILE_UPDATE: 'PROFILE_UPDATE',
  ROLE_ASSIGNED: 'ROLE_ASSIGNED',
  ROLE_REVOKED: 'ROLE_REVOKED',
  PERMISSION_UPDATED: 'PERMISSION_UPDATED',
  STATUS_CHANGE: 'STATUS_CHANGE',
  SPRINT_TRANSITION: 'SPRINT_TRANSITION',
  COMMENT_ADDED: 'COMMENT_ADDED',
  ATTACHMENT_UPLOADED: 'ATTACHMENT_UPLOADED',
  NOTIFICATION_SENT: 'NOTIFICATION_SENT',
  SYSTEM_START: 'SYSTEM_START',
  SYSTEM_ERROR: 'SYSTEM_ERROR',
  SCHEDULED_TASK_RUN: 'SCHEDULED_TASK_RUN',
  DATA_IMPORT: 'DATA_IMPORT',
  DATA_EXPORT: 'DATA_EXPORT',
} as const;

export type CreateAuditLogInputDetails = { [key: string]: unknown };

export interface CreateAuditLogInput {
  /** @maxLength 100 */
  entityType: string;
  entityId: string;
  action: CreateAuditLogInputAction;
  /** @nullable */
  changedBy?: string | null;
  details?: CreateAuditLogInputDetails;
  /**
   * @maxLength 100
   * @nullable
   */
  relatedEntityType?: string | null;
  /** @nullable */
  relatedEntityId?: string | null;
}

export type UpdateAuditLogInputAction =
  (typeof UpdateAuditLogInputAction)[keyof typeof UpdateAuditLogInputAction];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UpdateAuditLogInputAction = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  ARCHIVE: 'ARCHIVE',
  RESTORE: 'RESTORE',
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  PASSWORD_CHANGE: 'PASSWORD_CHANGE',
  PROFILE_UPDATE: 'PROFILE_UPDATE',
  ROLE_ASSIGNED: 'ROLE_ASSIGNED',
  ROLE_REVOKED: 'ROLE_REVOKED',
  PERMISSION_UPDATED: 'PERMISSION_UPDATED',
  STATUS_CHANGE: 'STATUS_CHANGE',
  SPRINT_TRANSITION: 'SPRINT_TRANSITION',
  COMMENT_ADDED: 'COMMENT_ADDED',
  ATTACHMENT_UPLOADED: 'ATTACHMENT_UPLOADED',
  NOTIFICATION_SENT: 'NOTIFICATION_SENT',
  SYSTEM_START: 'SYSTEM_START',
  SYSTEM_ERROR: 'SYSTEM_ERROR',
  SCHEDULED_TASK_RUN: 'SCHEDULED_TASK_RUN',
  DATA_IMPORT: 'DATA_IMPORT',
  DATA_EXPORT: 'DATA_EXPORT',
} as const;

export type UpdateAuditLogInputDetails = { [key: string]: unknown };

export interface UpdateAuditLogInput {
  /** @maxLength 100 */
  entityType?: string;
  entityId?: string;
  action?: UpdateAuditLogInputAction;
  /** @nullable */
  changedBy?: string | null;
  details?: UpdateAuditLogInputDetails;
  /**
   * @maxLength 100
   * @nullable
   */
  relatedEntityType?: string | null;
  /** @nullable */
  relatedEntityId?: string | null;
}

export type InvitationScopeType = (typeof InvitationScopeType)[keyof typeof InvitationScopeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const InvitationScopeType = {
  organization: 'organization',
  workspace: 'workspace',
  project: 'project',
} as const;

export type InvitationStatus = (typeof InvitationStatus)[keyof typeof InvitationStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const InvitationStatus = {
  pending: 'pending',
  accepted: 'accepted',
  rejected: 'rejected',
  expired: 'expired',
  revoked: 'revoked',
} as const;

export type InvitationExpiresAt = string | string | 'null' | null | unknown;

/**
 * @nullable
 */
export type InvitationAcceptedAt = string | string | 'null' | null | unknown | null;

export interface Invitation {
  id: string;
  /** @maxLength 255 */
  email: string;
  /** @maxLength 512 */
  token: string;
  invitedBy: string;
  roleId: string;
  scopeType: InvitationScopeType;
  scopeId: string;
  status: InvitationStatus;
  expiresAt?: InvitationExpiresAt;
  /** @nullable */
  acceptedAt?: InvitationAcceptedAt;
  createdAt: string;
  updatedAt: string;
  inviter: User;
  role: Role;
  organization: Organization;
  workspace: Workspace;
  project: Project;
}

export type CreateInvitationInputEmail = string | '' | 'null' | null | unknown;

export type CreateInvitationInputScopeType =
  (typeof CreateInvitationInputScopeType)[keyof typeof CreateInvitationInputScopeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CreateInvitationInputScopeType = {
  organization: 'organization',
  workspace: 'workspace',
  project: 'project',
} as const;

export type CreateInvitationInputStatus =
  (typeof CreateInvitationInputStatus)[keyof typeof CreateInvitationInputStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CreateInvitationInputStatus = {
  pending: 'pending',
  accepted: 'accepted',
  rejected: 'rejected',
  expired: 'expired',
  revoked: 'revoked',
} as const;

/**
 * @nullable
 */
export type CreateInvitationInputAcceptedAt = string | string | 'null' | null | unknown | null;

export interface CreateInvitationInput {
  email?: CreateInvitationInputEmail;
  invitedBy: string;
  roleId: string;
  scopeType: CreateInvitationInputScopeType;
  scopeId: string;
  status?: CreateInvitationInputStatus;
  /** @nullable */
  acceptedAt?: CreateInvitationInputAcceptedAt;
}

export type UpdateInvitationInputEmail = string | '' | 'null' | null | unknown;

export type UpdateInvitationInputScopeType =
  (typeof UpdateInvitationInputScopeType)[keyof typeof UpdateInvitationInputScopeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UpdateInvitationInputScopeType = {
  organization: 'organization',
  workspace: 'workspace',
  project: 'project',
} as const;

export type UpdateInvitationInputStatus =
  (typeof UpdateInvitationInputStatus)[keyof typeof UpdateInvitationInputStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UpdateInvitationInputStatus = {
  pending: 'pending',
  accepted: 'accepted',
  rejected: 'rejected',
  expired: 'expired',
  revoked: 'revoked',
} as const;

/**
 * @nullable
 */
export type UpdateInvitationInputAcceptedAt = string | string | 'null' | null | unknown | null;

export interface UpdateInvitationInput {
  email?: UpdateInvitationInputEmail;
  invitedBy?: string;
  roleId?: string;
  scopeType?: UpdateInvitationInputScopeType;
  scopeId?: string;
  status?: UpdateInvitationInputStatus;
  /** @nullable */
  acceptedAt?: UpdateInvitationInputAcceptedAt;
}

export type OrganizationSettings = { [key: string]: unknown };

export type OrganizationDomains = { [key: string]: unknown };

/**
 * @nullable
 */
export type OrganizationPlanExpiresAt = string | string | 'null' | null | unknown | null;

export interface Organization {
  id: string;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  slug: string;
  /** @nullable */
  description: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  website: string | null;
  /**
   * @maxLength 500
   * @nullable
   */
  logoUrl: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  /** @nullable */
  createdBy: string | null;
  settings?: OrganizationSettings;
  domains?: OrganizationDomains;
  /**
   * @maxLength 255
   * @nullable
   */
  billingEmail: string | null;
  /** @nullable */
  billingAddress: string | null;
  /**
   * @maxLength 50
   * @nullable
   */
  billingPlan: string | null;
  /** @nullable */
  planExpiresAt?: OrganizationPlanExpiresAt;
  creator: User;
}

export type CreateOrganizationInputSettings = { [key: string]: unknown };

export type CreateOrganizationInputDomains = { [key: string]: unknown };

/**
 * @nullable
 */
export type CreateOrganizationInputBillingEmail = string | '' | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type CreateOrganizationInputPlanExpiresAt = string | string | 'null' | null | unknown | null;

export interface CreateOrganizationInput {
  /**
   * @minLength 1
   * @maxLength 255
   */
  name: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  slug: string;
  /** @nullable */
  description?: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  website?: string | null;
  /** @nullable */
  logoUrl?: string | null;
  isActive?: boolean;
  /** @nullable */
  createdBy: string | null;
  settings?: CreateOrganizationInputSettings;
  domains?: CreateOrganizationInputDomains;
  /** @nullable */
  billingEmail?: CreateOrganizationInputBillingEmail;
  /** @nullable */
  billingAddress?: string | null;
  /**
   * @maxLength 50
   * @nullable
   */
  billingPlan?: string | null;
  /** @nullable */
  planExpiresAt?: CreateOrganizationInputPlanExpiresAt;
}

export type UpdateOrganizationInputSettings = { [key: string]: unknown };

export type UpdateOrganizationInputDomains = { [key: string]: unknown };

/**
 * @nullable
 */
export type UpdateOrganizationInputBillingEmail = string | '' | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type UpdateOrganizationInputPlanExpiresAt = string | string | 'null' | null | unknown | null;

export interface UpdateOrganizationInput {
  /**
   * @minLength 1
   * @maxLength 255
   */
  name?: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  slug?: string;
  /** @nullable */
  description?: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  website?: string | null;
  /** @nullable */
  logoUrl?: string | null;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
  settings?: UpdateOrganizationInputSettings;
  domains?: UpdateOrganizationInputDomains;
  /** @nullable */
  billingEmail?: UpdateOrganizationInputBillingEmail;
  /** @nullable */
  billingAddress?: string | null;
  /**
   * @maxLength 50
   * @nullable
   */
  billingPlan?: string | null;
  /** @nullable */
  planExpiresAt?: UpdateOrganizationInputPlanExpiresAt;
}

export interface Permission {
  id: string;
  /** @maxLength 255 */
  identifier: string;
  /** @maxLength 255 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @maxLength 100 */
  category: string;
  /** @maxLength 50 */
  scopeType: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export type CreatePermissionInputScopeType =
  (typeof CreatePermissionInputScopeType)[keyof typeof CreatePermissionInputScopeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CreatePermissionInputScopeType = {
  org: 'org',
  ws: 'ws',
} as const;

export interface CreatePermissionInput {
  /**
   * @minLength 1
   * @maxLength 255
   */
  identifier: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name: string;
  /** @nullable */
  description?: string | null;
  /**
   * @minLength 1
   * @maxLength 100
   */
  category: string;
  scopeType: CreatePermissionInputScopeType;
  isActive?: boolean;
}

export type UpdatePermissionInputScopeType =
  (typeof UpdatePermissionInputScopeType)[keyof typeof UpdatePermissionInputScopeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UpdatePermissionInputScopeType = {
  org: 'org',
  ws: 'ws',
} as const;

export interface UpdatePermissionInput {
  /**
   * @minLength 1
   * @maxLength 255
   */
  identifier?: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name?: string;
  /** @nullable */
  description?: string | null;
  /**
   * @minLength 1
   * @maxLength 100
   */
  category?: string;
  scopeType?: UpdatePermissionInputScopeType;
  isActive?: boolean;
}

export interface Priority {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  organizationId: string | null;
  level: number;
  isSystem: boolean;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  icon: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  organization: Organization;
}

export interface CreatePriorityInput {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  organizationId?: string | null;
  level: number;
  isSystem?: boolean;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  icon?: string | null;
  isActive?: boolean;
}

export interface UpdatePriorityInput {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name?: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  organizationId?: string | null;
  level?: number;
  isSystem?: boolean;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  icon?: string | null;
  isActive?: boolean;
}

export interface ProjectWorkflow {
  id: string;
  projectId: string;
  workflowId: string;
  isActive: boolean;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
  project: Project;
  workflow: Workflow;
}

export interface CreateProjectWorkflowInput {
  projectId: string;
  workflowId: string;
  isActive?: boolean;
  isDefault?: boolean;
}

export interface UpdateProjectWorkflowInput {
  projectId?: string;
  workflowId?: string;
  isActive?: boolean;
  isDefault?: boolean;
}

/**
 * @nullable
 */
export type ProjectStartDate = string | string | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type ProjectTargetDate = string | string | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type ProjectActualEndDate = string | string | 'null' | null | unknown | null;

export type ProjectStatus = (typeof ProjectStatus)[keyof typeof ProjectStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const ProjectStatus = {
  planning: 'planning',
  active: 'active',
  completed: 'completed',
  archived: 'archived',
} as const;

export type ProjectVisibility = (typeof ProjectVisibility)[keyof typeof ProjectVisibility];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const ProjectVisibility = {
  public: 'public',
  private: 'private',
} as const;

export type ProjectSettings = { [key: string]: unknown };

export type ProjectCreatedAt = string | string | 'null' | null | unknown;

export type ProjectUpdatedAt = string | string | 'null' | null | unknown;

export interface Project {
  id: string;
  workspaceId: string;
  /** @nullable */
  workflowId: string | null;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 10 */
  key: string;
  /** @nullable */
  description: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  icon: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  /** @nullable */
  startDate?: ProjectStartDate;
  /** @nullable */
  targetDate?: ProjectTargetDate;
  /** @nullable */
  actualEndDate?: ProjectActualEndDate;
  status: ProjectStatus;
  visibility: ProjectVisibility;
  /** @nullable */
  defaultAssigneeId: string | null;
  lastTicketNumber: number;
  settings?: ProjectSettings;
  isActive: boolean;
  createdAt?: ProjectCreatedAt;
  updatedAt?: ProjectUpdatedAt;
  /** @nullable */
  createdBy: string | null;
  workspace: Workspace;
  workflow: Workflow;
  creator: User;
  defaultAssignee: User;
}

/**
 * @nullable
 */
export type CreateProjectInputStartDate = string | string | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type CreateProjectInputTargetDate = string | string | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type CreateProjectInputActualEndDate = string | string | 'null' | null | unknown | null;

export type CreateProjectInputStatus =
  (typeof CreateProjectInputStatus)[keyof typeof CreateProjectInputStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CreateProjectInputStatus = {
  planning: 'planning',
  active: 'active',
  completed: 'completed',
  archived: 'archived',
} as const;

export type CreateProjectInputVisibility =
  (typeof CreateProjectInputVisibility)[keyof typeof CreateProjectInputVisibility];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CreateProjectInputVisibility = {
  public: 'public',
  private: 'private',
} as const;

export type CreateProjectInputSettings = { [key: string]: unknown };

export interface CreateProjectInput {
  workspaceId: string;
  /** @nullable */
  workflowId?: string | null;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name: string;
  /**
   * @minLength 1
   * @maxLength 10
   */
  key: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  icon?: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  /** @nullable */
  startDate?: CreateProjectInputStartDate;
  /** @nullable */
  targetDate?: CreateProjectInputTargetDate;
  /** @nullable */
  actualEndDate?: CreateProjectInputActualEndDate;
  status?: CreateProjectInputStatus;
  visibility?: CreateProjectInputVisibility;
  /** @nullable */
  defaultAssigneeId?: string | null;
  lastTicketNumber?: number;
  settings?: CreateProjectInputSettings;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
}

/**
 * @nullable
 */
export type UpdateProjectInputStartDate = string | string | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type UpdateProjectInputTargetDate = string | string | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type UpdateProjectInputActualEndDate = string | string | 'null' | null | unknown | null;

export type UpdateProjectInputStatus =
  (typeof UpdateProjectInputStatus)[keyof typeof UpdateProjectInputStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UpdateProjectInputStatus = {
  planning: 'planning',
  active: 'active',
  completed: 'completed',
  archived: 'archived',
} as const;

export type UpdateProjectInputVisibility =
  (typeof UpdateProjectInputVisibility)[keyof typeof UpdateProjectInputVisibility];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UpdateProjectInputVisibility = {
  public: 'public',
  private: 'private',
} as const;

export type UpdateProjectInputSettings = { [key: string]: unknown };

export interface UpdateProjectInput {
  workspaceId?: string;
  /** @nullable */
  workflowId?: string | null;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name?: string;
  /**
   * @minLength 1
   * @maxLength 10
   */
  key?: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  icon?: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  /** @nullable */
  startDate?: UpdateProjectInputStartDate;
  /** @nullable */
  targetDate?: UpdateProjectInputTargetDate;
  /** @nullable */
  actualEndDate?: UpdateProjectInputActualEndDate;
  status?: UpdateProjectInputStatus;
  visibility?: UpdateProjectInputVisibility;
  /** @nullable */
  defaultAssigneeId?: string | null;
  lastTicketNumber?: number;
  settings?: UpdateProjectInputSettings;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
}

export interface Resolution {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  organizationId: string | null;
  isSystem: boolean;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  organization: Organization;
}

export interface CreateResolutionInput {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  organizationId?: string | null;
  isSystem?: boolean;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  isActive?: boolean;
}

export interface UpdateResolutionInput {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name?: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  organizationId?: string | null;
  isSystem?: boolean;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  isActive?: boolean;
}

export type RoleAssignmentScopeType =
  (typeof RoleAssignmentScopeType)[keyof typeof RoleAssignmentScopeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const RoleAssignmentScopeType = {
  organization: 'organization',
  workspace: 'workspace',
  project: 'project',
} as const;

export interface RoleAssignment {
  id: string;
  userId: string;
  roleId: string;
  scopeType: RoleAssignmentScopeType;
  scopeId: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  user: User;
  role: Role;
  organization: Organization;
  workspace: Workspace;
  project: Project;
}

export type CreateRoleAssignmentInputScopeType =
  (typeof CreateRoleAssignmentInputScopeType)[keyof typeof CreateRoleAssignmentInputScopeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CreateRoleAssignmentInputScopeType = {
  organization: 'organization',
  workspace: 'workspace',
  project: 'project',
} as const;

export interface CreateRoleAssignmentInput {
  userId: string;
  roleId: string;
  scopeType: CreateRoleAssignmentInputScopeType;
  scopeId: string;
  isActive?: boolean;
}

export type UpdateRoleAssignmentInputScopeType =
  (typeof UpdateRoleAssignmentInputScopeType)[keyof typeof UpdateRoleAssignmentInputScopeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UpdateRoleAssignmentInputScopeType = {
  organization: 'organization',
  workspace: 'workspace',
  project: 'project',
} as const;

export interface UpdateRoleAssignmentInput {
  userId?: string;
  roleId?: string;
  scopeType?: UpdateRoleAssignmentInputScopeType;
  scopeId?: string;
  isActive?: boolean;
}

export interface Role {
  id: string;
  /** @maxLength 255 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @maxLength 255 */
  identifier: string;
  /** @maxLength 50 */
  level: string;
  isSystem: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  /** @nullable */
  createdBy: string | null;
}

export interface CreateRoleInput {
  /**
   * @minLength 1
   * @maxLength 255
   */
  name: string;
  /** @nullable */
  description?: string | null;
  /**
   * @minLength 1
   * @maxLength 255
   */
  identifier: string;
  /**
   * @minLength 1
   * @maxLength 50
   */
  level: string;
  isSystem?: boolean;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
}

export interface UpdateRoleInput {
  /**
   * @minLength 1
   * @maxLength 255
   */
  name?: string;
  /** @nullable */
  description?: string | null;
  /**
   * @minLength 1
   * @maxLength 255
   */
  identifier?: string;
  /**
   * @minLength 1
   * @maxLength 50
   */
  level?: string;
  isSystem?: boolean;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
}

/**
 * @nullable
 */
export type SprintStartDate = string | string | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type SprintEndDate = string | string | 'null' | null | unknown | null;

export type SprintStatus = (typeof SprintStatus)[keyof typeof SprintStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SprintStatus = {
  draft: 'draft',
  active: 'active',
  completed: 'completed',
  archived: 'archived',
} as const;

export interface Sprint {
  id: string;
  projectId: string;
  /** @maxLength 255 */
  name: string;
  /** @nullable */
  goal: string | null;
  /** @nullable */
  startDate?: SprintStartDate;
  /** @nullable */
  endDate?: SprintEndDate;
  /** @nullable */
  capacity: number | null;
  /** @nullable */
  velocity: number | null;
  status: SprintStatus;
  isActive: boolean;
  /** @nullable */
  createdBy: string | null;
  createdAt: string;
  updatedAt: string;
  project: Project;
  creator: User;
}

/**
 * @nullable
 */
export type CreateSprintInputStartDate = string | string | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type CreateSprintInputEndDate = string | string | 'null' | null | unknown | null;

export type CreateSprintInputStatus =
  (typeof CreateSprintInputStatus)[keyof typeof CreateSprintInputStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CreateSprintInputStatus = {
  draft: 'draft',
  active: 'active',
  completed: 'completed',
  archived: 'archived',
} as const;

export interface CreateSprintInput {
  projectId: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name: string;
  /** @nullable */
  goal?: string | null;
  /** @nullable */
  startDate?: CreateSprintInputStartDate;
  /** @nullable */
  endDate?: CreateSprintInputEndDate;
  /** @nullable */
  capacity?: number | null;
  /** @nullable */
  velocity?: number | null;
  status?: CreateSprintInputStatus;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
}

/**
 * @nullable
 */
export type UpdateSprintInputStartDate = string | string | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type UpdateSprintInputEndDate = string | string | 'null' | null | unknown | null;

export type UpdateSprintInputStatus =
  (typeof UpdateSprintInputStatus)[keyof typeof UpdateSprintInputStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UpdateSprintInputStatus = {
  draft: 'draft',
  active: 'active',
  completed: 'completed',
  archived: 'archived',
} as const;

export interface UpdateSprintInput {
  projectId?: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name?: string;
  /** @nullable */
  goal?: string | null;
  /** @nullable */
  startDate?: UpdateSprintInputStartDate;
  /** @nullable */
  endDate?: UpdateSprintInputEndDate;
  /** @nullable */
  capacity?: number | null;
  /** @nullable */
  velocity?: number | null;
  status?: UpdateSprintInputStatus;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
}

export interface StatusCategory {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateStatusCategoryInput {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name: string;
  /** @nullable */
  description?: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  isActive?: boolean;
}

export interface UpdateStatusCategoryInput {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name?: string;
  /** @nullable */
  description?: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  isActive?: boolean;
}

export type StatusStatusType = (typeof StatusStatusType)[keyof typeof StatusStatusType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const StatusStatusType = {
  todo: 'todo',
  in_progress: 'in_progress',
  done: 'done',
} as const;

export interface Status {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  organizationId: string | null;
  statusType: StatusStatusType;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  order: number;
  statusCategoryId: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  organization: Organization;
  statusCategory: StatusCategory;
}

export type CreateStatusInputStatusType =
  (typeof CreateStatusInputStatusType)[keyof typeof CreateStatusInputStatusType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CreateStatusInputStatusType = {
  todo: 'todo',
  in_progress: 'in_progress',
  done: 'done',
} as const;

export interface CreateStatusInput {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  organizationId?: string | null;
  statusType: CreateStatusInputStatusType;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  order?: number;
  statusCategoryId: string;
  isActive?: boolean;
}

export type UpdateStatusInputStatusType =
  (typeof UpdateStatusInputStatusType)[keyof typeof UpdateStatusInputStatusType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UpdateStatusInputStatusType = {
  todo: 'todo',
  in_progress: 'in_progress',
  done: 'done',
} as const;

export interface UpdateStatusInput {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name?: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  organizationId?: string | null;
  statusType?: UpdateStatusInputStatusType;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  order?: number;
  statusCategoryId?: string;
  isActive?: boolean;
}

export interface TestCaseComment {
  id: string;
  testCaseId: string;
  content: string;
  authorId: string;
  /** @nullable */
  parentId: string | null;
  isEdited: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  author: User;
}

export interface CreateTestCaseCommentInput {
  testCaseId: string;
  /** @minLength 1 */
  content: string;
  authorId: string;
  /** @nullable */
  parentId?: string | null;
  isEdited?: boolean;
}

export interface UpdateTestCaseCommentInput {
  /** @minLength 1 */
  content?: string;
  isEdited?: boolean;
}

export type TestCaseHistoryChangeType =
  (typeof TestCaseHistoryChangeType)[keyof typeof TestCaseHistoryChangeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const TestCaseHistoryChangeType = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  RESTORE: 'RESTORE',
  COMMENT_ADDED: 'COMMENT_ADDED',
  COMMENT_UPDATED: 'COMMENT_UPDATED',
  COMMENT_DELETED: 'COMMENT_DELETED',
  ATTACHMENT_ADDED: 'ATTACHMENT_ADDED',
  ATTACHMENT_DELETED: 'ATTACHMENT_DELETED',
  EXECUTION_ADDED: 'EXECUTION_ADDED',
  EXECUTION_UPDATED: 'EXECUTION_UPDATED',
} as const;

export type TestCaseHistoryChangedFieldsOldValue =
  | string
  | number
  | boolean
  | string[]
  | 'null'
  | null;

export type TestCaseHistoryChangedFieldsNewValue =
  | string
  | number
  | boolean
  | string[]
  | 'null'
  | null;

/**
 * @nullable
 */
export type TestCaseHistoryChangedFields = {
  [key: string]: {
    oldValue: TestCaseHistoryChangedFieldsOldValue;
    newValue: TestCaseHistoryChangedFieldsNewValue;
  };
} | null;

export type TestCaseHistoryMetadataComment = {
  id: string;
  content: string;
};

export type TestCaseHistoryMetadataAttachment = {
  id: string;
  filename: string;
  size?: number;
  mimeType?: string;
};

export type TestCaseHistoryMetadataExecution = {
  id: string;
  status: string;
  result?: string;
};

/**
 * @nullable
 */
export type TestCaseHistoryMetadata = {
  comment?: TestCaseHistoryMetadataComment;
  attachment?: TestCaseHistoryMetadataAttachment;
  execution?: TestCaseHistoryMetadataExecution;
  [key: string]: unknown;
} | null;

export type TestCaseHistoryTestCaseStepsItem = {
  step: string;
  expectedResult: string;
};

export type TestCaseHistoryTestCaseEstimate = { [key: string]: unknown };

/**
 * @nullable
 */
export type TestCaseHistoryTestCase = {
  id?: string;
  /** @maxLength 500 */
  title?: string;
  /** @nullable */
  description?: string | null;
  projectId?: string;
  /** @nullable */
  assigneeId?: string | null;
  /** @nullable */
  reporterId?: string | null;
  /** @nullable */
  workItemId?: string | null;
  /** @nullable */
  copiedFromId?: string | null;
  /** @nullable */
  preconditions?: string | null;
  steps?: TestCaseHistoryTestCaseStepsItem[];
  tags?: string[];
  estimate?: TestCaseHistoryTestCaseEstimate;
  order?: number;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
  createdAt?: string;
  updatedAt?: string;
  project?: Project;
  assignee?: User;
  reporter?: User;
  creator?: User;
  workItem?: WorkItem;
} | null;

export interface TestCaseHistory {
  id: string;
  testCaseId: string;
  changeType: TestCaseHistoryChangeType;
  changedBy: string;
  /** @nullable */
  changedFields: TestCaseHistoryChangedFields;
  /** @nullable */
  metadata: TestCaseHistoryMetadata;
  /**
   * @maxLength 500
   * @nullable
   */
  summary: string | null;
  createdAt: string;
  user: User;
  /** @nullable */
  testCase: TestCaseHistoryTestCase;
}

export type CreateTestCaseHistoryInputChangeType =
  (typeof CreateTestCaseHistoryInputChangeType)[keyof typeof CreateTestCaseHistoryInputChangeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CreateTestCaseHistoryInputChangeType = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  RESTORE: 'RESTORE',
  COMMENT_ADDED: 'COMMENT_ADDED',
  COMMENT_UPDATED: 'COMMENT_UPDATED',
  COMMENT_DELETED: 'COMMENT_DELETED',
  ATTACHMENT_ADDED: 'ATTACHMENT_ADDED',
  ATTACHMENT_DELETED: 'ATTACHMENT_DELETED',
  EXECUTION_ADDED: 'EXECUTION_ADDED',
  EXECUTION_UPDATED: 'EXECUTION_UPDATED',
} as const;

export type CreateTestCaseHistoryInputChangedFieldsOldValue =
  | string
  | number
  | boolean
  | string[]
  | 'null'
  | null;

export type CreateTestCaseHistoryInputChangedFieldsNewValue =
  | string
  | number
  | boolean
  | string[]
  | 'null'
  | null;

/**
 * @nullable
 */
export type CreateTestCaseHistoryInputChangedFields = {
  [key: string]: {
    oldValue: CreateTestCaseHistoryInputChangedFieldsOldValue;
    newValue: CreateTestCaseHistoryInputChangedFieldsNewValue;
  };
} | null;

export type CreateTestCaseHistoryInputMetadataComment = {
  id: string;
  content: string;
};

export type CreateTestCaseHistoryInputMetadataAttachment = {
  id: string;
  filename: string;
  size?: number;
  mimeType?: string;
};

export type CreateTestCaseHistoryInputMetadataExecution = {
  id: string;
  status: string;
  result?: string;
};

/**
 * @nullable
 */
export type CreateTestCaseHistoryInputMetadata = {
  comment?: CreateTestCaseHistoryInputMetadataComment;
  attachment?: CreateTestCaseHistoryInputMetadataAttachment;
  execution?: CreateTestCaseHistoryInputMetadataExecution;
  [key: string]: unknown;
} | null;

export type CreateTestCaseHistoryInputTestCaseStepsItem = {
  step: string;
  expectedResult: string;
};

export type CreateTestCaseHistoryInputTestCaseEstimate = {
  [key: string]: unknown;
};

/**
 * @nullable
 */
export type CreateTestCaseHistoryInputTestCase = {
  id?: string;
  /** @maxLength 500 */
  title?: string;
  /** @nullable */
  description?: string | null;
  projectId?: string;
  /** @nullable */
  assigneeId?: string | null;
  /** @nullable */
  reporterId?: string | null;
  /** @nullable */
  workItemId?: string | null;
  /** @nullable */
  copiedFromId?: string | null;
  /** @nullable */
  preconditions?: string | null;
  steps?: CreateTestCaseHistoryInputTestCaseStepsItem[];
  tags?: string[];
  estimate?: CreateTestCaseHistoryInputTestCaseEstimate;
  order?: number;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
  createdAt?: string;
  updatedAt?: string;
  project?: Project;
  assignee?: User;
  reporter?: User;
  creator?: User;
  workItem?: WorkItem;
} | null;

export interface CreateTestCaseHistoryInput {
  id: string;
  testCaseId: string;
  changeType: CreateTestCaseHistoryInputChangeType;
  changedBy: string;
  /** @nullable */
  changedFields: CreateTestCaseHistoryInputChangedFields;
  /** @nullable */
  metadata: CreateTestCaseHistoryInputMetadata;
  /**
   * @maxLength 500
   * @nullable
   */
  summary: string | null;
  createdAt: string;
  user: User;
  /** @nullable */
  testCase: CreateTestCaseHistoryInputTestCase;
}

export type UpdateTestCaseHistoryInputChangeType =
  (typeof UpdateTestCaseHistoryInputChangeType)[keyof typeof UpdateTestCaseHistoryInputChangeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UpdateTestCaseHistoryInputChangeType = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  RESTORE: 'RESTORE',
  COMMENT_ADDED: 'COMMENT_ADDED',
  COMMENT_UPDATED: 'COMMENT_UPDATED',
  COMMENT_DELETED: 'COMMENT_DELETED',
  ATTACHMENT_ADDED: 'ATTACHMENT_ADDED',
  ATTACHMENT_DELETED: 'ATTACHMENT_DELETED',
  EXECUTION_ADDED: 'EXECUTION_ADDED',
  EXECUTION_UPDATED: 'EXECUTION_UPDATED',
} as const;

export type UpdateTestCaseHistoryInputChangedFieldsOldValue =
  | string
  | number
  | boolean
  | string[]
  | 'null'
  | null;

export type UpdateTestCaseHistoryInputChangedFieldsNewValue =
  | string
  | number
  | boolean
  | string[]
  | 'null'
  | null;

/**
 * @nullable
 */
export type UpdateTestCaseHistoryInputChangedFields = {
  [key: string]: {
    oldValue: UpdateTestCaseHistoryInputChangedFieldsOldValue;
    newValue: UpdateTestCaseHistoryInputChangedFieldsNewValue;
  };
} | null;

export type UpdateTestCaseHistoryInputMetadataComment = {
  id: string;
  content: string;
};

export type UpdateTestCaseHistoryInputMetadataAttachment = {
  id: string;
  filename: string;
  size?: number;
  mimeType?: string;
};

export type UpdateTestCaseHistoryInputMetadataExecution = {
  id: string;
  status: string;
  result?: string;
};

/**
 * @nullable
 */
export type UpdateTestCaseHistoryInputMetadata = {
  comment?: UpdateTestCaseHistoryInputMetadataComment;
  attachment?: UpdateTestCaseHistoryInputMetadataAttachment;
  execution?: UpdateTestCaseHistoryInputMetadataExecution;
  [key: string]: unknown;
} | null;

export type UpdateTestCaseHistoryInputTestCaseStepsItem = {
  step: string;
  expectedResult: string;
};

export type UpdateTestCaseHistoryInputTestCaseEstimate = {
  [key: string]: unknown;
};

/**
 * @nullable
 */
export type UpdateTestCaseHistoryInputTestCase = {
  id?: string;
  /** @maxLength 500 */
  title?: string;
  /** @nullable */
  description?: string | null;
  projectId?: string;
  /** @nullable */
  assigneeId?: string | null;
  /** @nullable */
  reporterId?: string | null;
  /** @nullable */
  workItemId?: string | null;
  /** @nullable */
  copiedFromId?: string | null;
  /** @nullable */
  preconditions?: string | null;
  steps?: UpdateTestCaseHistoryInputTestCaseStepsItem[];
  tags?: string[];
  estimate?: UpdateTestCaseHistoryInputTestCaseEstimate;
  order?: number;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
  createdAt?: string;
  updatedAt?: string;
  project?: Project;
  assignee?: User;
  reporter?: User;
  creator?: User;
  workItem?: WorkItem;
} | null;

export interface UpdateTestCaseHistoryInput {
  id: string;
  testCaseId: string;
  changeType: UpdateTestCaseHistoryInputChangeType;
  changedBy: string;
  /** @nullable */
  changedFields: UpdateTestCaseHistoryInputChangedFields;
  /** @nullable */
  metadata: UpdateTestCaseHistoryInputMetadata;
  /**
   * @maxLength 500
   * @nullable
   */
  summary: string | null;
  createdAt: string;
  user: User;
  /** @nullable */
  testCase: UpdateTestCaseHistoryInputTestCase;
}

export type TestCaseWorkItemLinkLinkType =
  (typeof TestCaseWorkItemLinkLinkType)[keyof typeof TestCaseWorkItemLinkLinkType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const TestCaseWorkItemLinkLinkType = {
  tests: 'tests',
  tested_by: 'tested_by',
  validates: 'validates',
  verifies: 'verifies',
  relates_to: 'relates_to',
} as const;

export interface TestCaseWorkItemLink {
  id: string;
  testCaseId: string;
  workItemId: string;
  linkType: TestCaseWorkItemLinkLinkType;
  createdById: string;
  createdAt: string;
  createdBy: User;
}

export type CreateTestCaseWorkItemLinkInputLinkType =
  (typeof CreateTestCaseWorkItemLinkInputLinkType)[keyof typeof CreateTestCaseWorkItemLinkInputLinkType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CreateTestCaseWorkItemLinkInputLinkType = {
  tests: 'tests',
  tested_by: 'tested_by',
  validates: 'validates',
  verifies: 'verifies',
  relates_to: 'relates_to',
} as const;

export interface CreateTestCaseWorkItemLinkInput {
  testCaseId: string;
  workItemId: string;
  linkType: CreateTestCaseWorkItemLinkInputLinkType;
  createdById: string;
}

export type UpdateTestCaseWorkItemLinkInputLinkType =
  (typeof UpdateTestCaseWorkItemLinkInputLinkType)[keyof typeof UpdateTestCaseWorkItemLinkInputLinkType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UpdateTestCaseWorkItemLinkInputLinkType = {
  tests: 'tests',
  tested_by: 'tested_by',
  validates: 'validates',
  verifies: 'verifies',
  relates_to: 'relates_to',
} as const;

export interface UpdateTestCaseWorkItemLinkInput {
  testCaseId?: string;
  workItemId?: string;
  linkType?: UpdateTestCaseWorkItemLinkInputLinkType;
  createdById?: string;
}

export type TestCaseStepsItem = {
  step: string;
  expectedResult: string;
};

export type TestCaseEstimate = { [key: string]: unknown };

export interface TestCase {
  id: string;
  /** @maxLength 500 */
  title: string;
  /** @nullable */
  description: string | null;
  projectId: string;
  /** @nullable */
  assigneeId: string | null;
  /** @nullable */
  reporterId: string | null;
  /** @nullable */
  workItemId: string | null;
  /** @nullable */
  copiedFromId: string | null;
  /** @nullable */
  preconditions: string | null;
  steps?: TestCaseStepsItem[];
  tags?: string[];
  estimate?: TestCaseEstimate;
  order: number;
  isActive: boolean;
  /** @nullable */
  createdBy: string | null;
  createdAt: string;
  updatedAt: string;
  project?: Project;
  assignee?: User;
  reporter?: User;
  creator?: User;
  workItem?: WorkItem;
}

export type CreateTestCaseInputStepsItem = {
  step: string;
  expectedResult: string;
};

export type CreateTestCaseInputEstimate = { [key: string]: unknown };

export interface CreateTestCaseInput {
  /**
   * @minLength 1
   * @maxLength 500
   */
  title: string;
  /** @nullable */
  description?: string | null;
  projectId: string;
  /** @nullable */
  assigneeId?: string | null;
  /** @nullable */
  reporterId?: string | null;
  /** @nullable */
  workItemId?: string | null;
  /** @nullable */
  copiedFromId?: string | null;
  /** @nullable */
  preconditions?: string | null;
  steps?: CreateTestCaseInputStepsItem[];
  tags?: string[];
  estimate?: CreateTestCaseInputEstimate;
  order?: number;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
  /** @nullable */
  priorityId?: string | null;
  /** @nullable */
  statusId?: string | null;
}

export type UpdateTestCaseInputStepsItem = {
  step: string;
  expectedResult: string;
};

export type UpdateTestCaseInputEstimate = { [key: string]: unknown };

export interface UpdateTestCaseInput {
  /**
   * @minLength 1
   * @maxLength 500
   */
  title?: string;
  /** @nullable */
  description?: string | null;
  projectId?: string;
  /** @nullable */
  assigneeId?: string | null;
  /** @nullable */
  reporterId?: string | null;
  /** @nullable */
  workItemId?: string | null;
  /** @nullable */
  copiedFromId?: string | null;
  /** @nullable */
  preconditions?: string | null;
  steps?: UpdateTestCaseInputStepsItem[];
  tags?: string[];
  estimate?: UpdateTestCaseInputEstimate;
  order?: number;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
  /** @nullable */
  priorityId?: string | null;
  /** @nullable */
  statusId?: string | null;
}

export interface TestPlanComment {
  id: string;
  testPlanId: string;
  content: string;
  authorId: string;
  /** @nullable */
  parentId: string | null;
  isEdited: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  author: User;
}

export interface CreateTestPlanCommentInput {
  testPlanId: string;
  /** @minLength 1 */
  content: string;
  authorId: string;
  /** @nullable */
  parentId?: string | null;
  isEdited?: boolean;
}

export interface UpdateTestPlanCommentInput {
  /** @minLength 1 */
  content?: string;
  isEdited?: boolean;
}

export type TestPlanStatus = (typeof TestPlanStatus)[keyof typeof TestPlanStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const TestPlanStatus = {
  draft: 'draft',
  active: 'active',
  archived: 'archived',
} as const;

export interface TestPlan {
  id: string;
  /** @maxLength 255 */
  name: string;
  /**
   * @maxLength 1000
   * @nullable
   */
  description: string | null;
  projectId: string;
  status: TestPlanStatus;
  createdById: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  project: Project;
  createdBy: User;
}

export type CreateTestPlanInputStatus =
  (typeof CreateTestPlanInputStatus)[keyof typeof CreateTestPlanInputStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CreateTestPlanInputStatus = {
  draft: 'draft',
  active: 'active',
  archived: 'archived',
} as const;

export interface CreateTestPlanInput {
  /**
   * @minLength 1
   * @maxLength 255
   */
  name: string;
  /**
   * @maxLength 1000
   * @nullable
   */
  description?: string | null;
  projectId: string;
  status?: CreateTestPlanInputStatus;
  createdById: string;
  isActive?: boolean;
}

export type UpdateTestPlanInputStatus =
  (typeof UpdateTestPlanInputStatus)[keyof typeof UpdateTestPlanInputStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UpdateTestPlanInputStatus = {
  draft: 'draft',
  active: 'active',
  archived: 'archived',
} as const;

export interface UpdateTestPlanInput {
  /**
   * @minLength 1
   * @maxLength 255
   */
  name?: string;
  /**
   * @maxLength 1000
   * @nullable
   */
  description?: string | null;
  projectId?: string;
  status?: UpdateTestPlanInputStatus;
  createdById?: string;
  isActive?: boolean;
}

export interface TestSuiteComment {
  id: string;
  testSuiteId: string;
  content: string;
  authorId: string;
  /** @nullable */
  parentId: string | null;
  isEdited: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  author: User;
}

export interface CreateTestSuiteCommentInput {
  testSuiteId: string;
  /** @minLength 1 */
  content: string;
  authorId: string;
  /** @nullable */
  parentId?: string | null;
  isEdited?: boolean;
}

export interface UpdateTestSuiteCommentInput {
  /** @minLength 1 */
  content?: string;
  isEdited?: boolean;
}

export type TestSuiteType = (typeof TestSuiteType)[keyof typeof TestSuiteType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const TestSuiteType = {
  static: 'static',
  requirement: 'requirement',
  query: 'query',
} as const;

export type TestSuiteTestPlanStatus =
  (typeof TestSuiteTestPlanStatus)[keyof typeof TestSuiteTestPlanStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const TestSuiteTestPlanStatus = {
  draft: 'draft',
  active: 'active',
  archived: 'archived',
} as const;

/**
 * @nullable
 */
export type TestSuiteTestPlan = {
  id?: string;
  /** @maxLength 255 */
  name?: string;
  /**
   * @maxLength 1000
   * @nullable
   */
  description?: string | null;
  projectId?: string;
  status?: TestSuiteTestPlanStatus;
  createdById?: string;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
  project?: Project;
  createdBy?: User;
} | null;

export interface TestSuite {
  id: string;
  /** @maxLength 255 */
  name: string;
  /**
   * @maxLength 1000
   * @nullable
   */
  description: string | null;
  testPlanId: string;
  type: TestSuiteType;
  /**
   * @maxLength 1000
   * @nullable
   */
  requirementQuery: string | null;
  /** @nullable */
  parentSuiteId: string | null;
  /** @nullable */
  copiedFromId: string | null;
  order: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  /** @nullable */
  testPlan: TestSuiteTestPlan;
}

export type CreateTestSuiteInputType =
  (typeof CreateTestSuiteInputType)[keyof typeof CreateTestSuiteInputType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CreateTestSuiteInputType = {
  static: 'static',
  requirement: 'requirement',
  query: 'query',
} as const;

export interface CreateTestSuiteInput {
  /**
   * @minLength 1
   * @maxLength 255
   */
  name: string;
  /**
   * @maxLength 1000
   * @nullable
   */
  description?: string | null;
  testPlanId: string;
  type?: CreateTestSuiteInputType;
  /**
   * @maxLength 1000
   * @nullable
   */
  requirementQuery?: string | null;
  /** @nullable */
  parentSuiteId?: string | null;
  /** @nullable */
  copiedFromId?: string | null;
  order?: number;
  isActive?: boolean;
  includeTestCases?: boolean;
  sourceSuiteId?: string;
  selectedTestCaseIds?: string[];
  linkTestCases?: boolean;
}

export type UpdateTestSuiteInputType =
  (typeof UpdateTestSuiteInputType)[keyof typeof UpdateTestSuiteInputType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UpdateTestSuiteInputType = {
  static: 'static',
  requirement: 'requirement',
  query: 'query',
} as const;

export interface UpdateTestSuiteInput {
  /**
   * @minLength 1
   * @maxLength 255
   */
  name?: string;
  /**
   * @maxLength 1000
   * @nullable
   */
  description?: string | null;
  testPlanId?: string;
  type?: UpdateTestSuiteInputType;
  /**
   * @maxLength 1000
   * @nullable
   */
  requirementQuery?: string | null;
  /** @nullable */
  parentSuiteId?: string | null;
  /** @nullable */
  copiedFromId?: string | null;
  order?: number;
  isActive?: boolean;
  includeTestCases?: boolean;
  sourceSuiteId?: string;
  selectedTestCaseIds?: string[];
  linkTestCases?: boolean;
}

export interface CreateUserInput {
  /** @maxLength 255 */
  email: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  firstName: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  lastName: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  displayName: string;
  /**
   * @minLength 8
   * @maxLength 255
   */
  password: string;
  isActive?: boolean;
  isStaff?: boolean;
  isEmailVerified?: boolean;
  /** @nullable */
  lastLogin?: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  avatarUrl?: string | null;
  /** @nullable */
  bio?: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  jobTitle?: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  department?: string | null;
  /**
   * @minLength 10
   * @maxLength 50
   * @nullable
   */
  phone?: string | null;
  /** @maxLength 50 */
  timezone?: string;
  /** @maxLength 10 */
  language?: string;
  /** @maxLength 20 */
  dateFormat?: string;
  /** @maxLength 10 */
  timeFormat?: string;
  /**
   * @maxLength 512
   * @nullable
   */
  passwordResetToken?: string | null;
  /** @nullable */
  passwordResetExpires?: string | null;
  /**
   * @maxLength 512
   * @nullable
   */
  emailVerificationToken?: string | null;
  loginAttempts?: number;
  /** @nullable */
  lockedUntil?: string | null;
}

export interface UpdateUserInput {
  /** @maxLength 255 */
  email?: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  firstName?: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  lastName?: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  displayName?: string;
  /**
   * @minLength 8
   * @maxLength 255
   */
  password?: string;
  isActive?: boolean;
  isStaff?: boolean;
  isEmailVerified?: boolean;
  /** @nullable */
  lastLogin?: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  avatarUrl?: string | null;
  /** @nullable */
  bio?: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  jobTitle?: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  department?: string | null;
  /**
   * @minLength 10
   * @maxLength 50
   * @nullable
   */
  phone?: string | null;
  /** @maxLength 50 */
  timezone?: string;
  /** @maxLength 10 */
  language?: string;
  /** @maxLength 20 */
  dateFormat?: string;
  /** @maxLength 10 */
  timeFormat?: string;
  /**
   * @maxLength 512
   * @nullable
   */
  passwordResetToken?: string | null;
  /** @nullable */
  passwordResetExpires?: string | null;
  /**
   * @maxLength 512
   * @nullable
   */
  emailVerificationToken?: string | null;
  loginAttempts?: number;
  /** @nullable */
  lockedUntil?: string | null;
}

export interface WorkItemComment {
  id: string;
  workItemId: string;
  content: string;
  authorId: string;
  /** @nullable */
  parentId: string | null;
  isEdited: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  author: User;
}

export interface CreateWorkItemCommentInput {
  workItemId: string;
  /** @minLength 1 */
  content: string;
  authorId: string;
  /** @nullable */
  parentId?: string | null;
  isEdited?: boolean;
}

export interface UpdateWorkItemCommentInput {
  /** @minLength 1 */
  content?: string;
  isEdited?: boolean;
}

export type WorkItemFieldFieldType =
  (typeof WorkItemFieldFieldType)[keyof typeof WorkItemFieldFieldType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const WorkItemFieldFieldType = {
  text: 'text',
  number: 'number',
  date: 'date',
  boolean: 'boolean',
  select: 'select',
  multi_select: 'multi_select',
  user: 'user',
  json: 'json',
} as const;

/**
 * @nullable
 */
export type WorkItemFieldDefaultValue = { [key: string]: unknown } | null;

export type WorkItemFieldOptions = { [key: string]: unknown };

export interface WorkItemField {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  fieldType: WorkItemFieldFieldType;
  isRequired: boolean;
  /** @nullable */
  defaultValue?: WorkItemFieldDefaultValue;
  options?: WorkItemFieldOptions;
  workItemTypeId: string;
  /** @nullable */
  organizationId: string | null;
  order: number;
  isFilterable: boolean;
  isDisplayedInGrid: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  workItemType: WorkItemType;
  organization: Organization;
}

export type CreateWorkItemFieldInputFieldType =
  (typeof CreateWorkItemFieldInputFieldType)[keyof typeof CreateWorkItemFieldInputFieldType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CreateWorkItemFieldInputFieldType = {
  text: 'text',
  number: 'number',
  date: 'date',
  boolean: 'boolean',
  select: 'select',
  multi_select: 'multi_select',
  user: 'user',
  json: 'json',
} as const;

/**
 * @nullable
 */
export type CreateWorkItemFieldInputDefaultValue = {
  [key: string]: unknown;
} | null;

export type CreateWorkItemFieldInputOptions = { [key: string]: unknown };

export interface CreateWorkItemFieldInput {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name: string;
  /** @nullable */
  description?: string | null;
  fieldType: CreateWorkItemFieldInputFieldType;
  isRequired?: boolean;
  /** @nullable */
  defaultValue?: CreateWorkItemFieldInputDefaultValue;
  options?: CreateWorkItemFieldInputOptions;
  workItemTypeId: string;
  /** @nullable */
  organizationId?: string | null;
  order?: number;
  isFilterable?: boolean;
  isDisplayedInGrid?: boolean;
  isActive?: boolean;
}

export type UpdateWorkItemFieldInputFieldType =
  (typeof UpdateWorkItemFieldInputFieldType)[keyof typeof UpdateWorkItemFieldInputFieldType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UpdateWorkItemFieldInputFieldType = {
  text: 'text',
  number: 'number',
  date: 'date',
  boolean: 'boolean',
  select: 'select',
  multi_select: 'multi_select',
  user: 'user',
  json: 'json',
} as const;

/**
 * @nullable
 */
export type UpdateWorkItemFieldInputDefaultValue = {
  [key: string]: unknown;
} | null;

export type UpdateWorkItemFieldInputOptions = { [key: string]: unknown };

export interface UpdateWorkItemFieldInput {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name?: string;
  /** @nullable */
  description?: string | null;
  fieldType?: UpdateWorkItemFieldInputFieldType;
  isRequired?: boolean;
  /** @nullable */
  defaultValue?: UpdateWorkItemFieldInputDefaultValue;
  options?: UpdateWorkItemFieldInputOptions;
  workItemTypeId?: string;
  /** @nullable */
  organizationId?: string | null;
  order?: number;
  isFilterable?: boolean;
  isDisplayedInGrid?: boolean;
  isActive?: boolean;
}

export type WorkItemHistoryChangeType =
  (typeof WorkItemHistoryChangeType)[keyof typeof WorkItemHistoryChangeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const WorkItemHistoryChangeType = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  RESTORE: 'RESTORE',
  COMMENT_ADDED: 'COMMENT_ADDED',
  COMMENT_UPDATED: 'COMMENT_UPDATED',
  COMMENT_DELETED: 'COMMENT_DELETED',
  ATTACHMENT_ADDED: 'ATTACHMENT_ADDED',
  ATTACHMENT_DELETED: 'ATTACHMENT_DELETED',
} as const;

export type WorkItemHistoryChangedFieldsOldValue = string | number | boolean | 'null' | null;

export type WorkItemHistoryChangedFieldsNewValue = string | number | boolean | 'null' | null;

/**
 * @nullable
 */
export type WorkItemHistoryChangedFields = {
  [key: string]: {
    oldValue: WorkItemHistoryChangedFieldsOldValue;
    newValue: WorkItemHistoryChangedFieldsNewValue;
  };
} | null;

export type WorkItemHistoryMetadataComment = {
  id: string;
  content: string;
};

export type WorkItemHistoryMetadataAttachment = {
  id: string;
  filename: string;
  size?: number;
  mimeType?: string;
};

/**
 * @nullable
 */
export type WorkItemHistoryMetadata = {
  comment?: WorkItemHistoryMetadataComment;
  attachment?: WorkItemHistoryMetadataAttachment;
  [key: string]: unknown;
} | null;

export interface WorkItemHistory {
  id: string;
  workItemId: string;
  changeType: WorkItemHistoryChangeType;
  changedBy: string;
  /** @nullable */
  changedFields: WorkItemHistoryChangedFields;
  /** @nullable */
  metadata: WorkItemHistoryMetadata;
  /**
   * @maxLength 500
   * @nullable
   */
  summary: string | null;
  createdAt: string;
  user: User;
  workItem: WorkItem;
}

export type CreateWorkItemHistoryInputChangeType =
  (typeof CreateWorkItemHistoryInputChangeType)[keyof typeof CreateWorkItemHistoryInputChangeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CreateWorkItemHistoryInputChangeType = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  RESTORE: 'RESTORE',
  COMMENT_ADDED: 'COMMENT_ADDED',
  COMMENT_UPDATED: 'COMMENT_UPDATED',
  COMMENT_DELETED: 'COMMENT_DELETED',
  ATTACHMENT_ADDED: 'ATTACHMENT_ADDED',
  ATTACHMENT_DELETED: 'ATTACHMENT_DELETED',
} as const;

export type CreateWorkItemHistoryInputChangedFieldsOldValue =
  | string
  | number
  | boolean
  | 'null'
  | null;

export type CreateWorkItemHistoryInputChangedFieldsNewValue =
  | string
  | number
  | boolean
  | 'null'
  | null;

/**
 * @nullable
 */
export type CreateWorkItemHistoryInputChangedFields = {
  [key: string]: {
    oldValue: CreateWorkItemHistoryInputChangedFieldsOldValue;
    newValue: CreateWorkItemHistoryInputChangedFieldsNewValue;
  };
} | null;

export type CreateWorkItemHistoryInputMetadataComment = {
  id: string;
  content: string;
};

export type CreateWorkItemHistoryInputMetadataAttachment = {
  id: string;
  filename: string;
  size?: number;
  mimeType?: string;
};

/**
 * @nullable
 */
export type CreateWorkItemHistoryInputMetadata = {
  comment?: CreateWorkItemHistoryInputMetadataComment;
  attachment?: CreateWorkItemHistoryInputMetadataAttachment;
  [key: string]: unknown;
} | null;

export interface CreateWorkItemHistoryInput {
  workItemId: string;
  changeType: CreateWorkItemHistoryInputChangeType;
  changedBy: string;
  /** @nullable */
  changedFields?: CreateWorkItemHistoryInputChangedFields;
  /** @nullable */
  metadata?: CreateWorkItemHistoryInputMetadata;
  /**
   * @maxLength 500
   * @nullable
   */
  summary?: string | null;
}

export type UpdateWorkItemHistoryInputChangeType =
  (typeof UpdateWorkItemHistoryInputChangeType)[keyof typeof UpdateWorkItemHistoryInputChangeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UpdateWorkItemHistoryInputChangeType = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  RESTORE: 'RESTORE',
  COMMENT_ADDED: 'COMMENT_ADDED',
  COMMENT_UPDATED: 'COMMENT_UPDATED',
  COMMENT_DELETED: 'COMMENT_DELETED',
  ATTACHMENT_ADDED: 'ATTACHMENT_ADDED',
  ATTACHMENT_DELETED: 'ATTACHMENT_DELETED',
} as const;

export type UpdateWorkItemHistoryInputChangedFieldsOldValue =
  | string
  | number
  | boolean
  | 'null'
  | null;

export type UpdateWorkItemHistoryInputChangedFieldsNewValue =
  | string
  | number
  | boolean
  | 'null'
  | null;

/**
 * @nullable
 */
export type UpdateWorkItemHistoryInputChangedFields = {
  [key: string]: {
    oldValue: UpdateWorkItemHistoryInputChangedFieldsOldValue;
    newValue: UpdateWorkItemHistoryInputChangedFieldsNewValue;
  };
} | null;

export type UpdateWorkItemHistoryInputMetadataComment = {
  id: string;
  content: string;
};

export type UpdateWorkItemHistoryInputMetadataAttachment = {
  id: string;
  filename: string;
  size?: number;
  mimeType?: string;
};

/**
 * @nullable
 */
export type UpdateWorkItemHistoryInputMetadata = {
  comment?: UpdateWorkItemHistoryInputMetadataComment;
  attachment?: UpdateWorkItemHistoryInputMetadataAttachment;
  [key: string]: unknown;
} | null;

export interface UpdateWorkItemHistoryInput {
  workItemId?: string;
  changeType?: UpdateWorkItemHistoryInputChangeType;
  changedBy?: string;
  /** @nullable */
  changedFields?: UpdateWorkItemHistoryInputChangedFields;
  /** @nullable */
  metadata?: UpdateWorkItemHistoryInputMetadata;
  /**
   * @maxLength 500
   * @nullable
   */
  summary?: string | null;
}

export type WorkItemLinkLinkType = (typeof WorkItemLinkLinkType)[keyof typeof WorkItemLinkLinkType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const WorkItemLinkLinkType = {
  is_caused_by: 'is_caused_by',
  blocked_by: 'blocked_by',
  blocks: 'blocks',
  relates_to: 'relates_to',
  is_child_of: 'is_child_of',
  is_duplicated_by: 'is_duplicated_by',
  duplicates: 'duplicates',
} as const;

export interface WorkItemLink {
  id: string;
  sourceWorkItemId: string;
  targetWorkItemId: string;
  linkType: WorkItemLinkLinkType;
  /**
   * @maxLength 500
   * @nullable
   */
  title: string | null;
  /** @nullable */
  description: string | null;
  createdById: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: User;
  sourceWorkItem: WorkItem;
  targetWorkItem: WorkItem;
}

export type CreateWorkItemLinkInputLinkType =
  (typeof CreateWorkItemLinkInputLinkType)[keyof typeof CreateWorkItemLinkInputLinkType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CreateWorkItemLinkInputLinkType = {
  is_caused_by: 'is_caused_by',
  blocked_by: 'blocked_by',
  blocks: 'blocks',
  relates_to: 'relates_to',
  is_child_of: 'is_child_of',
  is_duplicated_by: 'is_duplicated_by',
  duplicates: 'duplicates',
} as const;

export interface CreateWorkItemLinkInput {
  sourceWorkItemId: string;
  targetWorkItemId: string;
  linkType: CreateWorkItemLinkInputLinkType;
  /**
   * @maxLength 500
   * @nullable
   */
  title?: string | null;
  /** @nullable */
  description?: string | null;
  createdById: string;
  isActive?: boolean;
}

export type UpdateWorkItemLinkInputLinkType =
  (typeof UpdateWorkItemLinkInputLinkType)[keyof typeof UpdateWorkItemLinkInputLinkType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UpdateWorkItemLinkInputLinkType = {
  is_caused_by: 'is_caused_by',
  blocked_by: 'blocked_by',
  blocks: 'blocks',
  relates_to: 'relates_to',
  is_child_of: 'is_child_of',
  is_duplicated_by: 'is_duplicated_by',
  duplicates: 'duplicates',
} as const;

export interface UpdateWorkItemLinkInput {
  sourceWorkItemId?: string;
  targetWorkItemId?: string;
  linkType?: UpdateWorkItemLinkInputLinkType;
  /**
   * @maxLength 500
   * @nullable
   */
  title?: string | null;
  /** @nullable */
  description?: string | null;
  createdById?: string;
  isActive?: boolean;
}

export type WorkItemTypeData = { [key: string]: unknown };

export interface WorkItemType {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  organizationId: string | null;
  isSystem: boolean;
  /**
   * @maxLength 255
   * @nullable
   */
  icon: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  data?: WorkItemTypeData;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  organization: Organization;
}

export type CreateWorkItemTypeInputData = { [key: string]: unknown };

export interface CreateWorkItemTypeInput {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  organizationId?: string | null;
  isSystem?: boolean;
  /**
   * @maxLength 255
   * @nullable
   */
  icon?: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  data?: CreateWorkItemTypeInputData;
  isActive?: boolean;
}

export type UpdateWorkItemTypeInputData = { [key: string]: unknown };

export interface UpdateWorkItemTypeInput {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name?: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  organizationId?: string | null;
  isSystem?: boolean;
  /**
   * @maxLength 255
   * @nullable
   */
  icon?: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  data?: UpdateWorkItemTypeInputData;
  isActive?: boolean;
}

export type WorkItemTags = { [key: string]: unknown };

export type WorkItemEstimate = { [key: string]: unknown };

export type WorkItemDates = { [key: string]: unknown };

export type WorkItemLinks = { [key: string]: unknown };

export interface WorkItem {
  id: string;
  projectId: string;
  typeId: string;
  /** @maxLength 500 */
  title: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  assigneeId: string | null;
  /** @nullable */
  reporterId: string | null;
  /** @nullable */
  sprintId: string | null;
  /** @nullable */
  initialSprintId: string | null;
  /** @nullable */
  parentId: string | null;
  statusId: string;
  priorityId: string;
  ticketNumber: number;
  /** @maxLength 20 */
  ticketId: string;
  tags?: WorkItemTags;
  estimate?: WorkItemEstimate;
  dates?: WorkItemDates;
  links?: WorkItemLinks;
  order: number;
  isActive: boolean;
  /** @nullable */
  createdBy: string | null;
  createdAt: string;
  updatedAt: string;
  /** @nullable */
  updatedBy: string | null;
  project: Project;
  type: WorkItemType;
  assignee: User;
  reporter: User;
  sprint: Sprint;
  initialSprint: Sprint;
  status: Status;
  priority: Priority;
  creator: User;
  updater: User;
}

export type CreateWorkItemInputTags = { [key: string]: unknown };

export type CreateWorkItemInputEstimate = { [key: string]: unknown };

export type CreateWorkItemInputDates = { [key: string]: unknown };

export type CreateWorkItemInputLinks = { [key: string]: unknown };

export interface CreateWorkItemInput {
  projectId: string;
  typeId: string;
  /**
   * @minLength 1
   * @maxLength 500
   */
  title: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  assigneeId?: string | null;
  /** @nullable */
  reporterId?: string | null;
  /** @nullable */
  sprintId?: string | null;
  /** @nullable */
  initialSprintId?: string | null;
  /** @nullable */
  parentId?: string | null;
  statusId: string;
  priorityId: string;
  tags?: CreateWorkItemInputTags;
  estimate?: CreateWorkItemInputEstimate;
  dates?: CreateWorkItemInputDates;
  links?: CreateWorkItemInputLinks;
  order?: number;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
  /** @nullable */
  updatedBy?: string | null;
}

export type UpdateWorkItemInputTags = { [key: string]: unknown };

export type UpdateWorkItemInputEstimate = { [key: string]: unknown };

export type UpdateWorkItemInputDates = { [key: string]: unknown };

export type UpdateWorkItemInputLinks = { [key: string]: unknown };

export interface UpdateWorkItemInput {
  projectId?: string;
  typeId?: string;
  /**
   * @minLength 1
   * @maxLength 500
   */
  title?: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  assigneeId?: string | null;
  /** @nullable */
  reporterId?: string | null;
  /** @nullable */
  sprintId?: string | null;
  /** @nullable */
  initialSprintId?: string | null;
  /** @nullable */
  parentId?: string | null;
  statusId?: string;
  priorityId?: string;
  tags?: UpdateWorkItemInputTags;
  estimate?: UpdateWorkItemInputEstimate;
  dates?: UpdateWorkItemInputDates;
  links?: UpdateWorkItemInputLinks;
  order?: number;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
  /** @nullable */
  updatedBy?: string | null;
}

export type WorkflowStatusProperties = { [key: string]: unknown };

export interface WorkflowStatus {
  id: string;
  workflowId: string;
  statusId: string;
  isInitial: boolean;
  isFinal: boolean;
  positionX: number;
  positionY: number;
  properties?: WorkflowStatusProperties;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  workflow: Workflow;
  status: Status;
}

export type CreateWorkflowStatusInputProperties = { [key: string]: unknown };

export interface CreateWorkflowStatusInput {
  workflowId: string;
  statusId: string;
  isInitial?: boolean;
  isFinal?: boolean;
  positionX?: number;
  positionY?: number;
  properties?: CreateWorkflowStatusInputProperties;
  isActive?: boolean;
}

export type UpdateWorkflowStatusInputProperties = { [key: string]: unknown };

export interface UpdateWorkflowStatusInput {
  workflowId?: string;
  statusId?: string;
  isInitial?: boolean;
  isFinal?: boolean;
  positionX?: number;
  positionY?: number;
  properties?: UpdateWorkflowStatusInputProperties;
  isActive?: boolean;
}

export interface WorkflowTransition {
  id: string;
  workflowId: string;
  /** @maxLength 255 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  fromStatusId: string | null;
  toStatusId: string;
  isInitial: boolean;
  isGlobal: boolean;
  order: number;
  /**
   * @maxLength 100
   * @nullable
   */
  buttonText: string | null;
  /** @nullable */
  confirmationMessage: string | null;
  /** @nullable */
  screenId: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  workflow: Workflow;
  fromStatus: WorkflowStatus;
  toStatus: WorkflowStatus;
}

export interface CreateWorkflowTransitionInput {
  workflowId: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  fromStatusId?: string | null;
  toStatusId: string;
  isInitial?: boolean;
  isGlobal?: boolean;
  order?: number;
  /**
   * @maxLength 100
   * @nullable
   */
  buttonText?: string | null;
  /** @nullable */
  confirmationMessage?: string | null;
  /** @nullable */
  screenId?: string | null;
  isActive?: boolean;
}

export interface UpdateWorkflowTransitionInput {
  workflowId?: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name?: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  fromStatusId?: string | null;
  toStatusId?: string;
  isInitial?: boolean;
  isGlobal?: boolean;
  order?: number;
  /**
   * @maxLength 100
   * @nullable
   */
  buttonText?: string | null;
  /** @nullable */
  confirmationMessage?: string | null;
  /** @nullable */
  screenId?: string | null;
  isActive?: boolean;
}

export type WorkflowSettings = { [key: string]: unknown };

export type WorkflowStatusProperty =
  (typeof WorkflowStatusProperty)[keyof typeof WorkflowStatusProperty];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const WorkflowStatusProperty = {
  draft: 'draft',
  active: 'active',
  archived: 'archived',
} as const;

export interface Workflow {
  id: string;
  /** @nullable */
  organizationId: string | null;
  /** @maxLength 255 */
  name: string;
  /** @nullable */
  description: string | null;
  isSystem: boolean;
  isDefault: boolean;
  /** @nullable */
  initialStatusId: string | null;
  version: number;
  settings?: WorkflowSettings;
  status: WorkflowStatusProperty;
  isActive: boolean;
  /** @nullable */
  createdBy: string | null;
  createdAt: string;
  updatedAt: string;
  organization: Organization;
  creator: User;
}

export type CreateWorkflowInputSettings = { [key: string]: unknown };

export type CreateWorkflowInputStatus =
  (typeof CreateWorkflowInputStatus)[keyof typeof CreateWorkflowInputStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CreateWorkflowInputStatus = {
  draft: 'draft',
  active: 'active',
  archived: 'archived',
} as const;

export interface CreateWorkflowInput {
  /** @nullable */
  organizationId?: string | null;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name: string;
  /** @nullable */
  description?: string | null;
  isSystem?: boolean;
  isDefault?: boolean;
  /** @nullable */
  initialStatusId?: string | null;
  version?: number;
  settings?: CreateWorkflowInputSettings;
  status?: CreateWorkflowInputStatus;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
}

export type UpdateWorkflowInputSettings = { [key: string]: unknown };

export type UpdateWorkflowInputStatus =
  (typeof UpdateWorkflowInputStatus)[keyof typeof UpdateWorkflowInputStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UpdateWorkflowInputStatus = {
  draft: 'draft',
  active: 'active',
  archived: 'archived',
} as const;

export interface UpdateWorkflowInput {
  /** @nullable */
  organizationId?: string | null;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name?: string;
  /** @nullable */
  description?: string | null;
  isSystem?: boolean;
  isDefault?: boolean;
  /** @nullable */
  initialStatusId?: string | null;
  version?: number;
  settings?: UpdateWorkflowInputSettings;
  status?: UpdateWorkflowInputStatus;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
}

export type WorkspaceSettings = { [key: string]: unknown };

export type WorkspaceVisibility = (typeof WorkspaceVisibility)[keyof typeof WorkspaceVisibility];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const WorkspaceVisibility = {
  public: 'public',
  private: 'private',
} as const;

export type WorkspaceStatus = (typeof WorkspaceStatus)[keyof typeof WorkspaceStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const WorkspaceStatus = {
  active: 'active',
  archived: 'archived',
} as const;

export interface Workspace {
  id: string;
  organizationId: string;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  slug: string;
  /** @nullable */
  description: string | null;
  /**
   * @maxLength 500
   * @nullable
   */
  icon: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  settings?: WorkspaceSettings;
  /** @nullable */
  defaultProjectId: string | null;
  visibility: WorkspaceVisibility;
  status: WorkspaceStatus;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  /** @nullable */
  createdBy: string | null;
  organization: Organization;
  creator: User;
}

export type CreateWorkspaceInputSettings = { [key: string]: unknown };

export type CreateWorkspaceInputVisibility =
  (typeof CreateWorkspaceInputVisibility)[keyof typeof CreateWorkspaceInputVisibility];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CreateWorkspaceInputVisibility = {
  public: 'public',
  private: 'private',
} as const;

export type CreateWorkspaceInputStatus =
  (typeof CreateWorkspaceInputStatus)[keyof typeof CreateWorkspaceInputStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CreateWorkspaceInputStatus = {
  active: 'active',
  archived: 'archived',
} as const;

export interface CreateWorkspaceInput {
  organizationId: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  slug: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  icon?: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  settings?: CreateWorkspaceInputSettings;
  /** @nullable */
  defaultProjectId?: string | null;
  visibility?: CreateWorkspaceInputVisibility;
  status?: CreateWorkspaceInputStatus;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
}

export type UpdateWorkspaceInputSettings = { [key: string]: unknown };

export type UpdateWorkspaceInputVisibility =
  (typeof UpdateWorkspaceInputVisibility)[keyof typeof UpdateWorkspaceInputVisibility];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UpdateWorkspaceInputVisibility = {
  public: 'public',
  private: 'private',
} as const;

export type UpdateWorkspaceInputStatus =
  (typeof UpdateWorkspaceInputStatus)[keyof typeof UpdateWorkspaceInputStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UpdateWorkspaceInputStatus = {
  active: 'active',
  archived: 'archived',
} as const;

export interface UpdateWorkspaceInput {
  organizationId?: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name?: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  slug?: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  icon?: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  settings?: UpdateWorkspaceInputSettings;
  /** @nullable */
  defaultProjectId?: string | null;
  visibility?: UpdateWorkspaceInputVisibility;
  status?: UpdateWorkspaceInputStatus;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
}

export type PostApiV1AttachmentsUploadParams = {
  entityType: PostApiV1AttachmentsUploadEntityType;
  entityId: string;
};

export type PostApiV1AttachmentsUploadEntityType =
  (typeof PostApiV1AttachmentsUploadEntityType)[keyof typeof PostApiV1AttachmentsUploadEntityType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1AttachmentsUploadEntityType = {
  work_item: 'work_item',
  project: 'project',
  workspace: 'workspace',
  organization: 'organization',
  comment: 'comment',
  test_case: 'test_case',
  test_plan: 'test_plan',
  test_suite: 'test_suite',
  test_run: 'test_run',
  user: 'user',
} as const;

export type PostApiV1AttachmentsUpload200 = {
  id: string;
  fileName: string;
  url: string;
  size: number;
  fileType: string;
};

export type GetApiV1AttachmentsIdSignedUrlParams = {
  expiry?: number;
};

export type GetApiV1AttachmentsIdSignedUrl200 = {
  url: string;
  expiresIn: number;
};

export type GetApiV1AttachmentsByEntityParams = {
  entityType: GetApiV1AttachmentsByEntityEntityType;
  entityId: string;
};

export type GetApiV1AttachmentsByEntityEntityType =
  (typeof GetApiV1AttachmentsByEntityEntityType)[keyof typeof GetApiV1AttachmentsByEntityEntityType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiV1AttachmentsByEntityEntityType = {
  work_item: 'work_item',
  project: 'project',
  workspace: 'workspace',
  organization: 'organization',
  comment: 'comment',
  test_case: 'test_case',
  test_plan: 'test_plan',
  test_suite: 'test_suite',
  test_run: 'test_run',
  user: 'user',
} as const;

export type GetApiV1AttachmentsByEntity200ItemEntityType =
  (typeof GetApiV1AttachmentsByEntity200ItemEntityType)[keyof typeof GetApiV1AttachmentsByEntity200ItemEntityType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiV1AttachmentsByEntity200ItemEntityType = {
  work_item: 'work_item',
  project: 'project',
  workspace: 'workspace',
  organization: 'organization',
  comment: 'comment',
  test_case: 'test_case',
  test_plan: 'test_plan',
  test_suite: 'test_suite',
  test_run: 'test_run',
  user: 'user',
} as const;

export type GetApiV1AttachmentsByEntity200Item = {
  id: string;
  entityType: GetApiV1AttachmentsByEntity200ItemEntityType;
  entityId: string;
  /** @maxLength 255 */
  fileName: string;
  /** @maxLength 500 */
  url: string;
  /**
   * @maxLength 100
   * @nullable
   */
  fileType: string | null;
  /** @nullable */
  size: number | null;
  isActive: boolean;
  /** @nullable */
  uploadedBy: string | null;
  uploadedAt: string;
  uploader: User;
  organization: Organization;
  workspace: Workspace;
  project: Project;
  workItem: WorkItem;
};

/**
 * @nullable
 */
export type DeleteApiV1AttachmentsId204 =
  | (typeof DeleteApiV1AttachmentsId204)[keyof typeof DeleteApiV1AttachmentsId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1AttachmentsId204 = {
  null: 'null',
} as const;

export type GetApiV1AttachmentsId200DataEntityType =
  (typeof GetApiV1AttachmentsId200DataEntityType)[keyof typeof GetApiV1AttachmentsId200DataEntityType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiV1AttachmentsId200DataEntityType = {
  work_item: 'work_item',
  project: 'project',
  workspace: 'workspace',
  organization: 'organization',
  comment: 'comment',
  test_case: 'test_case',
  test_plan: 'test_plan',
  test_suite: 'test_suite',
  test_run: 'test_run',
  user: 'user',
} as const;

export type GetApiV1AttachmentsId200Data = {
  id: string;
  entityType: GetApiV1AttachmentsId200DataEntityType;
  entityId: string;
  /** @maxLength 255 */
  fileName: string;
  /** @maxLength 500 */
  url: string;
  /**
   * @maxLength 100
   * @nullable
   */
  fileType: string | null;
  /** @nullable */
  size: number | null;
  isActive: boolean;
  /** @nullable */
  uploadedBy: string | null;
  uploadedAt: string;
  uploader: User;
  organization: Organization;
  workspace: Workspace;
  project: Project;
  workItem: WorkItem;
};

export type GetApiV1AttachmentsId200 = {
  data: GetApiV1AttachmentsId200Data;
};

export type PatchApiV1AttachmentsIdBodyEntityType =
  (typeof PatchApiV1AttachmentsIdBodyEntityType)[keyof typeof PatchApiV1AttachmentsIdBodyEntityType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1AttachmentsIdBodyEntityType = {
  work_item: 'work_item',
  project: 'project',
  workspace: 'workspace',
  organization: 'organization',
  comment: 'comment',
  test_case: 'test_case',
  test_plan: 'test_plan',
  test_suite: 'test_suite',
  test_run: 'test_run',
  user: 'user',
} as const;

export type PatchApiV1AttachmentsIdBody = {
  entityType?: PatchApiV1AttachmentsIdBodyEntityType;
  entityId?: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  fileName?: string;
  /**
   * @minLength 1
   * @maxLength 500
   */
  url?: string;
  /**
   * @maxLength 100
   * @nullable
   */
  fileType?: string | null;
  /** @nullable */
  size?: number | null;
  isActive?: boolean;
  /** @nullable */
  uploadedBy?: string | null;
};

export type PatchApiV1AttachmentsId200DataEntityType =
  (typeof PatchApiV1AttachmentsId200DataEntityType)[keyof typeof PatchApiV1AttachmentsId200DataEntityType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1AttachmentsId200DataEntityType = {
  work_item: 'work_item',
  project: 'project',
  workspace: 'workspace',
  organization: 'organization',
  comment: 'comment',
  test_case: 'test_case',
  test_plan: 'test_plan',
  test_suite: 'test_suite',
  test_run: 'test_run',
  user: 'user',
} as const;

export type PatchApiV1AttachmentsId200Data = {
  id: string;
  entityType: PatchApiV1AttachmentsId200DataEntityType;
  entityId: string;
  /** @maxLength 255 */
  fileName: string;
  /** @maxLength 500 */
  url: string;
  /**
   * @maxLength 100
   * @nullable
   */
  fileType: string | null;
  /** @nullable */
  size: number | null;
  isActive: boolean;
  /** @nullable */
  uploadedBy: string | null;
  uploadedAt: string;
  uploader: User;
  organization: Organization;
  workspace: Workspace;
  project: Project;
  workItem: WorkItem;
};

export type PatchApiV1AttachmentsId200 = {
  data: PatchApiV1AttachmentsId200Data;
};

export type GetApiV1AttachmentsParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1Attachments200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1Attachments200 = {
  data: Attachment[];
  meta: GetApiV1Attachments200Meta;
};

export type GetApiV1AuditLogsParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1AuditLogs200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1AuditLogs200 = {
  data: AuditLog[];
  meta: GetApiV1AuditLogs200Meta;
};

export type GetApiV1AuditLogsId200DataAction =
  (typeof GetApiV1AuditLogsId200DataAction)[keyof typeof GetApiV1AuditLogsId200DataAction];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiV1AuditLogsId200DataAction = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  ARCHIVE: 'ARCHIVE',
  RESTORE: 'RESTORE',
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  PASSWORD_CHANGE: 'PASSWORD_CHANGE',
  PROFILE_UPDATE: 'PROFILE_UPDATE',
  ROLE_ASSIGNED: 'ROLE_ASSIGNED',
  ROLE_REVOKED: 'ROLE_REVOKED',
  PERMISSION_UPDATED: 'PERMISSION_UPDATED',
  STATUS_CHANGE: 'STATUS_CHANGE',
  SPRINT_TRANSITION: 'SPRINT_TRANSITION',
  COMMENT_ADDED: 'COMMENT_ADDED',
  ATTACHMENT_UPLOADED: 'ATTACHMENT_UPLOADED',
  NOTIFICATION_SENT: 'NOTIFICATION_SENT',
  SYSTEM_START: 'SYSTEM_START',
  SYSTEM_ERROR: 'SYSTEM_ERROR',
  SCHEDULED_TASK_RUN: 'SCHEDULED_TASK_RUN',
  DATA_IMPORT: 'DATA_IMPORT',
  DATA_EXPORT: 'DATA_EXPORT',
} as const;

export type GetApiV1AuditLogsId200DataDetails = { [key: string]: unknown };

export type GetApiV1AuditLogsId200Data = {
  id: string;
  /** @maxLength 100 */
  entityType: string;
  entityId: string;
  action: GetApiV1AuditLogsId200DataAction;
  /** @nullable */
  changedBy: string | null;
  timestamp: string;
  details?: GetApiV1AuditLogsId200DataDetails;
  /**
   * @maxLength 100
   * @nullable
   */
  relatedEntityType: string | null;
  /** @nullable */
  relatedEntityId: string | null;
  user: User;
  organization: Organization;
  workspace: Workspace;
  project: Project;
  workItem: WorkItem;
  role: Role;
  sprint: Sprint;
  workflow: Workflow;
  status: Status;
  priority: Priority;
};

export type GetApiV1AuditLogsId200 = {
  data: GetApiV1AuditLogsId200Data;
};

export type PostApiV1AuthRegisterBody = {
  email: string;
  /**
   * @minLength 8
   * @maxLength 100
   * @pattern ^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]
   */
  password: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  firstName: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  lastName: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  displayName?: string;
};

export type PostApiV1AuthRegister200Tokens = {
  accessToken: string;
  refreshToken: string;
};

export type PostApiV1AuthRegister200 = {
  user: User;
  tokens: PostApiV1AuthRegister200Tokens;
};

export type PostApiV1AuthLoginBody = {
  email: string;
  /** @minLength 1 */
  password: string;
};

export type PostApiV1AuthLogin200Tokens = {
  accessToken: string;
  refreshToken: string;
};

export type PostApiV1AuthLogin200 = {
  user: User;
  tokens: PostApiV1AuthLogin200Tokens;
};

export type PostApiV1AuthRefreshBody = {
  /** @minLength 1 */
  refreshToken: string;
};

export type PostApiV1AuthRefresh200 = {
  accessToken: string;
  refreshToken: string;
};

export type PostApiV1AuthForgotPasswordBody = {
  email: string;
};

export type PostApiV1AuthForgotPassword200 = {
  message: string;
};

export type PostApiV1AuthResetPasswordBody = {
  /** @minLength 1 */
  token: string;
  /**
   * @minLength 8
   * @maxLength 100
   * @pattern ^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]
   */
  password: string;
};

export type PostApiV1AuthResetPassword200 = {
  message: string;
};

export type PostApiV1AuthVerifyEmailBody = {
  /** @minLength 1 */
  token: string;
};

export type PostApiV1AuthVerifyEmail200 = {
  message: string;
};

export type PostApiV1AuthChangePasswordBody = {
  /** @minLength 1 */
  currentPassword: string;
  /**
   * @minLength 8
   * @maxLength 100
   * @pattern ^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]
   */
  newPassword: string;
};

export type PostApiV1AuthChangePassword200 = {
  message: string;
};

export type PostApiV1AuthResendVerificationBody = {
  email: string;
};

export type PostApiV1AuthResendVerification200 = {
  message: string;
};

export type GetApiV1AuthMe200 = {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  displayName: string;
  isEmailVerified: boolean;
  isActive: boolean;
  isStaff: boolean;
  /** @nullable */
  lastLogin: string | null;
  createdAt: string;
  updatedAt: string;
};

export type PostApiV1AuthLogoutBody = {
  fcmToken?: string;
};

export type PostApiV1AuthLogout200 = {
  message: string;
};

export type GetApiV1InvitationsParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1Invitations200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1Invitations200 = {
  data: Invitation[];
  meta: GetApiV1Invitations200Meta;
};

export type PostApiV1InvitationsBodyEmail = string | '' | 'null' | null | unknown;

export type PostApiV1InvitationsBodyScopeType =
  (typeof PostApiV1InvitationsBodyScopeType)[keyof typeof PostApiV1InvitationsBodyScopeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1InvitationsBodyScopeType = {
  organization: 'organization',
  workspace: 'workspace',
  project: 'project',
} as const;

export type PostApiV1InvitationsBodyStatus =
  (typeof PostApiV1InvitationsBodyStatus)[keyof typeof PostApiV1InvitationsBodyStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1InvitationsBodyStatus = {
  pending: 'pending',
  accepted: 'accepted',
  rejected: 'rejected',
  expired: 'expired',
  revoked: 'revoked',
} as const;

/**
 * @nullable
 */
export type PostApiV1InvitationsBodyAcceptedAt = string | string | 'null' | null | unknown | null;

export type PostApiV1InvitationsBody = {
  email?: PostApiV1InvitationsBodyEmail;
  invitedBy: string;
  roleId: string;
  scopeType: PostApiV1InvitationsBodyScopeType;
  scopeId: string;
  status?: PostApiV1InvitationsBodyStatus;
  /** @nullable */
  acceptedAt?: PostApiV1InvitationsBodyAcceptedAt;
};

export type PostApiV1Invitations201DataScopeType =
  (typeof PostApiV1Invitations201DataScopeType)[keyof typeof PostApiV1Invitations201DataScopeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1Invitations201DataScopeType = {
  organization: 'organization',
  workspace: 'workspace',
  project: 'project',
} as const;

export type PostApiV1Invitations201DataStatus =
  (typeof PostApiV1Invitations201DataStatus)[keyof typeof PostApiV1Invitations201DataStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1Invitations201DataStatus = {
  pending: 'pending',
  accepted: 'accepted',
  rejected: 'rejected',
  expired: 'expired',
  revoked: 'revoked',
} as const;

export type PostApiV1Invitations201DataExpiresAt = string | string | 'null' | null | unknown;

/**
 * @nullable
 */
export type PostApiV1Invitations201DataAcceptedAt =
  | string
  | string
  | 'null'
  | null
  | unknown
  | null;

export type PostApiV1Invitations201Data = {
  id: string;
  /** @maxLength 255 */
  email: string;
  /** @maxLength 512 */
  token: string;
  invitedBy: string;
  roleId: string;
  scopeType: PostApiV1Invitations201DataScopeType;
  scopeId: string;
  status: PostApiV1Invitations201DataStatus;
  expiresAt?: PostApiV1Invitations201DataExpiresAt;
  /** @nullable */
  acceptedAt?: PostApiV1Invitations201DataAcceptedAt;
  createdAt: string;
  updatedAt: string;
  inviter: User;
  role: Role;
  organization: Organization;
  workspace: Workspace;
  project: Project;
};

export type PostApiV1Invitations201 = {
  data: PostApiV1Invitations201Data;
};

export type GetApiV1InvitationsId200DataScopeType =
  (typeof GetApiV1InvitationsId200DataScopeType)[keyof typeof GetApiV1InvitationsId200DataScopeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiV1InvitationsId200DataScopeType = {
  organization: 'organization',
  workspace: 'workspace',
  project: 'project',
} as const;

export type GetApiV1InvitationsId200DataStatus =
  (typeof GetApiV1InvitationsId200DataStatus)[keyof typeof GetApiV1InvitationsId200DataStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiV1InvitationsId200DataStatus = {
  pending: 'pending',
  accepted: 'accepted',
  rejected: 'rejected',
  expired: 'expired',
  revoked: 'revoked',
} as const;

export type GetApiV1InvitationsId200DataExpiresAt = string | string | 'null' | null | unknown;

/**
 * @nullable
 */
export type GetApiV1InvitationsId200DataAcceptedAt =
  | string
  | string
  | 'null'
  | null
  | unknown
  | null;

export type GetApiV1InvitationsId200Data = {
  id: string;
  /** @maxLength 255 */
  email: string;
  /** @maxLength 512 */
  token: string;
  invitedBy: string;
  roleId: string;
  scopeType: GetApiV1InvitationsId200DataScopeType;
  scopeId: string;
  status: GetApiV1InvitationsId200DataStatus;
  expiresAt?: GetApiV1InvitationsId200DataExpiresAt;
  /** @nullable */
  acceptedAt?: GetApiV1InvitationsId200DataAcceptedAt;
  createdAt: string;
  updatedAt: string;
  inviter: User;
  role: Role;
  organization: Organization;
  workspace: Workspace;
  project: Project;
};

export type GetApiV1InvitationsId200 = {
  data: GetApiV1InvitationsId200Data;
};

export type PatchApiV1InvitationsIdBodyEmail = string | '' | 'null' | null | unknown;

export type PatchApiV1InvitationsIdBodyScopeType =
  (typeof PatchApiV1InvitationsIdBodyScopeType)[keyof typeof PatchApiV1InvitationsIdBodyScopeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1InvitationsIdBodyScopeType = {
  organization: 'organization',
  workspace: 'workspace',
  project: 'project',
} as const;

export type PatchApiV1InvitationsIdBodyStatus =
  (typeof PatchApiV1InvitationsIdBodyStatus)[keyof typeof PatchApiV1InvitationsIdBodyStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1InvitationsIdBodyStatus = {
  pending: 'pending',
  accepted: 'accepted',
  rejected: 'rejected',
  expired: 'expired',
  revoked: 'revoked',
} as const;

/**
 * @nullable
 */
export type PatchApiV1InvitationsIdBodyAcceptedAt =
  | string
  | string
  | 'null'
  | null
  | unknown
  | null;

export type PatchApiV1InvitationsIdBody = {
  email?: PatchApiV1InvitationsIdBodyEmail;
  invitedBy?: string;
  roleId?: string;
  scopeType?: PatchApiV1InvitationsIdBodyScopeType;
  scopeId?: string;
  status?: PatchApiV1InvitationsIdBodyStatus;
  /** @nullable */
  acceptedAt?: PatchApiV1InvitationsIdBodyAcceptedAt;
};

export type PatchApiV1InvitationsId200DataScopeType =
  (typeof PatchApiV1InvitationsId200DataScopeType)[keyof typeof PatchApiV1InvitationsId200DataScopeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1InvitationsId200DataScopeType = {
  organization: 'organization',
  workspace: 'workspace',
  project: 'project',
} as const;

export type PatchApiV1InvitationsId200DataStatus =
  (typeof PatchApiV1InvitationsId200DataStatus)[keyof typeof PatchApiV1InvitationsId200DataStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1InvitationsId200DataStatus = {
  pending: 'pending',
  accepted: 'accepted',
  rejected: 'rejected',
  expired: 'expired',
  revoked: 'revoked',
} as const;

export type PatchApiV1InvitationsId200DataExpiresAt = string | string | 'null' | null | unknown;

/**
 * @nullable
 */
export type PatchApiV1InvitationsId200DataAcceptedAt =
  | string
  | string
  | 'null'
  | null
  | unknown
  | null;

export type PatchApiV1InvitationsId200Data = {
  id: string;
  /** @maxLength 255 */
  email: string;
  /** @maxLength 512 */
  token: string;
  invitedBy: string;
  roleId: string;
  scopeType: PatchApiV1InvitationsId200DataScopeType;
  scopeId: string;
  status: PatchApiV1InvitationsId200DataStatus;
  expiresAt?: PatchApiV1InvitationsId200DataExpiresAt;
  /** @nullable */
  acceptedAt?: PatchApiV1InvitationsId200DataAcceptedAt;
  createdAt: string;
  updatedAt: string;
  inviter: User;
  role: Role;
  organization: Organization;
  workspace: Workspace;
  project: Project;
};

export type PatchApiV1InvitationsId200 = {
  data: PatchApiV1InvitationsId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1InvitationsId204 =
  | (typeof DeleteApiV1InvitationsId204)[keyof typeof DeleteApiV1InvitationsId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1InvitationsId204 = {
  null: 'null',
} as const;

export type GetApiV1OrganizationsParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1Organizations200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1Organizations200 = {
  data: Organization[];
  meta: GetApiV1Organizations200Meta;
};

export type PostApiV1OrganizationsBodySettings = { [key: string]: unknown };

export type PostApiV1OrganizationsBodyDomains = { [key: string]: unknown };

/**
 * @nullable
 */
export type PostApiV1OrganizationsBodyBillingEmail = string | '' | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type PostApiV1OrganizationsBodyPlanExpiresAt =
  | string
  | string
  | 'null'
  | null
  | unknown
  | null;

export type PostApiV1OrganizationsBody = {
  /**
   * @minLength 1
   * @maxLength 255
   */
  name: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  slug: string;
  /** @nullable */
  description?: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  website?: string | null;
  /** @nullable */
  logoUrl?: string | null;
  isActive?: boolean;
  /** @nullable */
  createdBy: string | null;
  settings?: PostApiV1OrganizationsBodySettings;
  domains?: PostApiV1OrganizationsBodyDomains;
  /** @nullable */
  billingEmail?: PostApiV1OrganizationsBodyBillingEmail;
  /** @nullable */
  billingAddress?: string | null;
  /**
   * @maxLength 50
   * @nullable
   */
  billingPlan?: string | null;
  /** @nullable */
  planExpiresAt?: PostApiV1OrganizationsBodyPlanExpiresAt;
};

export type PostApiV1Organizations201DataSettings = { [key: string]: unknown };

export type PostApiV1Organizations201DataDomains = { [key: string]: unknown };

/**
 * @nullable
 */
export type PostApiV1Organizations201DataPlanExpiresAt =
  | string
  | string
  | 'null'
  | null
  | unknown
  | null;

export type PostApiV1Organizations201Data = {
  id: string;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  slug: string;
  /** @nullable */
  description: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  website: string | null;
  /**
   * @maxLength 500
   * @nullable
   */
  logoUrl: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  /** @nullable */
  createdBy: string | null;
  settings?: PostApiV1Organizations201DataSettings;
  domains?: PostApiV1Organizations201DataDomains;
  /**
   * @maxLength 255
   * @nullable
   */
  billingEmail: string | null;
  /** @nullable */
  billingAddress: string | null;
  /**
   * @maxLength 50
   * @nullable
   */
  billingPlan: string | null;
  /** @nullable */
  planExpiresAt?: PostApiV1Organizations201DataPlanExpiresAt;
  creator: User;
};

export type PostApiV1Organizations201 = {
  data: PostApiV1Organizations201Data;
};

export type GetApiV1OrganizationsId200DataSettings = { [key: string]: unknown };

export type GetApiV1OrganizationsId200DataDomains = { [key: string]: unknown };

/**
 * @nullable
 */
export type GetApiV1OrganizationsId200DataPlanExpiresAt =
  | string
  | string
  | 'null'
  | null
  | unknown
  | null;

export type GetApiV1OrganizationsId200Data = {
  id: string;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  slug: string;
  /** @nullable */
  description: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  website: string | null;
  /**
   * @maxLength 500
   * @nullable
   */
  logoUrl: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  /** @nullable */
  createdBy: string | null;
  settings?: GetApiV1OrganizationsId200DataSettings;
  domains?: GetApiV1OrganizationsId200DataDomains;
  /**
   * @maxLength 255
   * @nullable
   */
  billingEmail: string | null;
  /** @nullable */
  billingAddress: string | null;
  /**
   * @maxLength 50
   * @nullable
   */
  billingPlan: string | null;
  /** @nullable */
  planExpiresAt?: GetApiV1OrganizationsId200DataPlanExpiresAt;
  creator: User;
};

export type GetApiV1OrganizationsId200 = {
  data: GetApiV1OrganizationsId200Data;
};

export type PatchApiV1OrganizationsIdBodySettings = { [key: string]: unknown };

export type PatchApiV1OrganizationsIdBodyDomains = { [key: string]: unknown };

/**
 * @nullable
 */
export type PatchApiV1OrganizationsIdBodyBillingEmail =
  | string
  | ''
  | 'null'
  | null
  | unknown
  | null;

/**
 * @nullable
 */
export type PatchApiV1OrganizationsIdBodyPlanExpiresAt =
  | string
  | string
  | 'null'
  | null
  | unknown
  | null;

export type PatchApiV1OrganizationsIdBody = {
  /**
   * @minLength 1
   * @maxLength 255
   */
  name?: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  slug?: string;
  /** @nullable */
  description?: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  website?: string | null;
  /** @nullable */
  logoUrl?: string | null;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
  settings?: PatchApiV1OrganizationsIdBodySettings;
  domains?: PatchApiV1OrganizationsIdBodyDomains;
  /** @nullable */
  billingEmail?: PatchApiV1OrganizationsIdBodyBillingEmail;
  /** @nullable */
  billingAddress?: string | null;
  /**
   * @maxLength 50
   * @nullable
   */
  billingPlan?: string | null;
  /** @nullable */
  planExpiresAt?: PatchApiV1OrganizationsIdBodyPlanExpiresAt;
};

export type PatchApiV1OrganizationsId200DataSettings = {
  [key: string]: unknown;
};

export type PatchApiV1OrganizationsId200DataDomains = {
  [key: string]: unknown;
};

/**
 * @nullable
 */
export type PatchApiV1OrganizationsId200DataPlanExpiresAt =
  | string
  | string
  | 'null'
  | null
  | unknown
  | null;

export type PatchApiV1OrganizationsId200Data = {
  id: string;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  slug: string;
  /** @nullable */
  description: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  website: string | null;
  /**
   * @maxLength 500
   * @nullable
   */
  logoUrl: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  /** @nullable */
  createdBy: string | null;
  settings?: PatchApiV1OrganizationsId200DataSettings;
  domains?: PatchApiV1OrganizationsId200DataDomains;
  /**
   * @maxLength 255
   * @nullable
   */
  billingEmail: string | null;
  /** @nullable */
  billingAddress: string | null;
  /**
   * @maxLength 50
   * @nullable
   */
  billingPlan: string | null;
  /** @nullable */
  planExpiresAt?: PatchApiV1OrganizationsId200DataPlanExpiresAt;
  creator: User;
};

export type PatchApiV1OrganizationsId200 = {
  data: PatchApiV1OrganizationsId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1OrganizationsId204 =
  | (typeof DeleteApiV1OrganizationsId204)[keyof typeof DeleteApiV1OrganizationsId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1OrganizationsId204 = {
  null: 'null',
} as const;

export type GetApiV1ApiV1PermissionsParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1ApiV1Permissions200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1ApiV1Permissions200 = {
  data: Permission[];
  meta: GetApiV1ApiV1Permissions200Meta;
};

export type PostApiV1ApiV1PermissionsBodyScopeType =
  (typeof PostApiV1ApiV1PermissionsBodyScopeType)[keyof typeof PostApiV1ApiV1PermissionsBodyScopeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1ApiV1PermissionsBodyScopeType = {
  org: 'org',
  ws: 'ws',
} as const;

export type PostApiV1ApiV1PermissionsBody = {
  /**
   * @minLength 1
   * @maxLength 255
   */
  identifier: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name: string;
  /** @nullable */
  description?: string | null;
  /**
   * @minLength 1
   * @maxLength 100
   */
  category: string;
  scopeType: PostApiV1ApiV1PermissionsBodyScopeType;
  isActive?: boolean;
};

export type PostApiV1ApiV1Permissions201Data = {
  id: string;
  /** @maxLength 255 */
  identifier: string;
  /** @maxLength 255 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @maxLength 100 */
  category: string;
  /** @maxLength 50 */
  scopeType: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
};

export type PostApiV1ApiV1Permissions201 = {
  data: PostApiV1ApiV1Permissions201Data;
};

export type GetApiV1ApiV1PermissionsId200Data = {
  id: string;
  /** @maxLength 255 */
  identifier: string;
  /** @maxLength 255 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @maxLength 100 */
  category: string;
  /** @maxLength 50 */
  scopeType: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
};

export type GetApiV1ApiV1PermissionsId200 = {
  data: GetApiV1ApiV1PermissionsId200Data;
};

export type PatchApiV1ApiV1PermissionsIdBodyScopeType =
  (typeof PatchApiV1ApiV1PermissionsIdBodyScopeType)[keyof typeof PatchApiV1ApiV1PermissionsIdBodyScopeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1ApiV1PermissionsIdBodyScopeType = {
  org: 'org',
  ws: 'ws',
} as const;

export type PatchApiV1ApiV1PermissionsIdBody = {
  /**
   * @minLength 1
   * @maxLength 255
   */
  identifier?: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name?: string;
  /** @nullable */
  description?: string | null;
  /**
   * @minLength 1
   * @maxLength 100
   */
  category?: string;
  scopeType?: PatchApiV1ApiV1PermissionsIdBodyScopeType;
  isActive?: boolean;
};

export type PatchApiV1ApiV1PermissionsId200Data = {
  id: string;
  /** @maxLength 255 */
  identifier: string;
  /** @maxLength 255 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @maxLength 100 */
  category: string;
  /** @maxLength 50 */
  scopeType: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
};

export type PatchApiV1ApiV1PermissionsId200 = {
  data: PatchApiV1ApiV1PermissionsId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1ApiV1PermissionsId204 =
  | (typeof DeleteApiV1ApiV1PermissionsId204)[keyof typeof DeleteApiV1ApiV1PermissionsId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1ApiV1PermissionsId204 = {
  null: 'null',
} as const;

export type GetApiV1PrioritiesParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1Priorities200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1Priorities200 = {
  data: Priority[];
  meta: GetApiV1Priorities200Meta;
};

export type PostApiV1PrioritiesBody = {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  organizationId?: string | null;
  level: number;
  isSystem?: boolean;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  icon?: string | null;
  isActive?: boolean;
};

export type PostApiV1Priorities201Data = {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  organizationId: string | null;
  level: number;
  isSystem: boolean;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  icon: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  organization: Organization;
};

export type PostApiV1Priorities201 = {
  data: PostApiV1Priorities201Data;
};

export type GetApiV1PrioritiesId200Data = {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  organizationId: string | null;
  level: number;
  isSystem: boolean;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  icon: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  organization: Organization;
};

export type GetApiV1PrioritiesId200 = {
  data: GetApiV1PrioritiesId200Data;
};

export type PatchApiV1PrioritiesIdBody = {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name?: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  organizationId?: string | null;
  level?: number;
  isSystem?: boolean;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  icon?: string | null;
  isActive?: boolean;
};

export type PatchApiV1PrioritiesId200Data = {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  organizationId: string | null;
  level: number;
  isSystem: boolean;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  icon: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  organization: Organization;
};

export type PatchApiV1PrioritiesId200 = {
  data: PatchApiV1PrioritiesId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1PrioritiesId204 =
  | (typeof DeleteApiV1PrioritiesId204)[keyof typeof DeleteApiV1PrioritiesId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1PrioritiesId204 = {
  null: 'null',
} as const;

export type GetApiV1ProjectWorkflowsParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1ProjectWorkflows200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1ProjectWorkflows200 = {
  data: ProjectWorkflow[];
  meta: GetApiV1ProjectWorkflows200Meta;
};

export type PostApiV1ProjectWorkflowsBody = {
  projectId: string;
  workflowId: string;
  isActive?: boolean;
  isDefault?: boolean;
};

export type PostApiV1ProjectWorkflows201Data = {
  id: string;
  projectId: string;
  workflowId: string;
  isActive: boolean;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
  project: Project;
  workflow: Workflow;
};

export type PostApiV1ProjectWorkflows201 = {
  data: PostApiV1ProjectWorkflows201Data;
};

export type GetApiV1ProjectWorkflowsId200Data = {
  id: string;
  projectId: string;
  workflowId: string;
  isActive: boolean;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
  project: Project;
  workflow: Workflow;
};

export type GetApiV1ProjectWorkflowsId200 = {
  data: GetApiV1ProjectWorkflowsId200Data;
};

export type PatchApiV1ProjectWorkflowsIdBody = {
  projectId?: string;
  workflowId?: string;
  isActive?: boolean;
  isDefault?: boolean;
};

export type PatchApiV1ProjectWorkflowsId200Data = {
  id: string;
  projectId: string;
  workflowId: string;
  isActive: boolean;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
  project: Project;
  workflow: Workflow;
};

export type PatchApiV1ProjectWorkflowsId200 = {
  data: PatchApiV1ProjectWorkflowsId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1ProjectWorkflowsId204 =
  | (typeof DeleteApiV1ProjectWorkflowsId204)[keyof typeof DeleteApiV1ProjectWorkflowsId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1ProjectWorkflowsId204 = {
  null: 'null',
} as const;

export type GetApiV1ProjectsParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1Projects200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1Projects200 = {
  data: Project[];
  meta: GetApiV1Projects200Meta;
};

/**
 * @nullable
 */
export type PostApiV1ProjectsBodyStartDate = string | string | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type PostApiV1ProjectsBodyTargetDate = string | string | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type PostApiV1ProjectsBodyActualEndDate = string | string | 'null' | null | unknown | null;

export type PostApiV1ProjectsBodyStatus =
  (typeof PostApiV1ProjectsBodyStatus)[keyof typeof PostApiV1ProjectsBodyStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1ProjectsBodyStatus = {
  planning: 'planning',
  active: 'active',
  completed: 'completed',
  archived: 'archived',
} as const;

export type PostApiV1ProjectsBodyVisibility =
  (typeof PostApiV1ProjectsBodyVisibility)[keyof typeof PostApiV1ProjectsBodyVisibility];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1ProjectsBodyVisibility = {
  public: 'public',
  private: 'private',
} as const;

export type PostApiV1ProjectsBodySettings = { [key: string]: unknown };

export type PostApiV1ProjectsBody = {
  workspaceId: string;
  /** @nullable */
  workflowId?: string | null;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name: string;
  /**
   * @minLength 1
   * @maxLength 10
   */
  key: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  icon?: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  /** @nullable */
  startDate?: PostApiV1ProjectsBodyStartDate;
  /** @nullable */
  targetDate?: PostApiV1ProjectsBodyTargetDate;
  /** @nullable */
  actualEndDate?: PostApiV1ProjectsBodyActualEndDate;
  status?: PostApiV1ProjectsBodyStatus;
  visibility?: PostApiV1ProjectsBodyVisibility;
  /** @nullable */
  defaultAssigneeId?: string | null;
  lastTicketNumber?: number;
  settings?: PostApiV1ProjectsBodySettings;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
};

/**
 * @nullable
 */
export type PostApiV1Projects201DataStartDate = string | string | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type PostApiV1Projects201DataTargetDate = string | string | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type PostApiV1Projects201DataActualEndDate =
  | string
  | string
  | 'null'
  | null
  | unknown
  | null;

export type PostApiV1Projects201DataStatus =
  (typeof PostApiV1Projects201DataStatus)[keyof typeof PostApiV1Projects201DataStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1Projects201DataStatus = {
  planning: 'planning',
  active: 'active',
  completed: 'completed',
  archived: 'archived',
} as const;

export type PostApiV1Projects201DataVisibility =
  (typeof PostApiV1Projects201DataVisibility)[keyof typeof PostApiV1Projects201DataVisibility];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1Projects201DataVisibility = {
  public: 'public',
  private: 'private',
} as const;

export type PostApiV1Projects201DataSettings = { [key: string]: unknown };

export type PostApiV1Projects201DataCreatedAt = string | string | 'null' | null | unknown;

export type PostApiV1Projects201DataUpdatedAt = string | string | 'null' | null | unknown;

export type PostApiV1Projects201Data = {
  id: string;
  workspaceId: string;
  /** @nullable */
  workflowId: string | null;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 10 */
  key: string;
  /** @nullable */
  description: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  icon: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  /** @nullable */
  startDate?: PostApiV1Projects201DataStartDate;
  /** @nullable */
  targetDate?: PostApiV1Projects201DataTargetDate;
  /** @nullable */
  actualEndDate?: PostApiV1Projects201DataActualEndDate;
  status: PostApiV1Projects201DataStatus;
  visibility: PostApiV1Projects201DataVisibility;
  /** @nullable */
  defaultAssigneeId: string | null;
  lastTicketNumber: number;
  settings?: PostApiV1Projects201DataSettings;
  isActive: boolean;
  createdAt?: PostApiV1Projects201DataCreatedAt;
  updatedAt?: PostApiV1Projects201DataUpdatedAt;
  /** @nullable */
  createdBy: string | null;
  workspace: Workspace;
  workflow: Workflow;
  creator: User;
  defaultAssignee: User;
};

export type PostApiV1Projects201 = {
  data: PostApiV1Projects201Data;
};

/**
 * @nullable
 */
export type GetApiV1ProjectsId200DataStartDate = string | string | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type GetApiV1ProjectsId200DataTargetDate = string | string | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type GetApiV1ProjectsId200DataActualEndDate =
  | string
  | string
  | 'null'
  | null
  | unknown
  | null;

export type GetApiV1ProjectsId200DataStatus =
  (typeof GetApiV1ProjectsId200DataStatus)[keyof typeof GetApiV1ProjectsId200DataStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiV1ProjectsId200DataStatus = {
  planning: 'planning',
  active: 'active',
  completed: 'completed',
  archived: 'archived',
} as const;

export type GetApiV1ProjectsId200DataVisibility =
  (typeof GetApiV1ProjectsId200DataVisibility)[keyof typeof GetApiV1ProjectsId200DataVisibility];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiV1ProjectsId200DataVisibility = {
  public: 'public',
  private: 'private',
} as const;

export type GetApiV1ProjectsId200DataSettings = { [key: string]: unknown };

export type GetApiV1ProjectsId200DataCreatedAt = string | string | 'null' | null | unknown;

export type GetApiV1ProjectsId200DataUpdatedAt = string | string | 'null' | null | unknown;

export type GetApiV1ProjectsId200Data = {
  id: string;
  workspaceId: string;
  /** @nullable */
  workflowId: string | null;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 10 */
  key: string;
  /** @nullable */
  description: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  icon: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  /** @nullable */
  startDate?: GetApiV1ProjectsId200DataStartDate;
  /** @nullable */
  targetDate?: GetApiV1ProjectsId200DataTargetDate;
  /** @nullable */
  actualEndDate?: GetApiV1ProjectsId200DataActualEndDate;
  status: GetApiV1ProjectsId200DataStatus;
  visibility: GetApiV1ProjectsId200DataVisibility;
  /** @nullable */
  defaultAssigneeId: string | null;
  lastTicketNumber: number;
  settings?: GetApiV1ProjectsId200DataSettings;
  isActive: boolean;
  createdAt?: GetApiV1ProjectsId200DataCreatedAt;
  updatedAt?: GetApiV1ProjectsId200DataUpdatedAt;
  /** @nullable */
  createdBy: string | null;
  workspace: Workspace;
  workflow: Workflow;
  creator: User;
  defaultAssignee: User;
};

export type GetApiV1ProjectsId200 = {
  data: GetApiV1ProjectsId200Data;
};

/**
 * @nullable
 */
export type PatchApiV1ProjectsIdBodyStartDate = string | string | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type PatchApiV1ProjectsIdBodyTargetDate = string | string | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type PatchApiV1ProjectsIdBodyActualEndDate =
  | string
  | string
  | 'null'
  | null
  | unknown
  | null;

export type PatchApiV1ProjectsIdBodyStatus =
  (typeof PatchApiV1ProjectsIdBodyStatus)[keyof typeof PatchApiV1ProjectsIdBodyStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1ProjectsIdBodyStatus = {
  planning: 'planning',
  active: 'active',
  completed: 'completed',
  archived: 'archived',
} as const;

export type PatchApiV1ProjectsIdBodyVisibility =
  (typeof PatchApiV1ProjectsIdBodyVisibility)[keyof typeof PatchApiV1ProjectsIdBodyVisibility];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1ProjectsIdBodyVisibility = {
  public: 'public',
  private: 'private',
} as const;

export type PatchApiV1ProjectsIdBodySettings = { [key: string]: unknown };

export type PatchApiV1ProjectsIdBody = {
  workspaceId?: string;
  /** @nullable */
  workflowId?: string | null;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name?: string;
  /**
   * @minLength 1
   * @maxLength 10
   */
  key?: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  icon?: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  /** @nullable */
  startDate?: PatchApiV1ProjectsIdBodyStartDate;
  /** @nullable */
  targetDate?: PatchApiV1ProjectsIdBodyTargetDate;
  /** @nullable */
  actualEndDate?: PatchApiV1ProjectsIdBodyActualEndDate;
  status?: PatchApiV1ProjectsIdBodyStatus;
  visibility?: PatchApiV1ProjectsIdBodyVisibility;
  /** @nullable */
  defaultAssigneeId?: string | null;
  lastTicketNumber?: number;
  settings?: PatchApiV1ProjectsIdBodySettings;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
};

/**
 * @nullable
 */
export type PatchApiV1ProjectsId200DataStartDate = string | string | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type PatchApiV1ProjectsId200DataTargetDate =
  | string
  | string
  | 'null'
  | null
  | unknown
  | null;

/**
 * @nullable
 */
export type PatchApiV1ProjectsId200DataActualEndDate =
  | string
  | string
  | 'null'
  | null
  | unknown
  | null;

export type PatchApiV1ProjectsId200DataStatus =
  (typeof PatchApiV1ProjectsId200DataStatus)[keyof typeof PatchApiV1ProjectsId200DataStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1ProjectsId200DataStatus = {
  planning: 'planning',
  active: 'active',
  completed: 'completed',
  archived: 'archived',
} as const;

export type PatchApiV1ProjectsId200DataVisibility =
  (typeof PatchApiV1ProjectsId200DataVisibility)[keyof typeof PatchApiV1ProjectsId200DataVisibility];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1ProjectsId200DataVisibility = {
  public: 'public',
  private: 'private',
} as const;

export type PatchApiV1ProjectsId200DataSettings = { [key: string]: unknown };

export type PatchApiV1ProjectsId200DataCreatedAt = string | string | 'null' | null | unknown;

export type PatchApiV1ProjectsId200DataUpdatedAt = string | string | 'null' | null | unknown;

export type PatchApiV1ProjectsId200Data = {
  id: string;
  workspaceId: string;
  /** @nullable */
  workflowId: string | null;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 10 */
  key: string;
  /** @nullable */
  description: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  icon: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  /** @nullable */
  startDate?: PatchApiV1ProjectsId200DataStartDate;
  /** @nullable */
  targetDate?: PatchApiV1ProjectsId200DataTargetDate;
  /** @nullable */
  actualEndDate?: PatchApiV1ProjectsId200DataActualEndDate;
  status: PatchApiV1ProjectsId200DataStatus;
  visibility: PatchApiV1ProjectsId200DataVisibility;
  /** @nullable */
  defaultAssigneeId: string | null;
  lastTicketNumber: number;
  settings?: PatchApiV1ProjectsId200DataSettings;
  isActive: boolean;
  createdAt?: PatchApiV1ProjectsId200DataCreatedAt;
  updatedAt?: PatchApiV1ProjectsId200DataUpdatedAt;
  /** @nullable */
  createdBy: string | null;
  workspace: Workspace;
  workflow: Workflow;
  creator: User;
  defaultAssignee: User;
};

export type PatchApiV1ProjectsId200 = {
  data: PatchApiV1ProjectsId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1ProjectsId204 =
  | (typeof DeleteApiV1ProjectsId204)[keyof typeof DeleteApiV1ProjectsId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1ProjectsId204 = {
  null: 'null',
} as const;

export type GetApiV1ResolutionsParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1Resolutions200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1Resolutions200 = {
  data: Resolution[];
  meta: GetApiV1Resolutions200Meta;
};

export type PostApiV1ResolutionsBody = {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  organizationId?: string | null;
  isSystem?: boolean;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  isActive?: boolean;
};

export type PostApiV1Resolutions201Data = {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  organizationId: string | null;
  isSystem: boolean;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  organization: Organization;
};

export type PostApiV1Resolutions201 = {
  data: PostApiV1Resolutions201Data;
};

export type GetApiV1ResolutionsId200Data = {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  organizationId: string | null;
  isSystem: boolean;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  organization: Organization;
};

export type GetApiV1ResolutionsId200 = {
  data: GetApiV1ResolutionsId200Data;
};

export type PatchApiV1ResolutionsIdBody = {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name?: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  organizationId?: string | null;
  isSystem?: boolean;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  isActive?: boolean;
};

export type PatchApiV1ResolutionsId200Data = {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  organizationId: string | null;
  isSystem: boolean;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  organization: Organization;
};

export type PatchApiV1ResolutionsId200 = {
  data: PatchApiV1ResolutionsId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1ResolutionsId204 =
  | (typeof DeleteApiV1ResolutionsId204)[keyof typeof DeleteApiV1ResolutionsId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1ResolutionsId204 = {
  null: 'null',
} as const;

export type GetApiV1RoleAssignmentsParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1RoleAssignments200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1RoleAssignments200 = {
  data: RoleAssignment[];
  meta: GetApiV1RoleAssignments200Meta;
};

export type PostApiV1RoleAssignmentsBodyScopeType =
  (typeof PostApiV1RoleAssignmentsBodyScopeType)[keyof typeof PostApiV1RoleAssignmentsBodyScopeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1RoleAssignmentsBodyScopeType = {
  organization: 'organization',
  workspace: 'workspace',
  project: 'project',
} as const;

export type PostApiV1RoleAssignmentsBody = {
  userId: string;
  roleId: string;
  scopeType: PostApiV1RoleAssignmentsBodyScopeType;
  scopeId: string;
  isActive?: boolean;
};

export type PostApiV1RoleAssignments201DataScopeType =
  (typeof PostApiV1RoleAssignments201DataScopeType)[keyof typeof PostApiV1RoleAssignments201DataScopeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1RoleAssignments201DataScopeType = {
  organization: 'organization',
  workspace: 'workspace',
  project: 'project',
} as const;

export type PostApiV1RoleAssignments201Data = {
  id: string;
  userId: string;
  roleId: string;
  scopeType: PostApiV1RoleAssignments201DataScopeType;
  scopeId: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  user: User;
  role: Role;
  organization: Organization;
  workspace: Workspace;
  project: Project;
};

export type PostApiV1RoleAssignments201 = {
  data: PostApiV1RoleAssignments201Data;
};

export type GetApiV1RoleAssignmentsId200DataScopeType =
  (typeof GetApiV1RoleAssignmentsId200DataScopeType)[keyof typeof GetApiV1RoleAssignmentsId200DataScopeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiV1RoleAssignmentsId200DataScopeType = {
  organization: 'organization',
  workspace: 'workspace',
  project: 'project',
} as const;

export type GetApiV1RoleAssignmentsId200Data = {
  id: string;
  userId: string;
  roleId: string;
  scopeType: GetApiV1RoleAssignmentsId200DataScopeType;
  scopeId: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  user: User;
  role: Role;
  organization: Organization;
  workspace: Workspace;
  project: Project;
};

export type GetApiV1RoleAssignmentsId200 = {
  data: GetApiV1RoleAssignmentsId200Data;
};

export type PatchApiV1RoleAssignmentsIdBodyScopeType =
  (typeof PatchApiV1RoleAssignmentsIdBodyScopeType)[keyof typeof PatchApiV1RoleAssignmentsIdBodyScopeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1RoleAssignmentsIdBodyScopeType = {
  organization: 'organization',
  workspace: 'workspace',
  project: 'project',
} as const;

export type PatchApiV1RoleAssignmentsIdBody = {
  userId?: string;
  roleId?: string;
  scopeType?: PatchApiV1RoleAssignmentsIdBodyScopeType;
  scopeId?: string;
  isActive?: boolean;
};

export type PatchApiV1RoleAssignmentsId200DataScopeType =
  (typeof PatchApiV1RoleAssignmentsId200DataScopeType)[keyof typeof PatchApiV1RoleAssignmentsId200DataScopeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1RoleAssignmentsId200DataScopeType = {
  organization: 'organization',
  workspace: 'workspace',
  project: 'project',
} as const;

export type PatchApiV1RoleAssignmentsId200Data = {
  id: string;
  userId: string;
  roleId: string;
  scopeType: PatchApiV1RoleAssignmentsId200DataScopeType;
  scopeId: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  user: User;
  role: Role;
  organization: Organization;
  workspace: Workspace;
  project: Project;
};

export type PatchApiV1RoleAssignmentsId200 = {
  data: PatchApiV1RoleAssignmentsId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1RoleAssignmentsId204 =
  | (typeof DeleteApiV1RoleAssignmentsId204)[keyof typeof DeleteApiV1RoleAssignmentsId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1RoleAssignmentsId204 = {
  null: 'null',
} as const;

export type GetApiV1ApiV1RolePermissionsParams = {
  roleId: string;
};

export type GetApiV1ApiV1RolePermissions200Item = {
  id: string;
  identifier: string;
  name: string;
  /** @nullable */
  description: string | null;
  category: string;
  scopeType: string;
};

export type PutApiV1ApiV1RolePermissionsIdPermissionsBody = {
  permissionIds: string[];
};

export type PutApiV1ApiV1RolePermissionsIdPermissions200 = {
  message: string;
  count: number;
};

export type PostApiV1ApiV1RolePermissionsIdPermissionsBody = {
  permissionIds: string[];
};

export type PostApiV1ApiV1RolePermissionsIdPermissions201 = {
  message: string;
  added: number;
};

export type DeleteApiV1ApiV1RolePermissionsIdPermissionsBody = {
  permissionIds: string[];
};

export type DeleteApiV1ApiV1RolePermissionsIdPermissions200 = {
  message: string;
  removed: number;
};

export type GetApiV1RolesParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1Roles200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1Roles200 = {
  data: Role[];
  meta: GetApiV1Roles200Meta;
};

export type PostApiV1RolesBody = {
  /**
   * @minLength 1
   * @maxLength 255
   */
  name: string;
  /** @nullable */
  description?: string | null;
  /**
   * @minLength 1
   * @maxLength 255
   */
  identifier: string;
  /**
   * @minLength 1
   * @maxLength 50
   */
  level: string;
  isSystem?: boolean;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
};

export type PostApiV1Roles201Data = {
  id: string;
  /** @maxLength 255 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @maxLength 255 */
  identifier: string;
  /** @maxLength 50 */
  level: string;
  isSystem: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  /** @nullable */
  createdBy: string | null;
};

export type PostApiV1Roles201 = {
  data: PostApiV1Roles201Data;
};

export type GetApiV1RolesId200Data = {
  id: string;
  /** @maxLength 255 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @maxLength 255 */
  identifier: string;
  /** @maxLength 50 */
  level: string;
  isSystem: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  /** @nullable */
  createdBy: string | null;
};

export type GetApiV1RolesId200 = {
  data: GetApiV1RolesId200Data;
};

export type PatchApiV1RolesIdBody = {
  /**
   * @minLength 1
   * @maxLength 255
   */
  name?: string;
  /** @nullable */
  description?: string | null;
  /**
   * @minLength 1
   * @maxLength 255
   */
  identifier?: string;
  /**
   * @minLength 1
   * @maxLength 50
   */
  level?: string;
  isSystem?: boolean;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
};

export type PatchApiV1RolesId200Data = {
  id: string;
  /** @maxLength 255 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @maxLength 255 */
  identifier: string;
  /** @maxLength 50 */
  level: string;
  isSystem: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  /** @nullable */
  createdBy: string | null;
};

export type PatchApiV1RolesId200 = {
  data: PatchApiV1RolesId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1RolesId204 =
  | (typeof DeleteApiV1RolesId204)[keyof typeof DeleteApiV1RolesId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1RolesId204 = {
  null: 'null',
} as const;

export type GetApiV1SprintsParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1Sprints200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1Sprints200 = {
  data: Sprint[];
  meta: GetApiV1Sprints200Meta;
};

/**
 * @nullable
 */
export type PostApiV1SprintsBodyStartDate = string | string | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type PostApiV1SprintsBodyEndDate = string | string | 'null' | null | unknown | null;

export type PostApiV1SprintsBodyStatus =
  (typeof PostApiV1SprintsBodyStatus)[keyof typeof PostApiV1SprintsBodyStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1SprintsBodyStatus = {
  draft: 'draft',
  active: 'active',
  completed: 'completed',
  archived: 'archived',
} as const;

export type PostApiV1SprintsBody = {
  projectId: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name: string;
  /** @nullable */
  goal?: string | null;
  /** @nullable */
  startDate?: PostApiV1SprintsBodyStartDate;
  /** @nullable */
  endDate?: PostApiV1SprintsBodyEndDate;
  /** @nullable */
  capacity?: number | null;
  /** @nullable */
  velocity?: number | null;
  status?: PostApiV1SprintsBodyStatus;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
};

/**
 * @nullable
 */
export type PostApiV1Sprints201DataStartDate = string | string | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type PostApiV1Sprints201DataEndDate = string | string | 'null' | null | unknown | null;

export type PostApiV1Sprints201DataStatus =
  (typeof PostApiV1Sprints201DataStatus)[keyof typeof PostApiV1Sprints201DataStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1Sprints201DataStatus = {
  draft: 'draft',
  active: 'active',
  completed: 'completed',
  archived: 'archived',
} as const;

export type PostApiV1Sprints201Data = {
  id: string;
  projectId: string;
  /** @maxLength 255 */
  name: string;
  /** @nullable */
  goal: string | null;
  /** @nullable */
  startDate?: PostApiV1Sprints201DataStartDate;
  /** @nullable */
  endDate?: PostApiV1Sprints201DataEndDate;
  /** @nullable */
  capacity: number | null;
  /** @nullable */
  velocity: number | null;
  status: PostApiV1Sprints201DataStatus;
  isActive: boolean;
  /** @nullable */
  createdBy: string | null;
  createdAt: string;
  updatedAt: string;
  project: Project;
  creator: User;
};

export type PostApiV1Sprints201 = {
  data: PostApiV1Sprints201Data;
};

/**
 * @nullable
 */
export type GetApiV1SprintsId200DataStartDate = string | string | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type GetApiV1SprintsId200DataEndDate = string | string | 'null' | null | unknown | null;

export type GetApiV1SprintsId200DataStatus =
  (typeof GetApiV1SprintsId200DataStatus)[keyof typeof GetApiV1SprintsId200DataStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiV1SprintsId200DataStatus = {
  draft: 'draft',
  active: 'active',
  completed: 'completed',
  archived: 'archived',
} as const;

export type GetApiV1SprintsId200Data = {
  id: string;
  projectId: string;
  /** @maxLength 255 */
  name: string;
  /** @nullable */
  goal: string | null;
  /** @nullable */
  startDate?: GetApiV1SprintsId200DataStartDate;
  /** @nullable */
  endDate?: GetApiV1SprintsId200DataEndDate;
  /** @nullable */
  capacity: number | null;
  /** @nullable */
  velocity: number | null;
  status: GetApiV1SprintsId200DataStatus;
  isActive: boolean;
  /** @nullable */
  createdBy: string | null;
  createdAt: string;
  updatedAt: string;
  project: Project;
  creator: User;
};

export type GetApiV1SprintsId200 = {
  data: GetApiV1SprintsId200Data;
};

/**
 * @nullable
 */
export type PatchApiV1SprintsIdBodyStartDate = string | string | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type PatchApiV1SprintsIdBodyEndDate = string | string | 'null' | null | unknown | null;

export type PatchApiV1SprintsIdBodyStatus =
  (typeof PatchApiV1SprintsIdBodyStatus)[keyof typeof PatchApiV1SprintsIdBodyStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1SprintsIdBodyStatus = {
  draft: 'draft',
  active: 'active',
  completed: 'completed',
  archived: 'archived',
} as const;

export type PatchApiV1SprintsIdBody = {
  projectId?: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name?: string;
  /** @nullable */
  goal?: string | null;
  /** @nullable */
  startDate?: PatchApiV1SprintsIdBodyStartDate;
  /** @nullable */
  endDate?: PatchApiV1SprintsIdBodyEndDate;
  /** @nullable */
  capacity?: number | null;
  /** @nullable */
  velocity?: number | null;
  status?: PatchApiV1SprintsIdBodyStatus;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
};

/**
 * @nullable
 */
export type PatchApiV1SprintsId200DataStartDate = string | string | 'null' | null | unknown | null;

/**
 * @nullable
 */
export type PatchApiV1SprintsId200DataEndDate = string | string | 'null' | null | unknown | null;

export type PatchApiV1SprintsId200DataStatus =
  (typeof PatchApiV1SprintsId200DataStatus)[keyof typeof PatchApiV1SprintsId200DataStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1SprintsId200DataStatus = {
  draft: 'draft',
  active: 'active',
  completed: 'completed',
  archived: 'archived',
} as const;

export type PatchApiV1SprintsId200Data = {
  id: string;
  projectId: string;
  /** @maxLength 255 */
  name: string;
  /** @nullable */
  goal: string | null;
  /** @nullable */
  startDate?: PatchApiV1SprintsId200DataStartDate;
  /** @nullable */
  endDate?: PatchApiV1SprintsId200DataEndDate;
  /** @nullable */
  capacity: number | null;
  /** @nullable */
  velocity: number | null;
  status: PatchApiV1SprintsId200DataStatus;
  isActive: boolean;
  /** @nullable */
  createdBy: string | null;
  createdAt: string;
  updatedAt: string;
  project: Project;
  creator: User;
};

export type PatchApiV1SprintsId200 = {
  data: PatchApiV1SprintsId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1SprintsId204 =
  | (typeof DeleteApiV1SprintsId204)[keyof typeof DeleteApiV1SprintsId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1SprintsId204 = {
  null: 'null',
} as const;

export type GetApiV1StatusCategoriesParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1StatusCategories200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1StatusCategories200 = {
  data: StatusCategory[];
  meta: GetApiV1StatusCategories200Meta;
};

export type PostApiV1StatusCategoriesBody = {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name: string;
  /** @nullable */
  description?: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  isActive?: boolean;
};

export type PostApiV1StatusCategories201Data = {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
};

export type PostApiV1StatusCategories201 = {
  data: PostApiV1StatusCategories201Data;
};

export type GetApiV1StatusCategoriesId200Data = {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
};

export type GetApiV1StatusCategoriesId200 = {
  data: GetApiV1StatusCategoriesId200Data;
};

export type PatchApiV1StatusCategoriesIdBody = {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name?: string;
  /** @nullable */
  description?: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  isActive?: boolean;
};

export type PatchApiV1StatusCategoriesId200Data = {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
};

export type PatchApiV1StatusCategoriesId200 = {
  data: PatchApiV1StatusCategoriesId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1StatusCategoriesId204 =
  | (typeof DeleteApiV1StatusCategoriesId204)[keyof typeof DeleteApiV1StatusCategoriesId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1StatusCategoriesId204 = {
  null: 'null',
} as const;

export type GetApiV1StatusesParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1Statuses200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1Statuses200 = {
  data: Status[];
  meta: GetApiV1Statuses200Meta;
};

export type PostApiV1StatusesBodyStatusType =
  (typeof PostApiV1StatusesBodyStatusType)[keyof typeof PostApiV1StatusesBodyStatusType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1StatusesBodyStatusType = {
  todo: 'todo',
  in_progress: 'in_progress',
  done: 'done',
} as const;

export type PostApiV1StatusesBody = {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  organizationId?: string | null;
  statusType: PostApiV1StatusesBodyStatusType;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  order?: number;
  statusCategoryId: string;
  isActive?: boolean;
};

export type PostApiV1Statuses201DataStatusType =
  (typeof PostApiV1Statuses201DataStatusType)[keyof typeof PostApiV1Statuses201DataStatusType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1Statuses201DataStatusType = {
  todo: 'todo',
  in_progress: 'in_progress',
  done: 'done',
} as const;

export type PostApiV1Statuses201Data = {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  organizationId: string | null;
  statusType: PostApiV1Statuses201DataStatusType;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  order: number;
  statusCategoryId: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  organization: Organization;
  statusCategory: StatusCategory;
};

export type PostApiV1Statuses201 = {
  data: PostApiV1Statuses201Data;
};

export type GetApiV1StatusesId200DataStatusType =
  (typeof GetApiV1StatusesId200DataStatusType)[keyof typeof GetApiV1StatusesId200DataStatusType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiV1StatusesId200DataStatusType = {
  todo: 'todo',
  in_progress: 'in_progress',
  done: 'done',
} as const;

export type GetApiV1StatusesId200Data = {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  organizationId: string | null;
  statusType: GetApiV1StatusesId200DataStatusType;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  order: number;
  statusCategoryId: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  organization: Organization;
  statusCategory: StatusCategory;
};

export type GetApiV1StatusesId200 = {
  data: GetApiV1StatusesId200Data;
};

export type PatchApiV1StatusesIdBodyStatusType =
  (typeof PatchApiV1StatusesIdBodyStatusType)[keyof typeof PatchApiV1StatusesIdBodyStatusType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1StatusesIdBodyStatusType = {
  todo: 'todo',
  in_progress: 'in_progress',
  done: 'done',
} as const;

export type PatchApiV1StatusesIdBody = {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name?: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  organizationId?: string | null;
  statusType?: PatchApiV1StatusesIdBodyStatusType;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  order?: number;
  statusCategoryId?: string;
  isActive?: boolean;
};

export type PatchApiV1StatusesId200DataStatusType =
  (typeof PatchApiV1StatusesId200DataStatusType)[keyof typeof PatchApiV1StatusesId200DataStatusType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1StatusesId200DataStatusType = {
  todo: 'todo',
  in_progress: 'in_progress',
  done: 'done',
} as const;

export type PatchApiV1StatusesId200Data = {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  organizationId: string | null;
  statusType: PatchApiV1StatusesId200DataStatusType;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  order: number;
  statusCategoryId: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  organization: Organization;
  statusCategory: StatusCategory;
};

export type PatchApiV1StatusesId200 = {
  data: PatchApiV1StatusesId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1StatusesId204 =
  | (typeof DeleteApiV1StatusesId204)[keyof typeof DeleteApiV1StatusesId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1StatusesId204 = {
  null: 'null',
} as const;

export type GetApiV1TestCaseCommentsParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1TestCaseComments200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1TestCaseComments200 = {
  data: TestCaseComment[];
  meta: GetApiV1TestCaseComments200Meta;
};

export type PostApiV1TestCaseCommentsBody = {
  testCaseId: string;
  /** @minLength 1 */
  content: string;
  authorId: string;
  /** @nullable */
  parentId?: string | null;
  isEdited?: boolean;
};

export type PostApiV1TestCaseComments201Data = {
  id: string;
  testCaseId: string;
  content: string;
  authorId: string;
  /** @nullable */
  parentId: string | null;
  isEdited: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  author: User;
};

export type PostApiV1TestCaseComments201 = {
  data: PostApiV1TestCaseComments201Data;
};

export type GetApiV1TestCaseCommentsId200Data = {
  id: string;
  testCaseId: string;
  content: string;
  authorId: string;
  /** @nullable */
  parentId: string | null;
  isEdited: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  author: User;
};

export type GetApiV1TestCaseCommentsId200 = {
  data: GetApiV1TestCaseCommentsId200Data;
};

export type PatchApiV1TestCaseCommentsIdBody = {
  /** @minLength 1 */
  content?: string;
  isEdited?: boolean;
};

export type PatchApiV1TestCaseCommentsId200Data = {
  id: string;
  testCaseId: string;
  content: string;
  authorId: string;
  /** @nullable */
  parentId: string | null;
  isEdited: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  author: User;
};

export type PatchApiV1TestCaseCommentsId200 = {
  data: PatchApiV1TestCaseCommentsId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1TestCaseCommentsId204 =
  | (typeof DeleteApiV1TestCaseCommentsId204)[keyof typeof DeleteApiV1TestCaseCommentsId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1TestCaseCommentsId204 = {
  null: 'null',
} as const;

export type GetApiV1TestCaseHistoryParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1TestCaseHistory200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1TestCaseHistory200 = {
  data: TestCaseHistory[];
  meta: GetApiV1TestCaseHistory200Meta;
};

export type GetApiV1TestCaseHistoryId200DataChangeType =
  (typeof GetApiV1TestCaseHistoryId200DataChangeType)[keyof typeof GetApiV1TestCaseHistoryId200DataChangeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiV1TestCaseHistoryId200DataChangeType = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  RESTORE: 'RESTORE',
  COMMENT_ADDED: 'COMMENT_ADDED',
  COMMENT_UPDATED: 'COMMENT_UPDATED',
  COMMENT_DELETED: 'COMMENT_DELETED',
  ATTACHMENT_ADDED: 'ATTACHMENT_ADDED',
  ATTACHMENT_DELETED: 'ATTACHMENT_DELETED',
  EXECUTION_ADDED: 'EXECUTION_ADDED',
  EXECUTION_UPDATED: 'EXECUTION_UPDATED',
} as const;

export type GetApiV1TestCaseHistoryId200DataChangedFieldsOldValue =
  | string
  | number
  | boolean
  | string[]
  | 'null'
  | null;

export type GetApiV1TestCaseHistoryId200DataChangedFieldsNewValue =
  | string
  | number
  | boolean
  | string[]
  | 'null'
  | null;

/**
 * @nullable
 */
export type GetApiV1TestCaseHistoryId200DataChangedFields = {
  [key: string]: {
    oldValue: GetApiV1TestCaseHistoryId200DataChangedFieldsOldValue;
    newValue: GetApiV1TestCaseHistoryId200DataChangedFieldsNewValue;
  };
} | null;

export type GetApiV1TestCaseHistoryId200DataMetadataComment = {
  id: string;
  content: string;
};

export type GetApiV1TestCaseHistoryId200DataMetadataAttachment = {
  id: string;
  filename: string;
  size?: number;
  mimeType?: string;
};

export type GetApiV1TestCaseHistoryId200DataMetadataExecution = {
  id: string;
  status: string;
  result?: string;
};

/**
 * @nullable
 */
export type GetApiV1TestCaseHistoryId200DataMetadata = {
  comment?: GetApiV1TestCaseHistoryId200DataMetadataComment;
  attachment?: GetApiV1TestCaseHistoryId200DataMetadataAttachment;
  execution?: GetApiV1TestCaseHistoryId200DataMetadataExecution;
  [key: string]: unknown;
} | null;

export type GetApiV1TestCaseHistoryId200DataTestCaseStepsItem = {
  step: string;
  expectedResult: string;
};

export type GetApiV1TestCaseHistoryId200DataTestCaseEstimate = {
  [key: string]: unknown;
};

/**
 * @nullable
 */
export type GetApiV1TestCaseHistoryId200DataTestCase = {
  id?: string;
  /** @maxLength 500 */
  title?: string;
  /** @nullable */
  description?: string | null;
  projectId?: string;
  /** @nullable */
  assigneeId?: string | null;
  /** @nullable */
  reporterId?: string | null;
  /** @nullable */
  workItemId?: string | null;
  /** @nullable */
  copiedFromId?: string | null;
  /** @nullable */
  preconditions?: string | null;
  steps?: GetApiV1TestCaseHistoryId200DataTestCaseStepsItem[];
  tags?: string[];
  estimate?: GetApiV1TestCaseHistoryId200DataTestCaseEstimate;
  order?: number;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
  createdAt?: string;
  updatedAt?: string;
  project?: Project;
  assignee?: User;
  reporter?: User;
  creator?: User;
  workItem?: WorkItem;
} | null;

export type GetApiV1TestCaseHistoryId200Data = {
  id: string;
  testCaseId: string;
  changeType: GetApiV1TestCaseHistoryId200DataChangeType;
  changedBy: string;
  /** @nullable */
  changedFields: GetApiV1TestCaseHistoryId200DataChangedFields;
  /** @nullable */
  metadata: GetApiV1TestCaseHistoryId200DataMetadata;
  /**
   * @maxLength 500
   * @nullable
   */
  summary: string | null;
  createdAt: string;
  user: User;
  /** @nullable */
  testCase: GetApiV1TestCaseHistoryId200DataTestCase;
};

export type GetApiV1TestCaseHistoryId200 = {
  data: GetApiV1TestCaseHistoryId200Data;
};

export type GetApiV1TestCaseWorkItemLinksParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1TestCaseWorkItemLinks200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1TestCaseWorkItemLinks200 = {
  data: TestCaseWorkItemLink[];
  meta: GetApiV1TestCaseWorkItemLinks200Meta;
};

export type PostApiV1TestCaseWorkItemLinksBodyLinkType =
  (typeof PostApiV1TestCaseWorkItemLinksBodyLinkType)[keyof typeof PostApiV1TestCaseWorkItemLinksBodyLinkType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1TestCaseWorkItemLinksBodyLinkType = {
  tests: 'tests',
  tested_by: 'tested_by',
  validates: 'validates',
  verifies: 'verifies',
  relates_to: 'relates_to',
} as const;

export type PostApiV1TestCaseWorkItemLinksBody = {
  testCaseId: string;
  workItemId: string;
  linkType: PostApiV1TestCaseWorkItemLinksBodyLinkType;
  createdById: string;
};

export type PostApiV1TestCaseWorkItemLinks201DataLinkType =
  (typeof PostApiV1TestCaseWorkItemLinks201DataLinkType)[keyof typeof PostApiV1TestCaseWorkItemLinks201DataLinkType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1TestCaseWorkItemLinks201DataLinkType = {
  tests: 'tests',
  tested_by: 'tested_by',
  validates: 'validates',
  verifies: 'verifies',
  relates_to: 'relates_to',
} as const;

export type PostApiV1TestCaseWorkItemLinks201Data = {
  id: string;
  testCaseId: string;
  workItemId: string;
  linkType: PostApiV1TestCaseWorkItemLinks201DataLinkType;
  createdById: string;
  createdAt: string;
  createdBy: User;
};

export type PostApiV1TestCaseWorkItemLinks201 = {
  data: PostApiV1TestCaseWorkItemLinks201Data;
};

export type GetApiV1TestCaseWorkItemLinksId200DataLinkType =
  (typeof GetApiV1TestCaseWorkItemLinksId200DataLinkType)[keyof typeof GetApiV1TestCaseWorkItemLinksId200DataLinkType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiV1TestCaseWorkItemLinksId200DataLinkType = {
  tests: 'tests',
  tested_by: 'tested_by',
  validates: 'validates',
  verifies: 'verifies',
  relates_to: 'relates_to',
} as const;

export type GetApiV1TestCaseWorkItemLinksId200Data = {
  id: string;
  testCaseId: string;
  workItemId: string;
  linkType: GetApiV1TestCaseWorkItemLinksId200DataLinkType;
  createdById: string;
  createdAt: string;
  createdBy: User;
};

export type GetApiV1TestCaseWorkItemLinksId200 = {
  data: GetApiV1TestCaseWorkItemLinksId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1TestCaseWorkItemLinksId204 =
  | (typeof DeleteApiV1TestCaseWorkItemLinksId204)[keyof typeof DeleteApiV1TestCaseWorkItemLinksId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1TestCaseWorkItemLinksId204 = {
  null: 'null',
} as const;

export type GetApiV1TestCasesParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1TestCases200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1TestCases200 = {
  data: TestCase[];
  meta: GetApiV1TestCases200Meta;
};

export type PostApiV1TestCasesBodyStepsItem = {
  step: string;
  expectedResult: string;
};

export type PostApiV1TestCasesBodyEstimate = { [key: string]: unknown };

export type PostApiV1TestCasesBody = {
  /**
   * @minLength 1
   * @maxLength 500
   */
  title: string;
  /** @nullable */
  description?: string | null;
  projectId: string;
  /** @nullable */
  assigneeId?: string | null;
  /** @nullable */
  reporterId?: string | null;
  /** @nullable */
  workItemId?: string | null;
  /** @nullable */
  copiedFromId?: string | null;
  /** @nullable */
  preconditions?: string | null;
  steps?: PostApiV1TestCasesBodyStepsItem[];
  tags?: string[];
  estimate?: PostApiV1TestCasesBodyEstimate;
  order?: number;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
  /** @nullable */
  priorityId?: string | null;
  /** @nullable */
  statusId?: string | null;
};

export type PostApiV1TestCases201DataStepsItem = {
  step: string;
  expectedResult: string;
};

export type PostApiV1TestCases201DataEstimate = { [key: string]: unknown };

export type PostApiV1TestCases201Data = {
  id: string;
  /** @maxLength 500 */
  title: string;
  /** @nullable */
  description: string | null;
  projectId: string;
  /** @nullable */
  assigneeId: string | null;
  /** @nullable */
  reporterId: string | null;
  /** @nullable */
  workItemId: string | null;
  /** @nullable */
  copiedFromId: string | null;
  /** @nullable */
  preconditions: string | null;
  steps?: PostApiV1TestCases201DataStepsItem[];
  tags?: string[];
  estimate?: PostApiV1TestCases201DataEstimate;
  order: number;
  isActive: boolean;
  /** @nullable */
  createdBy: string | null;
  createdAt: string;
  updatedAt: string;
  project?: Project;
  assignee?: User;
  reporter?: User;
  creator?: User;
  workItem?: WorkItem;
};

export type PostApiV1TestCases201 = {
  data: PostApiV1TestCases201Data;
};

export type GetApiV1TestCasesId200DataStepsItem = {
  step: string;
  expectedResult: string;
};

export type GetApiV1TestCasesId200DataEstimate = { [key: string]: unknown };

export type GetApiV1TestCasesId200Data = {
  id: string;
  /** @maxLength 500 */
  title: string;
  /** @nullable */
  description: string | null;
  projectId: string;
  /** @nullable */
  assigneeId: string | null;
  /** @nullable */
  reporterId: string | null;
  /** @nullable */
  workItemId: string | null;
  /** @nullable */
  copiedFromId: string | null;
  /** @nullable */
  preconditions: string | null;
  steps?: GetApiV1TestCasesId200DataStepsItem[];
  tags?: string[];
  estimate?: GetApiV1TestCasesId200DataEstimate;
  order: number;
  isActive: boolean;
  /** @nullable */
  createdBy: string | null;
  createdAt: string;
  updatedAt: string;
  project?: Project;
  assignee?: User;
  reporter?: User;
  creator?: User;
  workItem?: WorkItem;
};

export type GetApiV1TestCasesId200 = {
  data: GetApiV1TestCasesId200Data;
};

export type PatchApiV1TestCasesIdBodyStepsItem = {
  step: string;
  expectedResult: string;
};

export type PatchApiV1TestCasesIdBodyEstimate = { [key: string]: unknown };

export type PatchApiV1TestCasesIdBody = {
  /**
   * @minLength 1
   * @maxLength 500
   */
  title?: string;
  /** @nullable */
  description?: string | null;
  projectId?: string;
  /** @nullable */
  assigneeId?: string | null;
  /** @nullable */
  reporterId?: string | null;
  /** @nullable */
  workItemId?: string | null;
  /** @nullable */
  copiedFromId?: string | null;
  /** @nullable */
  preconditions?: string | null;
  steps?: PatchApiV1TestCasesIdBodyStepsItem[];
  tags?: string[];
  estimate?: PatchApiV1TestCasesIdBodyEstimate;
  order?: number;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
  /** @nullable */
  priorityId?: string | null;
  /** @nullable */
  statusId?: string | null;
};

export type PatchApiV1TestCasesId200DataStepsItem = {
  step: string;
  expectedResult: string;
};

export type PatchApiV1TestCasesId200DataEstimate = { [key: string]: unknown };

export type PatchApiV1TestCasesId200Data = {
  id: string;
  /** @maxLength 500 */
  title: string;
  /** @nullable */
  description: string | null;
  projectId: string;
  /** @nullable */
  assigneeId: string | null;
  /** @nullable */
  reporterId: string | null;
  /** @nullable */
  workItemId: string | null;
  /** @nullable */
  copiedFromId: string | null;
  /** @nullable */
  preconditions: string | null;
  steps?: PatchApiV1TestCasesId200DataStepsItem[];
  tags?: string[];
  estimate?: PatchApiV1TestCasesId200DataEstimate;
  order: number;
  isActive: boolean;
  /** @nullable */
  createdBy: string | null;
  createdAt: string;
  updatedAt: string;
  project?: Project;
  assignee?: User;
  reporter?: User;
  creator?: User;
  workItem?: WorkItem;
};

export type PatchApiV1TestCasesId200 = {
  data: PatchApiV1TestCasesId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1TestCasesId204 =
  | (typeof DeleteApiV1TestCasesId204)[keyof typeof DeleteApiV1TestCasesId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1TestCasesId204 = {
  null: 'null',
} as const;

export type GetApiV1TestPlanCommentsParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1TestPlanComments200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1TestPlanComments200 = {
  data: TestPlanComment[];
  meta: GetApiV1TestPlanComments200Meta;
};

export type PostApiV1TestPlanCommentsBody = {
  testPlanId: string;
  /** @minLength 1 */
  content: string;
  authorId: string;
  /** @nullable */
  parentId?: string | null;
  isEdited?: boolean;
};

export type PostApiV1TestPlanComments201Data = {
  id: string;
  testPlanId: string;
  content: string;
  authorId: string;
  /** @nullable */
  parentId: string | null;
  isEdited: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  author: User;
};

export type PostApiV1TestPlanComments201 = {
  data: PostApiV1TestPlanComments201Data;
};

export type GetApiV1TestPlanCommentsId200Data = {
  id: string;
  testPlanId: string;
  content: string;
  authorId: string;
  /** @nullable */
  parentId: string | null;
  isEdited: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  author: User;
};

export type GetApiV1TestPlanCommentsId200 = {
  data: GetApiV1TestPlanCommentsId200Data;
};

export type PatchApiV1TestPlanCommentsIdBody = {
  /** @minLength 1 */
  content?: string;
  isEdited?: boolean;
};

export type PatchApiV1TestPlanCommentsId200Data = {
  id: string;
  testPlanId: string;
  content: string;
  authorId: string;
  /** @nullable */
  parentId: string | null;
  isEdited: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  author: User;
};

export type PatchApiV1TestPlanCommentsId200 = {
  data: PatchApiV1TestPlanCommentsId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1TestPlanCommentsId204 =
  | (typeof DeleteApiV1TestPlanCommentsId204)[keyof typeof DeleteApiV1TestPlanCommentsId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1TestPlanCommentsId204 = {
  null: 'null',
} as const;

export type GetApiV1TestPlansParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1TestPlans200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1TestPlans200 = {
  data: TestPlan[];
  meta: GetApiV1TestPlans200Meta;
};

export type PostApiV1TestPlansBodyStatus =
  (typeof PostApiV1TestPlansBodyStatus)[keyof typeof PostApiV1TestPlansBodyStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1TestPlansBodyStatus = {
  draft: 'draft',
  active: 'active',
  archived: 'archived',
} as const;

export type PostApiV1TestPlansBody = {
  /**
   * @minLength 1
   * @maxLength 255
   */
  name: string;
  /**
   * @maxLength 1000
   * @nullable
   */
  description?: string | null;
  projectId: string;
  status?: PostApiV1TestPlansBodyStatus;
  createdById: string;
  isActive?: boolean;
};

export type PostApiV1TestPlans201DataStatus =
  (typeof PostApiV1TestPlans201DataStatus)[keyof typeof PostApiV1TestPlans201DataStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1TestPlans201DataStatus = {
  draft: 'draft',
  active: 'active',
  archived: 'archived',
} as const;

export type PostApiV1TestPlans201Data = {
  id: string;
  /** @maxLength 255 */
  name: string;
  /**
   * @maxLength 1000
   * @nullable
   */
  description: string | null;
  projectId: string;
  status: PostApiV1TestPlans201DataStatus;
  createdById: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  project: Project;
  createdBy: User;
};

export type PostApiV1TestPlans201 = {
  data: PostApiV1TestPlans201Data;
};

export type GetApiV1TestPlansId200DataStatus =
  (typeof GetApiV1TestPlansId200DataStatus)[keyof typeof GetApiV1TestPlansId200DataStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiV1TestPlansId200DataStatus = {
  draft: 'draft',
  active: 'active',
  archived: 'archived',
} as const;

export type GetApiV1TestPlansId200Data = {
  id: string;
  /** @maxLength 255 */
  name: string;
  /**
   * @maxLength 1000
   * @nullable
   */
  description: string | null;
  projectId: string;
  status: GetApiV1TestPlansId200DataStatus;
  createdById: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  project: Project;
  createdBy: User;
};

export type GetApiV1TestPlansId200 = {
  data: GetApiV1TestPlansId200Data;
};

export type PatchApiV1TestPlansIdBodyStatus =
  (typeof PatchApiV1TestPlansIdBodyStatus)[keyof typeof PatchApiV1TestPlansIdBodyStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1TestPlansIdBodyStatus = {
  draft: 'draft',
  active: 'active',
  archived: 'archived',
} as const;

export type PatchApiV1TestPlansIdBody = {
  /**
   * @minLength 1
   * @maxLength 255
   */
  name?: string;
  /**
   * @maxLength 1000
   * @nullable
   */
  description?: string | null;
  projectId?: string;
  status?: PatchApiV1TestPlansIdBodyStatus;
  createdById?: string;
  isActive?: boolean;
};

export type PatchApiV1TestPlansId200DataStatus =
  (typeof PatchApiV1TestPlansId200DataStatus)[keyof typeof PatchApiV1TestPlansId200DataStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1TestPlansId200DataStatus = {
  draft: 'draft',
  active: 'active',
  archived: 'archived',
} as const;

export type PatchApiV1TestPlansId200Data = {
  id: string;
  /** @maxLength 255 */
  name: string;
  /**
   * @maxLength 1000
   * @nullable
   */
  description: string | null;
  projectId: string;
  status: PatchApiV1TestPlansId200DataStatus;
  createdById: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  project: Project;
  createdBy: User;
};

export type PatchApiV1TestPlansId200 = {
  data: PatchApiV1TestPlansId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1TestPlansId204 =
  | (typeof DeleteApiV1TestPlansId204)[keyof typeof DeleteApiV1TestPlansId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1TestPlansId204 = {
  null: 'null',
} as const;

export type GetApiV1TestSuiteCommentsParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1TestSuiteComments200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1TestSuiteComments200 = {
  data: TestSuiteComment[];
  meta: GetApiV1TestSuiteComments200Meta;
};

export type PostApiV1TestSuiteCommentsBody = {
  testSuiteId: string;
  /** @minLength 1 */
  content: string;
  authorId: string;
  /** @nullable */
  parentId?: string | null;
  isEdited?: boolean;
};

export type PostApiV1TestSuiteComments201Data = {
  id: string;
  testSuiteId: string;
  content: string;
  authorId: string;
  /** @nullable */
  parentId: string | null;
  isEdited: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  author: User;
};

export type PostApiV1TestSuiteComments201 = {
  data: PostApiV1TestSuiteComments201Data;
};

export type GetApiV1TestSuiteCommentsId200Data = {
  id: string;
  testSuiteId: string;
  content: string;
  authorId: string;
  /** @nullable */
  parentId: string | null;
  isEdited: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  author: User;
};

export type GetApiV1TestSuiteCommentsId200 = {
  data: GetApiV1TestSuiteCommentsId200Data;
};

export type PatchApiV1TestSuiteCommentsIdBody = {
  /** @minLength 1 */
  content?: string;
  isEdited?: boolean;
};

export type PatchApiV1TestSuiteCommentsId200Data = {
  id: string;
  testSuiteId: string;
  content: string;
  authorId: string;
  /** @nullable */
  parentId: string | null;
  isEdited: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  author: User;
};

export type PatchApiV1TestSuiteCommentsId200 = {
  data: PatchApiV1TestSuiteCommentsId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1TestSuiteCommentsId204 =
  | (typeof DeleteApiV1TestSuiteCommentsId204)[keyof typeof DeleteApiV1TestSuiteCommentsId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1TestSuiteCommentsId204 = {
  null: 'null',
} as const;

export type GetApiV1TestSuitesParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1TestSuites200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1TestSuites200 = {
  data: TestSuite[];
  meta: GetApiV1TestSuites200Meta;
};

export type PostApiV1TestSuitesBodyType =
  (typeof PostApiV1TestSuitesBodyType)[keyof typeof PostApiV1TestSuitesBodyType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1TestSuitesBodyType = {
  static: 'static',
  requirement: 'requirement',
  query: 'query',
} as const;

export type PostApiV1TestSuitesBody = {
  /**
   * @minLength 1
   * @maxLength 255
   */
  name: string;
  /**
   * @maxLength 1000
   * @nullable
   */
  description?: string | null;
  testPlanId: string;
  type?: PostApiV1TestSuitesBodyType;
  /**
   * @maxLength 1000
   * @nullable
   */
  requirementQuery?: string | null;
  /** @nullable */
  parentSuiteId?: string | null;
  /** @nullable */
  copiedFromId?: string | null;
  order?: number;
  isActive?: boolean;
  includeTestCases?: boolean;
  sourceSuiteId?: string;
  selectedTestCaseIds?: string[];
  linkTestCases?: boolean;
};

export type PostApiV1TestSuites201DataType =
  (typeof PostApiV1TestSuites201DataType)[keyof typeof PostApiV1TestSuites201DataType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1TestSuites201DataType = {
  static: 'static',
  requirement: 'requirement',
  query: 'query',
} as const;

export type PostApiV1TestSuites201DataTestPlanStatus =
  (typeof PostApiV1TestSuites201DataTestPlanStatus)[keyof typeof PostApiV1TestSuites201DataTestPlanStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1TestSuites201DataTestPlanStatus = {
  draft: 'draft',
  active: 'active',
  archived: 'archived',
} as const;

/**
 * @nullable
 */
export type PostApiV1TestSuites201DataTestPlan = {
  id?: string;
  /** @maxLength 255 */
  name?: string;
  /**
   * @maxLength 1000
   * @nullable
   */
  description?: string | null;
  projectId?: string;
  status?: PostApiV1TestSuites201DataTestPlanStatus;
  createdById?: string;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
  project?: Project;
  createdBy?: User;
} | null;

export type PostApiV1TestSuites201Data = {
  id: string;
  /** @maxLength 255 */
  name: string;
  /**
   * @maxLength 1000
   * @nullable
   */
  description: string | null;
  testPlanId: string;
  type: PostApiV1TestSuites201DataType;
  /**
   * @maxLength 1000
   * @nullable
   */
  requirementQuery: string | null;
  /** @nullable */
  parentSuiteId: string | null;
  /** @nullable */
  copiedFromId: string | null;
  order: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  /** @nullable */
  testPlan: PostApiV1TestSuites201DataTestPlan;
};

export type PostApiV1TestSuites201 = {
  data: PostApiV1TestSuites201Data;
};

export type GetApiV1TestSuitesId200DataType =
  (typeof GetApiV1TestSuitesId200DataType)[keyof typeof GetApiV1TestSuitesId200DataType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiV1TestSuitesId200DataType = {
  static: 'static',
  requirement: 'requirement',
  query: 'query',
} as const;

export type GetApiV1TestSuitesId200DataTestPlanStatus =
  (typeof GetApiV1TestSuitesId200DataTestPlanStatus)[keyof typeof GetApiV1TestSuitesId200DataTestPlanStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiV1TestSuitesId200DataTestPlanStatus = {
  draft: 'draft',
  active: 'active',
  archived: 'archived',
} as const;

/**
 * @nullable
 */
export type GetApiV1TestSuitesId200DataTestPlan = {
  id?: string;
  /** @maxLength 255 */
  name?: string;
  /**
   * @maxLength 1000
   * @nullable
   */
  description?: string | null;
  projectId?: string;
  status?: GetApiV1TestSuitesId200DataTestPlanStatus;
  createdById?: string;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
  project?: Project;
  createdBy?: User;
} | null;

export type GetApiV1TestSuitesId200Data = {
  id: string;
  /** @maxLength 255 */
  name: string;
  /**
   * @maxLength 1000
   * @nullable
   */
  description: string | null;
  testPlanId: string;
  type: GetApiV1TestSuitesId200DataType;
  /**
   * @maxLength 1000
   * @nullable
   */
  requirementQuery: string | null;
  /** @nullable */
  parentSuiteId: string | null;
  /** @nullable */
  copiedFromId: string | null;
  order: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  /** @nullable */
  testPlan: GetApiV1TestSuitesId200DataTestPlan;
};

export type GetApiV1TestSuitesId200 = {
  data: GetApiV1TestSuitesId200Data;
};

export type PatchApiV1TestSuitesIdBodyType =
  (typeof PatchApiV1TestSuitesIdBodyType)[keyof typeof PatchApiV1TestSuitesIdBodyType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1TestSuitesIdBodyType = {
  static: 'static',
  requirement: 'requirement',
  query: 'query',
} as const;

export type PatchApiV1TestSuitesIdBody = {
  /**
   * @minLength 1
   * @maxLength 255
   */
  name?: string;
  /**
   * @maxLength 1000
   * @nullable
   */
  description?: string | null;
  testPlanId?: string;
  type?: PatchApiV1TestSuitesIdBodyType;
  /**
   * @maxLength 1000
   * @nullable
   */
  requirementQuery?: string | null;
  /** @nullable */
  parentSuiteId?: string | null;
  /** @nullable */
  copiedFromId?: string | null;
  order?: number;
  isActive?: boolean;
  includeTestCases?: boolean;
  sourceSuiteId?: string;
  selectedTestCaseIds?: string[];
  linkTestCases?: boolean;
};

export type PatchApiV1TestSuitesId200DataType =
  (typeof PatchApiV1TestSuitesId200DataType)[keyof typeof PatchApiV1TestSuitesId200DataType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1TestSuitesId200DataType = {
  static: 'static',
  requirement: 'requirement',
  query: 'query',
} as const;

export type PatchApiV1TestSuitesId200DataTestPlanStatus =
  (typeof PatchApiV1TestSuitesId200DataTestPlanStatus)[keyof typeof PatchApiV1TestSuitesId200DataTestPlanStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1TestSuitesId200DataTestPlanStatus = {
  draft: 'draft',
  active: 'active',
  archived: 'archived',
} as const;

/**
 * @nullable
 */
export type PatchApiV1TestSuitesId200DataTestPlan = {
  id?: string;
  /** @maxLength 255 */
  name?: string;
  /**
   * @maxLength 1000
   * @nullable
   */
  description?: string | null;
  projectId?: string;
  status?: PatchApiV1TestSuitesId200DataTestPlanStatus;
  createdById?: string;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
  project?: Project;
  createdBy?: User;
} | null;

export type PatchApiV1TestSuitesId200Data = {
  id: string;
  /** @maxLength 255 */
  name: string;
  /**
   * @maxLength 1000
   * @nullable
   */
  description: string | null;
  testPlanId: string;
  type: PatchApiV1TestSuitesId200DataType;
  /**
   * @maxLength 1000
   * @nullable
   */
  requirementQuery: string | null;
  /** @nullable */
  parentSuiteId: string | null;
  /** @nullable */
  copiedFromId: string | null;
  order: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  /** @nullable */
  testPlan: PatchApiV1TestSuitesId200DataTestPlan;
};

export type PatchApiV1TestSuitesId200 = {
  data: PatchApiV1TestSuitesId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1TestSuitesId204 =
  | (typeof DeleteApiV1TestSuitesId204)[keyof typeof DeleteApiV1TestSuitesId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1TestSuitesId204 = {
  null: 'null',
} as const;

export type GetApiV1TestSuitesIdTestCases200ItemStepsItem = {
  step: string;
  expectedResult: string;
};

export type GetApiV1TestSuitesIdTestCases200ItemEstimate = {
  [key: string]: unknown;
};

export type GetApiV1TestSuitesIdTestCases200ItemCreatedAt = string | string;

export type GetApiV1TestSuitesIdTestCases200ItemUpdatedAt = string | string;

/**
 * @nullable
 */
export type GetApiV1TestSuitesIdTestCases200ItemSuiteStatus = {
  id: string;
  name: string;
} | null;

/**
 * @nullable
 */
export type GetApiV1TestSuitesIdTestCases200ItemSuitePriority = {
  id: string;
  name: string;
} | null;

export type GetApiV1TestSuitesIdTestCases200Item = {
  id: string;
  /** @maxLength 500 */
  title: string;
  /** @nullable */
  description: string | null;
  projectId: string;
  /** @nullable */
  assigneeId: string | null;
  /** @nullable */
  reporterId: string | null;
  /** @nullable */
  workItemId: string | null;
  /** @nullable */
  copiedFromId: string | null;
  /** @nullable */
  preconditions: string | null;
  steps?: GetApiV1TestSuitesIdTestCases200ItemStepsItem[];
  tags?: string[];
  estimate?: GetApiV1TestSuitesIdTestCases200ItemEstimate;
  order: number;
  isActive: boolean;
  /** @nullable */
  createdBy: string | null;
  createdAt: GetApiV1TestSuitesIdTestCases200ItemCreatedAt;
  updatedAt: GetApiV1TestSuitesIdTestCases200ItemUpdatedAt;
  project?: Project;
  assignee?: User;
  reporter?: User;
  creator?: User;
  workItem?: WorkItem;
  /** @nullable */
  suiteStatus: GetApiV1TestSuitesIdTestCases200ItemSuiteStatus;
  /** @nullable */
  suitePriority: GetApiV1TestSuitesIdTestCases200ItemSuitePriority;
  suiteOrder: number;
};

export type PostApiV1TestSuitesIdTestCasesLinkBody = {
  /** @minItems 1 */
  testCaseIds: string[];
  /** @nullable */
  statusId?: string | null;
  /** @nullable */
  priorityId?: string | null;
};

export type PostApiV1TestSuitesIdTestCasesLink201 = {
  message: string;
};

/**
 * @nullable
 */
export type DeleteApiV1TestSuitesIdTestCasesTestCaseId204 =
  | (typeof DeleteApiV1TestSuitesIdTestCasesTestCaseId204)[keyof typeof DeleteApiV1TestSuitesIdTestCasesTestCaseId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1TestSuitesIdTestCasesTestCaseId204 = {
  null: 'null',
} as const;

export type PatchApiV1TestSuitesIdTestCasesTestCaseIdBody = {
  /** @nullable */
  statusId?: string | null;
  /** @nullable */
  priorityId?: string | null;
  order?: number;
};

export type PatchApiV1TestSuitesIdTestCasesTestCaseId200 = {
  message: string;
};

export type GetApiV1TestSuitesIdAvailableTestCasesParams = {
  projectId?: string;
};

export type GetApiV1TestSuitesIdAvailableTestCases200Item = {
  id: string;
  title: string;
  /** @nullable */
  description: string | null;
  projectId: string;
  project?: Project;
  assignee?: User;
};

export type GetApiV1TestSuitesAllTestCasesParams = {
  projectId?: string;
  search?: string;
  limit?: number;
  offset?: number;
};

export type GetApiV1TestSuitesAllTestCases200DataItemTestSuitesItem = {
  id: string;
  name: string;
};

export type GetApiV1TestSuitesAllTestCases200DataItem = {
  id: string;
  title: string;
  /** @nullable */
  description: string | null;
  projectId: string;
  project?: Project;
  assignee?: User;
  testSuites?: GetApiV1TestSuitesAllTestCases200DataItemTestSuitesItem[];
};

export type GetApiV1TestSuitesAllTestCases200 = {
  data: GetApiV1TestSuitesAllTestCases200DataItem[];
  total: number;
};

export type GetApiV1TestSuitesAllTestSuitesParams = {
  testPlanId?: string;
  search?: string;
  limit?: number;
  offset?: number;
};

export type GetApiV1TestSuitesAllTestSuites200DataItemTestPlan = {
  id: string;
  name: string;
};

export type GetApiV1TestSuitesAllTestSuites200DataItem = {
  id: string;
  name: string;
  /** @nullable */
  description: string | null;
  testPlanId: string;
  testPlan?: GetApiV1TestSuitesAllTestSuites200DataItemTestPlan;
  testCaseCount: number;
};

export type GetApiV1TestSuitesAllTestSuites200 = {
  data: GetApiV1TestSuitesAllTestSuites200DataItem[];
  total: number;
};

export type PostApiV1UserFcmTokensBody = {
  /** @minLength 1 */
  fcmToken: string;
  deviceType?: string;
  deviceModel?: string;
  os?: string;
  browser?: string;
};

export type PostApiV1UserFcmTokens201 = {
  id: string;
  userId: string;
  fcmToken: string;
  /**
   * @maxLength 50
   * @nullable
   */
  deviceType: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  deviceModel: string | null;
  /**
   * @maxLength 50
   * @nullable
   */
  os: string | null;
  /**
   * @maxLength 100
   * @nullable
   */
  browser: string | null;
  lastActive: string;
  createdAt: string;
  updatedAt: string;
};

export type GetApiV1UserFcmTokens200Item = {
  id: string;
  userId: string;
  fcmToken: string;
  /**
   * @maxLength 50
   * @nullable
   */
  deviceType: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  deviceModel: string | null;
  /**
   * @maxLength 50
   * @nullable
   */
  os: string | null;
  /**
   * @maxLength 100
   * @nullable
   */
  browser: string | null;
  lastActive: string;
  createdAt: string;
  updatedAt: string;
};

export type GetApiV1UsersParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1Users200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1Users200 = {
  data: User[];
  meta: GetApiV1Users200Meta;
};

export type GetApiV1UsersId200Data = {
  id: string;
  /** @maxLength 255 */
  email: string;
  /** @maxLength 255 */
  firstName: string;
  /** @maxLength 255 */
  lastName: string;
  /** @maxLength 255 */
  displayName: string;
  /** @maxLength 255 */
  password: string;
  isActive: boolean;
  isStaff: boolean;
  isEmailVerified: boolean;
  /** @nullable */
  lastLogin: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  avatarUrl: string | null;
  /** @nullable */
  bio: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  jobTitle: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  department: string | null;
  /**
   * @maxLength 50
   * @nullable
   */
  phone: string | null;
  /** @maxLength 50 */
  timezone: string;
  /** @maxLength 10 */
  language: string;
  /** @maxLength 20 */
  dateFormat: string;
  /** @maxLength 10 */
  timeFormat: string;
  createdAt: string;
  updatedAt: string;
  /**
   * @maxLength 512
   * @nullable
   */
  passwordResetToken: string | null;
  /** @nullable */
  passwordResetExpires: string | null;
  /**
   * @maxLength 512
   * @nullable
   */
  emailVerificationToken: string | null;
  loginAttempts: number;
  /** @nullable */
  lockedUntil: string | null;
};

export type GetApiV1UsersId200 = {
  data: GetApiV1UsersId200Data;
};

export type PatchApiV1UsersIdBody = {
  /** @maxLength 255 */
  email?: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  firstName?: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  lastName?: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  displayName?: string;
  /**
   * @minLength 8
   * @maxLength 255
   */
  password?: string;
  isActive?: boolean;
  isStaff?: boolean;
  isEmailVerified?: boolean;
  /** @nullable */
  lastLogin?: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  avatarUrl?: string | null;
  /** @nullable */
  bio?: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  jobTitle?: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  department?: string | null;
  /**
   * @minLength 10
   * @maxLength 50
   * @nullable
   */
  phone?: string | null;
  /** @maxLength 50 */
  timezone?: string;
  /** @maxLength 10 */
  language?: string;
  /** @maxLength 20 */
  dateFormat?: string;
  /** @maxLength 10 */
  timeFormat?: string;
  /**
   * @maxLength 512
   * @nullable
   */
  passwordResetToken?: string | null;
  /** @nullable */
  passwordResetExpires?: string | null;
  /**
   * @maxLength 512
   * @nullable
   */
  emailVerificationToken?: string | null;
  loginAttempts?: number;
  /** @nullable */
  lockedUntil?: string | null;
};

export type PatchApiV1UsersId200Data = {
  id: string;
  /** @maxLength 255 */
  email: string;
  /** @maxLength 255 */
  firstName: string;
  /** @maxLength 255 */
  lastName: string;
  /** @maxLength 255 */
  displayName: string;
  /** @maxLength 255 */
  password: string;
  isActive: boolean;
  isStaff: boolean;
  isEmailVerified: boolean;
  /** @nullable */
  lastLogin: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  avatarUrl: string | null;
  /** @nullable */
  bio: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  jobTitle: string | null;
  /**
   * @maxLength 255
   * @nullable
   */
  department: string | null;
  /**
   * @maxLength 50
   * @nullable
   */
  phone: string | null;
  /** @maxLength 50 */
  timezone: string;
  /** @maxLength 10 */
  language: string;
  /** @maxLength 20 */
  dateFormat: string;
  /** @maxLength 10 */
  timeFormat: string;
  createdAt: string;
  updatedAt: string;
  /**
   * @maxLength 512
   * @nullable
   */
  passwordResetToken: string | null;
  /** @nullable */
  passwordResetExpires: string | null;
  /**
   * @maxLength 512
   * @nullable
   */
  emailVerificationToken: string | null;
  loginAttempts: number;
  /** @nullable */
  lockedUntil: string | null;
};

export type PatchApiV1UsersId200 = {
  data: PatchApiV1UsersId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1UsersId204 =
  | (typeof DeleteApiV1UsersId204)[keyof typeof DeleteApiV1UsersId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1UsersId204 = {
  null: 'null',
} as const;

export type GetApiV1WorkItemCommentsParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1WorkItemComments200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1WorkItemComments200 = {
  data: WorkItemComment[];
  meta: GetApiV1WorkItemComments200Meta;
};

export type PostApiV1WorkItemCommentsBody = {
  workItemId: string;
  /** @minLength 1 */
  content: string;
  authorId: string;
  /** @nullable */
  parentId?: string | null;
  isEdited?: boolean;
};

export type PostApiV1WorkItemComments201Data = {
  id: string;
  workItemId: string;
  content: string;
  authorId: string;
  /** @nullable */
  parentId: string | null;
  isEdited: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  author: User;
};

export type PostApiV1WorkItemComments201 = {
  data: PostApiV1WorkItemComments201Data;
};

export type GetApiV1WorkItemCommentsId200Data = {
  id: string;
  workItemId: string;
  content: string;
  authorId: string;
  /** @nullable */
  parentId: string | null;
  isEdited: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  author: User;
};

export type GetApiV1WorkItemCommentsId200 = {
  data: GetApiV1WorkItemCommentsId200Data;
};

export type PatchApiV1WorkItemCommentsIdBody = {
  /** @minLength 1 */
  content?: string;
  isEdited?: boolean;
};

export type PatchApiV1WorkItemCommentsId200Data = {
  id: string;
  workItemId: string;
  content: string;
  authorId: string;
  /** @nullable */
  parentId: string | null;
  isEdited: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  author: User;
};

export type PatchApiV1WorkItemCommentsId200 = {
  data: PatchApiV1WorkItemCommentsId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1WorkItemCommentsId204 =
  | (typeof DeleteApiV1WorkItemCommentsId204)[keyof typeof DeleteApiV1WorkItemCommentsId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1WorkItemCommentsId204 = {
  null: 'null',
} as const;

export type GetApiV1WorkItemFieldsParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1WorkItemFields200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1WorkItemFields200 = {
  data: WorkItemField[];
  meta: GetApiV1WorkItemFields200Meta;
};

export type PostApiV1WorkItemFieldsBodyFieldType =
  (typeof PostApiV1WorkItemFieldsBodyFieldType)[keyof typeof PostApiV1WorkItemFieldsBodyFieldType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1WorkItemFieldsBodyFieldType = {
  text: 'text',
  number: 'number',
  date: 'date',
  boolean: 'boolean',
  select: 'select',
  multi_select: 'multi_select',
  user: 'user',
  json: 'json',
} as const;

/**
 * @nullable
 */
export type PostApiV1WorkItemFieldsBodyDefaultValue = {
  [key: string]: unknown;
} | null;

export type PostApiV1WorkItemFieldsBodyOptions = { [key: string]: unknown };

export type PostApiV1WorkItemFieldsBody = {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name: string;
  /** @nullable */
  description?: string | null;
  fieldType: PostApiV1WorkItemFieldsBodyFieldType;
  isRequired?: boolean;
  /** @nullable */
  defaultValue?: PostApiV1WorkItemFieldsBodyDefaultValue;
  options?: PostApiV1WorkItemFieldsBodyOptions;
  workItemTypeId: string;
  /** @nullable */
  organizationId?: string | null;
  order?: number;
  isFilterable?: boolean;
  isDisplayedInGrid?: boolean;
  isActive?: boolean;
};

export type PostApiV1WorkItemFields201DataFieldType =
  (typeof PostApiV1WorkItemFields201DataFieldType)[keyof typeof PostApiV1WorkItemFields201DataFieldType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1WorkItemFields201DataFieldType = {
  text: 'text',
  number: 'number',
  date: 'date',
  boolean: 'boolean',
  select: 'select',
  multi_select: 'multi_select',
  user: 'user',
  json: 'json',
} as const;

/**
 * @nullable
 */
export type PostApiV1WorkItemFields201DataDefaultValue = {
  [key: string]: unknown;
} | null;

export type PostApiV1WorkItemFields201DataOptions = { [key: string]: unknown };

export type PostApiV1WorkItemFields201Data = {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  fieldType: PostApiV1WorkItemFields201DataFieldType;
  isRequired: boolean;
  /** @nullable */
  defaultValue?: PostApiV1WorkItemFields201DataDefaultValue;
  options?: PostApiV1WorkItemFields201DataOptions;
  workItemTypeId: string;
  /** @nullable */
  organizationId: string | null;
  order: number;
  isFilterable: boolean;
  isDisplayedInGrid: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  workItemType: WorkItemType;
  organization: Organization;
};

export type PostApiV1WorkItemFields201 = {
  data: PostApiV1WorkItemFields201Data;
};

export type GetApiV1WorkItemFieldsId200DataFieldType =
  (typeof GetApiV1WorkItemFieldsId200DataFieldType)[keyof typeof GetApiV1WorkItemFieldsId200DataFieldType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiV1WorkItemFieldsId200DataFieldType = {
  text: 'text',
  number: 'number',
  date: 'date',
  boolean: 'boolean',
  select: 'select',
  multi_select: 'multi_select',
  user: 'user',
  json: 'json',
} as const;

/**
 * @nullable
 */
export type GetApiV1WorkItemFieldsId200DataDefaultValue = {
  [key: string]: unknown;
} | null;

export type GetApiV1WorkItemFieldsId200DataOptions = { [key: string]: unknown };

export type GetApiV1WorkItemFieldsId200Data = {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  fieldType: GetApiV1WorkItemFieldsId200DataFieldType;
  isRequired: boolean;
  /** @nullable */
  defaultValue?: GetApiV1WorkItemFieldsId200DataDefaultValue;
  options?: GetApiV1WorkItemFieldsId200DataOptions;
  workItemTypeId: string;
  /** @nullable */
  organizationId: string | null;
  order: number;
  isFilterable: boolean;
  isDisplayedInGrid: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  workItemType: WorkItemType;
  organization: Organization;
};

export type GetApiV1WorkItemFieldsId200 = {
  data: GetApiV1WorkItemFieldsId200Data;
};

export type PatchApiV1WorkItemFieldsIdBodyFieldType =
  (typeof PatchApiV1WorkItemFieldsIdBodyFieldType)[keyof typeof PatchApiV1WorkItemFieldsIdBodyFieldType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1WorkItemFieldsIdBodyFieldType = {
  text: 'text',
  number: 'number',
  date: 'date',
  boolean: 'boolean',
  select: 'select',
  multi_select: 'multi_select',
  user: 'user',
  json: 'json',
} as const;

/**
 * @nullable
 */
export type PatchApiV1WorkItemFieldsIdBodyDefaultValue = {
  [key: string]: unknown;
} | null;

export type PatchApiV1WorkItemFieldsIdBodyOptions = { [key: string]: unknown };

export type PatchApiV1WorkItemFieldsIdBody = {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name?: string;
  /** @nullable */
  description?: string | null;
  fieldType?: PatchApiV1WorkItemFieldsIdBodyFieldType;
  isRequired?: boolean;
  /** @nullable */
  defaultValue?: PatchApiV1WorkItemFieldsIdBodyDefaultValue;
  options?: PatchApiV1WorkItemFieldsIdBodyOptions;
  workItemTypeId?: string;
  /** @nullable */
  organizationId?: string | null;
  order?: number;
  isFilterable?: boolean;
  isDisplayedInGrid?: boolean;
  isActive?: boolean;
};

export type PatchApiV1WorkItemFieldsId200DataFieldType =
  (typeof PatchApiV1WorkItemFieldsId200DataFieldType)[keyof typeof PatchApiV1WorkItemFieldsId200DataFieldType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1WorkItemFieldsId200DataFieldType = {
  text: 'text',
  number: 'number',
  date: 'date',
  boolean: 'boolean',
  select: 'select',
  multi_select: 'multi_select',
  user: 'user',
  json: 'json',
} as const;

/**
 * @nullable
 */
export type PatchApiV1WorkItemFieldsId200DataDefaultValue = {
  [key: string]: unknown;
} | null;

export type PatchApiV1WorkItemFieldsId200DataOptions = {
  [key: string]: unknown;
};

export type PatchApiV1WorkItemFieldsId200Data = {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  fieldType: PatchApiV1WorkItemFieldsId200DataFieldType;
  isRequired: boolean;
  /** @nullable */
  defaultValue?: PatchApiV1WorkItemFieldsId200DataDefaultValue;
  options?: PatchApiV1WorkItemFieldsId200DataOptions;
  workItemTypeId: string;
  /** @nullable */
  organizationId: string | null;
  order: number;
  isFilterable: boolean;
  isDisplayedInGrid: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  workItemType: WorkItemType;
  organization: Organization;
};

export type PatchApiV1WorkItemFieldsId200 = {
  data: PatchApiV1WorkItemFieldsId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1WorkItemFieldsId204 =
  | (typeof DeleteApiV1WorkItemFieldsId204)[keyof typeof DeleteApiV1WorkItemFieldsId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1WorkItemFieldsId204 = {
  null: 'null',
} as const;

export type GetApiV1WorkItemHistoryParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1WorkItemHistory200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1WorkItemHistory200 = {
  data: WorkItemHistory[];
  meta: GetApiV1WorkItemHistory200Meta;
};

export type PostApiV1WorkItemHistoryBodyChangeType =
  (typeof PostApiV1WorkItemHistoryBodyChangeType)[keyof typeof PostApiV1WorkItemHistoryBodyChangeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1WorkItemHistoryBodyChangeType = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  RESTORE: 'RESTORE',
  COMMENT_ADDED: 'COMMENT_ADDED',
  COMMENT_UPDATED: 'COMMENT_UPDATED',
  COMMENT_DELETED: 'COMMENT_DELETED',
  ATTACHMENT_ADDED: 'ATTACHMENT_ADDED',
  ATTACHMENT_DELETED: 'ATTACHMENT_DELETED',
} as const;

export type PostApiV1WorkItemHistoryBodyChangedFieldsOldValue =
  | string
  | number
  | boolean
  | 'null'
  | null;

export type PostApiV1WorkItemHistoryBodyChangedFieldsNewValue =
  | string
  | number
  | boolean
  | 'null'
  | null;

/**
 * @nullable
 */
export type PostApiV1WorkItemHistoryBodyChangedFields = {
  [key: string]: {
    oldValue: PostApiV1WorkItemHistoryBodyChangedFieldsOldValue;
    newValue: PostApiV1WorkItemHistoryBodyChangedFieldsNewValue;
  };
} | null;

export type PostApiV1WorkItemHistoryBodyMetadataComment = {
  id: string;
  content: string;
};

export type PostApiV1WorkItemHistoryBodyMetadataAttachment = {
  id: string;
  filename: string;
  size?: number;
  mimeType?: string;
};

/**
 * @nullable
 */
export type PostApiV1WorkItemHistoryBodyMetadata = {
  comment?: PostApiV1WorkItemHistoryBodyMetadataComment;
  attachment?: PostApiV1WorkItemHistoryBodyMetadataAttachment;
  [key: string]: unknown;
} | null;

export type PostApiV1WorkItemHistoryBody = {
  workItemId: string;
  changeType: PostApiV1WorkItemHistoryBodyChangeType;
  changedBy: string;
  /** @nullable */
  changedFields?: PostApiV1WorkItemHistoryBodyChangedFields;
  /** @nullable */
  metadata?: PostApiV1WorkItemHistoryBodyMetadata;
  /**
   * @maxLength 500
   * @nullable
   */
  summary?: string | null;
};

export type PostApiV1WorkItemHistory201DataChangeType =
  (typeof PostApiV1WorkItemHistory201DataChangeType)[keyof typeof PostApiV1WorkItemHistory201DataChangeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1WorkItemHistory201DataChangeType = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  RESTORE: 'RESTORE',
  COMMENT_ADDED: 'COMMENT_ADDED',
  COMMENT_UPDATED: 'COMMENT_UPDATED',
  COMMENT_DELETED: 'COMMENT_DELETED',
  ATTACHMENT_ADDED: 'ATTACHMENT_ADDED',
  ATTACHMENT_DELETED: 'ATTACHMENT_DELETED',
} as const;

export type PostApiV1WorkItemHistory201DataChangedFieldsOldValue =
  | string
  | number
  | boolean
  | 'null'
  | null;

export type PostApiV1WorkItemHistory201DataChangedFieldsNewValue =
  | string
  | number
  | boolean
  | 'null'
  | null;

/**
 * @nullable
 */
export type PostApiV1WorkItemHistory201DataChangedFields = {
  [key: string]: {
    oldValue: PostApiV1WorkItemHistory201DataChangedFieldsOldValue;
    newValue: PostApiV1WorkItemHistory201DataChangedFieldsNewValue;
  };
} | null;

export type PostApiV1WorkItemHistory201DataMetadataComment = {
  id: string;
  content: string;
};

export type PostApiV1WorkItemHistory201DataMetadataAttachment = {
  id: string;
  filename: string;
  size?: number;
  mimeType?: string;
};

/**
 * @nullable
 */
export type PostApiV1WorkItemHistory201DataMetadata = {
  comment?: PostApiV1WorkItemHistory201DataMetadataComment;
  attachment?: PostApiV1WorkItemHistory201DataMetadataAttachment;
  [key: string]: unknown;
} | null;

export type PostApiV1WorkItemHistory201Data = {
  id: string;
  workItemId: string;
  changeType: PostApiV1WorkItemHistory201DataChangeType;
  changedBy: string;
  /** @nullable */
  changedFields: PostApiV1WorkItemHistory201DataChangedFields;
  /** @nullable */
  metadata: PostApiV1WorkItemHistory201DataMetadata;
  /**
   * @maxLength 500
   * @nullable
   */
  summary: string | null;
  createdAt: string;
  user: User;
  workItem: WorkItem;
};

export type PostApiV1WorkItemHistory201 = {
  data: PostApiV1WorkItemHistory201Data;
};

export type GetApiV1WorkItemHistoryId200DataChangeType =
  (typeof GetApiV1WorkItemHistoryId200DataChangeType)[keyof typeof GetApiV1WorkItemHistoryId200DataChangeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiV1WorkItemHistoryId200DataChangeType = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  RESTORE: 'RESTORE',
  COMMENT_ADDED: 'COMMENT_ADDED',
  COMMENT_UPDATED: 'COMMENT_UPDATED',
  COMMENT_DELETED: 'COMMENT_DELETED',
  ATTACHMENT_ADDED: 'ATTACHMENT_ADDED',
  ATTACHMENT_DELETED: 'ATTACHMENT_DELETED',
} as const;

export type GetApiV1WorkItemHistoryId200DataChangedFieldsOldValue =
  | string
  | number
  | boolean
  | 'null'
  | null;

export type GetApiV1WorkItemHistoryId200DataChangedFieldsNewValue =
  | string
  | number
  | boolean
  | 'null'
  | null;

/**
 * @nullable
 */
export type GetApiV1WorkItemHistoryId200DataChangedFields = {
  [key: string]: {
    oldValue: GetApiV1WorkItemHistoryId200DataChangedFieldsOldValue;
    newValue: GetApiV1WorkItemHistoryId200DataChangedFieldsNewValue;
  };
} | null;

export type GetApiV1WorkItemHistoryId200DataMetadataComment = {
  id: string;
  content: string;
};

export type GetApiV1WorkItemHistoryId200DataMetadataAttachment = {
  id: string;
  filename: string;
  size?: number;
  mimeType?: string;
};

/**
 * @nullable
 */
export type GetApiV1WorkItemHistoryId200DataMetadata = {
  comment?: GetApiV1WorkItemHistoryId200DataMetadataComment;
  attachment?: GetApiV1WorkItemHistoryId200DataMetadataAttachment;
  [key: string]: unknown;
} | null;

export type GetApiV1WorkItemHistoryId200Data = {
  id: string;
  workItemId: string;
  changeType: GetApiV1WorkItemHistoryId200DataChangeType;
  changedBy: string;
  /** @nullable */
  changedFields: GetApiV1WorkItemHistoryId200DataChangedFields;
  /** @nullable */
  metadata: GetApiV1WorkItemHistoryId200DataMetadata;
  /**
   * @maxLength 500
   * @nullable
   */
  summary: string | null;
  createdAt: string;
  user: User;
  workItem: WorkItem;
};

export type GetApiV1WorkItemHistoryId200 = {
  data: GetApiV1WorkItemHistoryId200Data;
};

export type PatchApiV1WorkItemHistoryIdBodyChangeType =
  (typeof PatchApiV1WorkItemHistoryIdBodyChangeType)[keyof typeof PatchApiV1WorkItemHistoryIdBodyChangeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1WorkItemHistoryIdBodyChangeType = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  RESTORE: 'RESTORE',
  COMMENT_ADDED: 'COMMENT_ADDED',
  COMMENT_UPDATED: 'COMMENT_UPDATED',
  COMMENT_DELETED: 'COMMENT_DELETED',
  ATTACHMENT_ADDED: 'ATTACHMENT_ADDED',
  ATTACHMENT_DELETED: 'ATTACHMENT_DELETED',
} as const;

export type PatchApiV1WorkItemHistoryIdBodyChangedFieldsOldValue =
  | string
  | number
  | boolean
  | 'null'
  | null;

export type PatchApiV1WorkItemHistoryIdBodyChangedFieldsNewValue =
  | string
  | number
  | boolean
  | 'null'
  | null;

/**
 * @nullable
 */
export type PatchApiV1WorkItemHistoryIdBodyChangedFields = {
  [key: string]: {
    oldValue: PatchApiV1WorkItemHistoryIdBodyChangedFieldsOldValue;
    newValue: PatchApiV1WorkItemHistoryIdBodyChangedFieldsNewValue;
  };
} | null;

export type PatchApiV1WorkItemHistoryIdBodyMetadataComment = {
  id: string;
  content: string;
};

export type PatchApiV1WorkItemHistoryIdBodyMetadataAttachment = {
  id: string;
  filename: string;
  size?: number;
  mimeType?: string;
};

/**
 * @nullable
 */
export type PatchApiV1WorkItemHistoryIdBodyMetadata = {
  comment?: PatchApiV1WorkItemHistoryIdBodyMetadataComment;
  attachment?: PatchApiV1WorkItemHistoryIdBodyMetadataAttachment;
  [key: string]: unknown;
} | null;

export type PatchApiV1WorkItemHistoryIdBody = {
  workItemId?: string;
  changeType?: PatchApiV1WorkItemHistoryIdBodyChangeType;
  changedBy?: string;
  /** @nullable */
  changedFields?: PatchApiV1WorkItemHistoryIdBodyChangedFields;
  /** @nullable */
  metadata?: PatchApiV1WorkItemHistoryIdBodyMetadata;
  /**
   * @maxLength 500
   * @nullable
   */
  summary?: string | null;
};

export type PatchApiV1WorkItemHistoryId200DataChangeType =
  (typeof PatchApiV1WorkItemHistoryId200DataChangeType)[keyof typeof PatchApiV1WorkItemHistoryId200DataChangeType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1WorkItemHistoryId200DataChangeType = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  RESTORE: 'RESTORE',
  COMMENT_ADDED: 'COMMENT_ADDED',
  COMMENT_UPDATED: 'COMMENT_UPDATED',
  COMMENT_DELETED: 'COMMENT_DELETED',
  ATTACHMENT_ADDED: 'ATTACHMENT_ADDED',
  ATTACHMENT_DELETED: 'ATTACHMENT_DELETED',
} as const;

export type PatchApiV1WorkItemHistoryId200DataChangedFieldsOldValue =
  | string
  | number
  | boolean
  | 'null'
  | null;

export type PatchApiV1WorkItemHistoryId200DataChangedFieldsNewValue =
  | string
  | number
  | boolean
  | 'null'
  | null;

/**
 * @nullable
 */
export type PatchApiV1WorkItemHistoryId200DataChangedFields = {
  [key: string]: {
    oldValue: PatchApiV1WorkItemHistoryId200DataChangedFieldsOldValue;
    newValue: PatchApiV1WorkItemHistoryId200DataChangedFieldsNewValue;
  };
} | null;

export type PatchApiV1WorkItemHistoryId200DataMetadataComment = {
  id: string;
  content: string;
};

export type PatchApiV1WorkItemHistoryId200DataMetadataAttachment = {
  id: string;
  filename: string;
  size?: number;
  mimeType?: string;
};

/**
 * @nullable
 */
export type PatchApiV1WorkItemHistoryId200DataMetadata = {
  comment?: PatchApiV1WorkItemHistoryId200DataMetadataComment;
  attachment?: PatchApiV1WorkItemHistoryId200DataMetadataAttachment;
  [key: string]: unknown;
} | null;

export type PatchApiV1WorkItemHistoryId200Data = {
  id: string;
  workItemId: string;
  changeType: PatchApiV1WorkItemHistoryId200DataChangeType;
  changedBy: string;
  /** @nullable */
  changedFields: PatchApiV1WorkItemHistoryId200DataChangedFields;
  /** @nullable */
  metadata: PatchApiV1WorkItemHistoryId200DataMetadata;
  /**
   * @maxLength 500
   * @nullable
   */
  summary: string | null;
  createdAt: string;
  user: User;
  workItem: WorkItem;
};

export type PatchApiV1WorkItemHistoryId200 = {
  data: PatchApiV1WorkItemHistoryId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1WorkItemHistoryId204 =
  | (typeof DeleteApiV1WorkItemHistoryId204)[keyof typeof DeleteApiV1WorkItemHistoryId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1WorkItemHistoryId204 = {
  null: 'null',
} as const;

export type GetApiV1WorkItemLinkParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1WorkItemLink200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1WorkItemLink200 = {
  data: WorkItemLink[];
  meta: GetApiV1WorkItemLink200Meta;
};

export type PostApiV1WorkItemLinkBodyLinkType =
  (typeof PostApiV1WorkItemLinkBodyLinkType)[keyof typeof PostApiV1WorkItemLinkBodyLinkType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1WorkItemLinkBodyLinkType = {
  is_caused_by: 'is_caused_by',
  blocked_by: 'blocked_by',
  blocks: 'blocks',
  relates_to: 'relates_to',
  is_child_of: 'is_child_of',
  is_duplicated_by: 'is_duplicated_by',
  duplicates: 'duplicates',
} as const;

export type PostApiV1WorkItemLinkBody = {
  sourceWorkItemId: string;
  targetWorkItemId: string;
  linkType: PostApiV1WorkItemLinkBodyLinkType;
  /**
   * @maxLength 500
   * @nullable
   */
  title?: string | null;
  /** @nullable */
  description?: string | null;
  createdById: string;
  isActive?: boolean;
};

export type PostApiV1WorkItemLink201DataLinkType =
  (typeof PostApiV1WorkItemLink201DataLinkType)[keyof typeof PostApiV1WorkItemLink201DataLinkType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1WorkItemLink201DataLinkType = {
  is_caused_by: 'is_caused_by',
  blocked_by: 'blocked_by',
  blocks: 'blocks',
  relates_to: 'relates_to',
  is_child_of: 'is_child_of',
  is_duplicated_by: 'is_duplicated_by',
  duplicates: 'duplicates',
} as const;

export type PostApiV1WorkItemLink201Data = {
  id: string;
  sourceWorkItemId: string;
  targetWorkItemId: string;
  linkType: PostApiV1WorkItemLink201DataLinkType;
  /**
   * @maxLength 500
   * @nullable
   */
  title: string | null;
  /** @nullable */
  description: string | null;
  createdById: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: User;
  sourceWorkItem: WorkItem;
  targetWorkItem: WorkItem;
};

export type PostApiV1WorkItemLink201 = {
  data: PostApiV1WorkItemLink201Data;
};

export type GetApiV1WorkItemLinkId200DataLinkType =
  (typeof GetApiV1WorkItemLinkId200DataLinkType)[keyof typeof GetApiV1WorkItemLinkId200DataLinkType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiV1WorkItemLinkId200DataLinkType = {
  is_caused_by: 'is_caused_by',
  blocked_by: 'blocked_by',
  blocks: 'blocks',
  relates_to: 'relates_to',
  is_child_of: 'is_child_of',
  is_duplicated_by: 'is_duplicated_by',
  duplicates: 'duplicates',
} as const;

export type GetApiV1WorkItemLinkId200Data = {
  id: string;
  sourceWorkItemId: string;
  targetWorkItemId: string;
  linkType: GetApiV1WorkItemLinkId200DataLinkType;
  /**
   * @maxLength 500
   * @nullable
   */
  title: string | null;
  /** @nullable */
  description: string | null;
  createdById: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: User;
  sourceWorkItem: WorkItem;
  targetWorkItem: WorkItem;
};

export type GetApiV1WorkItemLinkId200 = {
  data: GetApiV1WorkItemLinkId200Data;
};

export type PatchApiV1WorkItemLinkIdBodyLinkType =
  (typeof PatchApiV1WorkItemLinkIdBodyLinkType)[keyof typeof PatchApiV1WorkItemLinkIdBodyLinkType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1WorkItemLinkIdBodyLinkType = {
  is_caused_by: 'is_caused_by',
  blocked_by: 'blocked_by',
  blocks: 'blocks',
  relates_to: 'relates_to',
  is_child_of: 'is_child_of',
  is_duplicated_by: 'is_duplicated_by',
  duplicates: 'duplicates',
} as const;

export type PatchApiV1WorkItemLinkIdBody = {
  sourceWorkItemId?: string;
  targetWorkItemId?: string;
  linkType?: PatchApiV1WorkItemLinkIdBodyLinkType;
  /**
   * @maxLength 500
   * @nullable
   */
  title?: string | null;
  /** @nullable */
  description?: string | null;
  createdById?: string;
  isActive?: boolean;
};

export type PatchApiV1WorkItemLinkId200DataLinkType =
  (typeof PatchApiV1WorkItemLinkId200DataLinkType)[keyof typeof PatchApiV1WorkItemLinkId200DataLinkType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1WorkItemLinkId200DataLinkType = {
  is_caused_by: 'is_caused_by',
  blocked_by: 'blocked_by',
  blocks: 'blocks',
  relates_to: 'relates_to',
  is_child_of: 'is_child_of',
  is_duplicated_by: 'is_duplicated_by',
  duplicates: 'duplicates',
} as const;

export type PatchApiV1WorkItemLinkId200Data = {
  id: string;
  sourceWorkItemId: string;
  targetWorkItemId: string;
  linkType: PatchApiV1WorkItemLinkId200DataLinkType;
  /**
   * @maxLength 500
   * @nullable
   */
  title: string | null;
  /** @nullable */
  description: string | null;
  createdById: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: User;
  sourceWorkItem: WorkItem;
  targetWorkItem: WorkItem;
};

export type PatchApiV1WorkItemLinkId200 = {
  data: PatchApiV1WorkItemLinkId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1WorkItemLinkId204 =
  | (typeof DeleteApiV1WorkItemLinkId204)[keyof typeof DeleteApiV1WorkItemLinkId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1WorkItemLinkId204 = {
  null: 'null',
} as const;

export type GetApiV1WorkItemTypesParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1WorkItemTypes200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1WorkItemTypes200 = {
  data: WorkItemType[];
  meta: GetApiV1WorkItemTypes200Meta;
};

export type PostApiV1WorkItemTypesBodyData = { [key: string]: unknown };

export type PostApiV1WorkItemTypesBody = {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  organizationId?: string | null;
  isSystem?: boolean;
  /**
   * @maxLength 255
   * @nullable
   */
  icon?: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  data?: PostApiV1WorkItemTypesBodyData;
  isActive?: boolean;
};

export type PostApiV1WorkItemTypes201DataData = { [key: string]: unknown };

export type PostApiV1WorkItemTypes201Data = {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  organizationId: string | null;
  isSystem: boolean;
  /**
   * @maxLength 255
   * @nullable
   */
  icon: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  data?: PostApiV1WorkItemTypes201DataData;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  organization: Organization;
};

export type PostApiV1WorkItemTypes201 = {
  data: PostApiV1WorkItemTypes201Data;
};

export type GetApiV1WorkItemTypesId200DataData = { [key: string]: unknown };

export type GetApiV1WorkItemTypesId200Data = {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  organizationId: string | null;
  isSystem: boolean;
  /**
   * @maxLength 255
   * @nullable
   */
  icon: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  data?: GetApiV1WorkItemTypesId200DataData;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  organization: Organization;
};

export type GetApiV1WorkItemTypesId200 = {
  data: GetApiV1WorkItemTypesId200Data;
};

export type PatchApiV1WorkItemTypesIdBodyData = { [key: string]: unknown };

export type PatchApiV1WorkItemTypesIdBody = {
  /**
   * @minLength 1
   * @maxLength 100
   */
  name?: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  organizationId?: string | null;
  isSystem?: boolean;
  /**
   * @maxLength 255
   * @nullable
   */
  icon?: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  data?: PatchApiV1WorkItemTypesIdBodyData;
  isActive?: boolean;
};

export type PatchApiV1WorkItemTypesId200DataData = { [key: string]: unknown };

export type PatchApiV1WorkItemTypesId200Data = {
  id: string;
  /** @maxLength 100 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  organizationId: string | null;
  isSystem: boolean;
  /**
   * @maxLength 255
   * @nullable
   */
  icon: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  data?: PatchApiV1WorkItemTypesId200DataData;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  organization: Organization;
};

export type PatchApiV1WorkItemTypesId200 = {
  data: PatchApiV1WorkItemTypesId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1WorkItemTypesId204 =
  | (typeof DeleteApiV1WorkItemTypesId204)[keyof typeof DeleteApiV1WorkItemTypesId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1WorkItemTypesId204 = {
  null: 'null',
} as const;

export type GetApiV1WorkItemsParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1WorkItems200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1WorkItems200 = {
  data: WorkItem[];
  meta: GetApiV1WorkItems200Meta;
};

export type PostApiV1WorkItemsBodyTags = { [key: string]: unknown };

export type PostApiV1WorkItemsBodyEstimate = { [key: string]: unknown };

export type PostApiV1WorkItemsBodyDates = { [key: string]: unknown };

export type PostApiV1WorkItemsBodyLinks = { [key: string]: unknown };

export type PostApiV1WorkItemsBody = {
  projectId: string;
  typeId: string;
  /**
   * @minLength 1
   * @maxLength 500
   */
  title: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  assigneeId?: string | null;
  /** @nullable */
  reporterId?: string | null;
  /** @nullable */
  sprintId?: string | null;
  /** @nullable */
  initialSprintId?: string | null;
  /** @nullable */
  parentId?: string | null;
  statusId: string;
  priorityId: string;
  tags?: PostApiV1WorkItemsBodyTags;
  estimate?: PostApiV1WorkItemsBodyEstimate;
  dates?: PostApiV1WorkItemsBodyDates;
  links?: PostApiV1WorkItemsBodyLinks;
  order?: number;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
  /** @nullable */
  updatedBy?: string | null;
};

export type PostApiV1WorkItems201DataTags = { [key: string]: unknown };

export type PostApiV1WorkItems201DataEstimate = { [key: string]: unknown };

export type PostApiV1WorkItems201DataDates = { [key: string]: unknown };

export type PostApiV1WorkItems201DataLinks = { [key: string]: unknown };

export type PostApiV1WorkItems201Data = {
  id: string;
  projectId: string;
  typeId: string;
  /** @maxLength 500 */
  title: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  assigneeId: string | null;
  /** @nullable */
  reporterId: string | null;
  /** @nullable */
  sprintId: string | null;
  /** @nullable */
  initialSprintId: string | null;
  /** @nullable */
  parentId: string | null;
  statusId: string;
  priorityId: string;
  ticketNumber: number;
  /** @maxLength 20 */
  ticketId: string;
  tags?: PostApiV1WorkItems201DataTags;
  estimate?: PostApiV1WorkItems201DataEstimate;
  dates?: PostApiV1WorkItems201DataDates;
  links?: PostApiV1WorkItems201DataLinks;
  order: number;
  isActive: boolean;
  /** @nullable */
  createdBy: string | null;
  createdAt: string;
  updatedAt: string;
  /** @nullable */
  updatedBy: string | null;
  project: Project;
  type: WorkItemType;
  assignee: User;
  reporter: User;
  sprint: Sprint;
  initialSprint: Sprint;
  status: Status;
  priority: Priority;
  creator: User;
  updater: User;
};

export type PostApiV1WorkItems201 = {
  data: PostApiV1WorkItems201Data;
};

export type GetApiV1WorkItemsId200DataTags = { [key: string]: unknown };

export type GetApiV1WorkItemsId200DataEstimate = { [key: string]: unknown };

export type GetApiV1WorkItemsId200DataDates = { [key: string]: unknown };

export type GetApiV1WorkItemsId200DataLinks = { [key: string]: unknown };

export type GetApiV1WorkItemsId200Data = {
  id: string;
  projectId: string;
  typeId: string;
  /** @maxLength 500 */
  title: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  assigneeId: string | null;
  /** @nullable */
  reporterId: string | null;
  /** @nullable */
  sprintId: string | null;
  /** @nullable */
  initialSprintId: string | null;
  /** @nullable */
  parentId: string | null;
  statusId: string;
  priorityId: string;
  ticketNumber: number;
  /** @maxLength 20 */
  ticketId: string;
  tags?: GetApiV1WorkItemsId200DataTags;
  estimate?: GetApiV1WorkItemsId200DataEstimate;
  dates?: GetApiV1WorkItemsId200DataDates;
  links?: GetApiV1WorkItemsId200DataLinks;
  order: number;
  isActive: boolean;
  /** @nullable */
  createdBy: string | null;
  createdAt: string;
  updatedAt: string;
  /** @nullable */
  updatedBy: string | null;
  project: Project;
  type: WorkItemType;
  assignee: User;
  reporter: User;
  sprint: Sprint;
  initialSprint: Sprint;
  status: Status;
  priority: Priority;
  creator: User;
  updater: User;
};

export type GetApiV1WorkItemsId200 = {
  data: GetApiV1WorkItemsId200Data;
};

export type PatchApiV1WorkItemsIdBodyTags = { [key: string]: unknown };

export type PatchApiV1WorkItemsIdBodyEstimate = { [key: string]: unknown };

export type PatchApiV1WorkItemsIdBodyDates = { [key: string]: unknown };

export type PatchApiV1WorkItemsIdBodyLinks = { [key: string]: unknown };

export type PatchApiV1WorkItemsIdBody = {
  projectId?: string;
  typeId?: string;
  /**
   * @minLength 1
   * @maxLength 500
   */
  title?: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  assigneeId?: string | null;
  /** @nullable */
  reporterId?: string | null;
  /** @nullable */
  sprintId?: string | null;
  /** @nullable */
  initialSprintId?: string | null;
  /** @nullable */
  parentId?: string | null;
  statusId?: string;
  priorityId?: string;
  tags?: PatchApiV1WorkItemsIdBodyTags;
  estimate?: PatchApiV1WorkItemsIdBodyEstimate;
  dates?: PatchApiV1WorkItemsIdBodyDates;
  links?: PatchApiV1WorkItemsIdBodyLinks;
  order?: number;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
  /** @nullable */
  updatedBy?: string | null;
};

export type PatchApiV1WorkItemsId200DataTags = { [key: string]: unknown };

export type PatchApiV1WorkItemsId200DataEstimate = { [key: string]: unknown };

export type PatchApiV1WorkItemsId200DataDates = { [key: string]: unknown };

export type PatchApiV1WorkItemsId200DataLinks = { [key: string]: unknown };

export type PatchApiV1WorkItemsId200Data = {
  id: string;
  projectId: string;
  typeId: string;
  /** @maxLength 500 */
  title: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  assigneeId: string | null;
  /** @nullable */
  reporterId: string | null;
  /** @nullable */
  sprintId: string | null;
  /** @nullable */
  initialSprintId: string | null;
  /** @nullable */
  parentId: string | null;
  statusId: string;
  priorityId: string;
  ticketNumber: number;
  /** @maxLength 20 */
  ticketId: string;
  tags?: PatchApiV1WorkItemsId200DataTags;
  estimate?: PatchApiV1WorkItemsId200DataEstimate;
  dates?: PatchApiV1WorkItemsId200DataDates;
  links?: PatchApiV1WorkItemsId200DataLinks;
  order: number;
  isActive: boolean;
  /** @nullable */
  createdBy: string | null;
  createdAt: string;
  updatedAt: string;
  /** @nullable */
  updatedBy: string | null;
  project: Project;
  type: WorkItemType;
  assignee: User;
  reporter: User;
  sprint: Sprint;
  initialSprint: Sprint;
  status: Status;
  priority: Priority;
  creator: User;
  updater: User;
};

export type PatchApiV1WorkItemsId200 = {
  data: PatchApiV1WorkItemsId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1WorkItemsId204 =
  | (typeof DeleteApiV1WorkItemsId204)[keyof typeof DeleteApiV1WorkItemsId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1WorkItemsId204 = {
  null: 'null',
} as const;

export type GetApiV1WorkflowStatusesParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1WorkflowStatuses200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1WorkflowStatuses200 = {
  data: WorkflowStatus[];
  meta: GetApiV1WorkflowStatuses200Meta;
};

export type PostApiV1WorkflowStatusesBodyProperties = {
  [key: string]: unknown;
};

export type PostApiV1WorkflowStatusesBody = {
  workflowId: string;
  statusId: string;
  isInitial?: boolean;
  isFinal?: boolean;
  positionX?: number;
  positionY?: number;
  properties?: PostApiV1WorkflowStatusesBodyProperties;
  isActive?: boolean;
};

export type PostApiV1WorkflowStatuses201DataProperties = {
  [key: string]: unknown;
};

export type PostApiV1WorkflowStatuses201Data = {
  id: string;
  workflowId: string;
  statusId: string;
  isInitial: boolean;
  isFinal: boolean;
  positionX: number;
  positionY: number;
  properties?: PostApiV1WorkflowStatuses201DataProperties;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  workflow: Workflow;
  status: Status;
};

export type PostApiV1WorkflowStatuses201 = {
  data: PostApiV1WorkflowStatuses201Data;
};

export type GetApiV1WorkflowStatusesId200DataProperties = {
  [key: string]: unknown;
};

export type GetApiV1WorkflowStatusesId200Data = {
  id: string;
  workflowId: string;
  statusId: string;
  isInitial: boolean;
  isFinal: boolean;
  positionX: number;
  positionY: number;
  properties?: GetApiV1WorkflowStatusesId200DataProperties;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  workflow: Workflow;
  status: Status;
};

export type GetApiV1WorkflowStatusesId200 = {
  data: GetApiV1WorkflowStatusesId200Data;
};

export type PatchApiV1WorkflowStatusesIdBodyProperties = {
  [key: string]: unknown;
};

export type PatchApiV1WorkflowStatusesIdBody = {
  workflowId?: string;
  statusId?: string;
  isInitial?: boolean;
  isFinal?: boolean;
  positionX?: number;
  positionY?: number;
  properties?: PatchApiV1WorkflowStatusesIdBodyProperties;
  isActive?: boolean;
};

export type PatchApiV1WorkflowStatusesId200DataProperties = {
  [key: string]: unknown;
};

export type PatchApiV1WorkflowStatusesId200Data = {
  id: string;
  workflowId: string;
  statusId: string;
  isInitial: boolean;
  isFinal: boolean;
  positionX: number;
  positionY: number;
  properties?: PatchApiV1WorkflowStatusesId200DataProperties;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  workflow: Workflow;
  status: Status;
};

export type PatchApiV1WorkflowStatusesId200 = {
  data: PatchApiV1WorkflowStatusesId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1WorkflowStatusesId204 =
  | (typeof DeleteApiV1WorkflowStatusesId204)[keyof typeof DeleteApiV1WorkflowStatusesId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1WorkflowStatusesId204 = {
  null: 'null',
} as const;

export type GetApiV1WorkflowTransitionsParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1WorkflowTransitions200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1WorkflowTransitions200 = {
  data: WorkflowTransition[];
  meta: GetApiV1WorkflowTransitions200Meta;
};

export type PostApiV1WorkflowTransitionsBody = {
  workflowId: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  fromStatusId?: string | null;
  toStatusId: string;
  isInitial?: boolean;
  isGlobal?: boolean;
  order?: number;
  /**
   * @maxLength 100
   * @nullable
   */
  buttonText?: string | null;
  /** @nullable */
  confirmationMessage?: string | null;
  /** @nullable */
  screenId?: string | null;
  isActive?: boolean;
};

export type PostApiV1WorkflowTransitions201Data = {
  id: string;
  workflowId: string;
  /** @maxLength 255 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  fromStatusId: string | null;
  toStatusId: string;
  isInitial: boolean;
  isGlobal: boolean;
  order: number;
  /**
   * @maxLength 100
   * @nullable
   */
  buttonText: string | null;
  /** @nullable */
  confirmationMessage: string | null;
  /** @nullable */
  screenId: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  workflow: Workflow;
  fromStatus: WorkflowStatus;
  toStatus: WorkflowStatus;
};

export type PostApiV1WorkflowTransitions201 = {
  data: PostApiV1WorkflowTransitions201Data;
};

export type GetApiV1WorkflowTransitionsId200Data = {
  id: string;
  workflowId: string;
  /** @maxLength 255 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  fromStatusId: string | null;
  toStatusId: string;
  isInitial: boolean;
  isGlobal: boolean;
  order: number;
  /**
   * @maxLength 100
   * @nullable
   */
  buttonText: string | null;
  /** @nullable */
  confirmationMessage: string | null;
  /** @nullable */
  screenId: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  workflow: Workflow;
  fromStatus: WorkflowStatus;
  toStatus: WorkflowStatus;
};

export type GetApiV1WorkflowTransitionsId200 = {
  data: GetApiV1WorkflowTransitionsId200Data;
};

export type PatchApiV1WorkflowTransitionsIdBody = {
  workflowId?: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name?: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  fromStatusId?: string | null;
  toStatusId?: string;
  isInitial?: boolean;
  isGlobal?: boolean;
  order?: number;
  /**
   * @maxLength 100
   * @nullable
   */
  buttonText?: string | null;
  /** @nullable */
  confirmationMessage?: string | null;
  /** @nullable */
  screenId?: string | null;
  isActive?: boolean;
};

export type PatchApiV1WorkflowTransitionsId200Data = {
  id: string;
  workflowId: string;
  /** @maxLength 255 */
  name: string;
  /** @nullable */
  description: string | null;
  /** @nullable */
  fromStatusId: string | null;
  toStatusId: string;
  isInitial: boolean;
  isGlobal: boolean;
  order: number;
  /**
   * @maxLength 100
   * @nullable
   */
  buttonText: string | null;
  /** @nullable */
  confirmationMessage: string | null;
  /** @nullable */
  screenId: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  workflow: Workflow;
  fromStatus: WorkflowStatus;
  toStatus: WorkflowStatus;
};

export type PatchApiV1WorkflowTransitionsId200 = {
  data: PatchApiV1WorkflowTransitionsId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1WorkflowTransitionsId204 =
  | (typeof DeleteApiV1WorkflowTransitionsId204)[keyof typeof DeleteApiV1WorkflowTransitionsId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1WorkflowTransitionsId204 = {
  null: 'null',
} as const;

export type GetApiV1WorkflowsParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1Workflows200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1Workflows200 = {
  data: Workflow[];
  meta: GetApiV1Workflows200Meta;
};

export type PostApiV1WorkflowsBodySettings = { [key: string]: unknown };

export type PostApiV1WorkflowsBodyStatus =
  (typeof PostApiV1WorkflowsBodyStatus)[keyof typeof PostApiV1WorkflowsBodyStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1WorkflowsBodyStatus = {
  draft: 'draft',
  active: 'active',
  archived: 'archived',
} as const;

export type PostApiV1WorkflowsBody = {
  /** @nullable */
  organizationId?: string | null;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name: string;
  /** @nullable */
  description?: string | null;
  isSystem?: boolean;
  isDefault?: boolean;
  /** @nullable */
  initialStatusId?: string | null;
  version?: number;
  settings?: PostApiV1WorkflowsBodySettings;
  status?: PostApiV1WorkflowsBodyStatus;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
};

export type PostApiV1Workflows201DataSettings = { [key: string]: unknown };

export type PostApiV1Workflows201DataStatus =
  (typeof PostApiV1Workflows201DataStatus)[keyof typeof PostApiV1Workflows201DataStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1Workflows201DataStatus = {
  draft: 'draft',
  active: 'active',
  archived: 'archived',
} as const;

export type PostApiV1Workflows201Data = {
  id: string;
  /** @nullable */
  organizationId: string | null;
  /** @maxLength 255 */
  name: string;
  /** @nullable */
  description: string | null;
  isSystem: boolean;
  isDefault: boolean;
  /** @nullable */
  initialStatusId: string | null;
  version: number;
  settings?: PostApiV1Workflows201DataSettings;
  status: PostApiV1Workflows201DataStatus;
  isActive: boolean;
  /** @nullable */
  createdBy: string | null;
  createdAt: string;
  updatedAt: string;
  organization: Organization;
  creator: User;
};

export type PostApiV1Workflows201 = {
  data: PostApiV1Workflows201Data;
};

export type GetApiV1WorkflowsId200DataSettings = { [key: string]: unknown };

export type GetApiV1WorkflowsId200DataStatus =
  (typeof GetApiV1WorkflowsId200DataStatus)[keyof typeof GetApiV1WorkflowsId200DataStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiV1WorkflowsId200DataStatus = {
  draft: 'draft',
  active: 'active',
  archived: 'archived',
} as const;

export type GetApiV1WorkflowsId200Data = {
  id: string;
  /** @nullable */
  organizationId: string | null;
  /** @maxLength 255 */
  name: string;
  /** @nullable */
  description: string | null;
  isSystem: boolean;
  isDefault: boolean;
  /** @nullable */
  initialStatusId: string | null;
  version: number;
  settings?: GetApiV1WorkflowsId200DataSettings;
  status: GetApiV1WorkflowsId200DataStatus;
  isActive: boolean;
  /** @nullable */
  createdBy: string | null;
  createdAt: string;
  updatedAt: string;
  organization: Organization;
  creator: User;
};

export type GetApiV1WorkflowsId200 = {
  data: GetApiV1WorkflowsId200Data;
};

export type PatchApiV1WorkflowsIdBodySettings = { [key: string]: unknown };

export type PatchApiV1WorkflowsIdBodyStatus =
  (typeof PatchApiV1WorkflowsIdBodyStatus)[keyof typeof PatchApiV1WorkflowsIdBodyStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1WorkflowsIdBodyStatus = {
  draft: 'draft',
  active: 'active',
  archived: 'archived',
} as const;

export type PatchApiV1WorkflowsIdBody = {
  /** @nullable */
  organizationId?: string | null;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name?: string;
  /** @nullable */
  description?: string | null;
  isSystem?: boolean;
  isDefault?: boolean;
  /** @nullable */
  initialStatusId?: string | null;
  version?: number;
  settings?: PatchApiV1WorkflowsIdBodySettings;
  status?: PatchApiV1WorkflowsIdBodyStatus;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
};

export type PatchApiV1WorkflowsId200DataSettings = { [key: string]: unknown };

export type PatchApiV1WorkflowsId200DataStatus =
  (typeof PatchApiV1WorkflowsId200DataStatus)[keyof typeof PatchApiV1WorkflowsId200DataStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1WorkflowsId200DataStatus = {
  draft: 'draft',
  active: 'active',
  archived: 'archived',
} as const;

export type PatchApiV1WorkflowsId200Data = {
  id: string;
  /** @nullable */
  organizationId: string | null;
  /** @maxLength 255 */
  name: string;
  /** @nullable */
  description: string | null;
  isSystem: boolean;
  isDefault: boolean;
  /** @nullable */
  initialStatusId: string | null;
  version: number;
  settings?: PatchApiV1WorkflowsId200DataSettings;
  status: PatchApiV1WorkflowsId200DataStatus;
  isActive: boolean;
  /** @nullable */
  createdBy: string | null;
  createdAt: string;
  updatedAt: string;
  organization: Organization;
  creator: User;
};

export type PatchApiV1WorkflowsId200 = {
  data: PatchApiV1WorkflowsId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1WorkflowsId204 =
  | (typeof DeleteApiV1WorkflowsId204)[keyof typeof DeleteApiV1WorkflowsId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1WorkflowsId204 = {
  null: 'null',
} as const;

export type GetApiV1WorkspacesParams = {
  filters?: string;
  sort?: string;
  page?: number;
  limit?: number;
  search?: string;
};

export type GetApiV1Workspaces200Meta = {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type GetApiV1Workspaces200 = {
  data: Workspace[];
  meta: GetApiV1Workspaces200Meta;
};

export type PostApiV1WorkspacesBodySettings = { [key: string]: unknown };

export type PostApiV1WorkspacesBodyVisibility =
  (typeof PostApiV1WorkspacesBodyVisibility)[keyof typeof PostApiV1WorkspacesBodyVisibility];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1WorkspacesBodyVisibility = {
  public: 'public',
  private: 'private',
} as const;

export type PostApiV1WorkspacesBodyStatus =
  (typeof PostApiV1WorkspacesBodyStatus)[keyof typeof PostApiV1WorkspacesBodyStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1WorkspacesBodyStatus = {
  active: 'active',
  archived: 'archived',
} as const;

export type PostApiV1WorkspacesBody = {
  organizationId: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  slug: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  icon?: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  settings?: PostApiV1WorkspacesBodySettings;
  /** @nullable */
  defaultProjectId?: string | null;
  visibility?: PostApiV1WorkspacesBodyVisibility;
  status?: PostApiV1WorkspacesBodyStatus;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
};

export type PostApiV1Workspaces201DataSettings = { [key: string]: unknown };

export type PostApiV1Workspaces201DataVisibility =
  (typeof PostApiV1Workspaces201DataVisibility)[keyof typeof PostApiV1Workspaces201DataVisibility];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1Workspaces201DataVisibility = {
  public: 'public',
  private: 'private',
} as const;

export type PostApiV1Workspaces201DataStatus =
  (typeof PostApiV1Workspaces201DataStatus)[keyof typeof PostApiV1Workspaces201DataStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PostApiV1Workspaces201DataStatus = {
  active: 'active',
  archived: 'archived',
} as const;

export type PostApiV1Workspaces201Data = {
  id: string;
  organizationId: string;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  slug: string;
  /** @nullable */
  description: string | null;
  /**
   * @maxLength 500
   * @nullable
   */
  icon: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  settings?: PostApiV1Workspaces201DataSettings;
  /** @nullable */
  defaultProjectId: string | null;
  visibility: PostApiV1Workspaces201DataVisibility;
  status: PostApiV1Workspaces201DataStatus;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  /** @nullable */
  createdBy: string | null;
  organization: Organization;
  creator: User;
};

export type PostApiV1Workspaces201 = {
  data: PostApiV1Workspaces201Data;
};

export type GetApiV1WorkspacesId200DataSettings = { [key: string]: unknown };

export type GetApiV1WorkspacesId200DataVisibility =
  (typeof GetApiV1WorkspacesId200DataVisibility)[keyof typeof GetApiV1WorkspacesId200DataVisibility];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiV1WorkspacesId200DataVisibility = {
  public: 'public',
  private: 'private',
} as const;

export type GetApiV1WorkspacesId200DataStatus =
  (typeof GetApiV1WorkspacesId200DataStatus)[keyof typeof GetApiV1WorkspacesId200DataStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiV1WorkspacesId200DataStatus = {
  active: 'active',
  archived: 'archived',
} as const;

export type GetApiV1WorkspacesId200Data = {
  id: string;
  organizationId: string;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  slug: string;
  /** @nullable */
  description: string | null;
  /**
   * @maxLength 500
   * @nullable
   */
  icon: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  settings?: GetApiV1WorkspacesId200DataSettings;
  /** @nullable */
  defaultProjectId: string | null;
  visibility: GetApiV1WorkspacesId200DataVisibility;
  status: GetApiV1WorkspacesId200DataStatus;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  /** @nullable */
  createdBy: string | null;
  organization: Organization;
  creator: User;
};

export type GetApiV1WorkspacesId200 = {
  data: GetApiV1WorkspacesId200Data;
};

export type PatchApiV1WorkspacesIdBodySettings = { [key: string]: unknown };

export type PatchApiV1WorkspacesIdBodyVisibility =
  (typeof PatchApiV1WorkspacesIdBodyVisibility)[keyof typeof PatchApiV1WorkspacesIdBodyVisibility];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1WorkspacesIdBodyVisibility = {
  public: 'public',
  private: 'private',
} as const;

export type PatchApiV1WorkspacesIdBodyStatus =
  (typeof PatchApiV1WorkspacesIdBodyStatus)[keyof typeof PatchApiV1WorkspacesIdBodyStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1WorkspacesIdBodyStatus = {
  active: 'active',
  archived: 'archived',
} as const;

export type PatchApiV1WorkspacesIdBody = {
  organizationId?: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  name?: string;
  /**
   * @minLength 1
   * @maxLength 255
   */
  slug?: string;
  /** @nullable */
  description?: string | null;
  /** @nullable */
  icon?: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color?: string | null;
  settings?: PatchApiV1WorkspacesIdBodySettings;
  /** @nullable */
  defaultProjectId?: string | null;
  visibility?: PatchApiV1WorkspacesIdBodyVisibility;
  status?: PatchApiV1WorkspacesIdBodyStatus;
  isActive?: boolean;
  /** @nullable */
  createdBy?: string | null;
};

export type PatchApiV1WorkspacesId200DataSettings = { [key: string]: unknown };

export type PatchApiV1WorkspacesId200DataVisibility =
  (typeof PatchApiV1WorkspacesId200DataVisibility)[keyof typeof PatchApiV1WorkspacesId200DataVisibility];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1WorkspacesId200DataVisibility = {
  public: 'public',
  private: 'private',
} as const;

export type PatchApiV1WorkspacesId200DataStatus =
  (typeof PatchApiV1WorkspacesId200DataStatus)[keyof typeof PatchApiV1WorkspacesId200DataStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PatchApiV1WorkspacesId200DataStatus = {
  active: 'active',
  archived: 'archived',
} as const;

export type PatchApiV1WorkspacesId200Data = {
  id: string;
  organizationId: string;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  slug: string;
  /** @nullable */
  description: string | null;
  /**
   * @maxLength 500
   * @nullable
   */
  icon: string | null;
  /**
   * @maxLength 7
   * @nullable
   */
  color: string | null;
  settings?: PatchApiV1WorkspacesId200DataSettings;
  /** @nullable */
  defaultProjectId: string | null;
  visibility: PatchApiV1WorkspacesId200DataVisibility;
  status: PatchApiV1WorkspacesId200DataStatus;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  /** @nullable */
  createdBy: string | null;
  organization: Organization;
  creator: User;
};

export type PatchApiV1WorkspacesId200 = {
  data: PatchApiV1WorkspacesId200Data;
};

/**
 * Successfully deleted
 * @nullable
 */
export type DeleteApiV1WorkspacesId204 =
  | (typeof DeleteApiV1WorkspacesId204)[keyof typeof DeleteApiV1WorkspacesId204]
  | null;

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DeleteApiV1WorkspacesId204 = {
  null: 'null',
} as const;

export type GetApiV1AdminHealth200Status =
  (typeof GetApiV1AdminHealth200Status)[keyof typeof GetApiV1AdminHealth200Status];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetApiV1AdminHealth200Status = {
  healthy: 'healthy',
  warning: 'warning',
  critical: 'critical',
} as const;

export type GetApiV1AdminHealth200Metrics = {
  totalRequests: number;
  errorRate: number;
  avgResponseTime: number;
};

export type GetApiV1AdminHealth200 = {
  status: GetApiV1AdminHealth200Status;
  timestamp: string;
  uptime: number;
  metrics: GetApiV1AdminHealth200Metrics;
};
