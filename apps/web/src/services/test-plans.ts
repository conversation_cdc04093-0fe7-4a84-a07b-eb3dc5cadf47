/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1TestPlansId204,
  ErrorResponse,
  GetApiV1TestPlans200,
  GetApiV1TestPlansId200,
  GetApiV1TestPlansParams,
  PatchApiV1TestPlansId200,
  PatchApiV1TestPlansIdBody,
  PostApiV1TestPlans201,
  PostApiV1TestPlansBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of test plans resources with optional filtering and sorting
 * @summary Get test plans list
 */
export const getApiV1TestPlans = (params?: GetApiV1TestPlansParams, signal?: AbortSignal) => {
  return axiosClient<GetApiV1TestPlans200>({
    url: '/api/v1/test-plans/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1TestPlansQueryKey = (params?: GetApiV1TestPlansParams) => {
  return ['/api/v1/test-plans/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1TestPlansQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1TestPlans>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestPlansParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestPlans>>, TError, TData>>;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1TestPlansQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1TestPlans>>> = ({ signal }) =>
    getApiV1TestPlans(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1TestPlans>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1TestPlansQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1TestPlans>>
>;
export type GetApiV1TestPlansQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1TestPlans<
  TData = Awaited<ReturnType<typeof getApiV1TestPlans>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1TestPlansParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestPlans>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestPlans>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestPlans>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestPlans<
  TData = Awaited<ReturnType<typeof getApiV1TestPlans>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestPlansParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestPlans>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestPlans>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestPlans>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestPlans<
  TData = Awaited<ReturnType<typeof getApiV1TestPlans>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestPlansParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestPlans>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get test plans list
 */

export function useGetApiV1TestPlans<
  TData = Awaited<ReturnType<typeof getApiV1TestPlans>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestPlansParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestPlans>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1TestPlansQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new test plan resource
 * @summary Create test plan
 */
export const postApiV1TestPlans = (
  postApiV1TestPlansBody: BodyType<PostApiV1TestPlansBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1TestPlans201>({
    url: '/api/v1/test-plans/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1TestPlansBody,
    signal,
  });
};

export const getPostApiV1TestPlansMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1TestPlans>>,
    TError,
    { data: BodyType<PostApiV1TestPlansBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1TestPlans>>,
  TError,
  { data: BodyType<PostApiV1TestPlansBody> },
  TContext
> => {
  const mutationKey = ['postApiV1TestPlans'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1TestPlans>>,
    { data: BodyType<PostApiV1TestPlansBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1TestPlans(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1TestPlansMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1TestPlans>>
>;
export type PostApiV1TestPlansMutationBody = BodyType<PostApiV1TestPlansBody>;
export type PostApiV1TestPlansMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create test plan
 */
export const usePostApiV1TestPlans = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1TestPlans>>,
    TError,
    { data: BodyType<PostApiV1TestPlansBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1TestPlans>>,
  TError,
  { data: BodyType<PostApiV1TestPlansBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1TestPlansMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific test plan resource by its unique identifier
 * @summary Get test plan by ID
 */
export const getApiV1TestPlansId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1TestPlansId200>({
    url: `/api/v1/test-plans/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1TestPlansIdQueryKey = (id: string) => {
  return [`/api/v1/test-plans/${id}`] as const;
};

export const getGetApiV1TestPlansIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1TestPlansId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestPlansId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1TestPlansIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1TestPlansId>>> = ({ signal }) =>
    getApiV1TestPlansId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestPlansId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1TestPlansIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1TestPlansId>>
>;
export type GetApiV1TestPlansIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1TestPlansId<
  TData = Awaited<ReturnType<typeof getApiV1TestPlansId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestPlansId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestPlansId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestPlansId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestPlansId<
  TData = Awaited<ReturnType<typeof getApiV1TestPlansId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestPlansId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestPlansId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestPlansId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestPlansId<
  TData = Awaited<ReturnType<typeof getApiV1TestPlansId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestPlansId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get test plan by ID
 */

export function useGetApiV1TestPlansId<
  TData = Awaited<ReturnType<typeof getApiV1TestPlansId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestPlansId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1TestPlansIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing test plan resource
 * @summary Update test plan
 */
export const patchApiV1TestPlansId = (
  id: string,
  patchApiV1TestPlansIdBody: BodyType<PatchApiV1TestPlansIdBody>,
) => {
  return axiosClient<PatchApiV1TestPlansId200>({
    url: `/api/v1/test-plans/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1TestPlansIdBody,
  });
};

export const getPatchApiV1TestPlansIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1TestPlansId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1TestPlansIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1TestPlansId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1TestPlansIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1TestPlansId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1TestPlansId>>,
    { id: string; data: BodyType<PatchApiV1TestPlansIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1TestPlansId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1TestPlansIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1TestPlansId>>
>;
export type PatchApiV1TestPlansIdMutationBody = BodyType<PatchApiV1TestPlansIdBody>;
export type PatchApiV1TestPlansIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update test plan
 */
export const usePatchApiV1TestPlansId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1TestPlansId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1TestPlansIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1TestPlansId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1TestPlansIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1TestPlansIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing test plan resource
 * @summary Delete test plan
 */
export const deleteApiV1TestPlansId = (id: string) => {
  return axiosClient<DeleteApiV1TestPlansId204>({
    url: `/api/v1/test-plans/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1TestPlansIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1TestPlansId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1TestPlansId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1TestPlansId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1TestPlansId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1TestPlansId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1TestPlansIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1TestPlansId>>
>;

export type DeleteApiV1TestPlansIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete test plan
 */
export const useDeleteApiV1TestPlansId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1TestPlansId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1TestPlansId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1TestPlansIdMutationOptions(options);

  return useMutation(mutationOptions);
};
