/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1WorkItemsId204,
  ErrorResponse,
  GetApiV1WorkItems200,
  GetApiV1WorkItemsId200,
  GetApiV1WorkItemsParams,
  PatchApiV1WorkItemsId200,
  PatchApiV1WorkItemsIdBody,
  PostApiV1WorkItems201,
  PostApiV1WorkItemsBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of work-items resources with optional filtering and sorting
 * @summary Get work-items list
 */
export const getApiV1WorkItems = (params?: GetApiV1WorkItemsParams, signal?: AbortSignal) => {
  return axiosClient<GetApiV1WorkItems200>({
    url: '/api/v1/work-items/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1WorkItemsQueryKey = (params?: GetApiV1WorkItemsParams) => {
  return ['/api/v1/work-items/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1WorkItemsQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1WorkItems>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItems>>, TError, TData>>;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1WorkItemsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1WorkItems>>> = ({ signal }) =>
    getApiV1WorkItems(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1WorkItems>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1WorkItemsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1WorkItems>>
>;
export type GetApiV1WorkItemsQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1WorkItems<
  TData = Awaited<ReturnType<typeof getApiV1WorkItems>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1WorkItemsParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItems>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItems>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItems>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItems<
  TData = Awaited<ReturnType<typeof getApiV1WorkItems>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItems>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItems>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItems>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItems<
  TData = Awaited<ReturnType<typeof getApiV1WorkItems>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItems>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get work-items list
 */

export function useGetApiV1WorkItems<
  TData = Awaited<ReturnType<typeof getApiV1WorkItems>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItems>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1WorkItemsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new work-item resource
 * @summary Create work-item
 */
export const postApiV1WorkItems = (
  postApiV1WorkItemsBody: BodyType<PostApiV1WorkItemsBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1WorkItems201>({
    url: '/api/v1/work-items/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1WorkItemsBody,
    signal,
  });
};

export const getPostApiV1WorkItemsMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1WorkItems>>,
    TError,
    { data: BodyType<PostApiV1WorkItemsBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1WorkItems>>,
  TError,
  { data: BodyType<PostApiV1WorkItemsBody> },
  TContext
> => {
  const mutationKey = ['postApiV1WorkItems'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1WorkItems>>,
    { data: BodyType<PostApiV1WorkItemsBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1WorkItems(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1WorkItemsMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1WorkItems>>
>;
export type PostApiV1WorkItemsMutationBody = BodyType<PostApiV1WorkItemsBody>;
export type PostApiV1WorkItemsMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create work-item
 */
export const usePostApiV1WorkItems = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1WorkItems>>,
    TError,
    { data: BodyType<PostApiV1WorkItemsBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1WorkItems>>,
  TError,
  { data: BodyType<PostApiV1WorkItemsBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1WorkItemsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific work-item resource by its unique identifier
 * @summary Get work-item by ID
 */
export const getApiV1WorkItemsId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1WorkItemsId200>({
    url: `/api/v1/work-items/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1WorkItemsIdQueryKey = (id: string) => {
  return [`/api/v1/work-items/${id}`] as const;
};

export const getGetApiV1WorkItemsIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1WorkItemsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemsId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1WorkItemsIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1WorkItemsId>>> = ({ signal }) =>
    getApiV1WorkItemsId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemsId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1WorkItemsIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1WorkItemsId>>
>;
export type GetApiV1WorkItemsIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1WorkItemsId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemsId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItemsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItemsId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItemsId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemsId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItemsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItemsId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItemsId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get work-item by ID
 */

export function useGetApiV1WorkItemsId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1WorkItemsIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing work-item resource
 * @summary Update work-item
 */
export const patchApiV1WorkItemsId = (
  id: string,
  patchApiV1WorkItemsIdBody: BodyType<PatchApiV1WorkItemsIdBody>,
) => {
  return axiosClient<PatchApiV1WorkItemsId200>({
    url: `/api/v1/work-items/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1WorkItemsIdBody,
  });
};

export const getPatchApiV1WorkItemsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1WorkItemsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1WorkItemsIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1WorkItemsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1WorkItemsIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1WorkItemsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1WorkItemsId>>,
    { id: string; data: BodyType<PatchApiV1WorkItemsIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1WorkItemsId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1WorkItemsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1WorkItemsId>>
>;
export type PatchApiV1WorkItemsIdMutationBody = BodyType<PatchApiV1WorkItemsIdBody>;
export type PatchApiV1WorkItemsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update work-item
 */
export const usePatchApiV1WorkItemsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1WorkItemsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1WorkItemsIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1WorkItemsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1WorkItemsIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1WorkItemsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing work-item resource
 * @summary Delete work-item
 */
export const deleteApiV1WorkItemsId = (id: string) => {
  return axiosClient<DeleteApiV1WorkItemsId204>({
    url: `/api/v1/work-items/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1WorkItemsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1WorkItemsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1WorkItemsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1WorkItemsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1WorkItemsId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1WorkItemsId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1WorkItemsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1WorkItemsId>>
>;

export type DeleteApiV1WorkItemsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete work-item
 */
export const useDeleteApiV1WorkItemsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1WorkItemsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1WorkItemsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1WorkItemsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
