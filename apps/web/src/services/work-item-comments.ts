/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1WorkItemCommentsId204,
  ErrorResponse,
  GetApiV1WorkItemComments200,
  GetApiV1WorkItemCommentsId200,
  GetApiV1WorkItemCommentsParams,
  PatchApiV1WorkItemCommentsId200,
  PatchApiV1WorkItemCommentsIdBody,
  PostApiV1WorkItemComments201,
  PostApiV1WorkItemCommentsBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of work-item-comments resources with optional filtering and sorting
 * @summary Get work-item-comments list
 */
export const getApiV1WorkItemComments = (
  params?: GetApiV1WorkItemCommentsParams,
  signal?: AbortSignal,
) => {
  return axiosClient<GetApiV1WorkItemComments200>({
    url: '/api/v1/work-item-comments/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1WorkItemCommentsQueryKey = (params?: GetApiV1WorkItemCommentsParams) => {
  return ['/api/v1/work-item-comments/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1WorkItemCommentsQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1WorkItemComments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemCommentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemComments>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1WorkItemCommentsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1WorkItemComments>>> = ({
    signal,
  }) => getApiV1WorkItemComments(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1WorkItemComments>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1WorkItemCommentsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1WorkItemComments>>
>;
export type GetApiV1WorkItemCommentsQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1WorkItemComments<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemComments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1WorkItemCommentsParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemComments>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItemComments>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItemComments>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItemComments<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemComments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemCommentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemComments>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItemComments>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItemComments>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItemComments<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemComments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemCommentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemComments>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get work-item-comments list
 */

export function useGetApiV1WorkItemComments<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemComments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemCommentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemComments>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1WorkItemCommentsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new work-item-comment resource
 * @summary Create work-item-comment
 */
export const postApiV1WorkItemComments = (
  postApiV1WorkItemCommentsBody: BodyType<PostApiV1WorkItemCommentsBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1WorkItemComments201>({
    url: '/api/v1/work-item-comments/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1WorkItemCommentsBody,
    signal,
  });
};

export const getPostApiV1WorkItemCommentsMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1WorkItemComments>>,
    TError,
    { data: BodyType<PostApiV1WorkItemCommentsBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1WorkItemComments>>,
  TError,
  { data: BodyType<PostApiV1WorkItemCommentsBody> },
  TContext
> => {
  const mutationKey = ['postApiV1WorkItemComments'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1WorkItemComments>>,
    { data: BodyType<PostApiV1WorkItemCommentsBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1WorkItemComments(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1WorkItemCommentsMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1WorkItemComments>>
>;
export type PostApiV1WorkItemCommentsMutationBody = BodyType<PostApiV1WorkItemCommentsBody>;
export type PostApiV1WorkItemCommentsMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create work-item-comment
 */
export const usePostApiV1WorkItemComments = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1WorkItemComments>>,
    TError,
    { data: BodyType<PostApiV1WorkItemCommentsBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1WorkItemComments>>,
  TError,
  { data: BodyType<PostApiV1WorkItemCommentsBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1WorkItemCommentsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific work-item-comment resource by its unique identifier
 * @summary Get work-item-comment by ID
 */
export const getApiV1WorkItemCommentsId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1WorkItemCommentsId200>({
    url: `/api/v1/work-item-comments/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1WorkItemCommentsIdQueryKey = (id: string) => {
  return [`/api/v1/work-item-comments/${id}`] as const;
};

export const getGetApiV1WorkItemCommentsIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1WorkItemCommentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemCommentsId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1WorkItemCommentsIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1WorkItemCommentsId>>> = ({
    signal,
  }) => getApiV1WorkItemCommentsId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemCommentsId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1WorkItemCommentsIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1WorkItemCommentsId>>
>;
export type GetApiV1WorkItemCommentsIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1WorkItemCommentsId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemCommentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemCommentsId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItemCommentsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItemCommentsId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItemCommentsId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemCommentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemCommentsId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItemCommentsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItemCommentsId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItemCommentsId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemCommentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemCommentsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get work-item-comment by ID
 */

export function useGetApiV1WorkItemCommentsId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemCommentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemCommentsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1WorkItemCommentsIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing work-item-comment resource
 * @summary Update work-item-comment
 */
export const patchApiV1WorkItemCommentsId = (
  id: string,
  patchApiV1WorkItemCommentsIdBody: BodyType<PatchApiV1WorkItemCommentsIdBody>,
) => {
  return axiosClient<PatchApiV1WorkItemCommentsId200>({
    url: `/api/v1/work-item-comments/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1WorkItemCommentsIdBody,
  });
};

export const getPatchApiV1WorkItemCommentsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1WorkItemCommentsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1WorkItemCommentsIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1WorkItemCommentsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1WorkItemCommentsIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1WorkItemCommentsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1WorkItemCommentsId>>,
    { id: string; data: BodyType<PatchApiV1WorkItemCommentsIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1WorkItemCommentsId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1WorkItemCommentsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1WorkItemCommentsId>>
>;
export type PatchApiV1WorkItemCommentsIdMutationBody = BodyType<PatchApiV1WorkItemCommentsIdBody>;
export type PatchApiV1WorkItemCommentsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update work-item-comment
 */
export const usePatchApiV1WorkItemCommentsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1WorkItemCommentsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1WorkItemCommentsIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1WorkItemCommentsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1WorkItemCommentsIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1WorkItemCommentsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing work-item-comment resource
 * @summary Delete work-item-comment
 */
export const deleteApiV1WorkItemCommentsId = (id: string) => {
  return axiosClient<DeleteApiV1WorkItemCommentsId204>({
    url: `/api/v1/work-item-comments/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1WorkItemCommentsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1WorkItemCommentsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1WorkItemCommentsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1WorkItemCommentsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1WorkItemCommentsId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1WorkItemCommentsId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1WorkItemCommentsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1WorkItemCommentsId>>
>;

export type DeleteApiV1WorkItemCommentsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete work-item-comment
 */
export const useDeleteApiV1WorkItemCommentsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1WorkItemCommentsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1WorkItemCommentsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1WorkItemCommentsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
