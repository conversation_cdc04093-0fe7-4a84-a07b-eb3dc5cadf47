/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1WorkItemFieldsId204,
  ErrorResponse,
  GetApiV1WorkItemFields200,
  GetApiV1WorkItemFieldsId200,
  GetApiV1WorkItemFieldsParams,
  PatchApiV1WorkItemFieldsId200,
  PatchApiV1WorkItemFieldsIdBody,
  PostApiV1WorkItemFields201,
  PostApiV1WorkItemFieldsBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of work-item-fields resources with optional filtering and sorting
 * @summary Get work-item-fields list
 */
export const getApiV1WorkItemFields = (
  params?: GetApiV1WorkItemFieldsParams,
  signal?: AbortSignal,
) => {
  return axiosClient<GetApiV1WorkItemFields200>({
    url: '/api/v1/work-item-fields/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1WorkItemFieldsQueryKey = (params?: GetApiV1WorkItemFieldsParams) => {
  return ['/api/v1/work-item-fields/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1WorkItemFieldsQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1WorkItemFields>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemFieldsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemFields>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1WorkItemFieldsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1WorkItemFields>>> = ({ signal }) =>
    getApiV1WorkItemFields(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1WorkItemFields>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1WorkItemFieldsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1WorkItemFields>>
>;
export type GetApiV1WorkItemFieldsQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1WorkItemFields<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemFields>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1WorkItemFieldsParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemFields>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItemFields>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItemFields>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItemFields<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemFields>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemFieldsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemFields>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItemFields>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItemFields>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItemFields<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemFields>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemFieldsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemFields>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get work-item-fields list
 */

export function useGetApiV1WorkItemFields<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemFields>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemFieldsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemFields>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1WorkItemFieldsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new work-item-field resource
 * @summary Create work-item-field
 */
export const postApiV1WorkItemFields = (
  postApiV1WorkItemFieldsBody: BodyType<PostApiV1WorkItemFieldsBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1WorkItemFields201>({
    url: '/api/v1/work-item-fields/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1WorkItemFieldsBody,
    signal,
  });
};

export const getPostApiV1WorkItemFieldsMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1WorkItemFields>>,
    TError,
    { data: BodyType<PostApiV1WorkItemFieldsBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1WorkItemFields>>,
  TError,
  { data: BodyType<PostApiV1WorkItemFieldsBody> },
  TContext
> => {
  const mutationKey = ['postApiV1WorkItemFields'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1WorkItemFields>>,
    { data: BodyType<PostApiV1WorkItemFieldsBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1WorkItemFields(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1WorkItemFieldsMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1WorkItemFields>>
>;
export type PostApiV1WorkItemFieldsMutationBody = BodyType<PostApiV1WorkItemFieldsBody>;
export type PostApiV1WorkItemFieldsMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create work-item-field
 */
export const usePostApiV1WorkItemFields = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1WorkItemFields>>,
    TError,
    { data: BodyType<PostApiV1WorkItemFieldsBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1WorkItemFields>>,
  TError,
  { data: BodyType<PostApiV1WorkItemFieldsBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1WorkItemFieldsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific work-item-field resource by its unique identifier
 * @summary Get work-item-field by ID
 */
export const getApiV1WorkItemFieldsId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1WorkItemFieldsId200>({
    url: `/api/v1/work-item-fields/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1WorkItemFieldsIdQueryKey = (id: string) => {
  return [`/api/v1/work-item-fields/${id}`] as const;
};

export const getGetApiV1WorkItemFieldsIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1WorkItemFieldsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemFieldsId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1WorkItemFieldsIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1WorkItemFieldsId>>> = ({
    signal,
  }) => getApiV1WorkItemFieldsId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemFieldsId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1WorkItemFieldsIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1WorkItemFieldsId>>
>;
export type GetApiV1WorkItemFieldsIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1WorkItemFieldsId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemFieldsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemFieldsId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItemFieldsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItemFieldsId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItemFieldsId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemFieldsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemFieldsId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItemFieldsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItemFieldsId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItemFieldsId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemFieldsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemFieldsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get work-item-field by ID
 */

export function useGetApiV1WorkItemFieldsId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemFieldsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemFieldsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1WorkItemFieldsIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing work-item-field resource
 * @summary Update work-item-field
 */
export const patchApiV1WorkItemFieldsId = (
  id: string,
  patchApiV1WorkItemFieldsIdBody: BodyType<PatchApiV1WorkItemFieldsIdBody>,
) => {
  return axiosClient<PatchApiV1WorkItemFieldsId200>({
    url: `/api/v1/work-item-fields/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1WorkItemFieldsIdBody,
  });
};

export const getPatchApiV1WorkItemFieldsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1WorkItemFieldsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1WorkItemFieldsIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1WorkItemFieldsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1WorkItemFieldsIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1WorkItemFieldsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1WorkItemFieldsId>>,
    { id: string; data: BodyType<PatchApiV1WorkItemFieldsIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1WorkItemFieldsId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1WorkItemFieldsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1WorkItemFieldsId>>
>;
export type PatchApiV1WorkItemFieldsIdMutationBody = BodyType<PatchApiV1WorkItemFieldsIdBody>;
export type PatchApiV1WorkItemFieldsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update work-item-field
 */
export const usePatchApiV1WorkItemFieldsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1WorkItemFieldsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1WorkItemFieldsIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1WorkItemFieldsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1WorkItemFieldsIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1WorkItemFieldsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing work-item-field resource
 * @summary Delete work-item-field
 */
export const deleteApiV1WorkItemFieldsId = (id: string) => {
  return axiosClient<DeleteApiV1WorkItemFieldsId204>({
    url: `/api/v1/work-item-fields/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1WorkItemFieldsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1WorkItemFieldsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1WorkItemFieldsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1WorkItemFieldsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1WorkItemFieldsId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1WorkItemFieldsId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1WorkItemFieldsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1WorkItemFieldsId>>
>;

export type DeleteApiV1WorkItemFieldsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete work-item-field
 */
export const useDeleteApiV1WorkItemFieldsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1WorkItemFieldsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1WorkItemFieldsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1WorkItemFieldsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
