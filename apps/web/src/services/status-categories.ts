/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1StatusCategoriesId204,
  ErrorResponse,
  GetApiV1StatusCategories200,
  GetApiV1StatusCategoriesId200,
  GetApiV1StatusCategoriesParams,
  PatchApiV1StatusCategoriesId200,
  PatchApiV1StatusCategoriesIdBody,
  PostApiV1StatusCategories201,
  PostApiV1StatusCategoriesBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of status-categories resources with optional filtering and sorting
 * @summary Get status-categories list
 */
export const getApiV1StatusCategories = (
  params?: GetApiV1StatusCategoriesParams,
  signal?: AbortSignal,
) => {
  return axiosClient<GetApiV1StatusCategories200>({
    url: '/api/v1/status-categories/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1StatusCategoriesQueryKey = (params?: GetApiV1StatusCategoriesParams) => {
  return ['/api/v1/status-categories/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1StatusCategoriesQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1StatusCategories>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1StatusCategoriesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1StatusCategories>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1StatusCategoriesQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1StatusCategories>>> = ({
    signal,
  }) => getApiV1StatusCategories(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1StatusCategories>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1StatusCategoriesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1StatusCategories>>
>;
export type GetApiV1StatusCategoriesQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1StatusCategories<
  TData = Awaited<ReturnType<typeof getApiV1StatusCategories>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1StatusCategoriesParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1StatusCategories>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1StatusCategories>>,
          TError,
          Awaited<ReturnType<typeof getApiV1StatusCategories>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1StatusCategories<
  TData = Awaited<ReturnType<typeof getApiV1StatusCategories>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1StatusCategoriesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1StatusCategories>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1StatusCategories>>,
          TError,
          Awaited<ReturnType<typeof getApiV1StatusCategories>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1StatusCategories<
  TData = Awaited<ReturnType<typeof getApiV1StatusCategories>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1StatusCategoriesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1StatusCategories>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get status-categories list
 */

export function useGetApiV1StatusCategories<
  TData = Awaited<ReturnType<typeof getApiV1StatusCategories>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1StatusCategoriesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1StatusCategories>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1StatusCategoriesQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new status-category resource
 * @summary Create status-category
 */
export const postApiV1StatusCategories = (
  postApiV1StatusCategoriesBody: BodyType<PostApiV1StatusCategoriesBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1StatusCategories201>({
    url: '/api/v1/status-categories/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1StatusCategoriesBody,
    signal,
  });
};

export const getPostApiV1StatusCategoriesMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1StatusCategories>>,
    TError,
    { data: BodyType<PostApiV1StatusCategoriesBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1StatusCategories>>,
  TError,
  { data: BodyType<PostApiV1StatusCategoriesBody> },
  TContext
> => {
  const mutationKey = ['postApiV1StatusCategories'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1StatusCategories>>,
    { data: BodyType<PostApiV1StatusCategoriesBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1StatusCategories(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1StatusCategoriesMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1StatusCategories>>
>;
export type PostApiV1StatusCategoriesMutationBody = BodyType<PostApiV1StatusCategoriesBody>;
export type PostApiV1StatusCategoriesMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create status-category
 */
export const usePostApiV1StatusCategories = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1StatusCategories>>,
    TError,
    { data: BodyType<PostApiV1StatusCategoriesBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1StatusCategories>>,
  TError,
  { data: BodyType<PostApiV1StatusCategoriesBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1StatusCategoriesMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific status-category resource by its unique identifier
 * @summary Get status-category by ID
 */
export const getApiV1StatusCategoriesId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1StatusCategoriesId200>({
    url: `/api/v1/status-categories/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1StatusCategoriesIdQueryKey = (id: string) => {
  return [`/api/v1/status-categories/${id}`] as const;
};

export const getGetApiV1StatusCategoriesIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1StatusCategoriesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1StatusCategoriesId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1StatusCategoriesIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1StatusCategoriesId>>> = ({
    signal,
  }) => getApiV1StatusCategoriesId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1StatusCategoriesId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1StatusCategoriesIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1StatusCategoriesId>>
>;
export type GetApiV1StatusCategoriesIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1StatusCategoriesId<
  TData = Awaited<ReturnType<typeof getApiV1StatusCategoriesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1StatusCategoriesId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1StatusCategoriesId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1StatusCategoriesId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1StatusCategoriesId<
  TData = Awaited<ReturnType<typeof getApiV1StatusCategoriesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1StatusCategoriesId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1StatusCategoriesId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1StatusCategoriesId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1StatusCategoriesId<
  TData = Awaited<ReturnType<typeof getApiV1StatusCategoriesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1StatusCategoriesId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get status-category by ID
 */

export function useGetApiV1StatusCategoriesId<
  TData = Awaited<ReturnType<typeof getApiV1StatusCategoriesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1StatusCategoriesId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1StatusCategoriesIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing status-category resource
 * @summary Update status-category
 */
export const patchApiV1StatusCategoriesId = (
  id: string,
  patchApiV1StatusCategoriesIdBody: BodyType<PatchApiV1StatusCategoriesIdBody>,
) => {
  return axiosClient<PatchApiV1StatusCategoriesId200>({
    url: `/api/v1/status-categories/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1StatusCategoriesIdBody,
  });
};

export const getPatchApiV1StatusCategoriesIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1StatusCategoriesId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1StatusCategoriesIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1StatusCategoriesId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1StatusCategoriesIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1StatusCategoriesId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1StatusCategoriesId>>,
    { id: string; data: BodyType<PatchApiV1StatusCategoriesIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1StatusCategoriesId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1StatusCategoriesIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1StatusCategoriesId>>
>;
export type PatchApiV1StatusCategoriesIdMutationBody = BodyType<PatchApiV1StatusCategoriesIdBody>;
export type PatchApiV1StatusCategoriesIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update status-category
 */
export const usePatchApiV1StatusCategoriesId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1StatusCategoriesId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1StatusCategoriesIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1StatusCategoriesId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1StatusCategoriesIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1StatusCategoriesIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing status-category resource
 * @summary Delete status-category
 */
export const deleteApiV1StatusCategoriesId = (id: string) => {
  return axiosClient<DeleteApiV1StatusCategoriesId204>({
    url: `/api/v1/status-categories/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1StatusCategoriesIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1StatusCategoriesId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1StatusCategoriesId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1StatusCategoriesId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1StatusCategoriesId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1StatusCategoriesId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1StatusCategoriesIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1StatusCategoriesId>>
>;

export type DeleteApiV1StatusCategoriesIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete status-category
 */
export const useDeleteApiV1StatusCategoriesId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1StatusCategoriesId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1StatusCategoriesId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1StatusCategoriesIdMutationOptions(options);

  return useMutation(mutationOptions);
};
