/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1OrganizationsId204,
  ErrorResponse,
  GetApiV1Organizations200,
  GetApiV1OrganizationsId200,
  GetApiV1OrganizationsParams,
  PatchApiV1OrganizationsId200,
  PatchApiV1OrganizationsIdBody,
  PostApiV1Organizations201,
  PostApiV1OrganizationsBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of organizations resources with optional filtering and sorting
 * @summary Get organizations list
 */
export const getApiV1Organizations = (
  params?: GetApiV1OrganizationsParams,
  signal?: AbortSignal,
) => {
  return axiosClient<GetApiV1Organizations200>({
    url: '/api/v1/organizations/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1OrganizationsQueryKey = (params?: GetApiV1OrganizationsParams) => {
  return ['/api/v1/organizations/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1OrganizationsQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1Organizations>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1OrganizationsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1Organizations>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1OrganizationsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1Organizations>>> = ({ signal }) =>
    getApiV1Organizations(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1Organizations>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1OrganizationsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1Organizations>>
>;
export type GetApiV1OrganizationsQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1Organizations<
  TData = Awaited<ReturnType<typeof getApiV1Organizations>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1OrganizationsParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1Organizations>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Organizations>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Organizations>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Organizations<
  TData = Awaited<ReturnType<typeof getApiV1Organizations>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1OrganizationsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1Organizations>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Organizations>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Organizations>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Organizations<
  TData = Awaited<ReturnType<typeof getApiV1Organizations>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1OrganizationsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1Organizations>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get organizations list
 */

export function useGetApiV1Organizations<
  TData = Awaited<ReturnType<typeof getApiV1Organizations>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1OrganizationsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1Organizations>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1OrganizationsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new organization resource
 * @summary Create organization
 */
export const postApiV1Organizations = (
  postApiV1OrganizationsBody: BodyType<PostApiV1OrganizationsBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1Organizations201>({
    url: '/api/v1/organizations/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1OrganizationsBody,
    signal,
  });
};

export const getPostApiV1OrganizationsMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1Organizations>>,
    TError,
    { data: BodyType<PostApiV1OrganizationsBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1Organizations>>,
  TError,
  { data: BodyType<PostApiV1OrganizationsBody> },
  TContext
> => {
  const mutationKey = ['postApiV1Organizations'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1Organizations>>,
    { data: BodyType<PostApiV1OrganizationsBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1Organizations(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1OrganizationsMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1Organizations>>
>;
export type PostApiV1OrganizationsMutationBody = BodyType<PostApiV1OrganizationsBody>;
export type PostApiV1OrganizationsMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create organization
 */
export const usePostApiV1Organizations = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1Organizations>>,
    TError,
    { data: BodyType<PostApiV1OrganizationsBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1Organizations>>,
  TError,
  { data: BodyType<PostApiV1OrganizationsBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1OrganizationsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific organization resource by its unique identifier
 * @summary Get organization by ID
 */
export const getApiV1OrganizationsId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1OrganizationsId200>({
    url: `/api/v1/organizations/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1OrganizationsIdQueryKey = (id: string) => {
  return [`/api/v1/organizations/${id}`] as const;
};

export const getGetApiV1OrganizationsIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1OrganizationsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1OrganizationsId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1OrganizationsIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1OrganizationsId>>> = ({
    signal,
  }) => getApiV1OrganizationsId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1OrganizationsId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1OrganizationsIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1OrganizationsId>>
>;
export type GetApiV1OrganizationsIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1OrganizationsId<
  TData = Awaited<ReturnType<typeof getApiV1OrganizationsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1OrganizationsId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1OrganizationsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1OrganizationsId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1OrganizationsId<
  TData = Awaited<ReturnType<typeof getApiV1OrganizationsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1OrganizationsId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1OrganizationsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1OrganizationsId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1OrganizationsId<
  TData = Awaited<ReturnType<typeof getApiV1OrganizationsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1OrganizationsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get organization by ID
 */

export function useGetApiV1OrganizationsId<
  TData = Awaited<ReturnType<typeof getApiV1OrganizationsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1OrganizationsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1OrganizationsIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing organization resource
 * @summary Update organization
 */
export const patchApiV1OrganizationsId = (
  id: string,
  patchApiV1OrganizationsIdBody: BodyType<PatchApiV1OrganizationsIdBody>,
) => {
  return axiosClient<PatchApiV1OrganizationsId200>({
    url: `/api/v1/organizations/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1OrganizationsIdBody,
  });
};

export const getPatchApiV1OrganizationsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1OrganizationsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1OrganizationsIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1OrganizationsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1OrganizationsIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1OrganizationsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1OrganizationsId>>,
    { id: string; data: BodyType<PatchApiV1OrganizationsIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1OrganizationsId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1OrganizationsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1OrganizationsId>>
>;
export type PatchApiV1OrganizationsIdMutationBody = BodyType<PatchApiV1OrganizationsIdBody>;
export type PatchApiV1OrganizationsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update organization
 */
export const usePatchApiV1OrganizationsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1OrganizationsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1OrganizationsIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1OrganizationsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1OrganizationsIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1OrganizationsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing organization resource
 * @summary Delete organization
 */
export const deleteApiV1OrganizationsId = (id: string) => {
  return axiosClient<DeleteApiV1OrganizationsId204>({
    url: `/api/v1/organizations/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1OrganizationsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1OrganizationsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1OrganizationsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1OrganizationsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1OrganizationsId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1OrganizationsId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1OrganizationsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1OrganizationsId>>
>;

export type DeleteApiV1OrganizationsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete organization
 */
export const useDeleteApiV1OrganizationsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1OrganizationsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1OrganizationsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1OrganizationsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
