/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1InvitationsId204,
  ErrorResponse,
  GetApiV1Invitations200,
  GetApiV1InvitationsId200,
  GetApiV1InvitationsParams,
  PatchApiV1InvitationsId200,
  PatchApiV1InvitationsIdBody,
  PostApiV1Invitations201,
  PostApiV1InvitationsBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of invitations resources with optional filtering and sorting
 * @summary Get invitations list
 */
export const getApiV1Invitations = (params?: GetApiV1InvitationsParams, signal?: AbortSignal) => {
  return axiosClient<GetApiV1Invitations200>({
    url: '/api/v1/invitations/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1InvitationsQueryKey = (params?: GetApiV1InvitationsParams) => {
  return ['/api/v1/invitations/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1InvitationsQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1Invitations>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1InvitationsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1Invitations>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1InvitationsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1Invitations>>> = ({ signal }) =>
    getApiV1Invitations(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1Invitations>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1InvitationsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1Invitations>>
>;
export type GetApiV1InvitationsQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1Invitations<
  TData = Awaited<ReturnType<typeof getApiV1Invitations>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1InvitationsParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1Invitations>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Invitations>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Invitations>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Invitations<
  TData = Awaited<ReturnType<typeof getApiV1Invitations>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1InvitationsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1Invitations>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1Invitations>>,
          TError,
          Awaited<ReturnType<typeof getApiV1Invitations>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1Invitations<
  TData = Awaited<ReturnType<typeof getApiV1Invitations>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1InvitationsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1Invitations>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get invitations list
 */

export function useGetApiV1Invitations<
  TData = Awaited<ReturnType<typeof getApiV1Invitations>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1InvitationsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1Invitations>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1InvitationsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new invitation resource
 * @summary Create invitation
 */
export const postApiV1Invitations = (
  postApiV1InvitationsBody: BodyType<PostApiV1InvitationsBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1Invitations201>({
    url: '/api/v1/invitations/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1InvitationsBody,
    signal,
  });
};

export const getPostApiV1InvitationsMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1Invitations>>,
    TError,
    { data: BodyType<PostApiV1InvitationsBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1Invitations>>,
  TError,
  { data: BodyType<PostApiV1InvitationsBody> },
  TContext
> => {
  const mutationKey = ['postApiV1Invitations'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1Invitations>>,
    { data: BodyType<PostApiV1InvitationsBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1Invitations(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1InvitationsMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1Invitations>>
>;
export type PostApiV1InvitationsMutationBody = BodyType<PostApiV1InvitationsBody>;
export type PostApiV1InvitationsMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create invitation
 */
export const usePostApiV1Invitations = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1Invitations>>,
    TError,
    { data: BodyType<PostApiV1InvitationsBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1Invitations>>,
  TError,
  { data: BodyType<PostApiV1InvitationsBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1InvitationsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific invitation resource by its unique identifier
 * @summary Get invitation by ID
 */
export const getApiV1InvitationsId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1InvitationsId200>({
    url: `/api/v1/invitations/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1InvitationsIdQueryKey = (id: string) => {
  return [`/api/v1/invitations/${id}`] as const;
};

export const getGetApiV1InvitationsIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1InvitationsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1InvitationsId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1InvitationsIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1InvitationsId>>> = ({ signal }) =>
    getApiV1InvitationsId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1InvitationsId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1InvitationsIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1InvitationsId>>
>;
export type GetApiV1InvitationsIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1InvitationsId<
  TData = Awaited<ReturnType<typeof getApiV1InvitationsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1InvitationsId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1InvitationsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1InvitationsId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1InvitationsId<
  TData = Awaited<ReturnType<typeof getApiV1InvitationsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1InvitationsId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1InvitationsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1InvitationsId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1InvitationsId<
  TData = Awaited<ReturnType<typeof getApiV1InvitationsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1InvitationsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get invitation by ID
 */

export function useGetApiV1InvitationsId<
  TData = Awaited<ReturnType<typeof getApiV1InvitationsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1InvitationsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1InvitationsIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing invitation resource
 * @summary Update invitation
 */
export const patchApiV1InvitationsId = (
  id: string,
  patchApiV1InvitationsIdBody: BodyType<PatchApiV1InvitationsIdBody>,
) => {
  return axiosClient<PatchApiV1InvitationsId200>({
    url: `/api/v1/invitations/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1InvitationsIdBody,
  });
};

export const getPatchApiV1InvitationsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1InvitationsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1InvitationsIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1InvitationsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1InvitationsIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1InvitationsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1InvitationsId>>,
    { id: string; data: BodyType<PatchApiV1InvitationsIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1InvitationsId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1InvitationsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1InvitationsId>>
>;
export type PatchApiV1InvitationsIdMutationBody = BodyType<PatchApiV1InvitationsIdBody>;
export type PatchApiV1InvitationsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update invitation
 */
export const usePatchApiV1InvitationsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1InvitationsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1InvitationsIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1InvitationsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1InvitationsIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1InvitationsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing invitation resource
 * @summary Delete invitation
 */
export const deleteApiV1InvitationsId = (id: string) => {
  return axiosClient<DeleteApiV1InvitationsId204>({
    url: `/api/v1/invitations/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1InvitationsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1InvitationsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1InvitationsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1InvitationsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1InvitationsId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1InvitationsId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1InvitationsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1InvitationsId>>
>;

export type DeleteApiV1InvitationsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete invitation
 */
export const useDeleteApiV1InvitationsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1InvitationsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1InvitationsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1InvitationsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
