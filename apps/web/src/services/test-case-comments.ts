/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1TestCaseCommentsId204,
  ErrorResponse,
  GetApiV1TestCaseComments200,
  GetApiV1TestCaseCommentsId200,
  GetApiV1TestCaseCommentsParams,
  PatchApiV1TestCaseCommentsId200,
  PatchApiV1TestCaseCommentsIdBody,
  PostApiV1TestCaseComments201,
  PostApiV1TestCaseCommentsBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of test case comments resources with optional filtering and sorting
 * @summary Get test case comments list
 */
export const getApiV1TestCaseComments = (
  params?: GetApiV1TestCaseCommentsParams,
  signal?: AbortSignal,
) => {
  return axiosClient<GetApiV1TestCaseComments200>({
    url: '/api/v1/test-case-comments/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1TestCaseCommentsQueryKey = (params?: GetApiV1TestCaseCommentsParams) => {
  return ['/api/v1/test-case-comments/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1TestCaseCommentsQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1TestCaseComments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestCaseCommentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseComments>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1TestCaseCommentsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1TestCaseComments>>> = ({
    signal,
  }) => getApiV1TestCaseComments(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1TestCaseComments>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1TestCaseCommentsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1TestCaseComments>>
>;
export type GetApiV1TestCaseCommentsQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1TestCaseComments<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseComments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1TestCaseCommentsParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseComments>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestCaseComments>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestCaseComments>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestCaseComments<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseComments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestCaseCommentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseComments>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestCaseComments>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestCaseComments>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestCaseComments<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseComments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestCaseCommentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseComments>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get test case comments list
 */

export function useGetApiV1TestCaseComments<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseComments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestCaseCommentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseComments>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1TestCaseCommentsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new test case comment resource
 * @summary Create test case comment
 */
export const postApiV1TestCaseComments = (
  postApiV1TestCaseCommentsBody: BodyType<PostApiV1TestCaseCommentsBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1TestCaseComments201>({
    url: '/api/v1/test-case-comments/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1TestCaseCommentsBody,
    signal,
  });
};

export const getPostApiV1TestCaseCommentsMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1TestCaseComments>>,
    TError,
    { data: BodyType<PostApiV1TestCaseCommentsBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1TestCaseComments>>,
  TError,
  { data: BodyType<PostApiV1TestCaseCommentsBody> },
  TContext
> => {
  const mutationKey = ['postApiV1TestCaseComments'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1TestCaseComments>>,
    { data: BodyType<PostApiV1TestCaseCommentsBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1TestCaseComments(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1TestCaseCommentsMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1TestCaseComments>>
>;
export type PostApiV1TestCaseCommentsMutationBody = BodyType<PostApiV1TestCaseCommentsBody>;
export type PostApiV1TestCaseCommentsMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create test case comment
 */
export const usePostApiV1TestCaseComments = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1TestCaseComments>>,
    TError,
    { data: BodyType<PostApiV1TestCaseCommentsBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1TestCaseComments>>,
  TError,
  { data: BodyType<PostApiV1TestCaseCommentsBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1TestCaseCommentsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific test case comment resource by its unique identifier
 * @summary Get test case comment by ID
 */
export const getApiV1TestCaseCommentsId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1TestCaseCommentsId200>({
    url: `/api/v1/test-case-comments/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1TestCaseCommentsIdQueryKey = (id: string) => {
  return [`/api/v1/test-case-comments/${id}`] as const;
};

export const getGetApiV1TestCaseCommentsIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1TestCaseCommentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseCommentsId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1TestCaseCommentsIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1TestCaseCommentsId>>> = ({
    signal,
  }) => getApiV1TestCaseCommentsId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseCommentsId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1TestCaseCommentsIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1TestCaseCommentsId>>
>;
export type GetApiV1TestCaseCommentsIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1TestCaseCommentsId<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseCommentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseCommentsId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestCaseCommentsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestCaseCommentsId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestCaseCommentsId<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseCommentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseCommentsId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestCaseCommentsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestCaseCommentsId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestCaseCommentsId<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseCommentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseCommentsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get test case comment by ID
 */

export function useGetApiV1TestCaseCommentsId<
  TData = Awaited<ReturnType<typeof getApiV1TestCaseCommentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCaseCommentsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1TestCaseCommentsIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing test case comment resource
 * @summary Update test case comment
 */
export const patchApiV1TestCaseCommentsId = (
  id: string,
  patchApiV1TestCaseCommentsIdBody: BodyType<PatchApiV1TestCaseCommentsIdBody>,
) => {
  return axiosClient<PatchApiV1TestCaseCommentsId200>({
    url: `/api/v1/test-case-comments/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1TestCaseCommentsIdBody,
  });
};

export const getPatchApiV1TestCaseCommentsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1TestCaseCommentsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1TestCaseCommentsIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1TestCaseCommentsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1TestCaseCommentsIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1TestCaseCommentsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1TestCaseCommentsId>>,
    { id: string; data: BodyType<PatchApiV1TestCaseCommentsIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1TestCaseCommentsId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1TestCaseCommentsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1TestCaseCommentsId>>
>;
export type PatchApiV1TestCaseCommentsIdMutationBody = BodyType<PatchApiV1TestCaseCommentsIdBody>;
export type PatchApiV1TestCaseCommentsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update test case comment
 */
export const usePatchApiV1TestCaseCommentsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1TestCaseCommentsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1TestCaseCommentsIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1TestCaseCommentsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1TestCaseCommentsIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1TestCaseCommentsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing test case comment resource
 * @summary Delete test case comment
 */
export const deleteApiV1TestCaseCommentsId = (id: string) => {
  return axiosClient<DeleteApiV1TestCaseCommentsId204>({
    url: `/api/v1/test-case-comments/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1TestCaseCommentsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1TestCaseCommentsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1TestCaseCommentsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1TestCaseCommentsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1TestCaseCommentsId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1TestCaseCommentsId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1TestCaseCommentsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1TestCaseCommentsId>>
>;

export type DeleteApiV1TestCaseCommentsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete test case comment
 */
export const useDeleteApiV1TestCaseCommentsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1TestCaseCommentsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1TestCaseCommentsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1TestCaseCommentsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
