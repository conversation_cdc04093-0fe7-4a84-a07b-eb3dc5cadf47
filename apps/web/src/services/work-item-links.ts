/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1WorkItemLinkId204,
  ErrorResponse,
  GetApiV1WorkItemLink200,
  GetApiV1WorkItemLinkId200,
  GetApiV1WorkItemLinkParams,
  PatchApiV1WorkItemLinkId200,
  PatchApiV1WorkItemLinkIdBody,
  PostApiV1WorkItemLink201,
  PostApiV1WorkItemLinkBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of workItemLinks resources with optional filtering and sorting
 * @summary Get workItemLinks list
 */
export const getApiV1WorkItemLink = (params?: GetApiV1WorkItemLinkParams, signal?: AbortSignal) => {
  return axiosClient<GetApiV1WorkItemLink200>({
    url: '/api/v1/workItemLink/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1WorkItemLinkQueryKey = (params?: GetApiV1WorkItemLinkParams) => {
  return ['/api/v1/workItemLink/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1WorkItemLinkQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1WorkItemLink>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemLinkParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemLink>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1WorkItemLinkQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1WorkItemLink>>> = ({ signal }) =>
    getApiV1WorkItemLink(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1WorkItemLink>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1WorkItemLinkQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1WorkItemLink>>
>;
export type GetApiV1WorkItemLinkQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1WorkItemLink<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemLink>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1WorkItemLinkParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemLink>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItemLink>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItemLink>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItemLink<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemLink>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemLinkParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemLink>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItemLink>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItemLink>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItemLink<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemLink>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemLinkParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemLink>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get workItemLinks list
 */

export function useGetApiV1WorkItemLink<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemLink>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkItemLinkParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemLink>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1WorkItemLinkQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new workItemLink resource
 * @summary Create workItemLink
 */
export const postApiV1WorkItemLink = (
  postApiV1WorkItemLinkBody: BodyType<PostApiV1WorkItemLinkBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1WorkItemLink201>({
    url: '/api/v1/workItemLink/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1WorkItemLinkBody,
    signal,
  });
};

export const getPostApiV1WorkItemLinkMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1WorkItemLink>>,
    TError,
    { data: BodyType<PostApiV1WorkItemLinkBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1WorkItemLink>>,
  TError,
  { data: BodyType<PostApiV1WorkItemLinkBody> },
  TContext
> => {
  const mutationKey = ['postApiV1WorkItemLink'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1WorkItemLink>>,
    { data: BodyType<PostApiV1WorkItemLinkBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1WorkItemLink(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1WorkItemLinkMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1WorkItemLink>>
>;
export type PostApiV1WorkItemLinkMutationBody = BodyType<PostApiV1WorkItemLinkBody>;
export type PostApiV1WorkItemLinkMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create workItemLink
 */
export const usePostApiV1WorkItemLink = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1WorkItemLink>>,
    TError,
    { data: BodyType<PostApiV1WorkItemLinkBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1WorkItemLink>>,
  TError,
  { data: BodyType<PostApiV1WorkItemLinkBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1WorkItemLinkMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific workItemLink resource by its unique identifier
 * @summary Get workItemLink by ID
 */
export const getApiV1WorkItemLinkId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1WorkItemLinkId200>({
    url: `/api/v1/workItemLink/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1WorkItemLinkIdQueryKey = (id: string) => {
  return [`/api/v1/workItemLink/${id}`] as const;
};

export const getGetApiV1WorkItemLinkIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1WorkItemLinkId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemLinkId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1WorkItemLinkIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1WorkItemLinkId>>> = ({ signal }) =>
    getApiV1WorkItemLinkId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemLinkId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1WorkItemLinkIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1WorkItemLinkId>>
>;
export type GetApiV1WorkItemLinkIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1WorkItemLinkId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemLinkId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemLinkId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItemLinkId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItemLinkId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItemLinkId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemLinkId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemLinkId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkItemLinkId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkItemLinkId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkItemLinkId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemLinkId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemLinkId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get workItemLink by ID
 */

export function useGetApiV1WorkItemLinkId<
  TData = Awaited<ReturnType<typeof getApiV1WorkItemLinkId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkItemLinkId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1WorkItemLinkIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing workItemLink resource
 * @summary Update workItemLink
 */
export const patchApiV1WorkItemLinkId = (
  id: string,
  patchApiV1WorkItemLinkIdBody: BodyType<PatchApiV1WorkItemLinkIdBody>,
) => {
  return axiosClient<PatchApiV1WorkItemLinkId200>({
    url: `/api/v1/workItemLink/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1WorkItemLinkIdBody,
  });
};

export const getPatchApiV1WorkItemLinkIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1WorkItemLinkId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1WorkItemLinkIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1WorkItemLinkId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1WorkItemLinkIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1WorkItemLinkId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1WorkItemLinkId>>,
    { id: string; data: BodyType<PatchApiV1WorkItemLinkIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1WorkItemLinkId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1WorkItemLinkIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1WorkItemLinkId>>
>;
export type PatchApiV1WorkItemLinkIdMutationBody = BodyType<PatchApiV1WorkItemLinkIdBody>;
export type PatchApiV1WorkItemLinkIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update workItemLink
 */
export const usePatchApiV1WorkItemLinkId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1WorkItemLinkId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1WorkItemLinkIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1WorkItemLinkId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1WorkItemLinkIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1WorkItemLinkIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing workItemLink resource
 * @summary Delete workItemLink
 */
export const deleteApiV1WorkItemLinkId = (id: string) => {
  return axiosClient<DeleteApiV1WorkItemLinkId204>({
    url: `/api/v1/workItemLink/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1WorkItemLinkIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1WorkItemLinkId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1WorkItemLinkId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1WorkItemLinkId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1WorkItemLinkId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1WorkItemLinkId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1WorkItemLinkIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1WorkItemLinkId>>
>;

export type DeleteApiV1WorkItemLinkIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete workItemLink
 */
export const useDeleteApiV1WorkItemLinkId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1WorkItemLinkId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1WorkItemLinkId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1WorkItemLinkIdMutationOptions(options);

  return useMutation(mutationOptions);
};
