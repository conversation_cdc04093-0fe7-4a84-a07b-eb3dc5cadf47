/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1TestSuiteCommentsId204,
  ErrorResponse,
  GetApiV1TestSuiteComments200,
  GetApiV1TestSuiteCommentsId200,
  GetApiV1TestSuiteCommentsParams,
  PatchApiV1TestSuiteCommentsId200,
  PatchApiV1TestSuiteCommentsIdBody,
  PostApiV1TestSuiteComments201,
  PostApiV1TestSuiteCommentsBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of test suite comments resources with optional filtering and sorting
 * @summary Get test suite comments list
 */
export const getApiV1TestSuiteComments = (
  params?: GetApiV1TestSuiteCommentsParams,
  signal?: AbortSignal,
) => {
  return axiosClient<GetApiV1TestSuiteComments200>({
    url: '/api/v1/test-suite-comments/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1TestSuiteCommentsQueryKey = (params?: GetApiV1TestSuiteCommentsParams) => {
  return ['/api/v1/test-suite-comments/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1TestSuiteCommentsQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1TestSuiteComments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestSuiteCommentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuiteComments>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1TestSuiteCommentsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1TestSuiteComments>>> = ({
    signal,
  }) => getApiV1TestSuiteComments(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1TestSuiteComments>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1TestSuiteCommentsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1TestSuiteComments>>
>;
export type GetApiV1TestSuiteCommentsQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1TestSuiteComments<
  TData = Awaited<ReturnType<typeof getApiV1TestSuiteComments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1TestSuiteCommentsParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuiteComments>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestSuiteComments>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestSuiteComments>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestSuiteComments<
  TData = Awaited<ReturnType<typeof getApiV1TestSuiteComments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestSuiteCommentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuiteComments>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestSuiteComments>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestSuiteComments>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestSuiteComments<
  TData = Awaited<ReturnType<typeof getApiV1TestSuiteComments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestSuiteCommentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuiteComments>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get test suite comments list
 */

export function useGetApiV1TestSuiteComments<
  TData = Awaited<ReturnType<typeof getApiV1TestSuiteComments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestSuiteCommentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuiteComments>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1TestSuiteCommentsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new test suite comment resource
 * @summary Create test suite comment
 */
export const postApiV1TestSuiteComments = (
  postApiV1TestSuiteCommentsBody: BodyType<PostApiV1TestSuiteCommentsBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1TestSuiteComments201>({
    url: '/api/v1/test-suite-comments/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1TestSuiteCommentsBody,
    signal,
  });
};

export const getPostApiV1TestSuiteCommentsMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1TestSuiteComments>>,
    TError,
    { data: BodyType<PostApiV1TestSuiteCommentsBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1TestSuiteComments>>,
  TError,
  { data: BodyType<PostApiV1TestSuiteCommentsBody> },
  TContext
> => {
  const mutationKey = ['postApiV1TestSuiteComments'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1TestSuiteComments>>,
    { data: BodyType<PostApiV1TestSuiteCommentsBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1TestSuiteComments(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1TestSuiteCommentsMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1TestSuiteComments>>
>;
export type PostApiV1TestSuiteCommentsMutationBody = BodyType<PostApiV1TestSuiteCommentsBody>;
export type PostApiV1TestSuiteCommentsMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create test suite comment
 */
export const usePostApiV1TestSuiteComments = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1TestSuiteComments>>,
    TError,
    { data: BodyType<PostApiV1TestSuiteCommentsBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1TestSuiteComments>>,
  TError,
  { data: BodyType<PostApiV1TestSuiteCommentsBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1TestSuiteCommentsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific test suite comment resource by its unique identifier
 * @summary Get test suite comment by ID
 */
export const getApiV1TestSuiteCommentsId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1TestSuiteCommentsId200>({
    url: `/api/v1/test-suite-comments/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1TestSuiteCommentsIdQueryKey = (id: string) => {
  return [`/api/v1/test-suite-comments/${id}`] as const;
};

export const getGetApiV1TestSuiteCommentsIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1TestSuiteCommentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuiteCommentsId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1TestSuiteCommentsIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1TestSuiteCommentsId>>> = ({
    signal,
  }) => getApiV1TestSuiteCommentsId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuiteCommentsId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1TestSuiteCommentsIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1TestSuiteCommentsId>>
>;
export type GetApiV1TestSuiteCommentsIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1TestSuiteCommentsId<
  TData = Awaited<ReturnType<typeof getApiV1TestSuiteCommentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuiteCommentsId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestSuiteCommentsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestSuiteCommentsId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestSuiteCommentsId<
  TData = Awaited<ReturnType<typeof getApiV1TestSuiteCommentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuiteCommentsId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestSuiteCommentsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestSuiteCommentsId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestSuiteCommentsId<
  TData = Awaited<ReturnType<typeof getApiV1TestSuiteCommentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuiteCommentsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get test suite comment by ID
 */

export function useGetApiV1TestSuiteCommentsId<
  TData = Awaited<ReturnType<typeof getApiV1TestSuiteCommentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuiteCommentsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1TestSuiteCommentsIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing test suite comment resource
 * @summary Update test suite comment
 */
export const patchApiV1TestSuiteCommentsId = (
  id: string,
  patchApiV1TestSuiteCommentsIdBody: BodyType<PatchApiV1TestSuiteCommentsIdBody>,
) => {
  return axiosClient<PatchApiV1TestSuiteCommentsId200>({
    url: `/api/v1/test-suite-comments/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1TestSuiteCommentsIdBody,
  });
};

export const getPatchApiV1TestSuiteCommentsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1TestSuiteCommentsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1TestSuiteCommentsIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1TestSuiteCommentsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1TestSuiteCommentsIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1TestSuiteCommentsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1TestSuiteCommentsId>>,
    { id: string; data: BodyType<PatchApiV1TestSuiteCommentsIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1TestSuiteCommentsId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1TestSuiteCommentsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1TestSuiteCommentsId>>
>;
export type PatchApiV1TestSuiteCommentsIdMutationBody = BodyType<PatchApiV1TestSuiteCommentsIdBody>;
export type PatchApiV1TestSuiteCommentsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update test suite comment
 */
export const usePatchApiV1TestSuiteCommentsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1TestSuiteCommentsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1TestSuiteCommentsIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1TestSuiteCommentsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1TestSuiteCommentsIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1TestSuiteCommentsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing test suite comment resource
 * @summary Delete test suite comment
 */
export const deleteApiV1TestSuiteCommentsId = (id: string) => {
  return axiosClient<DeleteApiV1TestSuiteCommentsId204>({
    url: `/api/v1/test-suite-comments/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1TestSuiteCommentsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1TestSuiteCommentsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1TestSuiteCommentsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1TestSuiteCommentsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1TestSuiteCommentsId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1TestSuiteCommentsId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1TestSuiteCommentsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1TestSuiteCommentsId>>
>;

export type DeleteApiV1TestSuiteCommentsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete test suite comment
 */
export const useDeleteApiV1TestSuiteCommentsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1TestSuiteCommentsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1TestSuiteCommentsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1TestSuiteCommentsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
