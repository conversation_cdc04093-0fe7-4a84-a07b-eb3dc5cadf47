/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1WorkflowTransitionsId204,
  ErrorResponse,
  GetApiV1WorkflowTransitions200,
  GetApiV1WorkflowTransitionsId200,
  GetApiV1WorkflowTransitionsParams,
  PatchApiV1WorkflowTransitionsId200,
  PatchApiV1WorkflowTransitionsIdBody,
  PostApiV1WorkflowTransitions201,
  PostApiV1WorkflowTransitionsBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of workflow-transitions resources with optional filtering and sorting
 * @summary Get workflow-transitions list
 */
export const getApiV1WorkflowTransitions = (
  params?: GetApiV1WorkflowTransitionsParams,
  signal?: AbortSignal,
) => {
  return axiosClient<GetApiV1WorkflowTransitions200>({
    url: '/api/v1/workflow-transitions/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1WorkflowTransitionsQueryKey = (
  params?: GetApiV1WorkflowTransitionsParams,
) => {
  return ['/api/v1/workflow-transitions/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1WorkflowTransitionsQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1WorkflowTransitions>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkflowTransitionsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowTransitions>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1WorkflowTransitionsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1WorkflowTransitions>>> = ({
    signal,
  }) => getApiV1WorkflowTransitions(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1WorkflowTransitions>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1WorkflowTransitionsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1WorkflowTransitions>>
>;
export type GetApiV1WorkflowTransitionsQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1WorkflowTransitions<
  TData = Awaited<ReturnType<typeof getApiV1WorkflowTransitions>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1WorkflowTransitionsParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowTransitions>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkflowTransitions>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkflowTransitions>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkflowTransitions<
  TData = Awaited<ReturnType<typeof getApiV1WorkflowTransitions>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkflowTransitionsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowTransitions>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkflowTransitions>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkflowTransitions>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkflowTransitions<
  TData = Awaited<ReturnType<typeof getApiV1WorkflowTransitions>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkflowTransitionsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowTransitions>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get workflow-transitions list
 */

export function useGetApiV1WorkflowTransitions<
  TData = Awaited<ReturnType<typeof getApiV1WorkflowTransitions>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1WorkflowTransitionsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowTransitions>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1WorkflowTransitionsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new workflow-transition resource
 * @summary Create workflow-transition
 */
export const postApiV1WorkflowTransitions = (
  postApiV1WorkflowTransitionsBody: BodyType<PostApiV1WorkflowTransitionsBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1WorkflowTransitions201>({
    url: '/api/v1/workflow-transitions/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1WorkflowTransitionsBody,
    signal,
  });
};

export const getPostApiV1WorkflowTransitionsMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1WorkflowTransitions>>,
    TError,
    { data: BodyType<PostApiV1WorkflowTransitionsBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1WorkflowTransitions>>,
  TError,
  { data: BodyType<PostApiV1WorkflowTransitionsBody> },
  TContext
> => {
  const mutationKey = ['postApiV1WorkflowTransitions'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1WorkflowTransitions>>,
    { data: BodyType<PostApiV1WorkflowTransitionsBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1WorkflowTransitions(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1WorkflowTransitionsMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1WorkflowTransitions>>
>;
export type PostApiV1WorkflowTransitionsMutationBody = BodyType<PostApiV1WorkflowTransitionsBody>;
export type PostApiV1WorkflowTransitionsMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create workflow-transition
 */
export const usePostApiV1WorkflowTransitions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1WorkflowTransitions>>,
    TError,
    { data: BodyType<PostApiV1WorkflowTransitionsBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1WorkflowTransitions>>,
  TError,
  { data: BodyType<PostApiV1WorkflowTransitionsBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1WorkflowTransitionsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific workflow-transition resource by its unique identifier
 * @summary Get workflow-transition by ID
 */
export const getApiV1WorkflowTransitionsId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1WorkflowTransitionsId200>({
    url: `/api/v1/workflow-transitions/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1WorkflowTransitionsIdQueryKey = (id: string) => {
  return [`/api/v1/workflow-transitions/${id}`] as const;
};

export const getGetApiV1WorkflowTransitionsIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1WorkflowTransitionsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowTransitionsId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1WorkflowTransitionsIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1WorkflowTransitionsId>>> = ({
    signal,
  }) => getApiV1WorkflowTransitionsId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowTransitionsId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1WorkflowTransitionsIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1WorkflowTransitionsId>>
>;
export type GetApiV1WorkflowTransitionsIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1WorkflowTransitionsId<
  TData = Awaited<ReturnType<typeof getApiV1WorkflowTransitionsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowTransitionsId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkflowTransitionsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkflowTransitionsId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkflowTransitionsId<
  TData = Awaited<ReturnType<typeof getApiV1WorkflowTransitionsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowTransitionsId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1WorkflowTransitionsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1WorkflowTransitionsId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1WorkflowTransitionsId<
  TData = Awaited<ReturnType<typeof getApiV1WorkflowTransitionsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowTransitionsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get workflow-transition by ID
 */

export function useGetApiV1WorkflowTransitionsId<
  TData = Awaited<ReturnType<typeof getApiV1WorkflowTransitionsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1WorkflowTransitionsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1WorkflowTransitionsIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing workflow-transition resource
 * @summary Update workflow-transition
 */
export const patchApiV1WorkflowTransitionsId = (
  id: string,
  patchApiV1WorkflowTransitionsIdBody: BodyType<PatchApiV1WorkflowTransitionsIdBody>,
) => {
  return axiosClient<PatchApiV1WorkflowTransitionsId200>({
    url: `/api/v1/workflow-transitions/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1WorkflowTransitionsIdBody,
  });
};

export const getPatchApiV1WorkflowTransitionsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1WorkflowTransitionsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1WorkflowTransitionsIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1WorkflowTransitionsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1WorkflowTransitionsIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1WorkflowTransitionsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1WorkflowTransitionsId>>,
    { id: string; data: BodyType<PatchApiV1WorkflowTransitionsIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1WorkflowTransitionsId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1WorkflowTransitionsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1WorkflowTransitionsId>>
>;
export type PatchApiV1WorkflowTransitionsIdMutationBody =
  BodyType<PatchApiV1WorkflowTransitionsIdBody>;
export type PatchApiV1WorkflowTransitionsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update workflow-transition
 */
export const usePatchApiV1WorkflowTransitionsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1WorkflowTransitionsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1WorkflowTransitionsIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1WorkflowTransitionsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1WorkflowTransitionsIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1WorkflowTransitionsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing workflow-transition resource
 * @summary Delete workflow-transition
 */
export const deleteApiV1WorkflowTransitionsId = (id: string) => {
  return axiosClient<DeleteApiV1WorkflowTransitionsId204>({
    url: `/api/v1/workflow-transitions/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1WorkflowTransitionsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1WorkflowTransitionsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1WorkflowTransitionsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1WorkflowTransitionsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1WorkflowTransitionsId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1WorkflowTransitionsId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1WorkflowTransitionsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1WorkflowTransitionsId>>
>;

export type DeleteApiV1WorkflowTransitionsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete workflow-transition
 */
export const useDeleteApiV1WorkflowTransitionsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1WorkflowTransitionsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1WorkflowTransitionsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1WorkflowTransitionsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
