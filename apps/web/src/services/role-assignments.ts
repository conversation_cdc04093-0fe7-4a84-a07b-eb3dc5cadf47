/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1RoleAssignmentsId204,
  ErrorResponse,
  GetApiV1RoleAssignments200,
  GetApiV1RoleAssignmentsId200,
  GetApiV1RoleAssignmentsParams,
  PatchApiV1RoleAssignmentsId200,
  PatchApiV1RoleAssignmentsIdBody,
  PostApiV1RoleAssignments201,
  PostApiV1RoleAssignmentsBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of role-assignments resources with optional filtering and sorting
 * @summary Get role-assignments list
 */
export const getApiV1RoleAssignments = (
  params?: GetApiV1RoleAssignmentsParams,
  signal?: AbortSignal,
) => {
  return axiosClient<GetApiV1RoleAssignments200>({
    url: '/api/v1/role-assignments/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1RoleAssignmentsQueryKey = (params?: GetApiV1RoleAssignmentsParams) => {
  return ['/api/v1/role-assignments/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1RoleAssignmentsQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1RoleAssignments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1RoleAssignmentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1RoleAssignments>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1RoleAssignmentsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1RoleAssignments>>> = ({
    signal,
  }) => getApiV1RoleAssignments(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1RoleAssignments>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1RoleAssignmentsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1RoleAssignments>>
>;
export type GetApiV1RoleAssignmentsQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1RoleAssignments<
  TData = Awaited<ReturnType<typeof getApiV1RoleAssignments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1RoleAssignmentsParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1RoleAssignments>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1RoleAssignments>>,
          TError,
          Awaited<ReturnType<typeof getApiV1RoleAssignments>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1RoleAssignments<
  TData = Awaited<ReturnType<typeof getApiV1RoleAssignments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1RoleAssignmentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1RoleAssignments>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1RoleAssignments>>,
          TError,
          Awaited<ReturnType<typeof getApiV1RoleAssignments>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1RoleAssignments<
  TData = Awaited<ReturnType<typeof getApiV1RoleAssignments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1RoleAssignmentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1RoleAssignments>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get role-assignments list
 */

export function useGetApiV1RoleAssignments<
  TData = Awaited<ReturnType<typeof getApiV1RoleAssignments>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1RoleAssignmentsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1RoleAssignments>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1RoleAssignmentsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new role-assignment resource
 * @summary Create role-assignment
 */
export const postApiV1RoleAssignments = (
  postApiV1RoleAssignmentsBody: BodyType<PostApiV1RoleAssignmentsBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1RoleAssignments201>({
    url: '/api/v1/role-assignments/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1RoleAssignmentsBody,
    signal,
  });
};

export const getPostApiV1RoleAssignmentsMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1RoleAssignments>>,
    TError,
    { data: BodyType<PostApiV1RoleAssignmentsBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1RoleAssignments>>,
  TError,
  { data: BodyType<PostApiV1RoleAssignmentsBody> },
  TContext
> => {
  const mutationKey = ['postApiV1RoleAssignments'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1RoleAssignments>>,
    { data: BodyType<PostApiV1RoleAssignmentsBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1RoleAssignments(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1RoleAssignmentsMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1RoleAssignments>>
>;
export type PostApiV1RoleAssignmentsMutationBody = BodyType<PostApiV1RoleAssignmentsBody>;
export type PostApiV1RoleAssignmentsMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create role-assignment
 */
export const usePostApiV1RoleAssignments = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1RoleAssignments>>,
    TError,
    { data: BodyType<PostApiV1RoleAssignmentsBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1RoleAssignments>>,
  TError,
  { data: BodyType<PostApiV1RoleAssignmentsBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1RoleAssignmentsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific role-assignment resource by its unique identifier
 * @summary Get role-assignment by ID
 */
export const getApiV1RoleAssignmentsId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1RoleAssignmentsId200>({
    url: `/api/v1/role-assignments/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1RoleAssignmentsIdQueryKey = (id: string) => {
  return [`/api/v1/role-assignments/${id}`] as const;
};

export const getGetApiV1RoleAssignmentsIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1RoleAssignmentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1RoleAssignmentsId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1RoleAssignmentsIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1RoleAssignmentsId>>> = ({
    signal,
  }) => getApiV1RoleAssignmentsId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1RoleAssignmentsId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1RoleAssignmentsIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1RoleAssignmentsId>>
>;
export type GetApiV1RoleAssignmentsIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1RoleAssignmentsId<
  TData = Awaited<ReturnType<typeof getApiV1RoleAssignmentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1RoleAssignmentsId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1RoleAssignmentsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1RoleAssignmentsId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1RoleAssignmentsId<
  TData = Awaited<ReturnType<typeof getApiV1RoleAssignmentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1RoleAssignmentsId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1RoleAssignmentsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1RoleAssignmentsId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1RoleAssignmentsId<
  TData = Awaited<ReturnType<typeof getApiV1RoleAssignmentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1RoleAssignmentsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get role-assignment by ID
 */

export function useGetApiV1RoleAssignmentsId<
  TData = Awaited<ReturnType<typeof getApiV1RoleAssignmentsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1RoleAssignmentsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1RoleAssignmentsIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing role-assignment resource
 * @summary Update role-assignment
 */
export const patchApiV1RoleAssignmentsId = (
  id: string,
  patchApiV1RoleAssignmentsIdBody: BodyType<PatchApiV1RoleAssignmentsIdBody>,
) => {
  return axiosClient<PatchApiV1RoleAssignmentsId200>({
    url: `/api/v1/role-assignments/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1RoleAssignmentsIdBody,
  });
};

export const getPatchApiV1RoleAssignmentsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1RoleAssignmentsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1RoleAssignmentsIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1RoleAssignmentsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1RoleAssignmentsIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1RoleAssignmentsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1RoleAssignmentsId>>,
    { id: string; data: BodyType<PatchApiV1RoleAssignmentsIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1RoleAssignmentsId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1RoleAssignmentsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1RoleAssignmentsId>>
>;
export type PatchApiV1RoleAssignmentsIdMutationBody = BodyType<PatchApiV1RoleAssignmentsIdBody>;
export type PatchApiV1RoleAssignmentsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update role-assignment
 */
export const usePatchApiV1RoleAssignmentsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1RoleAssignmentsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1RoleAssignmentsIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1RoleAssignmentsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1RoleAssignmentsIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1RoleAssignmentsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing role-assignment resource
 * @summary Delete role-assignment
 */
export const deleteApiV1RoleAssignmentsId = (id: string) => {
  return axiosClient<DeleteApiV1RoleAssignmentsId204>({
    url: `/api/v1/role-assignments/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1RoleAssignmentsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1RoleAssignmentsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1RoleAssignmentsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1RoleAssignmentsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1RoleAssignmentsId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1RoleAssignmentsId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1RoleAssignmentsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1RoleAssignmentsId>>
>;

export type DeleteApiV1RoleAssignmentsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete role-assignment
 */
export const useDeleteApiV1RoleAssignmentsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1RoleAssignmentsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1RoleAssignmentsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1RoleAssignmentsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
