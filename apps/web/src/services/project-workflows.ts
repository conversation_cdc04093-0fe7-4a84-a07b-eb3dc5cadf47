/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1ProjectWorkflowsId204,
  ErrorResponse,
  GetApiV1ProjectWorkflows200,
  GetApiV1ProjectWorkflowsId200,
  GetApiV1ProjectWorkflowsParams,
  PatchApiV1ProjectWorkflowsId200,
  PatchApiV1ProjectWorkflowsIdBody,
  PostApiV1ProjectWorkflows201,
  PostApiV1ProjectWorkflowsBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of project-workflows resources with optional filtering and sorting
 * @summary Get project-workflows list
 */
export const getApiV1ProjectWorkflows = (
  params?: GetApiV1ProjectWorkflowsParams,
  signal?: AbortSignal,
) => {
  return axiosClient<GetApiV1ProjectWorkflows200>({
    url: '/api/v1/project-workflows/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1ProjectWorkflowsQueryKey = (params?: GetApiV1ProjectWorkflowsParams) => {
  return ['/api/v1/project-workflows/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1ProjectWorkflowsQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1ProjectWorkflows>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1ProjectWorkflowsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ProjectWorkflows>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1ProjectWorkflowsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1ProjectWorkflows>>> = ({
    signal,
  }) => getApiV1ProjectWorkflows(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1ProjectWorkflows>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1ProjectWorkflowsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1ProjectWorkflows>>
>;
export type GetApiV1ProjectWorkflowsQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1ProjectWorkflows<
  TData = Awaited<ReturnType<typeof getApiV1ProjectWorkflows>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1ProjectWorkflowsParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ProjectWorkflows>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1ProjectWorkflows>>,
          TError,
          Awaited<ReturnType<typeof getApiV1ProjectWorkflows>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1ProjectWorkflows<
  TData = Awaited<ReturnType<typeof getApiV1ProjectWorkflows>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1ProjectWorkflowsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ProjectWorkflows>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1ProjectWorkflows>>,
          TError,
          Awaited<ReturnType<typeof getApiV1ProjectWorkflows>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1ProjectWorkflows<
  TData = Awaited<ReturnType<typeof getApiV1ProjectWorkflows>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1ProjectWorkflowsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ProjectWorkflows>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get project-workflows list
 */

export function useGetApiV1ProjectWorkflows<
  TData = Awaited<ReturnType<typeof getApiV1ProjectWorkflows>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1ProjectWorkflowsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ProjectWorkflows>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1ProjectWorkflowsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new project-workflow resource
 * @summary Create project-workflow
 */
export const postApiV1ProjectWorkflows = (
  postApiV1ProjectWorkflowsBody: BodyType<PostApiV1ProjectWorkflowsBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1ProjectWorkflows201>({
    url: '/api/v1/project-workflows/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1ProjectWorkflowsBody,
    signal,
  });
};

export const getPostApiV1ProjectWorkflowsMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1ProjectWorkflows>>,
    TError,
    { data: BodyType<PostApiV1ProjectWorkflowsBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1ProjectWorkflows>>,
  TError,
  { data: BodyType<PostApiV1ProjectWorkflowsBody> },
  TContext
> => {
  const mutationKey = ['postApiV1ProjectWorkflows'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1ProjectWorkflows>>,
    { data: BodyType<PostApiV1ProjectWorkflowsBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1ProjectWorkflows(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1ProjectWorkflowsMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1ProjectWorkflows>>
>;
export type PostApiV1ProjectWorkflowsMutationBody = BodyType<PostApiV1ProjectWorkflowsBody>;
export type PostApiV1ProjectWorkflowsMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create project-workflow
 */
export const usePostApiV1ProjectWorkflows = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1ProjectWorkflows>>,
    TError,
    { data: BodyType<PostApiV1ProjectWorkflowsBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1ProjectWorkflows>>,
  TError,
  { data: BodyType<PostApiV1ProjectWorkflowsBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1ProjectWorkflowsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific project-workflow resource by its unique identifier
 * @summary Get project-workflow by ID
 */
export const getApiV1ProjectWorkflowsId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1ProjectWorkflowsId200>({
    url: `/api/v1/project-workflows/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1ProjectWorkflowsIdQueryKey = (id: string) => {
  return [`/api/v1/project-workflows/${id}`] as const;
};

export const getGetApiV1ProjectWorkflowsIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1ProjectWorkflowsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ProjectWorkflowsId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1ProjectWorkflowsIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1ProjectWorkflowsId>>> = ({
    signal,
  }) => getApiV1ProjectWorkflowsId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1ProjectWorkflowsId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1ProjectWorkflowsIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1ProjectWorkflowsId>>
>;
export type GetApiV1ProjectWorkflowsIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1ProjectWorkflowsId<
  TData = Awaited<ReturnType<typeof getApiV1ProjectWorkflowsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ProjectWorkflowsId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1ProjectWorkflowsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1ProjectWorkflowsId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1ProjectWorkflowsId<
  TData = Awaited<ReturnType<typeof getApiV1ProjectWorkflowsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ProjectWorkflowsId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1ProjectWorkflowsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1ProjectWorkflowsId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1ProjectWorkflowsId<
  TData = Awaited<ReturnType<typeof getApiV1ProjectWorkflowsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ProjectWorkflowsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get project-workflow by ID
 */

export function useGetApiV1ProjectWorkflowsId<
  TData = Awaited<ReturnType<typeof getApiV1ProjectWorkflowsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1ProjectWorkflowsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1ProjectWorkflowsIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing project-workflow resource
 * @summary Update project-workflow
 */
export const patchApiV1ProjectWorkflowsId = (
  id: string,
  patchApiV1ProjectWorkflowsIdBody: BodyType<PatchApiV1ProjectWorkflowsIdBody>,
) => {
  return axiosClient<PatchApiV1ProjectWorkflowsId200>({
    url: `/api/v1/project-workflows/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1ProjectWorkflowsIdBody,
  });
};

export const getPatchApiV1ProjectWorkflowsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1ProjectWorkflowsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1ProjectWorkflowsIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1ProjectWorkflowsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1ProjectWorkflowsIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1ProjectWorkflowsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1ProjectWorkflowsId>>,
    { id: string; data: BodyType<PatchApiV1ProjectWorkflowsIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1ProjectWorkflowsId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1ProjectWorkflowsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1ProjectWorkflowsId>>
>;
export type PatchApiV1ProjectWorkflowsIdMutationBody = BodyType<PatchApiV1ProjectWorkflowsIdBody>;
export type PatchApiV1ProjectWorkflowsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update project-workflow
 */
export const usePatchApiV1ProjectWorkflowsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1ProjectWorkflowsId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1ProjectWorkflowsIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1ProjectWorkflowsId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1ProjectWorkflowsIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1ProjectWorkflowsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing project-workflow resource
 * @summary Delete project-workflow
 */
export const deleteApiV1ProjectWorkflowsId = (id: string) => {
  return axiosClient<DeleteApiV1ProjectWorkflowsId204>({
    url: `/api/v1/project-workflows/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1ProjectWorkflowsIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1ProjectWorkflowsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1ProjectWorkflowsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1ProjectWorkflowsId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1ProjectWorkflowsId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1ProjectWorkflowsId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1ProjectWorkflowsIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1ProjectWorkflowsId>>
>;

export type DeleteApiV1ProjectWorkflowsIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete project-workflow
 */
export const useDeleteApiV1ProjectWorkflowsId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1ProjectWorkflowsId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1ProjectWorkflowsId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1ProjectWorkflowsIdMutationOptions(options);

  return useMutation(mutationOptions);
};
