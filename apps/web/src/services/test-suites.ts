/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1TestSuitesId204,
  ErrorResponse,
  GetApiV1TestSuites200,
  GetApiV1TestSuitesId200,
  GetApiV1TestSuitesParams,
  PatchApiV1TestSuitesId200,
  PatchApiV1TestSuitesIdBody,
  PostApiV1TestSuites201,
  PostApiV1TestSuitesBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of test suites resources with optional filtering and sorting
 * @summary Get test suites list
 */
export const getApiV1TestSuites = (params?: GetApiV1TestSuitesParams, signal?: AbortSignal) => {
  return axiosClient<GetApiV1TestSuites200>({
    url: '/api/v1/test-suites/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1TestSuitesQueryKey = (params?: GetApiV1TestSuitesParams) => {
  return ['/api/v1/test-suites/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1TestSuitesQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1TestSuites>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestSuitesParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuites>>, TError, TData>>;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1TestSuitesQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1TestSuites>>> = ({ signal }) =>
    getApiV1TestSuites(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1TestSuites>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1TestSuitesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1TestSuites>>
>;
export type GetApiV1TestSuitesQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1TestSuites<
  TData = Awaited<ReturnType<typeof getApiV1TestSuites>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1TestSuitesParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuites>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestSuites>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestSuites>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestSuites<
  TData = Awaited<ReturnType<typeof getApiV1TestSuites>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestSuitesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuites>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestSuites>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestSuites>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestSuites<
  TData = Awaited<ReturnType<typeof getApiV1TestSuites>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestSuitesParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuites>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get test suites list
 */

export function useGetApiV1TestSuites<
  TData = Awaited<ReturnType<typeof getApiV1TestSuites>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestSuitesParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuites>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1TestSuitesQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new test suite resource
 * @summary Create test suite
 */
export const postApiV1TestSuites = (
  postApiV1TestSuitesBody: BodyType<PostApiV1TestSuitesBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1TestSuites201>({
    url: '/api/v1/test-suites/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1TestSuitesBody,
    signal,
  });
};

export const getPostApiV1TestSuitesMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1TestSuites>>,
    TError,
    { data: BodyType<PostApiV1TestSuitesBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1TestSuites>>,
  TError,
  { data: BodyType<PostApiV1TestSuitesBody> },
  TContext
> => {
  const mutationKey = ['postApiV1TestSuites'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1TestSuites>>,
    { data: BodyType<PostApiV1TestSuitesBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1TestSuites(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1TestSuitesMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1TestSuites>>
>;
export type PostApiV1TestSuitesMutationBody = BodyType<PostApiV1TestSuitesBody>;
export type PostApiV1TestSuitesMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create test suite
 */
export const usePostApiV1TestSuites = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1TestSuites>>,
    TError,
    { data: BodyType<PostApiV1TestSuitesBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1TestSuites>>,
  TError,
  { data: BodyType<PostApiV1TestSuitesBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1TestSuitesMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific test suite resource by its unique identifier
 * @summary Get test suite by ID
 */
export const getApiV1TestSuitesId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1TestSuitesId200>({
    url: `/api/v1/test-suites/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1TestSuitesIdQueryKey = (id: string) => {
  return [`/api/v1/test-suites/${id}`] as const;
};

export const getGetApiV1TestSuitesIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1TestSuitesIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1TestSuitesId>>> = ({ signal }) =>
    getApiV1TestSuitesId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1TestSuitesIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1TestSuitesId>>
>;
export type GetApiV1TestSuitesIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1TestSuitesId<
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestSuitesId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestSuitesId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestSuitesId<
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestSuitesId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestSuitesId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestSuitesId<
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get test suite by ID
 */

export function useGetApiV1TestSuitesId<
  TData = Awaited<ReturnType<typeof getApiV1TestSuitesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestSuitesId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1TestSuitesIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing test suite resource
 * @summary Update test suite
 */
export const patchApiV1TestSuitesId = (
  id: string,
  patchApiV1TestSuitesIdBody: BodyType<PatchApiV1TestSuitesIdBody>,
) => {
  return axiosClient<PatchApiV1TestSuitesId200>({
    url: `/api/v1/test-suites/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1TestSuitesIdBody,
  });
};

export const getPatchApiV1TestSuitesIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1TestSuitesId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1TestSuitesIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1TestSuitesId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1TestSuitesIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1TestSuitesId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1TestSuitesId>>,
    { id: string; data: BodyType<PatchApiV1TestSuitesIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1TestSuitesId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1TestSuitesIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1TestSuitesId>>
>;
export type PatchApiV1TestSuitesIdMutationBody = BodyType<PatchApiV1TestSuitesIdBody>;
export type PatchApiV1TestSuitesIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update test suite
 */
export const usePatchApiV1TestSuitesId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1TestSuitesId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1TestSuitesIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1TestSuitesId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1TestSuitesIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1TestSuitesIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing test suite resource
 * @summary Delete test suite
 */
export const deleteApiV1TestSuitesId = (id: string) => {
  return axiosClient<DeleteApiV1TestSuitesId204>({
    url: `/api/v1/test-suites/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1TestSuitesIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1TestSuitesId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1TestSuitesId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1TestSuitesId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1TestSuitesId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1TestSuitesId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1TestSuitesIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1TestSuitesId>>
>;

export type DeleteApiV1TestSuitesIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete test suite
 */
export const useDeleteApiV1TestSuitesId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1TestSuitesId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1TestSuitesId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1TestSuitesIdMutationOptions(options);

  return useMutation(mutationOptions);
};
