/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';
import type { ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  ErrorResponse,
  GetApiV1AuditLogs200,
  GetApiV1AuditLogsId200,
  GetApiV1AuditLogsParams,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of audit-logs resources with optional filtering and sorting
 * @summary Get audit-logs list
 */
export const getApiV1AuditLogs = (params?: GetApiV1AuditLogsParams, signal?: AbortSignal) => {
  return axiosClient<GetApiV1AuditLogs200>({
    url: '/api/v1/audit-logs/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1AuditLogsQueryKey = (params?: GetApiV1AuditLogsParams) => {
  return ['/api/v1/audit-logs/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1AuditLogsQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1AuditLogs>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1AuditLogsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1AuditLogs>>, TError, TData>>;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1AuditLogsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1AuditLogs>>> = ({ signal }) =>
    getApiV1AuditLogs(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1AuditLogs>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1AuditLogsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1AuditLogs>>
>;
export type GetApiV1AuditLogsQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1AuditLogs<
  TData = Awaited<ReturnType<typeof getApiV1AuditLogs>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1AuditLogsParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1AuditLogs>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1AuditLogs>>,
          TError,
          Awaited<ReturnType<typeof getApiV1AuditLogs>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1AuditLogs<
  TData = Awaited<ReturnType<typeof getApiV1AuditLogs>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1AuditLogsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1AuditLogs>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1AuditLogs>>,
          TError,
          Awaited<ReturnType<typeof getApiV1AuditLogs>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1AuditLogs<
  TData = Awaited<ReturnType<typeof getApiV1AuditLogs>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1AuditLogsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1AuditLogs>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get audit-logs list
 */

export function useGetApiV1AuditLogs<
  TData = Awaited<ReturnType<typeof getApiV1AuditLogs>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1AuditLogsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1AuditLogs>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1AuditLogsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Retrieve a specific audit-log resource by its unique identifier
 * @summary Get audit-log by ID
 */
export const getApiV1AuditLogsId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1AuditLogsId200>({
    url: `/api/v1/audit-logs/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1AuditLogsIdQueryKey = (id: string) => {
  return [`/api/v1/audit-logs/${id}`] as const;
};

export const getGetApiV1AuditLogsIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1AuditLogsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1AuditLogsId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1AuditLogsIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1AuditLogsId>>> = ({ signal }) =>
    getApiV1AuditLogsId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1AuditLogsId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1AuditLogsIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1AuditLogsId>>
>;
export type GetApiV1AuditLogsIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1AuditLogsId<
  TData = Awaited<ReturnType<typeof getApiV1AuditLogsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1AuditLogsId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1AuditLogsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1AuditLogsId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1AuditLogsId<
  TData = Awaited<ReturnType<typeof getApiV1AuditLogsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1AuditLogsId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1AuditLogsId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1AuditLogsId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1AuditLogsId<
  TData = Awaited<ReturnType<typeof getApiV1AuditLogsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1AuditLogsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get audit-log by ID
 */

export function useGetApiV1AuditLogsId<
  TData = Awaited<ReturnType<typeof getApiV1AuditLogsId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1AuditLogsId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1AuditLogsIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}
