/**
 * Generated by orval v7.7.0 🍺
 * Do not edit manually.
 * API Documentation
 * API documentation for workspace management
 * OpenAPI spec version: 1.0.0
 */

import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { BodyType, ErrorType } from './axiosClient';

import { axiosClient } from './axiosClient';
import type {
  DeleteApiV1TestCasesId204,
  ErrorResponse,
  GetApiV1TestCases200,
  GetApiV1TestCasesId200,
  GetApiV1TestCasesParams,
  PatchApiV1TestCasesId200,
  PatchApiV1TestCasesIdBody,
  PostApiV1TestCases201,
  PostApiV1TestCasesBody,
} from './hooks.schemas';

/**
 * Retrieve a paginated list of test cases resources with optional filtering and sorting
 * @summary Get test cases list
 */
export const getApiV1TestCases = (params?: GetApiV1TestCasesParams, signal?: AbortSignal) => {
  return axiosClient<GetApiV1TestCases200>({
    url: '/api/v1/test-cases/',
    method: 'GET',
    params,
    signal,
  });
};

export const getGetApiV1TestCasesQueryKey = (params?: GetApiV1TestCasesParams) => {
  return ['/api/v1/test-cases/', ...(params ? [params] : [])] as const;
};

export const getGetApiV1TestCasesQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1TestCases>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestCasesParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCases>>, TError, TData>>;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1TestCasesQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1TestCases>>> = ({ signal }) =>
    getApiV1TestCases(params, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getApiV1TestCases>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetApiV1TestCasesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1TestCases>>
>;
export type GetApiV1TestCasesQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1TestCases<
  TData = Awaited<ReturnType<typeof getApiV1TestCases>>,
  TError = ErrorType<ErrorResponse>,
>(
  params: undefined | GetApiV1TestCasesParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCases>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestCases>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestCases>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestCases<
  TData = Awaited<ReturnType<typeof getApiV1TestCases>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestCasesParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCases>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestCases>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestCases>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestCases<
  TData = Awaited<ReturnType<typeof getApiV1TestCases>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestCasesParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCases>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get test cases list
 */

export function useGetApiV1TestCases<
  TData = Awaited<ReturnType<typeof getApiV1TestCases>>,
  TError = ErrorType<ErrorResponse>,
>(
  params?: GetApiV1TestCasesParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCases>>, TError, TData>>;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1TestCasesQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a new test case resource
 * @summary Create test case
 */
export const postApiV1TestCases = (
  postApiV1TestCasesBody: BodyType<PostApiV1TestCasesBody>,
  signal?: AbortSignal,
) => {
  return axiosClient<PostApiV1TestCases201>({
    url: '/api/v1/test-cases/',
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: postApiV1TestCasesBody,
    signal,
  });
};

export const getPostApiV1TestCasesMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1TestCases>>,
    TError,
    { data: BodyType<PostApiV1TestCasesBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postApiV1TestCases>>,
  TError,
  { data: BodyType<PostApiV1TestCasesBody> },
  TContext
> => {
  const mutationKey = ['postApiV1TestCases'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postApiV1TestCases>>,
    { data: BodyType<PostApiV1TestCasesBody> }
  > = (props) => {
    const { data } = props ?? {};

    return postApiV1TestCases(data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostApiV1TestCasesMutationResult = NonNullable<
  Awaited<ReturnType<typeof postApiV1TestCases>>
>;
export type PostApiV1TestCasesMutationBody = BodyType<PostApiV1TestCasesBody>;
export type PostApiV1TestCasesMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Create test case
 */
export const usePostApiV1TestCases = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postApiV1TestCases>>,
    TError,
    { data: BodyType<PostApiV1TestCasesBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof postApiV1TestCases>>,
  TError,
  { data: BodyType<PostApiV1TestCasesBody> },
  TContext
> => {
  const mutationOptions = getPostApiV1TestCasesMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Retrieve a specific test case resource by its unique identifier
 * @summary Get test case by ID
 */
export const getApiV1TestCasesId = (id: string, signal?: AbortSignal) => {
  return axiosClient<GetApiV1TestCasesId200>({
    url: `/api/v1/test-cases/${id}`,
    method: 'GET',
    signal,
  });
};

export const getGetApiV1TestCasesIdQueryKey = (id: string) => {
  return [`/api/v1/test-cases/${id}`] as const;
};

export const getGetApiV1TestCasesIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getApiV1TestCasesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCasesId>>, TError, TData>
    >;
  },
) => {
  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetApiV1TestCasesIdQueryKey(id);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getApiV1TestCasesId>>> = ({ signal }) =>
    getApiV1TestCasesId(id, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCasesId>>, TError, TData> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };
};

export type GetApiV1TestCasesIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getApiV1TestCasesId>>
>;
export type GetApiV1TestCasesIdQueryError = ErrorType<ErrorResponse>;

export function useGetApiV1TestCasesId<
  TData = Awaited<ReturnType<typeof getApiV1TestCasesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCasesId>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestCasesId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestCasesId>>
        >,
        'initialData'
      >;
  },
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestCasesId<
  TData = Awaited<ReturnType<typeof getApiV1TestCasesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCasesId>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getApiV1TestCasesId>>,
          TError,
          Awaited<ReturnType<typeof getApiV1TestCasesId>>
        >,
        'initialData'
      >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetApiV1TestCasesId<
  TData = Awaited<ReturnType<typeof getApiV1TestCasesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCasesId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get test case by ID
 */

export function useGetApiV1TestCasesId<
  TData = Awaited<ReturnType<typeof getApiV1TestCasesId>>,
  TError = ErrorType<ErrorResponse>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getApiV1TestCasesId>>, TError, TData>
    >;
  },
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetApiV1TestCasesIdQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update an existing test case resource
 * @summary Update test case
 */
export const patchApiV1TestCasesId = (
  id: string,
  patchApiV1TestCasesIdBody: BodyType<PatchApiV1TestCasesIdBody>,
) => {
  return axiosClient<PatchApiV1TestCasesId200>({
    url: `/api/v1/test-cases/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: patchApiV1TestCasesIdBody,
  });
};

export const getPatchApiV1TestCasesIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1TestCasesId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1TestCasesIdBody> },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchApiV1TestCasesId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1TestCasesIdBody> },
  TContext
> => {
  const mutationKey = ['patchApiV1TestCasesId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchApiV1TestCasesId>>,
    { id: string; data: BodyType<PatchApiV1TestCasesIdBody> }
  > = (props) => {
    const { id, data } = props ?? {};

    return patchApiV1TestCasesId(id, data);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchApiV1TestCasesIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchApiV1TestCasesId>>
>;
export type PatchApiV1TestCasesIdMutationBody = BodyType<PatchApiV1TestCasesIdBody>;
export type PatchApiV1TestCasesIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Update test case
 */
export const usePatchApiV1TestCasesId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchApiV1TestCasesId>>,
    TError,
    { id: string; data: BodyType<PatchApiV1TestCasesIdBody> },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchApiV1TestCasesId>>,
  TError,
  { id: string; data: BodyType<PatchApiV1TestCasesIdBody> },
  TContext
> => {
  const mutationOptions = getPatchApiV1TestCasesIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete an existing test case resource
 * @summary Delete test case
 */
export const deleteApiV1TestCasesId = (id: string) => {
  return axiosClient<DeleteApiV1TestCasesId204>({
    url: `/api/v1/test-cases/${id}`,
    method: 'DELETE',
  });
};

export const getDeleteApiV1TestCasesIdMutationOptions = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1TestCasesId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteApiV1TestCasesId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationKey = ['deleteApiV1TestCasesId'];
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteApiV1TestCasesId>>,
    { id: string }
  > = (props) => {
    const { id } = props ?? {};

    return deleteApiV1TestCasesId(id);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteApiV1TestCasesIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteApiV1TestCasesId>>
>;

export type DeleteApiV1TestCasesIdMutationError = ErrorType<ErrorResponse>;

/**
 * @summary Delete test case
 */
export const useDeleteApiV1TestCasesId = <
  TError = ErrorType<ErrorResponse>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteApiV1TestCasesId>>,
    TError,
    { id: string },
    TContext
  >;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteApiV1TestCasesId>>,
  TError,
  { id: string },
  TContext
> => {
  const mutationOptions = getDeleteApiV1TestCasesIdMutationOptions(options);

  return useMutation(mutationOptions);
};
