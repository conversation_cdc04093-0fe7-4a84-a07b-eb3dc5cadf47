'use client';

import { Card, CardContent } from '@repo/ui/components/card';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate, useParams } from '@tanstack/react-router';
import { getGetApiV1InvitationsQueryKey } from '@/web/services/invitations';
import { getGetApiV1RoleAssignmentsQueryKey } from '@/web/services/role-assignments';
import {
  deleteApiV1WorkspacesId,
  getGetApiV1WorkspacesQueryKey,
  useGetApiV1WorkspacesId,
} from '@/web/services/workspaces';
import { WorkspaceForm } from './workspace-form';

const WorkspaceOverview = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { wsId } = useParams({
    from: '/__mainLayout/workspace/$wsId',
  });

  const { data: { data } = {} } = useGetApiV1WorkspacesId(wsId);

  const handleSubmit = () => {
    // Navigate back to dashboard or stay on the same page
    // The WorkspaceForm component handles all the query invalidation
  };

  const handleCancel = () => {
    navigate({ to: '/' });
  };

  const handleDelete = async () => {
    try {
      if (data) {
        await deleteApiV1WorkspacesId(data.id);

        // Invalidate role-related queries
        await queryClient.invalidateQueries({
          queryKey: getGetApiV1RoleAssignmentsQueryKey(),
        });
        await queryClient.invalidateQueries({
          queryKey: getGetApiV1InvitationsQueryKey(),
        });
        // Invalidate workspaces list query to refresh the UI
        await queryClient.invalidateQueries({
          queryKey: getGetApiV1WorkspacesQueryKey(),
        });

        navigate({ to: '/' });
      }
    } catch (error) {
      console.error('Failed to delete workspace:', error);
    }
  };

  if (wsId && !data) {
    return (
      <div className="flex h-screen items-center justify-center">
        <p className="text-muted-foreground">Loading workspace data...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-h-screen p-6">
      <Card className="w-full overflow-y-auto rounded-none border-none">
        <CardContent className="p-0">
          <WorkspaceForm
            editPayload={data}
            onCancel={handleCancel}
            onDelete={handleDelete}
            onSubmit={handleSubmit}
            showDelete={true}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default WorkspaceOverview;
