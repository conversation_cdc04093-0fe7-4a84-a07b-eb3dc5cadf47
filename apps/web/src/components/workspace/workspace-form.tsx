import { Button } from '@repo/ui/components/button';
import { Confirmation } from '@repo/ui/components/custom/confirmDialog';
import { DialogFooter } from '@repo/ui/components/dialog';
import { useQueryClient } from '@tanstack/react-query';
import type React from 'react';
import { useState } from 'react';
import type {
  GetApiV1WorkspacesId200Data,
  PatchApiV1WorkspacesIdBody,
  PostApiV1WorkspacesBody,
} from '@/web/services/hooks.schemas';
import { getGetApiV1InvitationsQueryKey } from '@/web/services/invitations';
import { getGetApiV1RoleAssignmentsQueryKey } from '@/web/services/role-assignments';
import { usePatchApiV1WorkspacesId, usePostApiV1Workspaces } from '@/web/services/workspaces';
import { useRootStore } from '@/web/store/store';
import { WorkspaceForm as WorkspaceFormComponent } from '../forms/workspace-form';

interface WorkspaceFormProps {
  onSubmit?: () => void;
  onCancel?: () => void;
  editPayload?: GetApiV1WorkspacesId200Data | null;
  showDelete?: boolean;
  onDelete?: () => void;
}

export const WorkspaceForm: React.FC<WorkspaceFormProps> = ({
  onSubmit,
  onCancel,
  editPayload,
  showDelete = false,
  onDelete,
}) => {
  const queryClient = useQueryClient();
  const currentOrganizationId = useRootStore((state) => state.currentOrganizationId);
  const currentUser = useRootStore((state) => state.currentUser);
  const organizations = useRootStore((state) => state.organizations);

  const currentOrganization = organizations.find((org) => org.id === currentOrganizationId);

  // Define the form data type to match WorkspaceFormData from the reusable component
  interface FormData {
    name: string;
    slug: string;
    description?: string;
    type: string;
    color: string;
    visibility: 'public' | 'private';
    icon?: string;
    organizationId?: string;
  }

  const [formData, setFormData] = useState<FormData>({
    name: editPayload?.name || '',
    slug: editPayload?.slug || '',
    description: editPayload?.description || undefined,
    type: (editPayload?.settings as { type?: string })?.type || 'general',
    color: editPayload?.color || '#8b5cf6',
    visibility: (editPayload?.visibility || 'private') as 'public' | 'private',
    icon: editPayload?.icon || undefined,
    organizationId: editPayload?.organizationId || currentOrganizationId || undefined,
  });

  const workspaceMutation = usePostApiV1Workspaces({
    mutation: {
      onSuccess: async () => {
        // Invalidate role-related queries
        await queryClient.invalidateQueries({
          queryKey: getGetApiV1RoleAssignmentsQueryKey(),
        });
        await queryClient.invalidateQueries({
          queryKey: getGetApiV1InvitationsQueryKey(),
        });
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (_error) => {},
    },
  });

  const workspaceUpdateMutation = usePatchApiV1WorkspacesId({
    mutation: {
      onSuccess: async () => {
        // Invalidate role-related queries
        await queryClient.invalidateQueries({
          queryKey: getGetApiV1RoleAssignmentsQueryKey(),
        });
        await queryClient.invalidateQueries({
          queryKey: getGetApiV1InvitationsQueryKey(),
        });
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (_error) => {},
    },
  });

  const handleSubmit = async () => {
    const dataToSubmit: PatchApiV1WorkspacesIdBody | PostApiV1WorkspacesBody = {
      name: formData.name,
      slug: formData.slug,
      description: formData.description || null,
      icon: formData.icon || null,
      color: formData.color,
      visibility: formData.visibility,
      organizationId: formData.organizationId || currentOrganizationId!,
      isActive: true,
      settings: {
        type: formData.type,
      },
      createdBy: currentUser?.id,
    };

    if (editPayload) {
      await workspaceUpdateMutation.mutateAsync({
        data: dataToSubmit,
        id: editPayload.id,
      });
    } else {
      await workspaceMutation.mutateAsync({
        data: dataToSubmit as PostApiV1WorkspacesBody,
      });
    }
  };

  const isLoading = workspaceMutation.isPending || workspaceUpdateMutation.isPending;

  return (
    <>
      <div className="overflow-auto p-6">
        <WorkspaceFormComponent
          data={formData}
          isOnboarding={false}
          onDataChange={setFormData}
          organizationSlug={currentOrganization?.slug}
          showTypeSelector={!editPayload} // Show type selector only for new workspaces
        />
      </div>
      <DialogFooter className={showDelete && editPayload ? 'justify-between' : ''}>
        {showDelete && editPayload && onDelete && (
          <Confirmation
            cancelButton={{
              name: 'Cancel',
              onClick: () => {},
              variant: 'secondary',
            }}
            confirmButton={{
              name: 'Delete',
              onClick: onDelete,
              variant: 'destructive',
            }}
            description="This will permanently delete the workspace and all its contents. This action cannot be undone."
            title="Delete Workspace"
          >
            <Button size="sm" variant="destructive">
              Delete Workspace
            </Button>
          </Confirmation>
        )}
        <div className="flex gap-2">
          <Button disabled={isLoading} onClick={onCancel} variant="outline">
            Cancel
          </Button>
          <Button disabled={isLoading || !formData.name || !formData.slug} onClick={handleSubmit}>
            {isLoading ? 'Saving...' : editPayload ? 'Update' : 'Create'}
          </Button>
        </div>
      </DialogFooter>
    </>
  );
};
