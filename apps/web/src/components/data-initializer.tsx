import { Button } from '@repo/ui/components/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@repo/ui/components/dialog';
import { useNavigate } from '@tanstack/react-router';
import { Building2, Plus } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useRootStore } from '@/web/store/store';
import { useGetApiV1RoleAssignments } from '../services/role-assignments';
import { useGetApiV1WorkflowTransitions } from '../services/workflow-transitions';
import { OrganizationForm } from './organization/organization-form';

export function DataInitializer() {
  const [showOrgSelector, setShowOrgSelector] = useState(false);
  const [showCreateOrgDialog, setShowCreateOrgDialog] = useState(false);

  const isAuthenticated = useRootStore((state) => !!state.accessToken);
  const currentUserId = useRootStore((state) => state.currentUser?.id);
  const currentOrganizationId = useRootStore((state) => state.currentOrganizationId);
  const currentProjectId = useRootStore((state) => state.currentProjectId);
  const navigate = useNavigate();
  const setRoleAssignments = useRootStore((state) => state.setRoleAssignments);
  const setLoadingRoleAssignments = useRootStore((state) => state.setLoadingRoleAssignments);
  const setRoleAssignmentsError = useRootStore((state) => state.setRoleAssignmentsError);
  const getOrganizationsFromRoleAssignments = useRootStore(
    (state) => state.getOrganizationsFromRoleAssignments,
  );
  const getProjectById = useRootStore((state) => state.getProjectById);
  const setCurrentProjectWorkflowTransitions = useRootStore(
    (state) => state.setCurrentProjectWorkflowTransitions,
  );

  const populateDataFromRoleAssignments = useRootStore(
    (state) => state.populateDataFromRoleAssignments,
  );

  // Only fetch role assignments - everything else will be derived from them

  // Fetch role assignments for current user
  const {
    data: roleAssignmentsData,
    isLoading: isLoadingRole,
    error: roleError,
    isSuccess: roleSuccess,
  } = useGetApiV1RoleAssignments({
    filters: currentUserId
      ? JSON.stringify({
          userId: { $eq: currentUserId },
        })
      : undefined,
  });

  // Get current project and its workflow ID
  const currentProject = React.useMemo(() => {
    return currentProjectId ? getProjectById(currentProjectId) : null;
  }, [currentProjectId, getProjectById]);

  // Fetch workflow transitions for current project's workflow
  const {
    data: workflowTransitionsData,
    error: transitionsError,
    isSuccess: transitionsSuccess,
  } = useGetApiV1WorkflowTransitions({
    filters: currentProject?.workflowId
      ? JSON.stringify({
          workflowId: { $eq: currentProject.workflowId },
        })
      : undefined,
  });

  // Update role assignments in store
  useEffect(() => {
    setLoadingRoleAssignments(isLoadingRole);

    if (roleSuccess && roleAssignmentsData?.data) {
      setRoleAssignments(roleAssignmentsData.data);
      setRoleAssignmentsError(null);
      // Populate organizations, workspaces, and projects from role assignments
      populateDataFromRoleAssignments();
      if (roleAssignmentsData.data.length === 0) {
        navigate({ to: '/' });
      }
    }

    if (roleError) {
      setRoleAssignmentsError(roleError.message || 'Failed to load role assignments');
    }
  }, [
    isLoadingRole,
    roleSuccess,
    roleAssignmentsData,
    roleError,
    setRoleAssignments,
    setLoadingRoleAssignments,
    setRoleAssignmentsError,
    populateDataFromRoleAssignments,
    navigate,
  ]);

  // Update workflow transitions in store
  useEffect(() => {
    if (transitionsSuccess && workflowTransitionsData?.data) {
      setCurrentProjectWorkflowTransitions(workflowTransitionsData.data);
    }

    if (transitionsError) {
      // Clear transitions if there's an error
      setCurrentProjectWorkflowTransitions([]);
    }

    // Clear transitions when no project is selected
    if (!currentProject?.workflowId) {
      setCurrentProjectWorkflowTransitions([]);
    }
  }, [
    transitionsSuccess,
    workflowTransitionsData,
    transitionsError,
    currentProject?.workflowId,
    setCurrentProjectWorkflowTransitions,
  ]);

  // Clear data when user logs out
  useEffect(() => {
    if (!isAuthenticated) {
      // The store's resetStore method will clear all data
      useRootStore.getState().clearAppData();
    }
  }, [isAuthenticated]);

  // Get organizations from role assignments
  const organizations = React.useMemo(() => {
    if (!currentUserId) {
      return [];
    }
    return getOrganizationsFromRoleAssignments(currentUserId);
  }, [currentUserId, getOrganizationsFromRoleAssignments]);

  // Check if organization selection is needed
  useEffect(() => {
    if (isAuthenticated && !isLoadingRole && organizations.length > 0 && !currentOrganizationId) {
      setShowOrgSelector(true);
    } else {
      setShowOrgSelector(false);
    }
  }, [isAuthenticated, isLoadingRole, organizations.length, currentOrganizationId]);

  // Render organization selector if needed
  if (showOrgSelector) {
    return (
      <div className="fixed inset-0 z-50 bg-background">
        <div className="flex h-full flex-col items-center justify-center p-8">
          <div className="mb-8 text-center">
            <h1 className="mb-2 font-semibold text-2xl">Welcome!</h1>
            <p className="text-muted-foreground">Please select an organization to continue</p>
          </div>
          <div className="grid w-full max-w-2xl gap-4">
            {organizations.map((org) => (
              <Button
                className="h-auto w-full justify-start p-6"
                key={org.id}
                onClick={() => {
                  const {
                    setCurrentOrganizationId,
                    setCurrentWorkspaceId,
                    getWorkspacesFromRoleAssignments,
                    currentUser,
                  } = useRootStore.getState();
                  setCurrentOrganizationId(org.id);

                  // Auto-select first workspace user has access to in this org
                  const userWorkspaces = getWorkspacesFromRoleAssignments(currentUser?.id || '');
                  const orgWorkspaces = userWorkspaces.filter((ws) => ws.organizationId === org.id);
                  if (orgWorkspaces.length > 0) {
                    setCurrentWorkspaceId(orgWorkspaces[0].id);
                  }

                  setShowOrgSelector(false);
                }}
                variant="outline"
              >
                <div className="flex items-start gap-4">
                  <Building2 className="mt-1 h-8 w-8 shrink-0" />
                  <div className="text-left">
                    <h3 className="font-semibold text-lg">{org.name}</h3>
                    {org.description && (
                      <p className="mt-1 text-muted-foreground text-sm">{org.description}</p>
                    )}
                  </div>
                </div>
              </Button>
            ))}
            <Button
              className="h-auto w-full p-6"
              onClick={() => {
                setShowCreateOrgDialog(true);
              }}
              variant="default"
            >
              <Plus className="mr-2 h-5 w-5" />
              Create New Organization
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // This component doesn't render anything normally
  return (
    <>
      {/* Create organization dialog */}
      <Dialog onOpenChange={setShowCreateOrgDialog} open={showCreateOrgDialog}>
        <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Organization</DialogTitle>
            <DialogDescription>
              Create a new organization to manage your workspaces and projects.
            </DialogDescription>
          </DialogHeader>
          <OrganizationForm
            onCancel={() => setShowCreateOrgDialog(false)}
            onSuccess={(newOrg) => {
              const {
                setCurrentOrganizationId,
                setCurrentWorkspaceId,
                getWorkspacesFromRoleAssignments,
                currentUser,
              } = useRootStore.getState();
              setCurrentOrganizationId(newOrg.id);

              // Auto-select first workspace if available
              const userWorkspaces = getWorkspacesFromRoleAssignments(currentUser?.id || '');
              const orgWorkspaces = userWorkspaces.filter((ws) => ws.organizationId === newOrg.id);
              if (orgWorkspaces.length > 0) {
                setCurrentWorkspaceId(orgWorkspaces[0].id);
              }

              setShowCreateOrgDialog(false);
              setShowOrgSelector(false);
            }}
          />
        </DialogContent>
      </Dialog>
      {/* show loader while fetching */}
      {isLoadingRole ? (
        <div className="flex min-h-screen items-center justify-center">
          <div className="text-muted-foreground">Loading role assignments...</div>
        </div>
      ) : null}
      {/* No organization selector needed */}
    </>
  );
}
