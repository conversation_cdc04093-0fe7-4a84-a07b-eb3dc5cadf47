import { Button } from '@repo/ui/components/button';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@repo/ui/components/dialog';
import { useState } from 'react';

import { useGetApiV1Projects } from '@/web/services/projects';
import { useRootStore } from '@/web/store/store';
import ProjectCard from './project-card';
import { ProjectForm } from './project-form';

export const ProjectsList = () => {
  const currentWorkspaceId = useRootStore((state) => state.currentWorkspaceId);

  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Fetch projects (assuming this hook exists and filters by workspaceId)
  const {
    data: projects,
    isLoading,
    refetch,
  } = useGetApiV1Projects({
    filters: JSON.stringify({
      workspaceId: {
        $eq: currentWorkspaceId,
      },
    }),
  });

  const handleProjectSubmit = () => {
    refetch();
    setIsDialogO<PERSON>(false); // Close dialog after submission
  };

  const handleCancel = () => {
    setIsDialogOpen(false); // Close dialog on cancel
  };

  if (isLoading) {
    return <div>Loading projects...</div>;
  }

  return (
    <div className="p-6">
      {/* Header with Add Project Button */}
      <div className="mb-6 flex items-center justify-between">
        <div className="font-semibold text-2xl">Projects</div>
        <Dialog onOpenChange={setIsDialogOpen} open={isDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setIsDialogOpen(true)}>Add New Project</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Project</DialogTitle>
            </DialogHeader>
            <ProjectForm
              editPayload={null}
              onCancel={handleCancel}
              onSubmit={handleProjectSubmit}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Projects Grid */}
      {projects?.data && projects.data.length > 0 ? (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {projects.data.map((project) => (
            <ProjectCard key={project.id} onEditSave={handleProjectSubmit} project={project} />
          ))}
        </div>
      ) : (
        <div className="text-center text-muted-foreground">
          No projects found for this workspace.
        </div>
      )}
    </div>
  );
};
