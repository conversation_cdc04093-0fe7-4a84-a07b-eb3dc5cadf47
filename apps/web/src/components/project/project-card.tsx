import { Button } from '@repo/ui/components/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@repo/ui/components/card';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@repo/ui/components/dialog';
import { Link } from '@tanstack/react-router';
import { Pencil } from 'lucide-react';
import { useState } from 'react';
import type { Project } from '@/web/services/hooks.schemas';
import { ProjectForm } from './project-form';

//prop types
type ProjectProps = {
  project: Project;
  onEditSave: () => void;
};

const ProjectCard: React.FC<ProjectProps> = ({ project, onEditSave }) => {
  const [openForm, setOpenForm] = useState(false);

  return (
    <>
      <Card className="transition-shadow hover:shadow-lg" key={project.id}>
        <CardHeader className="">
          <CardTitle className="flex items-center justify-between gap-2">
            {project.icon && (
              <span className="text-xl" style={{ color: project.color || '#000000' }}>
                {project.icon}
              </span>
            )}
            <Link params={{ projId: project.id }} to={'/project/$projId'}>
              {' '}
              {project.name}
            </Link>
            <Button
              onClick={() => {
                setOpenForm(true);
              }}
              size="sm"
              variant="ghost"
            >
              <Pencil size={14} />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-sm">Key: {project.key}</p>
          <p className="mt-1 text-muted-foreground text-sm">
            {project.description || 'No description available'}
          </p>
          <div className="mt-2">
            <span
              className={`inline-block rounded px-2 py-1 font-semibold text-xs ${
                project.status === 'active'
                  ? 'bg-primary/10 text-primary-foreground dark:bg-primary/20 dark:text-primary'
                  : 'bg-muted text-foreground'
              }`}
            >
              {project.status}
            </span>
            <span
              className={`ml-2 inline-block rounded px-2 py-1 font-semibold text-xs ${
                project.visibility === 'public'
                  ? 'bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary'
                  : 'bg-muted text-foreground'
              }`}
            >
              {project.visibility}
            </span>
          </div>
          <div className="mt-2 text-muted-foreground text-xs">
            <p>
              Start:{' '}
              {project.startDate
                ? new Date(project.startDate as string).toLocaleDateString()
                : 'N/A'}
            </p>
            <p>
              Target:{' '}
              {project.targetDate
                ? new Date(project.targetDate as string).toLocaleDateString()
                : 'N/A'}
            </p>
          </div>
        </CardContent>
      </Card>
      {openForm && (
        <Dialog onOpenChange={setOpenForm} open={openForm}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Project</DialogTitle>
            </DialogHeader>
            <ProjectForm
              editPayload={project}
              onCancel={() => {
                setOpenForm(false);
              }}
              onSubmit={() => {
                setOpenForm(false);
                onEditSave();
              }}
            />
          </DialogContent>
        </Dialog>
      )}
    </>
  );
};

export default ProjectCard;
