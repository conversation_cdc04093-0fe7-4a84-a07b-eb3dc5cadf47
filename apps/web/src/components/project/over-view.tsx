'use client';

import { Card, CardContent } from '@repo/ui/components/card';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate, useParams } from '@tanstack/react-router';
import { getGetApiV1InvitationsQueryKey } from '@/web/services/invitations';
import {
  deleteApiV1ProjectsId,
  getGetApiV1ProjectsQueryKey,
  useGetApiV1ProjectsId,
} from '@/web/services/projects';
import { getGetApiV1RoleAssignmentsQueryKey } from '@/web/services/role-assignments';
import { ProjectForm } from './project-form';

const ProjectOverview = () => {
  const { projId } = useParams({
    from: '/__mainLayout/project/$projId',
  });

  const { data: { data } = {}, isLoading } = useGetApiV1ProjectsId(projId);
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const handleSubmit = () => {
    // Navigate back to dashboard or stay on the same page
    // The ProjectForm component handles all the query invalidation
  };

  const handleCancel = () => {
    navigate({ to: '/' });
  };

  const handleDelete = async () => {
    try {
      if (data) {
        await deleteApiV1ProjectsId(data.id);

        // Invalidate role-related queries
        await queryClient.invalidateQueries({
          queryKey: getGetApiV1RoleAssignmentsQueryKey(),
        });
        await queryClient.invalidateQueries({
          queryKey: getGetApiV1InvitationsQueryKey(),
        });
        // Invalidate projects list query to refresh the UI
        await queryClient.invalidateQueries({
          queryKey: getGetApiV1ProjectsQueryKey(),
        });

        navigate({ to: '/' });
      }
    } catch (error) {
      console.error('Failed to delete project:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <p className="">Loading project data...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-h-screen p-6">
      <Card className="w-full overflow-y-auto rounded-none border-none">
        <CardContent className="p-0">
          <ProjectForm
            editPayload={data}
            onCancel={handleCancel}
            onDelete={handleDelete}
            onSubmit={handleSubmit}
            showDelete={true}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default ProjectOverview;
