'use client';

import { <PERSON><PERSON> } from '@repo/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/ui/components/card';
import { Input } from '@repo/ui/components/input';
import { Label } from '@repo/ui/components/label';
import { Progress } from '@repo/ui/components/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { Textarea } from '@repo/ui/components/textarea';
import { useNavigate } from '@tanstack/react-router';
import { AnimatePresence, motion } from 'framer-motion';
import { Check, Loader2 } from 'lucide-react';
import { useState } from 'react';
import type { PostApiV1ProjectsBody } from '@/web/services/hooks.schemas';
import { usePostApiV1Projects } from '@/web/services/projects';
import { useRootStore } from '@/web/store/store';

export function ProjectCreateForm() {
  const navigate = useNavigate();
  const currentUser = useRootStore((state) => state.currentUser);
  const currentWorkspaceId = useRootStore((state) => state.currentWorkspaceId);
  const createProject = usePostApiV1Projects();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [formData, setFormData] = useState<PostApiV1ProjectsBody>({
    name: '',
    key: '',
    description: '',
    workspaceId: currentWorkspaceId || '',
    isActive: true,
    createdBy: currentUser?.id || null,
    icon: '',
    color: '#000000',
    startDate: null,
    targetDate: null,
    actualEndDate: null,
    status: 'active',
    visibility: 'private',
  });

  const totalSteps = 2;
  const progress = (currentStep / totalSteps) * 100;

  const generateKey = (name: string): string => {
    return name
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '');
  };

  const handleInputChange = (field: keyof PostApiV1ProjectsBody, value: string | boolean) => {
    const newFormData = { ...formData, [field]: value };
    if (field === 'name') {
      newFormData.key = generateKey(value as string);
    }
    setFormData(newFormData);
  };

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      const projectData = {
        name: formData.name,
        key: formData.key,
        description: formData.description,
        workspaceId: formData.workspaceId,
        isActive: formData.isActive,
        createdBy: formData.createdBy,
        icon: formData.icon,
        color: formData.color,
        startDate: formData.startDate,
        targetDate: formData.targetDate,
        actualEndDate: formData.actualEndDate,
        status: formData.status,
        visibility: formData.visibility,
        settings: {},
      };

      await createProject.mutateAsync({ data: projectData });
      setShowSuccess(true);

      setTimeout(() => {
        navigate({ to: '/' });
      }, 2000);
    } catch (_error) {
    } finally {
      setIsSubmitting(false);
    }
  };

  if (showSuccess) {
    return (
      <div className="flex min-h-screen w-full items-center justify-center">
        <motion.div
          animate={{ scale: 1 }}
          className="text-center"
          initial={{ scale: 0 }}
          transition={{ duration: 0.5, type: 'spring' }}
        >
          <div className="mx-auto mb-4 flex h-24 w-24 items-center justify-center rounded-full bg-success/10">
            <Check className="h-12 w-12 text-success" />
          </div>
          <h2 className="mb-2 font-bold text-2xl text-foreground">Project Created!</h2>
          <p className="text-muted-foreground">Redirecting to dashboard...</p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-10">
      <Card>
        <CardHeader>
          <CardTitle>Create Your Project</CardTitle>
          <CardDescription>
            Let's set up your project. This will only take a minute.
          </CardDescription>
          <div className="pt-4">
            <Progress className="h-2" value={progress} />
          </div>
        </CardHeader>
        <CardContent>
          <AnimatePresence mode="wait">
            {currentStep === 1 && (
              <motion.div
                animate={{ opacity: 1, x: 0 }}
                className="space-y-4"
                exit={{ opacity: 0, x: -20 }}
                initial={{ opacity: 0, x: 20 }}
                key="step1"
                transition={{ duration: 0.3 }}
              >
                <div>
                  <Label htmlFor="name">Project Name *</Label>
                  <Input
                    className="mt-1"
                    id="name"
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Enter your project name"
                    value={formData.name}
                  />
                  <p className="mt-1 text-muted-foreground text-sm">
                    This will be your project's display name
                  </p>
                </div>
                <div>
                  <Label htmlFor="key">Key *</Label>
                  <Input
                    className="mt-1"
                    disabled
                    id="key"
                    onChange={(e) => handleInputChange('key', e.target.value)}
                    placeholder="project-key"
                    value={formData.key}
                  />
                  <p className="mt-1 text-muted-foreground text-sm">
                    Auto-generated from project name
                  </p>
                </div>
                <div className="flex justify-end">
                  <Button
                    disabled={!(formData.name.trim() && formData.key.trim())}
                    onClick={handleNext}
                  >
                    Next
                  </Button>
                </div>
              </motion.div>
            )}

            {currentStep === 2 && (
              <motion.div
                animate={{ opacity: 1, x: 0 }}
                className="space-y-4"
                exit={{ opacity: 0, x: -20 }}
                initial={{ opacity: 0, x: 20 }}
                key="step2"
                transition={{ duration: 0.3 }}
              >
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    className="mt-1"
                    id="description"
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Tell us about your project"
                    rows={4}
                    value={formData.description || ''}
                  />
                </div>
                <div>
                  {/* <Label htmlFor="workspaceId">Workspace ID</Label>
                  <Input
                    id="workspaceId"
                    placeholder="Enter workspace ID"
                    value={formData.workspaceId}
                    onChange={(e) =>
                      handleInputChange("workspaceId", e.target.value)
                    }
                    className="mt-1"
                  /> */}
                </div>
                <div>
                  <Label htmlFor="icon">Icon</Label>
                  <Input
                    className="mt-1"
                    id="icon"
                    onChange={(e) => handleInputChange('icon', e.target.value)}
                    placeholder="Icon URL or name"
                    value={formData.icon || ''}
                  />
                </div>
                <div>
                  <Label htmlFor="color">Color</Label>
                  <Input
                    className="mt-1"
                    id="color"
                    onChange={(e) => handleInputChange('color', e.target.value)}
                    type="color"
                    value={formData.color || ''}
                  />
                </div>
                <div>
                  <Label htmlFor="startDate">Start Date</Label>
                  <Input
                    className="mt-1"
                    id="startDate"
                    onChange={(e) => handleInputChange('startDate', e.target.value)}
                    type="date"
                    value={formData.startDate as string}
                  />
                </div>
                <div>
                  <Label htmlFor="targetDate">Target Date</Label>
                  <Input
                    className="mt-1"
                    id="targetDate"
                    onChange={(e) => handleInputChange('targetDate', e.target.value)}
                    type="date"
                    value={formData.targetDate as string}
                  />
                </div>
                <div>
                  <Label htmlFor="actualEndDate">Actual End Date</Label>
                  <Input
                    className="mt-1"
                    id="actualEndDate"
                    onChange={(e) => handleInputChange('actualEndDate', e.target.value)}
                    type="date"
                    value={formData.actualEndDate as string}
                  />
                </div>
                <div>
                  <Label htmlFor="status">Status</Label>
                  <Select
                    onValueChange={(value) => handleInputChange('status', value)}
                    value={formData.status}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select a status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="planned">Planned</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="on_hold">On Hold</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="visibility">Visibility</Label>
                  <Select
                    onValueChange={(value) => handleInputChange('visibility', value)}
                    value={formData.visibility}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select visibility" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="private">Private</SelectItem>
                      <SelectItem value="public">Public</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-between">
                  <Button onClick={handleBack} variant="outline">
                    Back
                  </Button>
                  <Button disabled={isSubmitting} onClick={handleSubmit}>
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      'Create Project'
                    )}
                  </Button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </CardContent>
      </Card>
    </div>
  );
}
