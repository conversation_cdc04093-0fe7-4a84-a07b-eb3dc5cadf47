'use client';

import { Button } from '@repo/ui/components/button';
import { Confirmation } from '@repo/ui/components/custom/confirmDialog';
import { DialogFooter } from '@repo/ui/components/dialog';
import { useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import type {
  PatchApiV1ProjectsIdBody,
  PostApiV1ProjectsBody,
  Project,
} from '@/web/services/hooks.schemas';
import { getGetApiV1InvitationsQueryKey } from '@/web/services/invitations';
import { usePostApiV1ProjectWorkflows } from '@/web/services/project-workflows';
import { usePatchApiV1ProjectsId, usePostApiV1Projects } from '@/web/services/projects';
import { getGetApiV1RoleAssignmentsQueryKey } from '@/web/services/role-assignments';
import { useRootStore } from '@/web/store/store';
import { ProjectForm as ProjectFormComponent } from '../forms/project-form';

interface ProjectFormProps {
  onSubmit?: () => void;
  onCancel?: () => void;
  editPayload?: null | Project;
  showDelete?: boolean;
  onDelete?: () => void;
}

export const ProjectForm: React.FC<ProjectFormProps> = ({
  onSubmit,
  onCancel,
  editPayload,
  showDelete = false,
  onDelete,
}) => {
  const queryClient = useQueryClient();
  const currentUser = useRootStore((state) => state.currentUser);
  const currentWorkspaceId = useRootStore((state) => state.currentWorkspaceId);
  const organizations = useRootStore((state) => state.organizations);
  const workspaces = useRootStore((state) => state.workspaces);

  const currentWorkspace = workspaces.find((ws) => ws.id === currentWorkspaceId);
  const currentOrganization = organizations.find(
    (org) => org.id === currentWorkspace?.organizationId,
  );

  // Define the form data type to match ProjectFormData from the reusable component

  const [formData, setFormData] = useState<PatchApiV1ProjectsIdBody | PostApiV1ProjectsBody>({
    workspaceId: currentWorkspaceId,
    visibility: 'private',
    ...editPayload,
  });

  const projectWorkflowMutation = usePostApiV1ProjectWorkflows({
    mutation: {
      onError: (_error) => {},
    },
  });

  const projectMutation = usePostApiV1Projects({
    mutation: {
      onSuccess: async (projectData) => {
        // If a workflow is selected, create the project-workflow association
        if (formData.workflowId) {
          try {
            await projectWorkflowMutation.mutateAsync({
              data: {
                projectId: projectData.data.id,
                workflowId: formData.workflowId,
                isActive: true,
                isDefault: true,
              },
            });
          } catch (_error) {}
        }

        // Invalidate role-related queries
        await queryClient.invalidateQueries({
          queryKey: getGetApiV1RoleAssignmentsQueryKey(),
        });
        await queryClient.invalidateQueries({
          queryKey: getGetApiV1InvitationsQueryKey(),
        });
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (_error) => {},
    },
  });

  const projectUpdateMutation = usePatchApiV1ProjectsId({
    mutation: {
      onSuccess: async () => {
        // Invalidate role-related queries
        await queryClient.invalidateQueries({
          queryKey: getGetApiV1RoleAssignmentsQueryKey(),
        });
        await queryClient.invalidateQueries({
          queryKey: getGetApiV1InvitationsQueryKey(),
        });
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (_error) => {},
    },
  });

  const handleSubmit = async () => {
    const dataToSubmit: PatchApiV1ProjectsIdBody | PostApiV1ProjectsBody = {
      name: formData.name,
      key: formData.key,
      description: formData.description || null,
      workspaceId: currentWorkspaceId,
      isActive: true,
      createdBy: currentUser?.id || null,
      settings: {
        // methodology: formData.methodology,
      },
      icon: formData.icon || null,
      color: formData.color,
      startDate: formData.startDate || null,
      targetDate: formData.targetDate || null,
      actualEndDate: null,
      status: editPayload?.status || 'planning',
      visibility: formData.visibility,
      defaultAssigneeId: null,
      workflowId: formData?.workflowId,
    };

    if (editPayload) {
      await projectUpdateMutation.mutateAsync({
        data: dataToSubmit,
        id: editPayload.id,
      });
    } else {
      await projectMutation.mutateAsync({
        data: dataToSubmit as PostApiV1ProjectsBody,
      });
    }
  };

  const isLoading = projectMutation.isPending || projectUpdateMutation.isPending;

  return (
    <>
      <div className="overflow-auto p-6">
        <ProjectFormComponent
          data={formData}
          isOnboarding={false}
          onDataChange={setFormData}
          organizationSlug={currentOrganization?.slug}
          showMethodology={!editPayload}
          showTimeline={true} // Show methodology selector only for new projects
          workspaceSlug={currentWorkspace?.slug}
        />
      </div>
      <DialogFooter className={showDelete && editPayload ? 'justify-between' : ''}>
        {showDelete && editPayload && onDelete && (
          <Confirmation
            cancelButton={{
              name: 'Cancel',
              onClick: () => {},
              variant: 'secondary',
            }}
            confirmButton={{
              name: 'Delete',
              onClick: onDelete,
              variant: 'destructive',
            }}
            description="This will permanently delete the project and all its contents. This action cannot be undone."
            title="Delete Project"
          >
            <Button size="sm" variant="destructive">
              Delete Project
            </Button>
          </Confirmation>
        )}
        <div className="flex gap-2">
          <Button disabled={isLoading} onClick={onCancel} type="button" variant="outline">
            Cancel
          </Button>
          <Button
            disabled={
              isLoading || !formData.name || !formData.key || !(editPayload || formData.workflowId)
            }
            onClick={handleSubmit}
          >
            {isLoading ? 'Saving...' : editPayload ? 'Update' : 'Create'}
          </Button>
        </div>
      </DialogFooter>
    </>
  );
};
