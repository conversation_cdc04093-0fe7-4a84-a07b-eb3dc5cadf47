import { Alert, AlertDescription } from '@repo/ui/components/alert';
import { Badge } from '@repo/ui/components/badge';
import { Button } from '@repo/ui/components/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@repo/ui/components/dialog';
import { Input } from '@repo/ui/components/input';
import { Label } from '@repo/ui/components/label';
import { Switch } from '@repo/ui/components/switch';
import { toast } from '@repo/ui/components/use-toast';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import {
  AlertTriangle,
  CheckCircle,
  Clock,
  Loader2,
  MousePointer,
  MoveRight,
  Play,
  Save,
  Workflow,
} from 'lucide-react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

// API hooks
import { StatusStatusType } from '../../services/hooks.schemas';
import { useGetApiV1Statuses } from '../../services/statuses';
import {
  getGetApiV1WorkflowStatusesQueryKey,
  useDeleteApiV1WorkflowStatusesId,
  useGetApiV1WorkflowStatuses,
  usePatchApiV1WorkflowStatusesId,
  usePostApiV1WorkflowStatuses,
} from '../../services/workflow-statuses';
import {
  getGetApiV1WorkflowTransitionsQueryKey,
  useDeleteApiV1WorkflowTransitionsId,
  useGetApiV1WorkflowTransitions,
  usePatchApiV1WorkflowTransitionsId,
  usePostApiV1WorkflowTransitions,
} from '../../services/workflow-transitions';
import {
  getGetApiV1WorkflowsQueryKey,
  useGetApiV1WorkflowsId,
  usePatchApiV1WorkflowsId,
  usePostApiV1Workflows,
} from '../../services/workflows';
import { useRootStore } from '../../store/store';

interface WorkflowEditorProps {
  workflowId?: string;
  mode?: 'create' | 'edit';
}

// Types
interface Status {
  id: string;
  name: string;
  status_type: StatusStatusType;
  color?: string;
  position?: { x: number; y: number };
  workflowStatusId?: string;
  isInitial?: boolean;
  isFinal?: boolean;
}

interface Transition {
  id: string;
  from: string | null;
  to: string;
  name: string;
  description?: string;
  isInitial?: boolean;
  isGlobal?: boolean;
  buttonText?: string;
  confirmationMessage?: string;
}

const JiraWorkflowEditor = ({ workflowId, mode = 'create' }: WorkflowEditorProps) => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const currentOrganizationId = useRootStore((state) => state.currentOrganizationId);
  // Status types (system-level)
  const statusTypes = [
    { id: StatusStatusType.todo, name: 'To Do', color: '#94a3b8', icon: Clock },
    {
      id: StatusStatusType.in_progress,
      name: 'In Progress',
      color: '#3b82f6',
      icon: Play,
    },
    {
      id: StatusStatusType.done,
      name: 'Done',
      color: '#10b981',
      icon: CheckCircle,
    },
  ];

  // Fetch existing workflow data if editing
  const { data: existingWorkflow, isLoading: isLoadingWorkflow } = useGetApiV1WorkflowsId(
    workflowId || '',
    {
      query: {
        enabled: mode === 'edit' && !!workflowId,
      },
    },
  );

  // Fetch workflow statuses if editing
  const { data: workflowStatusesData } = useGetApiV1WorkflowStatuses(
    {
      filters: workflowId
        ? JSON.stringify({
            workflowId: {
              $eq: workflowId,
            },
          })
        : undefined,
      limit: 10_000,
    },
    {
      query: {
        enabled: mode === 'edit' && !!workflowId,
      },
    },
  );

  // Fetch workflow transitions if editing
  const { data: workflowTransitionsData } = useGetApiV1WorkflowTransitions(
    {
      filters: workflowId
        ? JSON.stringify({
            workflowId: {
              $eq: workflowId,
            },
          })
        : undefined,
      limit: 10_000,
    },
    {
      query: {
        enabled: mode === 'edit' && !!workflowId,
      },
    },
  );

  // Fetch available canvasStatuses from API
  const { data: canvasStatusesData, isLoading: isLoadingStatuses } = useGetApiV1Statuses({
    limit: 100,
  });

  const availableStatuses = useMemo(() => {
    if (!canvasStatusesData?.data) {
      return [];
    }
    return canvasStatusesData.data.map((status) => ({
      id: status.id,
      name: status.name,
      status_type: status.statusType,
      color: status.color || undefined,
    }));
  }, [canvasStatusesData]);

  // API mutations
  const createWorkflowMutation = usePostApiV1Workflows();
  const updateWorkflowMutation = usePatchApiV1WorkflowsId();
  const createWorkflowStatusMutation = usePostApiV1WorkflowStatuses();
  const updateWorkflowStatusMutation = usePatchApiV1WorkflowStatusesId();
  const deleteWorkflowStatusMutation = useDeleteApiV1WorkflowStatusesId();
  const createWorkflowTransitionMutation = usePostApiV1WorkflowTransitions();
  const updateWorkflowTransitionMutation = usePatchApiV1WorkflowTransitionsId();
  const deleteWorkflowTransitionMutation = useDeleteApiV1WorkflowTransitionsId();

  // Workflow state
  const [workflow, setWorkflow] = useState({
    id: workflowId || null,
    name: 'New Workflow',
    description: 'Create your workflow by dragging canvasStatuses to the canvas',
    is_active: false,
  });

  // Canvas state - tracks canvasStatuses placed on canvas with their positions
  const [canvasStatuses, setCanvasStatuses] = useState<Status[]>([]);
  const [transitions, setTransitions] = useState<Transition[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<Status | null>(null);
  const [selectedTransition, setSelectedTransition] = useState<Transition | null>(null);
  const [editingTransition, setEditingTransition] = useState<Transition | null>(null);
  const [draggedStatus, setDraggedStatus] = useState<string | null>(null);
  const [draggedFromPalette, setDraggedFromPalette] = useState<Status | null>(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [isCreatingTransition, setIsCreatingTransition] = useState(false);
  const [transitionStart, setTransitionStart] = useState<string | null>(null);
  const [errors, setErrors] = useState<string[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const canvasRef = useRef<HTMLDivElement>(null);

  // Maps to track database IDs
  const [statusToWorkflowStatusMap, setStatusToWorkflowStatusMap] = useState<Map<string, string>>(
    new Map(),
  );
  const [transitionIdMap, setTransitionIdMap] = useState<Map<string, string>>(new Map());

  // Initialize workflow data when editing
  useEffect(() => {
    if (mode === 'edit' && existingWorkflow?.data) {
      setWorkflow({
        id: existingWorkflow.data.id,
        name: existingWorkflow.data.name,
        description: existingWorkflow.data.description || '',
        is_active: existingWorkflow.data.isActive,
      });
    }
  }, [existingWorkflow, mode]);

  // Initialize canvasStatuses and transitions when editing
  useEffect(() => {
    if (mode === 'edit' && workflowStatusesData?.data && canvasStatusesData?.data) {
      const statusMap = new Map(canvasStatusesData.data.map((s) => [s.id, s]));
      const workflowStatusMap = new Map<string, string>();

      const loadedStatuses = workflowStatusesData.data
        .map((ws) => {
          const status = statusMap.get(ws.statusId);
          if (!status) {
            return null;
          }

          workflowStatusMap.set(status.id, ws.id);

          return {
            id: status.id,
            workflowStatusId: ws.id,
            name: status.name,
            status_type: status.statusType,
            position: { x: ws.positionX, y: ws.positionY },
            isInitial: ws.isInitial,
            isFinal: ws.isFinal,
          };
        })
        .filter((status): status is NonNullable<typeof status> => status !== null);

      setCanvasStatuses(loadedStatuses);
      setStatusToWorkflowStatusMap(workflowStatusMap);
    }
  }, [workflowStatusesData, canvasStatusesData, mode]);

  // Initialize transitions when editing
  useEffect(() => {
    if (mode === 'edit' && workflowTransitionsData?.data) {
      const transitionMap = new Map<string, string>();

      const loadedTransitions = workflowTransitionsData.data
        .map((wt) => {
          transitionMap.set(wt.id, wt.id);

          // Find the status IDs from workflow status IDs
          const fromStatus = canvasStatuses.find((s) => s.workflowStatusId === wt.fromStatusId);
          const toStatus = canvasStatuses.find((s) => s.workflowStatusId === wt.toStatusId);

          if (!toStatus) {
            return null;
          }

          return {
            id: wt.id,
            from: fromStatus?.id || null,
            to: toStatus.id,
            name: wt.name,
            description: wt.description || undefined,
            isInitial: wt.isInitial || undefined,
            isGlobal: wt.isGlobal || undefined,
            buttonText: wt.buttonText || undefined,
            confirmationMessage: wt.confirmationMessage || undefined,
          };
        })
        .filter((transition): transition is NonNullable<typeof transition> => transition !== null);

      setTransitions(loadedTransitions);
      setTransitionIdMap(transitionMap);
    }
  }, [workflowTransitionsData, canvasStatuses, mode]);

  const getStatusTypeInfo = (statusType: StatusStatusType) => {
    return statusTypes.find((st) => st.id === statusType) || statusTypes[0];
  };

  // Validation
  const validateWorkflow = useCallback(() => {
    const newErrors = [];

    if (canvasStatuses.length === 0) {
      newErrors.push('Workflow must have at least one status');
    }

    // Validate initial statuses must be TODO type
    const initialStatuses = canvasStatuses.filter((s) => s.isInitial);
    const invalidInitialStatuses = initialStatuses.filter(
      (s) => s.status_type !== StatusStatusType.todo,
    );
    if (invalidInitialStatuses.length > 0) {
      newErrors.push(
        `Initial status must be of type "To Do". Invalid: ${invalidInitialStatuses.map((s) => s.name).join(', ')}`,
      );
    }

    // Validate final statuses must be DONE type
    const finalStatuses = canvasStatuses.filter((s) => s.isFinal);
    const invalidFinalStatuses = finalStatuses.filter(
      (s) => s.status_type !== StatusStatusType.done,
    );
    if (invalidFinalStatuses.length > 0) {
      newErrors.push(
        `Final status must be of type "Done". Invalid: ${invalidFinalStatuses.map((s) => s.name).join(', ')}`,
      );
    }

    // Find statuses that should have incoming transitions
    // Exclude: initial statuses, statuses that have incoming transitions, or statuses that are targets of global transitions
    const canvasStatusesWithoutIncoming = canvasStatuses.filter(
      (status) =>
        !(
          (
            status.isInitial || // Initial statuses don't need incoming transitions
            transitions.some((t) => t.to === status.id) || // No regular incoming transitions
            transitions.some((t) => t.from === null && t.to === status.id)
          ) // No global transitions to this status
        ),
    );

    // Find statuses that should have outgoing transitions
    // Exclude: final statuses (done type or explicitly marked as final)
    const canvasStatusesWithoutOutgoing = canvasStatuses.filter(
      (status) =>
        !status.isFinal && // Not marked as final
        status.status_type !== StatusStatusType.done && // Not a done type status
        !transitions.some((t) => t.from === status.id) && // No outgoing transitions
        !transitions.some((t) => t.from === null), // No global transitions exist (which can be used from any status)
    );

    if (canvasStatusesWithoutIncoming.length > 0) {
      newErrors.push(
        `Status(es) ${canvasStatusesWithoutIncoming.map((s) => s.name).join(', ')} have no incoming transitions`,
      );
    }

    if (canvasStatusesWithoutOutgoing.length > 0) {
      newErrors.push(
        `Status(es) ${canvasStatusesWithoutOutgoing.map((s) => s.name).join(', ')} have no outgoing transitions`,
      );
    }
    setErrors(newErrors);
    return newErrors.length === 0;
  }, [canvasStatuses, transitions]);

  // Re-validate when canvasStatuses or transitions change
  useEffect(() => {
    if (canvasStatuses.length > 0 || transitions.length > 0) {
      validateWorkflow();
    }
  }, [canvasStatuses, transitions, validateWorkflow]);

  // Keep selectedStatus in sync with canvasStatuses
  useEffect(() => {
    if (selectedStatus) {
      const updatedStatus = canvasStatuses.find((s) => s.id === selectedStatus.id);
      if (
        updatedStatus &&
        (updatedStatus.isInitial !== selectedStatus.isInitial ||
          updatedStatus.isFinal !== selectedStatus.isFinal)
      ) {
        setSelectedStatus(updatedStatus);
      }
    }
  }, [canvasStatuses, selectedStatus]);

  // Drag and drop from palette
  const handlePaletteDragStart = (e: React.DragEvent<HTMLDivElement>, statusTemplate: Status) => {
    setDraggedFromPalette(statusTemplate);
    const rect = (e.target as HTMLElement).getBoundingClientRect();
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    });
  };

  // Canvas drop handler
  const handleCanvasDrop = (e: React.DragEvent<HTMLDivElement>) => {
    if (!draggedFromPalette) {
      return;
    }

    const canvas = canvasRef.current;
    if (!canvas) {
      return;
    }

    e.preventDefault();
    const canvasRect = canvas.getBoundingClientRect();
    const x = e.clientX - canvasRect.left - dragOffset.x;
    const y = e.clientY - canvasRect.top - dragOffset.y;

    // Check if status already exists (by ID, not name)
    const existingStatus = canvasStatuses.find((s) => s.id === draggedFromPalette.id);
    if (existingStatus) {
      alert('This status is already in the workflow');
      setDraggedFromPalette(null);
      return;
    }

    const newStatus = {
      id: draggedFromPalette.id, // Use the actual status ID from the database
      name: draggedFromPalette.name,
      status_type: draggedFromPalette.status_type,
      color: draggedFromPalette.color,
      position: {
        x: Math.round(Math.max(0, x)),
        y: Math.round(Math.max(0, y)),
      },
      isInitial:
        canvasStatuses.length === 0 && draggedFromPalette.status_type === StatusStatusType.todo, // First status is initial only if it's TODO type
      isFinal: false,
    };

    setCanvasStatuses((prev) => [...prev, newStatus]);
    setDraggedFromPalette(null);
  };

  // Regular drag and drop for existing canvasStatuses
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>, status: Status) => {
    if ((e.target as HTMLElement).closest('.no-drag')) {
      return;
    }

    const rect = e.currentTarget.getBoundingClientRect();
    setDraggedStatus(status.id);
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    });
  };

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!draggedStatus) {
        return;
      }

      const canvas = canvasRef.current;
      if (!canvas) {
        return;
      }

      const canvasRect = canvas.getBoundingClientRect();
      const newX = e.clientX - canvasRect.left - dragOffset.x;
      const newY = e.clientY - canvasRect.top - dragOffset.y;

      // Optimized update - only update the dragged status
      setCanvasStatuses((prev) => {
        const newStatuses = [...prev];
        const statusIndex = newStatuses.findIndex((s) => s.id === draggedStatus);
        if (statusIndex !== -1) {
          newStatuses[statusIndex] = {
            ...newStatuses[statusIndex],
            position: {
              x: Math.round(Math.max(0, newX)),
              y: Math.round(Math.max(0, newY)),
            },
          };
        }
        return newStatuses;
      });
    },
    [draggedStatus, dragOffset],
  );

  const handleMouseUp = useCallback(() => {
    setDraggedStatus(null);
    setDragOffset({ x: 0, y: 0 });
  }, []);

  // Delete functions - wrapped in useCallback to prevent re-renders
  const deleteStatus = useCallback(
    (statusId: string) => {
      setCanvasStatuses((prev) => prev.filter((s) => s.id !== statusId));
      setTransitions((prev) => prev.filter((t) => t.from !== statusId && t.to !== statusId));
      if (selectedStatus?.id === statusId) {
        setSelectedStatus(null);
      }
    },
    [selectedStatus],
  );

  const deleteTransition = useCallback(
    (transitionId: string) => {
      setTransitions((prev) => prev.filter((t) => t.id !== transitionId));
      if (selectedTransition?.id === transitionId) {
        setSelectedTransition(null);
      }
    },
    [selectedTransition],
  );

  // Memoize the transitions to avoid re-rendering during drag
  const memoizedTransitions = useMemo(() => {
    if (transitions.length === 0) {
      return null;
    }

    // Simple, intelligent approach: group transitions by path similarity
    const transitionGroups = new Map();

    transitions.forEach((transition) => {
      const fromStatus = canvasStatuses.find((s) => s.id === transition.from);
      const toStatus = canvasStatuses.find((s) => s.id === transition.to);
      if (!(fromStatus && toStatus)) {
        return;
      }

      // Create a path key based on the two endpoints (sorted for consistency)
      const pathKey = [transition.from, transition.to].sort().join('-');

      if (!transitionGroups.has(pathKey)) {
        transitionGroups.set(pathKey, []);
      }
      transitionGroups.get(pathKey).push(transition);
    });

    return transitions
      .map((transition) => {
        const fromStatus = canvasStatuses.find((s) => s.id === transition.from);
        const toStatus = canvasStatuses.find((s) => s.id === transition.to);

        if (!(fromStatus && toStatus && fromStatus.position && toStatus.position)) {
          return null;
        }

        // Calculate centers and angle
        const fromCenterX = fromStatus.position.x + 100;
        const fromCenterY = fromStatus.position.y + 45;
        const toCenterX = toStatus.position.x + 100;
        const toCenterY = toStatus.position.y + 45;
        const angle = Math.atan2(toCenterY - fromCenterY, toCenterX - fromCenterX);

        // Smart edge connection points
        let fromX, fromY, toX, toY;

        if (Math.abs(Math.cos(angle)) > 0.7) {
          // Horizontal
          fromX = Math.cos(angle) > 0 ? fromStatus.position?.x + 200 : fromStatus.position?.x;
          fromY = fromCenterY;
          toX = Math.cos(angle) > 0 ? toStatus.position?.x : toStatus.position?.x + 200;
          toY = toCenterY;
        } else {
          // Vertical
          fromX = fromCenterX;
          fromY = Math.sin(angle) > 0 ? fromStatus.position?.y + 90 : fromStatus.position?.y;
          toX = toCenterX;
          toY = Math.sin(angle) > 0 ? toStatus.position?.y : toStatus.position?.y + 90;
        }

        // Check for bidirectional and get group info
        const pathKey = [transition.from, transition.to].sort().join('-');
        const group = transitionGroups.get(pathKey) || [];
        const isBidirectional = group.length === 2;

        // For bidirectional: one goes above, one below (simple!)
        let curveOffset = 0;
        let labelVerticalOffset = 0;

        if (isBidirectional) {
          const isFirstInGroup = group[0].id === transition.id;
          curveOffset = isFirstInGroup ? -30 : 30; // Curve separation
          labelVerticalOffset = isFirstInGroup ? -25 : 25; // Label vertical separation
        }

        // Apply curve offset
        const perpAngle = angle + Math.PI / 2;
        const offsetX = Math.cos(perpAngle) * curveOffset;
        const offsetY = Math.sin(perpAngle) * curveOffset;

        // Control points for smooth curves
        const distance = Math.sqrt((toX - fromX) ** 2 + (toY - fromY) ** 2);
        const controlDistance = Math.min(distance * 0.3, 60);

        const control1X = fromX + Math.cos(angle) * controlDistance + offsetX;
        const control1Y = fromY + Math.sin(angle) * controlDistance + offsetY;
        const control2X = toX - Math.cos(angle) * controlDistance + offsetX;
        const control2Y = toY - Math.sin(angle) * controlDistance + offsetY;

        const pathData = `M ${fromX} ${fromY} C ${control1X} ${control1Y}, ${control2X} ${control2Y}, ${toX} ${toY}`;

        // Calculate curve midpoint
        const t = 0.5;
        const curveMidX =
          (1 - t) ** 3 * fromX +
          3 * (1 - t) ** 2 * t * control1X +
          3 * (1 - t) * t ** 2 * control2X +
          t ** 3 * toX;
        const curveMidY =
          (1 - t) ** 3 * fromY +
          3 * (1 - t) ** 2 * t * control1Y +
          3 * (1 - t) * t ** 2 * control2Y +
          t ** 3 * toY;

        // Final label position: curve midpoint + vertical offset for bidirectional
        const labelX = curveMidX;
        const labelY = curveMidY + labelVerticalOffset;

        const isSelected = selectedTransition?.id === transition.id;
        const textWidth = Math.max(transition.name.length * 6 + 40, 110);
        const textHeight = 28;

        return (
          <g key={transition.id}>
            <defs>
              <marker
                id={`arrow-${transition.id}`}
                markerHeight="6"
                markerUnits="strokeWidth"
                markerWidth="8"
                orient="auto"
                refX="7"
                refY="3"
              >
                <path d="M0,0 L0,6 L8,3 z" fill={isSelected ? '#3b82f6' : '#6b7280'} />
              </marker>
            </defs>

            {/* Wider invisible path for easier clicking */}
            <path
              className="cursor-pointer"
              d={pathData}
              fill="none"
              onClick={(e) => {
                e.stopPropagation();
                setSelectedTransition(transition);
              }}
              stroke="transparent"
              strokeWidth="16"
            />

            {/* Main path */}
            <path
              className="transition-all duration-200 hover:stroke-accent"
              d={pathData}
              fill="none"
              markerEnd={`url(#arrow-${transition.id})`}
              onClick={(e) => {
                e.stopPropagation();
                setSelectedTransition(transition);
              }}
              stroke={isSelected ? '#3b82f6' : '#6b7280'}
              strokeWidth={isSelected ? '2.5' : '2'}
              style={{
                cursor: 'pointer',
                filter: isSelected ? 'drop-shadow(0 2px 4px rgba(59, 130, 246, 0.3))' : 'none',
              }}
            />

            {/* Simple connector line for labels moved away from curve */}
            {Math.abs(labelVerticalOffset) > 15 && (
              <line
                className="transition-colors duration-200"
                stroke={isSelected ? '#3b82f6' : '#d1d5db'}
                strokeDasharray="3,3"
                strokeWidth="1"
                x1={curveMidX}
                x2={labelX}
                y1={curveMidY}
                y2={labelY}
              />
            )}

            {/* Label */}
            <g>
              <rect
                className="transition-colors duration-200 hover:stroke-accent/40"
                fill="white"
                height={textHeight}
                onClick={(e) => {
                  e.stopPropagation();
                  setSelectedTransition(transition);
                }}
                rx="14"
                stroke={isSelected ? '#3b82f6' : '#d1d5db'}
                strokeWidth="1.5"
                style={{
                  filter: 'drop-shadow(0 2px 6px rgba(0, 0, 0, 0.1))',
                  cursor: 'pointer',
                }}
                width={textWidth}
                x={labelX - textWidth / 2}
                y={labelY - textHeight / 2}
              />

              <text
                className="pointer-events-none select-none font-medium text-xs"
                dominantBaseline="middle"
                style={{
                  fill: '#374151',
                  fontSize: '10px',
                }}
                textAnchor="middle"
                x={labelX}
                y={labelY + 1}
              >
                {transition.name}
              </text>

              {/* Edit gear icon */}
              <circle
                className="cursor-pointer transition-colors duration-200 hover:fill-accent/10"
                cx={labelX + textWidth / 2 - 18}
                cy={labelY}
                fill={isSelected ? '#3b82f6' : '#f8f9fa'}
                onClick={(e) => {
                  e.stopPropagation();
                  setEditingTransition(transition);
                }}
                r="9"
                stroke={isSelected ? '#3b82f6' : '#d1d5db'}
                strokeWidth="1"
              />
              <text
                className="pointer-events-none select-none"
                dominantBaseline="middle"
                style={{
                  fontSize: '11px',
                  fill: isSelected ? 'white' : '#6b7280',
                }}
                textAnchor="middle"
                x={labelX + textWidth / 2 - 18}
                y={labelY + 1}
              >
                ⚙
              </text>

              {/* Delete X when selected */}
              {isSelected && (
                <>
                  <circle
                    className="cursor-pointer transition-colors hover:fill-destructive"
                    cx={labelX - textWidth / 2 + 18}
                    cy={labelY}
                    fill="#ef4444"
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteTransition(transition.id);
                    }}
                    r="9"
                  />
                  <text
                    className="pointer-events-none select-none"
                    dominantBaseline="middle"
                    style={{ fontSize: '11px', fill: 'white' }}
                    textAnchor="middle"
                    x={labelX - textWidth / 2 + 18}
                    y={labelY + 1}
                  >
                    ×
                  </text>
                </>
              )}
            </g>
          </g>
        );
      })
      .filter(Boolean);
  }, [transitions, canvasStatuses, selectedTransition, deleteTransition]);

  // Throttle the mouse move for better performance
  interface ThrottledFunction {
    (e: MouseEvent): void;
    lastUpdate?: number;
  }

  const throttledMouseMove = useCallback<ThrottledFunction>(
    (e) => {
      if (!draggedStatus) {
        return;
      }

      // Throttle to ~60fps
      const now = Date.now();
      if (now - (throttledMouseMove.lastUpdate || 0) < 16) {
        return;
      }
      throttledMouseMove.lastUpdate = now;

      handleMouseMove(e);
    },
    [draggedStatus, handleMouseMove],
  );

  useEffect(() => {
    if (draggedStatus) {
      document.addEventListener('mousemove', throttledMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', throttledMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [draggedStatus, throttledMouseMove, handleMouseUp]);

  // Transition creation
  const startTransitionCreation = (fromStatusId: string) => {
    setIsCreatingTransition(true);
    setTransitionStart(fromStatusId);
  };

  const completeTransitionCreation = (toStatusId: string) => {
    if (transitionStart && transitionStart !== toStatusId) {
      const existingTransition = transitions.find(
        (t) => t.from === transitionStart && t.to === toStatusId,
      );

      if (existingTransition) {
        alert('Transition already exists from this status to the target status');
      } else {
        const fromStatus = canvasStatuses.find((s) => s.id === transitionStart);
        const toStatus = canvasStatuses.find((s) => s.id === toStatusId);
        const newTransition = {
          id: `trans-${Date.now()}`,
          from: transitionStart,
          to: toStatusId,
          name: `${fromStatus?.name} → ${toStatus?.name}`,
        };
        setTransitions((prev) => {
          const updated = [...prev, newTransition];
          return updated;
        });
      }
    }
    setIsCreatingTransition(false);
    setTransitionStart(null);
  };

  // Save workflow function
  const saveWorkflow = async () => {
    if (isSaving) {
      return; // Prevent multiple concurrent saves
    }

    try {
      setIsSaving(true);

      // Validate organization ID
      if (!currentOrganizationId) {
        toast({
          title: 'No Organization Selected',
          description: 'Please select an organization before creating a workflow.',
          variant: 'destructive',
        });
        setIsSaving(false);
        return;
      }

      // Validate before saving
      if (!validateWorkflow()) {
        toast({
          title: 'Validation Failed',
          description: 'Please fix the validation errors before saving.',
          variant: 'destructive',
        });
        return;
      }

      let savedWorkflowId = workflow.id;

      // Step 1: Create or update the workflow
      if (mode === 'create') {
        const result = await createWorkflowMutation.mutateAsync({
          data: {
            name: workflow.name,
            description: workflow.description || null,
            organizationId: currentOrganizationId,
            isActive: workflow.is_active,
            initialStatusId: canvasStatuses.find((s) => s.isInitial)?.id || null,
          },
        });
        savedWorkflowId = result.data.id;
        setWorkflow((prev) => ({ ...prev, id: savedWorkflowId }));
      } else {
        await updateWorkflowMutation.mutateAsync({
          id: savedWorkflowId!,
          data: {
            name: workflow.name,
            description: workflow.description || null,
            isActive: workflow.is_active,
            initialStatusId: canvasStatuses.find((s) => s.isInitial)?.id || null,
          },
        });
      }

      // Step 2: Save workflow statuses
      for (const status of canvasStatuses) {
        const existingWorkflowStatusId =
          status.workflowStatusId || statusToWorkflowStatusMap.get(status.id);

        if (existingWorkflowStatusId) {
          // Update existing workflow status
          await updateWorkflowStatusMutation.mutateAsync({
            id: existingWorkflowStatusId,
            data: {
              positionX: Math.round(status.position?.x ?? 0),
              positionY: Math.round(status.position?.y ?? 0),
              isInitial: status.isInitial,
              isFinal: status.isFinal,
            },
          });
          // Ensure the mapping is updated
          if (!status.workflowStatusId) {
            status.workflowStatusId = existingWorkflowStatusId;
          }
        } else {
          // Create new workflow status only if it doesn't exist
          const result = await createWorkflowStatusMutation.mutateAsync({
            data: {
              workflowId: savedWorkflowId!,
              statusId: status.id,
              positionX: Math.round(status.position?.x ?? 0),
              positionY: Math.round(status.position?.y ?? 0),
              isInitial: status.isInitial,
              isFinal: status.isFinal,
              isActive: true,
            },
          });
          // Update the mapping
          status.workflowStatusId = result.data.id;
          statusToWorkflowStatusMap.set(status.id, result.data.id);
        }
      }

      // Step 3: Delete removed workflow statuses
      if (mode === 'edit' && workflowStatusesData?.data) {
        const currentStatusIds = new Set(canvasStatuses.map((s) => s.id));
        for (const ws of workflowStatusesData.data) {
          const statusId = canvasStatusesData?.data.find((s) => s.id === ws.statusId)?.id;
          if (statusId && !currentStatusIds.has(statusId)) {
            await deleteWorkflowStatusMutation.mutateAsync({ id: ws.id });
          }
        }
      }

      // Step 4: Save workflow transitions
      for (const transition of transitions) {
        const fromWorkflowStatusId = canvasStatuses.find(
          (s) => s.id === transition.from,
        )?.workflowStatusId;
        const toWorkflowStatusId = canvasStatuses.find(
          (s) => s.id === transition.to,
        )?.workflowStatusId;

        if (!toWorkflowStatusId) {
          continue;
        }

        if (transition.id && transitionIdMap.has(transition.id)) {
          // Update existing transition
          await updateWorkflowTransitionMutation.mutateAsync({
            id: transition.id,
            data: {
              name: transition.name,
              description: transition.description || null,
              fromStatusId: fromWorkflowStatusId || null,
              toStatusId: toWorkflowStatusId,
              isInitial: transition.isInitial,
              isGlobal: transition.isGlobal,
              buttonText: transition.buttonText || null,
              confirmationMessage: transition.confirmationMessage || null,
            },
          });
        } else {
          // Create new transition
          const result = await createWorkflowTransitionMutation.mutateAsync({
            data: {
              workflowId: savedWorkflowId!,
              name: transition.name,
              description: transition.description || null,
              fromStatusId: fromWorkflowStatusId || null,
              toStatusId: toWorkflowStatusId,
              isInitial: transition.isInitial,
              isGlobal: transition.isGlobal,
              buttonText: transition.buttonText || null,
              confirmationMessage: transition.confirmationMessage || null,
              isActive: true,
            },
          });
          transition.id = result.data.id;
          transitionIdMap.set(result.data.id, result.data.id);
        }
      }

      // Step 5: Delete removed transitions
      if (mode === 'edit' && workflowTransitionsData?.data) {
        const currentTransitionIds = new Set(transitions.filter((t) => t.id).map((t) => t.id));
        for (const wt of workflowTransitionsData.data) {
          if (!currentTransitionIds.has(wt.id)) {
            await deleteWorkflowTransitionMutation.mutateAsync({ id: wt.id });
          }
        }
      }

      // Invalidate queries to refresh data
      await queryClient.invalidateQueries({ queryKey: getGetApiV1WorkflowsQueryKey() });
      await queryClient.invalidateQueries({
        queryKey: getGetApiV1WorkflowStatusesQueryKey(),
      });
      await queryClient.invalidateQueries({
        queryKey: getGetApiV1WorkflowTransitionsQueryKey(),
      });

      toast({
        title: 'Success',
        description: 'Workflow saved successfully!',
      });

      // If creating, navigate to edit mode
      if (mode === 'create' && savedWorkflowId) {
        // Update the local mode to prevent re-saving
        navigate({
          to: '/settings/work-flow/$workflowId/design',
          params: { workflowId: savedWorkflowId },
          replace: true,
        });
      }
    } catch (_error) {
      toast({
        title: 'Error',
        description: 'Failed to save workflow. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  interface TransitionEditDialogProps {
    transition: Transition;
    onClose: () => void;
    onSave: (updatedTransition: Partial<Transition>) => void;
  }

  const TransitionEditDialog = ({ transition, onClose, onSave }: TransitionEditDialogProps) => {
    const [formData, setFormData] = useState({
      name: transition?.name || '',
    });

    return (
      <Dialog onOpenChange={onClose} open={true}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Transition</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="transition-name">Transition Name</Label>
              <Input
                className="mt-1"
                id="transition-name"
                onChange={(e) => setFormData({ name: e.target.value })}
                placeholder="Enter transition name"
                value={formData.name}
              />
            </div>
            <div className="flex justify-end space-x-2 pt-4">
              <Button onClick={onClose} variant="outline">
                Cancel
              </Button>
              <Button
                disabled={!formData.name.trim()}
                onClick={() => {
                  if (formData.name.trim()) {
                    onSave(formData);
                    onClose();
                  } else {
                    toast({
                      title: 'Error',
                      description: 'Transition name is required',
                      variant: 'destructive',
                    });
                  }
                }}
              >
                Save Transition
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  // Show loading state while data is being fetched
  if (mode === 'edit' && (isLoadingWorkflow || isLoadingStatuses)) {
    return (
      <div className="flex h-full items-center justify-center bg-muted">
        <div className="rounded-lg border border-border bg-background p-8 text-center shadow-sm">
          <Loader2 className="mx-auto mb-4 h-10 w-10 animate-spin text-primary" />
          <p className="font-medium text-muted-foreground">Loading workflow...</p>
          <p className="mt-1 text-muted-foreground text-sm">
            Please wait while we fetch your workflow data
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col overflow-hidden">
      {/* Clean Header without modal elements */}
      <div className="border-b bg-background px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Workflow className="h-6 w-6 text-primary" />
            <div>
              <Input
                className="border-none bg-transparent px-0 font-semibold text-foreground text-xl focus:outline-none focus:ring-0"
                onChange={(e) => setWorkflow((prev) => ({ ...prev, name: e.target.value }))}
                placeholder="Workflow Name"
                value={workflow.name}
              />
              <Input
                className="mt-1 w-[50vw] resize-none border-none bg-transparent px-0 text-muted-foreground text-sm focus:outline-none focus:ring-0"
                onChange={(e) =>
                  setWorkflow((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                placeholder="Workflow Description"
                value={workflow.description}
              />
            </div>
          </div>
          <div className="flex items-center justify-end space-x-2">
            <div className="flex items-center justify-end space-x-2">
              <Label className="text-muted-foreground text-sm" htmlFor="workflow-active">
                Active
              </Label>
              <Switch
                checked={workflow.is_active}
                id="workflow-active"
                onCheckedChange={(checked) =>
                  setWorkflow((prev) => ({ ...prev, is_active: checked }))
                }
              />
            </div>
            <Button
              onClick={() => {
                navigate({
                  to: '/settings/work-flow',
                  params: {},
                  replace: true,
                });
              }}
              variant="outline"
            >
              Cancel
            </Button>
            <Button disabled={isSaving} onClick={() => saveWorkflow()}>
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Status Palette */}
        <div className="flex w-80 flex-col overflow-hidden border-r bg-background">
          <div className="flex-shrink-0 border-b p-4">
            <div className="mb-2 font-semibold text-foreground text-lg">Available Statuses</div>
            <p className="mb-4 text-muted-foreground text-sm">
              Drag statuses to the canvas to build your workflow
            </p>

            {isLoadingStatuses ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
              </div>
            ) : (
              <div className="space-y-2">
                {availableStatuses.map((statusTemplate) => {
                  const typeInfo = getStatusTypeInfo(statusTemplate.status_type);
                  const StatusIcon = typeInfo.icon;
                  const isUsed = canvasStatuses.some((s) => s.id === statusTemplate.id);

                  return (
                    <div
                      className={`cursor-move rounded-lg border-2 border-dashed p-3 transition-all ${
                        isUsed
                          ? 'cursor-not-allowed border-border bg-muted opacity-50'
                          : 'border-border hover:border-accent/40 hover:bg-primary/5'
                      }`}
                      draggable={!isUsed}
                      key={statusTemplate.id}
                      onDragStart={(e) => !isUsed && handlePaletteDragStart(e, statusTemplate)}
                    >
                      <div className="flex items-center space-x-3">
                        <div
                          className="h-4 w-4 flex-shrink-0 rounded-full"
                          style={{
                            backgroundColor: statusTemplate.color || typeInfo.color,
                          }}
                        />
                        <StatusIcon className="h-4 w-4 flex-shrink-0 text-muted-foreground" />
                        <div className="min-w-0 flex-1">
                          <p className="truncate font-medium text-sm">{statusTemplate.name}</p>
                          <p className="text-muted-foreground text-xs capitalize">
                            {statusTemplate.status_type.replace('_', ' ')}
                          </p>
                        </div>
                        {isUsed && (
                          <Badge className="flex-shrink-0 text-xs" variant="secondary">
                            Used
                          </Badge>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* Scrollable content area */}
          <div className="flex-1 overflow-y-auto">
            {/* Workflow Stats */}
            {/* <div className="p-4 border-b">
            <div className="text-base font-medium text-foreground mb-3">Workflow Stats</div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Statuses:</span>
                <span className="font-medium">{canvasStatuses.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Transitions:</span>
                <span className="font-medium">{transitions.length}</span>
              </div>
              {transitions.length > 0 && (
                <div className="mt-3">
                  <p className="text-xs text-muted-foreground mb-2">Recent Transitions:</p>
                  <div className="space-y-1">
                    {transitions.slice(-3).map(t => (
                      <div key={t.id} className="text-xs text-muted-foreground truncate">
                        {t.name}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div> */}

            {/* Validation Errors */}
            {errors.length > 0 && (
              <div className="p-4">
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-1">
                      {errors.map((error, index) => (
                        <p className="text-sm" key={index}>
                          {error}
                        </p>
                      ))}
                      {errors.some((e) => e.includes('no incoming transitions')) && (
                        <p className="mt-2 border-t pt-2 text-muted-foreground text-xs">
                          <strong>Tip:</strong> Select the status and toggle "Initial Status" ON to
                          mark it as a starting point that doesn't need incoming transitions.
                        </p>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              </div>
            )}

            {/* Selected Status Configuration */}
            {selectedStatus && (
              <div className="border-primary/20 border-t bg-primary/5 p-4">
                <div className="mb-3 flex items-center font-medium text-base text-foreground">
                  <div className="mr-2 h-2 w-2 rounded-full bg-primary" />
                  Status Configuration
                </div>
                <div className="space-y-3">
                  <div>
                    <p className="mb-1 font-medium text-muted-foreground text-sm">
                      {selectedStatus.name}
                    </p>
                    <p className="text-muted-foreground text-xs capitalize">
                      {selectedStatus.status_type.replace('_', ' ')}
                    </p>
                    <p className="mt-1 text-muted-foreground text-xs">
                      Status ID: {selectedStatus.id}
                    </p>
                    {selectedStatus.isInitial && (
                      <p className="mt-1 font-medium text-success text-xs">
                        ✓ This is an initial status
                      </p>
                    )}
                    {selectedStatus.isFinal && (
                      <p className="mt-1 font-medium text-primary text-xs">
                        ✓ This is a final status
                      </p>
                    )}
                  </div>

                  <div className="space-y-3 rounded-md border border-border bg-background p-3">
                    <div className="flex items-center justify-between">
                      <Label className="font-medium text-sm" htmlFor="initial-status">
                        Initial Status
                      </Label>
                      <Switch
                        checked={selectedStatus.isInitial}
                        disabled={selectedStatus.status_type !== StatusStatusType.todo}
                        id="initial-status"
                        onCheckedChange={(checked) => {
                          if (checked && selectedStatus.status_type !== StatusStatusType.todo) {
                            toast({
                              title: 'Invalid Selection',
                              description: "Only 'To Do' type statuses can be marked as initial.",
                              variant: 'destructive',
                            });
                            return;
                          }
                          setCanvasStatuses((prev) => {
                            const updated = prev.map((s) =>
                              s.id === selectedStatus.id
                                ? { ...s, isInitial: checked }
                                : checked
                                  ? { ...s, isInitial: false } // Only one initial status
                                  : s,
                            );
                            // Also update the selectedStatus to match
                            const updatedSelectedStatus = updated.find(
                              (s) => s.id === selectedStatus.id,
                            );
                            if (updatedSelectedStatus) {
                              setSelectedStatus(updatedSelectedStatus);
                            }
                            return updated;
                          });
                        }}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label className="font-medium text-sm" htmlFor="final-status">
                        Final Status
                      </Label>
                      <Switch
                        checked={selectedStatus.isFinal}
                        disabled={selectedStatus.status_type !== StatusStatusType.done}
                        id="final-status"
                        onCheckedChange={(checked) => {
                          if (checked && selectedStatus.status_type !== StatusStatusType.done) {
                            toast({
                              title: 'Invalid Selection',
                              description: "Only 'Done' type statuses can be marked as final.",
                              variant: 'destructive',
                            });
                            return;
                          }
                          setCanvasStatuses((prev) =>
                            prev.map((s) =>
                              s.id === selectedStatus.id ? { ...s, isFinal: checked } : s,
                            ),
                          );
                          setSelectedStatus({
                            ...selectedStatus,
                            isFinal: checked,
                          });
                        }}
                      />
                    </div>
                  </div>

                  <p className="mt-2 text-muted-foreground text-xs">
                    <span className="block">
                      • Initial statuses must be of type "To Do" and don't require incoming
                      transitions.
                    </span>
                    <span className="block">
                      • Final statuses must be of type "Done" and don't require outgoing
                      transitions.
                    </span>
                  </p>

                  {/* Debug button to test state */}
                  <Button
                    className="mt-2 w-full"
                    onClick={() => {
                      alert(
                        `Selected: ${selectedStatus.name}\nInitial: ${selectedStatus.isInitial}\nFinal: ${selectedStatus.isFinal}\nType: ${selectedStatus.status_type}`,
                      );
                    }}
                    size="sm"
                    variant="outline"
                  >
                    Debug Status State
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Canvas Area */}
        <div className="relative flex-1 overflow-auto bg-gradient-to-br from-gray-50 to-gray-100">
          <div
            className="relative h-full w-full"
            onClick={() => {
              setSelectedStatus(null);
              setSelectedTransition(null);
              if (isCreatingTransition) {
                setIsCreatingTransition(false);
                setTransitionStart(null);
              }
            }}
            onDragOver={(e) => e.preventDefault()}
            onDrop={handleCanvasDrop}
            ref={canvasRef}
            style={{ minWidth: '1200px', minHeight: '800px' }}
          >
            {/* Grid background - more subtle */}
            <div
              className="absolute inset-0 opacity-20"
              style={{
                backgroundImage: `
                  linear-gradient(to right, #d1d5db 1px, transparent 1px),
                  linear-gradient(to bottom, #d1d5db 1px, transparent 1px)
                `,
                backgroundSize: '40px 40px',
              }}
            />

            {/* Instructions */}
            {canvasStatuses.length === 0 && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="max-w-lg rounded-lg border border-border bg-background p-8 text-center text-muted-foreground shadow-sm">
                  <MousePointer className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                  <div className="mb-4 font-semibold text-muted-foreground text-xl">
                    Start Building Your Workflow
                  </div>
                  <div className="space-y-3 text-muted-foreground text-sm">
                    <p className="flex items-center justify-center">
                      <span className="mr-2 flex h-6 w-6 items-center justify-center rounded-full bg-accent/10 font-semibold text-primary text-xs">
                        1
                      </span>
                      Drag statuses from the palette to the canvas
                    </p>
                    <p className="flex items-center justify-center">
                      <span className="mr-2 flex h-6 w-6 items-center justify-center rounded-full bg-accent/10 font-semibold text-primary text-xs">
                        2
                      </span>
                      Click the link icon to create transitions between statuses
                    </p>
                    <p className="flex items-center justify-center">
                      <span className="mr-2 flex h-6 w-6 items-center justify-center rounded-full bg-accent/10 font-semibold text-primary text-xs">
                        3
                      </span>
                      Configure transition properties as needed
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Transition creation guide */}
            {isCreatingTransition && (
              <div className="absolute top-4 left-4 rounded-lg border border-primary/20 bg-primary/5 p-3">
                <div className="flex items-center space-x-2">
                  <MoveRight className="h-4 w-4 text-primary" />
                  <p className="text-primary text-sm">
                    Click on a destination status to create the transition
                  </p>
                </div>
              </div>
            )}

            {/* SVG for transitions */}
            <svg
              className="pointer-events-auto absolute inset-0 h-full w-full"
              style={{ zIndex: 5 }}
            >
              <title>Workflow Transitions</title>
              {memoizedTransitions}
            </svg>

            {/* Status nodes */}
            {canvasStatuses.map((status) => {
              const typeInfo = getStatusTypeInfo(status.status_type);
              const StatusIcon = typeInfo.icon;

              return (
                <div
                  className={`absolute cursor-move select-none rounded-lg border-2 bg-background shadow-sm transition-all ${
                    selectedStatus?.id === status.id
                      ? 'border-accent shadow-lg ring-2 ring-blue-200'
                      : 'border-border hover:border-border hover:shadow-md'
                  } ${draggedStatus === status.id ? 'z-50 shadow-xl' : 'z-10'}`}
                  key={status.id}
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedStatus(status);
                    if (isCreatingTransition) {
                      completeTransitionCreation(status.id);
                    }
                  }}
                  onMouseDown={(e) => handleMouseDown(e, status)}
                  style={{
                    left: status.position?.x ?? 0,
                    top: status.position?.y ?? 0,
                    width: '200px',
                    height: '80px',
                  }}
                >
                  <div className="flex h-full items-center p-3">
                    <div
                      className="mr-3 h-4 w-4 flex-shrink-0 rounded-full"
                      style={{
                        backgroundColor: status.color || typeInfo.color,
                      }}
                    />
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center space-x-2">
                        <StatusIcon className="h-4 w-4 flex-shrink-0 text-muted-foreground" />
                        <h4 className="truncate font-medium text-sm">{status.name}</h4>
                      </div>
                      <div className="mt-1 flex items-center space-x-2">
                        <p className="text-muted-foreground text-xs capitalize">
                          {status.status_type.replace('_', ' ')}
                        </p>
                        {status.isInitial && (
                          <Badge className="px-1 py-0 text-xs" variant="secondary">
                            Initial
                          </Badge>
                        )}
                        {status.isFinal && (
                          <Badge className="px-1 py-0 text-xs" variant="secondary">
                            Final
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div className="no-drag flex flex-col space-y-1">
                      <button
                        className="flex h-6 w-6 items-center justify-center rounded p-0 transition-colors hover:bg-muted"
                        onClick={(e) => {
                          e.stopPropagation();
                          startTransitionCreation(status.id);
                        }}
                        title="Create transition"
                        type="button"
                      >
                        <svg
                          className="h-3 w-3 text-muted-foreground"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <title>Create transition</title>
                          <path
                            d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                          />
                        </svg>
                      </button>
                      <button
                        className="flex h-6 w-6 items-center justify-center rounded p-0 text-destructive transition-colors hover:bg-destructive/5 hover:text-destructive"
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteStatus(status.id);
                        }}
                        title="Delete status"
                        type="button"
                      >
                        <svg
                          className="h-3 w-3"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <title>Delete status</title>
                          <path
                            d="M6 18L18 6M6 6l12 12"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Dialogs */}
      {editingTransition && (
        <TransitionEditDialog
          onClose={() => setEditingTransition(null)}
          onSave={(updatedTransition: Partial<Transition>) => {
            setTransitions((prev) =>
              prev.map((t) => (t.id === editingTransition.id ? { ...t, ...updatedTransition } : t)),
            );
          }}
          transition={editingTransition}
        />
      )}
    </div>
  );
};

export default JiraWorkflowEditor;
