import { Badge } from '@repo/ui/components/badge';
import { But<PERSON> } from '@repo/ui/components/button';
import { <PERSON>, CardContent, <PERSON><PERSON>ooter, CardHeader } from '@repo/ui/components/card';
import { Input } from '@repo/ui/components/input';
import { Label } from '@repo/ui/components/label';
import { RadioGroup, RadioGroupItem } from '@repo/ui/components/radio-group';
import {
  ArrowLeft,
  ArrowRight,
  Briefcase,
  Eye,
  Globe2,
  Hash,
  Link2,
  Lock,
  Palette,
  SkipForward,
} from 'lucide-react';
import type React from 'react';
import { ColorPicker } from './color-picker';
import { StepHeader } from './step-header';

interface WorkspaceStepProps {
  formData: {
    name: string;
    slug: string;
    description: string;
    color: string;
    visibility: string;
    type: string;
  };
  organizationSlug: string;
  onInputChange: (field: string, value: string) => void;
  onNext: () => void;
  onBack: () => void;
  onSkip: () => void;
  isLoading?: boolean;
}

const workspaceTypes = [
  {
    value: 'engineering',
    label: 'Engineering',
    description: 'Code, deploy, iterate',
  },
  {
    value: 'design',
    label: 'Design',
    description: 'Create, prototype, ship',
  },
  {
    value: 'marketing',
    label: 'Marketing',
    description: 'Plan, execute, measure',
  },
  {
    value: 'operations',
    label: 'Operations',
    description: 'Optimize, scale, grow',
  },
  { value: 'other', label: 'Other', description: 'Custom workspace' },
];

const colors = [
  { value: '#6366f1', label: 'Indigo', class: 'bg-[#6366f1]' },
  { value: '#8b5cf6', label: 'Purple', class: 'bg-[#8b5cf6]' },
  { value: '#ec4899', label: 'Pink', class: 'bg-[#ec4899]' },
  { value: '#f43f5e', label: 'Rose', class: 'bg-[#f43f5e]' },
  { value: '#3b82f6', label: 'Blue', class: 'bg-[#3b82f6]' },
  { value: '#06b6d4', label: 'Cyan', class: 'bg-[#06b6d4]' },
  { value: '#10b981', label: 'Emerald', class: 'bg-[#10b981]' },
  { value: '#f59e0b', label: 'Amber', class: 'bg-[#f59e0b]' },
];

export const WorkspaceStep: React.FC<WorkspaceStepProps> = ({
  formData,
  organizationSlug,
  onInputChange,
  onNext,
  onBack,
  onSkip,
  isLoading = false,
}) => {
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  };

  const handleNameChange = (value: string) => {
    onInputChange('name', value);
    onInputChange('slug', generateSlug(value));
  };

  return (
    <Card className="mx-auto w-full max-w-2xl">
      <CardHeader className="space-y-4">
        <StepHeader
          description="Workspaces are where your team collaborates"
          icon={Briefcase}
          iconColor="bg-gradient-to-br from-primary to-primary/80"
          title="Create a workspace"
        />
      </CardHeader>

      <CardContent className="max-h-[60vh] space-y-6 overflow-y-auto">
        {/* Workspace Type */}
        <div className="space-y-3">
          <Label>What kind of team is this for?</Label>
          <div className="grid gap-3">
            {workspaceTypes.map((type) => (
              <button
                className={`rounded-xl border-2 p-4 text-left transition-all ${
                  formData.type === type.value
                    ? 'border-primary bg-secondary dark:bg-secondary/30'
                    : 'border-border hover:border-muted-foreground dark:border-border'
                }`}
                key={type.value}
                onClick={() => onInputChange('type', type.value)}
                type="button"
              >
                <div className="mb-1 font-medium">{type.label}</div>
                <div className="text-muted-foreground text-sm">{type.description}</div>
              </button>
            ))}
          </div>
        </div>

        {/* Workspace Name */}
        <div className="space-y-3">
          <Label className="flex items-center gap-2" htmlFor="ws-name">
            Workspace name
            <Badge className="text-xs" variant="secondary">
              Required
            </Badge>
          </Label>
          <div className="relative">
            <Hash className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
            <Input
              className="pl-10"
              id="ws-name"
              onChange={(e) => handleNameChange(e.target.value)}
              placeholder={formData.type === 'engineering' ? 'Engineering Team' : 'My Workspace'}
              value={formData.name}
            />
          </div>
          {formData.name && (
            <p className="slide-in-from-bottom-2 flex animate-in items-center gap-1 text-muted-foreground text-sm">
              <Link2 className="h-3 w-3" />
              {organizationSlug}/{formData.slug}
            </p>
          )}
        </div>

        {/* Color Selection */}
        <div className="space-y-3">
          <Label className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            Choose a color
          </Label>
          <ColorPicker
            colors={colors}
            onChange={(value) => onInputChange('color', value)}
            value={formData.color}
          />
        </div>

        {/* Visibility */}
        <div className="space-y-3">
          <Label className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Who can see this workspace?
          </Label>
          <RadioGroup
            onValueChange={(value) => onInputChange('visibility', value)}
            value={formData.visibility}
          >
            <div className="grid gap-3">
              <label
                className={`flex cursor-pointer items-start gap-3 rounded-xl border-2 p-4 transition-all ${
                  formData.visibility === 'private'
                    ? 'border-primary bg-secondary dark:bg-secondary/30'
                    : 'border-border hover:border-muted-foreground dark:border-border'
                }`}
                htmlFor="ws-private"
              >
                <RadioGroupItem className="mt-1" id="ws-private" value="private" />
                <div className="flex-1">
                  <div className="mb-1 flex items-center gap-2 font-medium">
                    <Lock className="h-4 w-4" />
                    Private
                  </div>
                  <div className="text-muted-foreground text-sm">
                    Only people you invite can access
                  </div>
                </div>
              </label>

              <label
                className={`flex cursor-pointer items-start gap-3 rounded-xl border-2 p-4 transition-all ${
                  formData.visibility === 'public'
                    ? 'border-primary bg-secondary dark:bg-secondary/30'
                    : 'border-border hover:border-muted-foreground dark:border-border'
                }`}
                htmlFor="ws-public"
              >
                <RadioGroupItem className="mt-1" id="ws-public" value="public" />
                <div className="flex-1">
                  <div className="mb-1 flex items-center gap-2 font-medium">
                    <Globe2 className="h-4 w-4" />
                    Public
                  </div>
                  <div className="text-muted-foreground text-sm">
                    Anyone in your organization can view
                  </div>
                </div>
              </label>
            </div>
          </RadioGroup>
        </div>
      </CardContent>

      <CardFooter className="flex justify-between border-t pt-6">
        <Button onClick={onBack} variant="ghost">
          <ArrowLeft className="mr-1 h-4 w-4" />
          Back
        </Button>
        <div className="flex gap-2">
          <Button onClick={onSkip} variant="ghost">
            <SkipForward className="mr-1 h-4 w-4" />
            Skip
          </Button>
          <Button
            className="group"
            disabled={!(formData.name && formData.type) || isLoading}
            onClick={onNext}
          >
            Continue
            <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};
