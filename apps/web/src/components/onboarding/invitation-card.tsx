import { Avatar, AvatarFallback } from '@repo/ui/components/avatar';
import { Badge } from '@repo/ui/components/badge';
import { Button } from '@repo/ui/components/button';
import { Card, CardContent } from '@repo/ui/components/card';
import { Briefcase, Building2, Clock, FolderOpen, UserCheck } from 'lucide-react';
import type React from 'react';

interface InvitationCardProps {
  invitation: {
    id: string;
    type: string;
    entityName: string;
    organizationName?: string;
    inviterName: string;
    inviterEmail: string;
    role: string;
    createdAt: string;
  };
  onAccept: () => void;
  onDecline: () => void;
  isProcessing: boolean;
}

export const InvitationCard: React.FC<InvitationCardProps> = ({
  invitation,
  onAccept,
  onDecline,
  isProcessing,
}) => {
  const getIcon = () => {
    switch (invitation.type) {
      case 'organization':
        return Building2;
      case 'workspace':
        return Briefcase;
      default:
        return FolderOpen;
    }
  };

  const Icon = getIcon();
  const getIconColor = () => {
    switch (invitation.type) {
      case 'organization':
        return 'bg-primary';
      case 'workspace':
        return 'bg-chart-3';
      default:
        return 'bg-chart-2';
    }
  };

  return (
    <Card className="transition-shadow hover:shadow-md">
      <CardContent className="">
        <div className="flex items-start gap-4">
          <div
            className={`h-12 w-12 ${getIconColor()} flex flex-shrink-0 items-center justify-center rounded-xl`}
          >
            <Icon className="h-6 w-6 text-primary-foreground" />
          </div>

          <div className="flex-1 space-y-3">
            <div>
              <div className="mb-1 flex items-center gap-2">
                <h3 className="font-medium text-base">{invitation.entityName}</h3>
                <Badge className="text-xs" variant="secondary">
                  {invitation.type}
                </Badge>
              </div>
              {invitation.organizationName && (
                <p className="text-muted-foreground text-sm">
                  Part of {invitation.organizationName}
                </p>
              )}
            </div>

            <div className="flex items-center gap-4 text-muted-foreground text-sm">
              <div className="flex items-center gap-2">
                <Avatar className="h-6 w-6">
                  <AvatarFallback className="text-xs">
                    {invitation.inviterName
                      .split(' ')
                      .map((n) => n[0])
                      .join('')}
                  </AvatarFallback>
                </Avatar>
                <span>Invited by {invitation.inviterName}</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>2 days ago</span>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Badge className="text-xs" variant="outline">
                <UserCheck className="mr-1 h-3 w-3" />
                {invitation.role}
              </Badge>
            </div>
          </div>

          <div className="flex gap-2">
            <Button disabled={isProcessing} onClick={onDecline} size="sm" variant="outline">
              Decline
            </Button>
            <Button disabled={isProcessing} onClick={onAccept} size="sm">
              Accept
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
