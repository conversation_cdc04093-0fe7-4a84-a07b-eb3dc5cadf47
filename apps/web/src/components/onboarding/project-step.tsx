import { Badge } from '@repo/ui/components/badge';
import { <PERSON><PERSON> } from '@repo/ui/components/button';
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@repo/ui/components/card';
import { Input } from '@repo/ui/components/input';
import { Label } from '@repo/ui/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import {
  ArrowLeft,
  ArrowRight,
  AtSign,
  Calendar,
  Flag,
  FolderKanban,
  FolderOpen,
  Globe2,
  HelpCircle,
  Lock,
  SkipForward,
  Sparkles,
  Target,
  TrendingUp,
  Zap,
} from 'lucide-react';
import type React from 'react';
import { ColorPicker } from './color-picker';
import { StepHeader } from './step-header';

interface ProjectStepProps {
  formData: {
    name: string;
    key: string;
    description: string;
    color: string;
    visibility: string;
    status: string;
    startDate: string;
    targetDate: string;
    methodology: string;
  };
  onInputChange: (field: string, value: string) => void;
  onProjectNameChange: (value: string) => void;
  onNext: () => void;
  onBack: () => void;
  onSkip: () => void;
  isLoading?: boolean;
}

const methodologies = [
  { value: 'agile', label: 'Agile', icon: Zap },
  { value: 'waterfall', label: 'Waterfall', icon: TrendingUp },
  { value: 'kanban', label: 'Kanban', icon: FolderKanban },
  { value: 'custom', label: 'Custom', icon: Sparkles },
];

const colors = [
  { value: '#6366f1', label: 'Indigo', class: 'bg-[#6366f1]' },
  { value: '#8b5cf6', label: 'Purple', class: 'bg-[#8b5cf6]' },
  { value: '#ec4899', label: 'Pink', class: 'bg-[#ec4899]' },
  { value: '#f43f5e', label: 'Rose', class: 'bg-[#f43f5e]' },
  { value: '#3b82f6', label: 'Blue', class: 'bg-[#3b82f6]' },
  { value: '#06b6d4', label: 'Cyan', class: 'bg-[#06b6d4]' },
  { value: '#10b981', label: 'Emerald', class: 'bg-[#10b981]' },
  { value: '#f59e0b', label: 'Amber', class: 'bg-[#f59e0b]' },
];

export const ProjectStep: React.FC<ProjectStepProps> = ({
  formData,
  onInputChange,
  onProjectNameChange,
  onNext,
  onBack,
  onSkip,
  isLoading = false,
}) => {
  return (
    <Card className="mx-auto w-full max-w-2xl">
      <CardHeader className="space-y-4">
        <StepHeader
          description="Projects help you organize and track your work"
          icon={FolderOpen}
          iconColor="bg-gradient-to-br from-chart-2 to-chart-2/80"
          title="Create your first project"
        />
      </CardHeader>

      <CardContent className="max-h-[60vh] space-y-6 overflow-y-auto">
        {/* Project Name and Key */}
        <div className="space-y-4">
          <div className="grid grid-cols-3 gap-4">
            <div className="col-span-2 space-y-2">
              <Label className="flex items-center gap-2" htmlFor="proj-name">
                Project name
                <Badge className="text-xs" variant="secondary">
                  Required
                </Badge>
              </Label>
              <Input
                className=""
                id="proj-name"
                onChange={(e) => onProjectNameChange(e.target.value)}
                placeholder="Website Redesign"
                value={formData.name}
              />
            </div>
            <div className="space-y-2">
              <Label className="flex items-center gap-1" htmlFor="proj-key">
                Key
                <HelpCircle className="h-3 w-3 text-muted-foreground" />
              </Label>
              <div className="relative">
                <AtSign className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                <Input
                  className="pl-10 uppercase"
                  id="proj-key"
                  maxLength={4}
                  onChange={(e) => onInputChange('key', e.target.value.toUpperCase())}
                  placeholder="WEB"
                  value={formData.key}
                />
              </div>
            </div>
          </div>
          <p className="text-muted-foreground text-sm">
            Issues will be labeled like {formData.key || 'KEY'}-1, {formData.key || 'KEY'}-2
          </p>
        </div>

        {/* Methodology */}
        <div className="space-y-3">
          <Label>How does your team work?</Label>
          <div className="grid grid-cols-2 gap-3">
            {methodologies.map((method) => {
              const Icon = method.icon;
              return (
                <button
                  className={`rounded-xl border-2 p-4 transition-all ${
                    formData.methodology === method.value
                      ? 'border-[--tt-color-green-base] bg-[--tt-color-green-dec-4] dark:bg-[--tt-color-green-inc-3]/30'
                      : 'border-[--tt-gray-light-200] hover:border-[--tt-gray-light-300] dark:border-[--tt-gray-dark-700]'
                  }`}
                  key={method.value}
                  onClick={() => onInputChange('methodology', method.value)}
                  type="button"
                >
                  <Icon
                    className={`mb-2 h-5 w-5 ${
                      formData.methodology === method.value
                        ? 'text-[--tt-color-green-base] dark:text-[--tt-color-green-dec-1]'
                        : 'text-muted-foreground'
                    }`}
                  />
                  <div className="font-medium text-sm">{method.label}</div>
                </button>
              );
            })}
          </div>
        </div>

        {/* Timeline */}
        <div className="space-y-3">
          <Label className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Project timeline
            <Badge className="text-xs" variant="outline">
              Optional
            </Badge>
          </Label>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm" htmlFor="proj-start">
                Start date
              </Label>
              <div className="relative">
                <Flag className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                <Input
                  className="pl-10"
                  id="proj-start"
                  onChange={(e) => onInputChange('startDate', e.target.value)}
                  type="date"
                  value={formData.startDate}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label className="text-sm" htmlFor="proj-target">
                Target date
              </Label>
              <div className="relative">
                <Target className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                <Input
                  className="pl-10"
                  id="proj-target"
                  onChange={(e) => onInputChange('targetDate', e.target.value)}
                  type="date"
                  value={formData.targetDate}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Color and Visibility */}
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-3">
            <Label>Project color</Label>
            <ColorPicker
              colors={colors.slice(0, 4)}
              onChange={(value) => onInputChange('color', value)}
              value={formData.color}
            />
          </div>

          <div className="space-y-3">
            <Label>Visibility</Label>
            <Select
              onValueChange={(value) => onInputChange('visibility', value)}
              value={formData.visibility}
            >
              <SelectTrigger className="h-10">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="private">
                  <div className="flex items-center gap-2">
                    <Lock className="h-4 w-4" />
                    Private
                  </div>
                </SelectItem>
                <SelectItem value="public">
                  <div className="flex items-center gap-2">
                    <Globe2 className="h-4 w-4" />
                    Public
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>

      <CardFooter className="flex justify-between border-t pt-6">
        <Button onClick={onBack} variant="ghost">
          <ArrowLeft className="mr-1 h-4 w-4" />
          Back
        </Button>
        <div className="flex gap-2">
          <Button onClick={onSkip} variant="ghost">
            <SkipForward className="mr-1 h-4 w-4" />
            Skip
          </Button>
          <Button
            className="group"
            disabled={!(formData.name && formData.methodology) || isLoading}
            onClick={onNext}
          >
            Continue
            <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};
