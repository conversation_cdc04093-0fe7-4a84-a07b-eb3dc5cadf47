'use client';

import { createContext, type ReactNode, useContext, useState } from 'react';

export type InvitationType = 'organization' | 'workspace' | 'project';

export interface Invitation {
  id: string;
  scopeType: InvitationType;
  scopeId: string;
  scopeName: string;
  inviterName: string;
  inviterEmail: string;
  role: string;
  createdAt: string;
}

export interface Organization {
  name: string;
  slug: string;
  description?: string;
  website?: string;
  logoUrl?: string;
  size?: string;
}

export interface Workspace {
  name: string;
  slug: string;
  description?: string;
  visibility: 'public' | 'private';
  type?: string;
}

export interface Project {
  name: string;
  slug: string;
  description?: string;
  visibility: 'public' | 'private';
  methodology?: string;
}

export interface TeamMember {
  email: string;
  role: string;
}

interface OnboardingContextType {
  step: number;
  setStep: (step: number) => void;
  invitations: Invitation[];
  acceptedInvitations: Invitation[];
  acceptInvitation: (invitation: Invitation) => void;
  declineInvitation: (invitation: Invitation) => void;
  organization: Organization | null;
  setOrganization: (org: Organization) => void;
  workspace: Workspace | null;
  setWorkspace: (workspace: Workspace) => void;
  project: Project | null;
  setProject: (project: Project) => void;
  teamMembers: TeamMember[];
  addTeamMember: (member: TeamMember) => void;
  removeTeamMember: (email: string) => void;
  hasAcceptedAnyInvitation: boolean;
  isCreatingOwn: boolean;
  setIsCreatingOwn: (value: boolean) => void;
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

export function OnboardingProvider({
  children,
  initialInvitations = [],
}: {
  children: ReactNode;
  initialInvitations?: Invitation[];
}) {
  const [step, setStep] = useState(0);
  const [invitations, setInvitations] = useState<Invitation[]>(initialInvitations);
  const [acceptedInvitations, setAcceptedInvitations] = useState<Invitation[]>([]);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [workspace, setWorkspace] = useState<Workspace | null>(null);
  const [project, setProject] = useState<Project | null>(null);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [isCreatingOwn, setIsCreatingOwn] = useState(false);

  const acceptInvitation = (invitation: Invitation) => {
    setAcceptedInvitations([...acceptedInvitations, invitation]);
    setInvitations(invitations.filter((inv) => inv.id !== invitation.id));
  };

  const declineInvitation = (invitation: Invitation) => {
    setInvitations(invitations.filter((inv) => inv.id !== invitation.id));
  };

  const addTeamMember = (member: TeamMember) => {
    setTeamMembers([...teamMembers, member]);
  };

  const removeTeamMember = (email: string) => {
    setTeamMembers(teamMembers.filter((member) => member.email !== email));
  };

  const hasAcceptedAnyInvitation = acceptedInvitations.length > 0;

  return (
    <OnboardingContext.Provider
      value={{
        step,
        setStep,
        invitations,
        acceptedInvitations,
        acceptInvitation,
        declineInvitation,
        organization,
        setOrganization,
        workspace,
        setWorkspace,
        project,
        setProject,
        teamMembers,
        addTeamMember,
        removeTeamMember,
        hasAcceptedAnyInvitation,
        isCreatingOwn,
        setIsCreatingOwn,
      }}
    >
      {children}
    </OnboardingContext.Provider>
  );
}

export function useOnboarding() {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
}
