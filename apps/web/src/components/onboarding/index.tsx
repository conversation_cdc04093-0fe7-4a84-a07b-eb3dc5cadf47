import { Alert, AlertDescription } from '@repo/ui/components/alert';
import { Button } from '@repo/ui/components/button';
import { Progress } from '@repo/ui/components/progress';
import { useNavigate } from '@tanstack/react-router';
import { ArrowRight, CheckCircle2, Layers, Plus } from 'lucide-react';
import { useEffect, useState } from 'react';
import {
  useGetApiV1Invitations,
  usePatchApiV1InvitationsId,
  usePostApiV1Invitations,
} from '@/web/services/invitations';
import { usePostApiV1Organizations } from '@/web/services/organizations';
import { usePostApiV1Projects } from '@/web/services/projects';
import { useGetApiV1RoleAssignments } from '@/web/services/role-assignments';
import { useGetApiV1Roles } from '@/web/services/roles';
import { usePostApiV1Workspaces } from '@/web/services/workspaces';
import { useRootStore } from '@/web/store/store';

// Import onboarding components
import { InvitationCard } from './invitation-card';
import { InviteTeamStep } from './invite-team-step';
import { OrganizationStep } from './organization-step';
import { ProjectStep } from './project-step';
import { SuccessStep } from './success-step';
import { WorkspaceStep } from './workspace-step';

export const Onboarding = () => {
  const navigate = useNavigate();
  const currentUser = useRootStore((state) => state.currentUser);
  const setCurrentOrganizationId = useRootStore((state) => state.setCurrentOrganizationId);
  const setCurrentWorkspaceId = useRootStore((state) => state.setCurrentWorkspaceId);
  const setCurrentProjectId = useRootStore((state) => state.setCurrentProjectId);

  // API hooks
  const { data: invitationsData, refetch: refetchInvitations } = useGetApiV1Invitations({
    filters: JSON.stringify({ status: { $eq: 'pending' } }),
  });
  const { data: rolesData } = useGetApiV1Roles({
    filters: JSON.stringify({ level: { $eq: 'organization' } }),
  });

  // Get role assignments for current user
  const { data: roleAssignmentsData } = useGetApiV1RoleAssignments({
    filters: currentUser?.id ? JSON.stringify({ userId: { $eq: currentUser.id } }) : undefined,
  });

  const createOrganization = usePostApiV1Organizations();
  const createWorkspace = usePostApiV1Workspaces();
  const createProject = usePostApiV1Projects();
  const createInvitation = usePostApiV1Invitations();
  const updateInvitation = usePatchApiV1InvitationsId();

  // State management
  const [mode, setMode] = useState('initial');
  const [currentStep, setCurrentStep] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [uploadedLogo, setUploadedLogo] = useState<string | null>(null);
  const [processingInvite, setProcessingInvite] = useState<string | null>(null);
  const [createdIds, setCreatedIds] = useState({
    organizationId: null as string | null,
    workspaceId: null as string | null,
    projectId: null as string | null,
  });

  const [formData, setFormData] = useState({
    organization: {
      name: '',
      slug: '',
      description: '',
      website: '',
      logoUrl: '',
      billingEmail: '',
      billingAddress: '',
      size: '',
    },
    workspace: {
      name: '',
      slug: '',
      description: '',
      color: 'var(--primary)',
      visibility: 'private',
      type: '',
    },
    project: {
      name: '',
      key: '',
      description: '',
      color: 'var(--chart-2)',
      visibility: 'private',
      status: 'planning',
      startDate: '',
      targetDate: '',
      methodology: '',
    },
  });

  const [inviteMembers, setInviteMembers] = useState([{ email: '', roleId: '' }]);

  // Store role assignments data
  const setRoleAssignments = useRootStore((state) => state.setRoleAssignments);
  const setOrganizations = useRootStore((state) => state.setOrganizations);
  const setWorkspaces = useRootStore((state) => state.setWorkspaces);
  const setProjects = useRootStore((state) => state.setProjects);

  // Check for existing role assignments and invitations on mount
  useEffect(() => {
    if (roleAssignmentsData?.data && roleAssignmentsData.data.length > 0) {
      // Store role assignments
      setRoleAssignments(roleAssignmentsData.data);

      // Extract and store organizations, workspaces, and projects from role assignments
      const organizations = roleAssignmentsData.data
        .filter((ra) => ra.scopeType === 'organization' && ra.organization)
        .map((ra) => ra.organization!)
        .filter((org, index, self) => self.findIndex((o) => o.id === org.id) === index);

      const workspaces = roleAssignmentsData.data
        .filter((ra) => ra.scopeType === 'workspace' && ra.workspace)
        .map((ra) => ra.workspace!)
        .filter((ws, index, self) => self.findIndex((w) => w.id === ws.id) === index);

      const projects = roleAssignmentsData.data
        .filter((ra) => ra.scopeType === 'project' && ra.project)
        .map((ra) => ra.project!)
        .filter((proj, index, self) => self.findIndex((p) => p.id === proj.id) === index);

      if (organizations.length > 0) {
        setOrganizations(organizations);
      }
      if (workspaces.length > 0) {
        setWorkspaces(workspaces);
      }
      if (projects.length > 0) {
        setProjects(projects);
      }

      // User has role assignments (organizations), redirect to dashboard
      if (organizations.length > 0) {
        navigate({ to: '/dashboard' });
      } else if (invitationsData?.data && invitationsData.data.length > 0) {
        setMode('invitations');
      } else {
        setMode('creation');
      }
    } else if (invitationsData?.data && invitationsData.data.length > 0) {
      setMode('invitations');
    } else {
      setMode('creation');
    }
  }, [
    roleAssignmentsData,
    invitationsData,
    navigate,
    setRoleAssignments,
    setOrganizations,
    setWorkspaces,
    setProjects,
  ]);

  // Helper functions
  const generateProjectKey = (name: string) => {
    const words = name.split(' ');
    if (words.length === 1) {
      return name.substring(0, 3).toUpperCase();
    }
    return words
      .map((word) => word[0])
      .join('')
      .toUpperCase()
      .substring(0, 4);
  };

  const handleInputChange = (
    section: 'organization' | 'workspace' | 'project',
    field: string,
    value: string,
  ) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
  };

  const handleProjectNameChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      project: {
        ...prev.project,
        name: value,
        key: generateProjectKey(value),
      },
    }));
  };

  const handleAcceptInvitation = async (invitationId: string) => {
    setProcessingInvite(invitationId);
    try {
      await updateInvitation.mutateAsync({
        id: invitationId,
        data: { status: 'accepted' },
      });
      await refetchInvitations();

      // Check if there are more invitations
      const remainingInvitations = invitationsData?.data?.filter((inv) => inv.id !== invitationId);
      if (!remainingInvitations || remainingInvitations.length === 0) {
        // No more invitations, redirect to dashboard
        navigate({ to: '/dashboard' });
      }
    } catch (_error) {
    } finally {
      setProcessingInvite(null);
    }
  };

  const handleDeclineInvitation = async (invitationId: string) => {
    setProcessingInvite(invitationId);
    try {
      await updateInvitation.mutateAsync({
        id: invitationId,
        data: { status: 'rejected' },
      });
      await refetchInvitations();
    } catch (_error) {
    } finally {
      setProcessingInvite(null);
    }
  };

  const startCreationFlow = () => {
    setMode('creation');
    setCurrentStep(0);
  };

  const createOrganizationStep = async () => {
    const orgData = await createOrganization.mutateAsync({
      data: {
        name: formData.organization.name,
        slug: formData.organization.slug,
        description: formData.organization.description || undefined,
        website: formData.organization.website || undefined,
        logoUrl: uploadedLogo || undefined,
        createdBy: currentUser?.id || '',
        settings: {
          billingEmail: formData.organization.billingEmail || undefined,
          organizationSize: formData.organization.size,
        },
      },
    });

    interface OrgResponse {
      data?: { id: string };
      id?: string;
    }
    const response = orgData as OrgResponse;
    const organizationId = response.data?.id || response.id || '';
    setCreatedIds((prev) => ({ ...prev, organizationId }));
    setCurrentOrganizationId(organizationId);
    return organizationId;
  };

  const createWorkspaceStep = async (organizationId: string) => {
    const wsData = await createWorkspace.mutateAsync({
      data: {
        organizationId,
        name: formData.workspace.name,
        slug: formData.workspace.slug,
        description: formData.workspace.description || undefined,
        color: formData.workspace.color,
        visibility: formData.workspace.visibility as 'public' | 'private',
        settings: {
          type: formData.workspace.type,
        },
      },
    });

    interface WsResponse {
      data?: { id: string };
      id?: string;
    }
    const response = wsData as WsResponse;
    const workspaceId = response.data?.id || response.id || '';
    setCreatedIds((prev) => ({ ...prev, workspaceId }));
    setCurrentWorkspaceId(workspaceId);
    return workspaceId;
  };

  const createProjectStep = async (workspaceId: string) => {
    const projData = await createProject.mutateAsync({
      data: {
        workspaceId,
        name: formData.project.name,
        key: formData.project.key,
        description: formData.project.description || undefined,
        color: formData.project.color,
        visibility: formData.project.visibility as 'public' | 'private',
        status: formData.project.status as 'planning' | 'active' | 'completed' | 'archived',
        startDate: formData.project.startDate || undefined,
        targetDate: formData.project.targetDate || undefined,
        settings: {
          methodology: formData.project.methodology,
        },
      },
    });

    interface ProjResponse {
      data?: { id: string };
      id?: string;
    }
    const response = projData as ProjResponse;
    const projectId = response.data?.id || response.id || '';
    setCreatedIds((prev) => ({ ...prev, projectId }));
    setCurrentProjectId(projectId);
    return projectId;
  };

  const sendInvitations = async () => {
    const validInvites = inviteMembers.filter((member) => member.email && member.roleId);

    if (validInvites.length === 0) {
      return;
    }

    try {
      await Promise.all(
        validInvites.map((member) =>
          createInvitation.mutateAsync({
            data: {
              email: member.email,
              invitedBy: currentUser?.id || '',
              roleId: member.roleId,
              scopeType: 'organization',
              scopeId: createdIds.organizationId || '',
            },
          }),
        ),
      );
    } catch (_error) {}
  };

  const nextStep = async () => {
    setIsTransitioning(true);

    try {
      // Handle step-specific actions
      if (currentStep === 0) {
        // Create organization
        await createOrganizationStep();
      } else if (currentStep === 1 && createdIds.organizationId) {
        // Create workspace
        await createWorkspaceStep(createdIds.organizationId);
      } else if (currentStep === 2 && createdIds.workspaceId) {
        // Create project
        await createProjectStep(createdIds.workspaceId);
      } else if (currentStep === 3) {
        // Send invitations
        await sendInvitations();
      }

      setCurrentStep((prev) => prev + 1);
    } catch (_error) {
    } finally {
      setIsTransitioning(false);
    }
  };

  const prevStep = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      setCurrentStep((prev) => prev - 1);
      setIsTransitioning(false);
    }, 300);
  };

  const skipStep = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      setCurrentStep((prev) => prev + 1);
      setIsTransitioning(false);
    }, 300);
  };

  const completeOnboarding = () => {
    // Navigate to dashboard
    navigate({ to: '/dashboard' });
  };

  // Team invite management
  const updateInviteMember = (
    index: number,
    field: keyof (typeof inviteMembers)[0],
    value: string,
  ) => {
    const updatedMembers = [...inviteMembers];
    updatedMembers[index] = { ...updatedMembers[index], [field]: value };
    setInviteMembers(updatedMembers);
  };

  const addInviteMember = () => {
    setInviteMembers([...inviteMembers, { email: '', roleId: '' }]);
  };

  const removeInviteMember = (index: number) => {
    setInviteMembers(inviteMembers.filter((_, i) => i !== index));
  };

  // Calculate progress
  const totalSteps = 4;
  const progressPercentage = ((currentStep + 1) / totalSteps) * 100;

  const invitations = invitationsData?.data || [];
  const roles = rolesData?.data || [];

  return (
    <div className="h-full bg-gradient-to-br from-secondary via-background to-secondary dark:from-secondary dark:via-secondary dark:to-muted">
      {/* Progress Bar for Creation Flow */}
      {mode === 'creation' && currentStep < 4 && (
        <div className="border-b bg-card/80 backdrop-blur-sm dark:bg-card/80">
          <Progress className="h-1" value={progressPercentage} />
          <div className="mx-auto flex max-w-4xl items-center justify-between px-4 py-3">
            <div className="flex items-center gap-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-primary to-primary/80">
                <Layers className="h-5 w-5 text-primary-foreground" />
              </div>
              <span className="text-muted-foreground text-sm">
                Step {currentStep + 1} of {totalSteps}
              </span>
            </div>
          </div>
        </div>
      )}

      <div className="flex min-h-screen items-center justify-center p-4 pt-20">
        {/* Invitations View */}
        {mode === 'invitations' && (
          <div className="w-full max-w-4xl">
            <div className="mb-8 text-center">
              <h1 className="mb-4 font-semibold text-2xl">Welcome back! 👋</h1>
              <p className="text-base text-muted-foreground">
                You have {invitations.length} pending invitation
                {invitations.length !== 1 ? 's' : ''}
              </p>
            </div>

            <div className="mb-8 space-y-4">
              {invitations.map((invitation) => (
                <InvitationCard
                  invitation={{
                    id: invitation.id,
                    type: invitation.scopeType,
                    entityName: invitation.organization?.name || 'Organization',
                    inviterName: invitation.invitedBy || 'Someone',
                    inviterEmail: '',
                    role: invitation.role?.name || 'Member',
                    createdAt: invitation.createdAt,
                  }}
                  isProcessing={processingInvite === invitation.id}
                  key={invitation.id}
                  onAccept={() => handleAcceptInvitation(invitation.id)}
                  onDecline={() => handleDeclineInvitation(invitation.id)}
                />
              ))}
            </div>

            {invitations.length === 0 && (
              <Alert className="mb-8">
                <CheckCircle2 className="h-4 w-4" />
                <AlertDescription>
                  All invitations processed! You can now create your own organization or continue to
                  your dashboard.
                </AlertDescription>
              </Alert>
            )}

            <div className="flex flex-col justify-center gap-4 sm:flex-row">
              <Button className="group" onClick={startCreationFlow} size="lg" variant="outline">
                <Plus className="mr-2 h-5 w-5" />
                Create new organization
              </Button>
              {invitations.length === 0 && (
                <Button
                  className="group bg-gradient-to-r from-primary to-primary/80 text-primary-foreground hover:from-primary/80 hover:to-primary/60"
                  onClick={completeOnboarding}
                  size="lg"
                >
                  Continue to dashboard
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Creation Flow */}
        {mode === 'creation' && (
          <div
            className={`w-full transition-all duration-500 ${
              currentStep === 4 ? 'max-w-3xl' : 'max-w-2xl'
            }`}
          >
            <div
              className={`transition-all duration-500 ${
                isTransitioning ? 'translate-y-4 opacity-0' : 'translate-y-0 opacity-100'
              }`}
            >
              {/* Step 0: Organization */}
              {currentStep === 0 && (
                <OrganizationStep
                  formData={formData.organization}
                  isLoading={createOrganization.isPending}
                  onInputChange={(field, value) => handleInputChange('organization', field, value)}
                  onNext={nextStep}
                  setUploadedLogo={setUploadedLogo}
                  uploadedLogo={uploadedLogo}
                />
              )}

              {/* Step 1: Workspace */}
              {currentStep === 1 && (
                <WorkspaceStep
                  formData={formData.workspace}
                  isLoading={createWorkspace.isPending}
                  onBack={prevStep}
                  onInputChange={(field, value) => handleInputChange('workspace', field, value)}
                  onNext={nextStep}
                  onSkip={skipStep}
                  organizationSlug={formData.organization.slug}
                />
              )}

              {/* Step 2: Project */}
              {currentStep === 2 && (
                <ProjectStep
                  formData={formData.project}
                  isLoading={createProject.isPending}
                  onBack={prevStep}
                  onInputChange={(field, value) => handleInputChange('project', field, value)}
                  onNext={nextStep}
                  onProjectNameChange={handleProjectNameChange}
                  onSkip={skipStep}
                />
              )}

              {/* Step 3: Invite Team */}
              {currentStep === 3 && (
                <InviteTeamStep
                  inviteMembers={inviteMembers}
                  isLoading={createInvitation.isPending}
                  onAddMember={addInviteMember}
                  onBack={prevStep}
                  onNext={nextStep}
                  onRemoveMember={removeInviteMember}
                  onSkip={skipStep}
                  onUpdateMember={updateInviteMember}
                  organizationName={formData.organization.name}
                  roles={roles.map((role) => ({
                    id: role.id,
                    name: role.name,
                    description: role.description || undefined,
                  }))}
                />
              )}

              {/* Step 4: Success */}
              {currentStep === 4 && (
                <SuccessStep
                  onComplete={completeOnboarding}
                  organizationData={{
                    name: formData.organization.name,
                    slug: formData.organization.slug,
                  }}
                  projectData={
                    createdIds.projectId
                      ? {
                          name: formData.project.name,
                          key: formData.project.key,
                          methodology: formData.project.methodology,
                          color: formData.project.color,
                        }
                      : undefined
                  }
                  workspaceData={
                    createdIds.workspaceId
                      ? {
                          name: formData.workspace.name,
                          type: formData.workspace.type,
                          color: formData.workspace.color,
                        }
                      : undefined
                  }
                />
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
