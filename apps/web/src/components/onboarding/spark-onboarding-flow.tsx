'use client';

import { Alert, AlertDescription } from '@repo/ui/components/alert';
import { Badge } from '@repo/ui/components/badge';
import { Button } from '@repo/ui/components/button';
import { Progress } from '@repo/ui/components/progress';
import { cn } from '@repo/ui/lib/utils';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import { AnimatePresence, motion } from 'framer-motion';
import {
  ArrowRight,
  Briefcase,
  Building2,
  Check,
  CheckCircle2,
  ChevronRight,
  FolderKanban,
  Plus,
  Sparkles,
  Users,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import {
  getGetApiV1InvitationsQueryKey,
  useGetApiV1Invitations,
  usePatchApiV1InvitationsId,
  usePostApiV1Invitations,
} from '@/web/services/invitations';
import { usePostApiV1Organizations } from '@/web/services/organizations';
import { usePostApiV1Projects } from '@/web/services/projects';
import {
  getGetApiV1RoleAssignmentsQueryKey,
  useGetApiV1RoleAssignments,
} from '@/web/services/role-assignments';
import { useGetApiV1Roles } from '@/web/services/roles';
import { usePostApiV1Workspaces } from '@/web/services/workspaces';
import { useRootStore } from '@/web/store/store';

// Import step components
import { InvitationCard } from './invitation-card';
import CompletionStep from './steps/completion-step';
import OrganizationInviteStep from './steps/organization-invite-step';
import OrganizationStep from './steps/organization-step';
import ProjectInviteStep from './steps/project-invite-step';
import ProjectStep from './steps/project-step';
import WelcomeStep from './steps/welcome-step';
import WorkspaceInviteStep from './steps/workspace-invite-step';
import WorkspaceStep from './steps/workspace-step';

export interface OnboardingData {
  organization: {
    name: string;
    slug: string;
    description: string;
    size: string;
    color: string;
    website: string;
    logoUrl?: string;
  };
  workspace: {
    name: string;
    slug: string;
    description: string;
    type: string;
    color: string;
    visibility: string;
    icon?: string;
  };
  project: {
    name: string;
    slug: string;
    description: string;
    methodology: string;
    color: string;
    visibility: string;
    icon?: string;
  };
  invitations: {
    organization: string[];
    workspace: string[];
    project: string[];
  };
}

const steps = [
  {
    id: 'welcome',
    title: 'Welcome',
    icon: Sparkles,
    description: 'Get started with Spark',
    component: WelcomeStep,
  },
  {
    id: 'organization',
    title: 'Organization',
    icon: Building2,
    description: 'Set up your organization',
    component: OrganizationStep,
  },
  {
    id: 'org-invite',
    title: 'Invite Team',
    icon: Users,
    description: 'Invite organization members',
    component: OrganizationInviteStep,
  },
  {
    id: 'workspace',
    title: 'Workspace',
    icon: Briefcase,
    description: 'Create your workspace',
    component: WorkspaceStep,
  },
  {
    id: 'workspace-invite',
    title: 'Invite Collaborators',
    icon: Users,
    description: 'Invite workspace members',
    component: WorkspaceInviteStep,
  },
  {
    id: 'project',
    title: 'Project',
    icon: FolderKanban,
    description: 'Set up your first project',
    component: ProjectStep,
  },
  {
    id: 'project-invite',
    title: 'Project Team',
    icon: Users,
    description: 'Invite project members',
    component: ProjectInviteStep,
  },
  {
    id: 'completion',
    title: 'Complete',
    icon: Check,
    description: "You're all set!",
    component: CompletionStep,
  },
];

export default function SparkOnboardingFlow() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const currentUser = useRootStore((state) => state.currentUser);
  const setCurrentOrganizationId = useRootStore((state) => state.setCurrentOrganizationId);
  const setCurrentWorkspaceId = useRootStore((state) => state.setCurrentWorkspaceId);
  const setCurrentProjectId = useRootStore((state) => state.setCurrentProjectId);
  const setRoleAssignments = useRootStore((state) => state.setRoleAssignments);
  const setOrganizations = useRootStore((state) => state.setOrganizations);
  const setWorkspaces = useRootStore((state) => state.setWorkspaces);
  const setProjects = useRootStore((state) => state.setProjects);

  // API hooks
  const createOrganization = usePostApiV1Organizations();
  const createWorkspace = usePostApiV1Workspaces();
  const createProject = usePostApiV1Projects();
  const createInvitation = usePostApiV1Invitations();
  const updateInvitation = usePatchApiV1InvitationsId();

  const { data: invitationsData, refetch: refetchInvitations } = useGetApiV1Invitations({
    filters: JSON.stringify({ status: { $eq: 'pending' } }),
  });

  const { data: roleAssignmentsData } = useGetApiV1RoleAssignments({
    filters: currentUser?.id ? JSON.stringify({ userId: { $eq: currentUser.id } }) : undefined,
  });

  const { data: rolesData } = useGetApiV1Roles({
    filters: JSON.stringify({ level: { $eq: 'organization' } }),
  });

  const [mode, setMode] = useState<'initial' | 'invitations' | 'creation'>('initial');
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [skippedSteps, setSkippedSteps] = useState<number[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [processingInvite, setProcessingInvite] = useState<string | null>(null);
  const [createdIds, setCreatedIds] = useState({
    organizationId: null as string | null,
    workspaceId: null as string | null,
    projectId: null as string | null,
  });
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    organization: {
      name: '',
      slug: '',
      description: '',
      size: '',
      color: '#6366f1',
      website: '',
    },
    workspace: {
      name: '',
      slug: '',
      description: '',
      type: '',
      color: '#8b5cf6',
      visibility: 'private',
    },
    project: {
      name: '',
      slug: '',
      description: '',
      methodology: '',
      color: '#ec4899',
      visibility: 'private',
    },
    invitations: {
      organization: [],
      workspace: [],
      project: [],
    },
  });

  // Invalidate role-related queries
  const invalidateRoleQueries = async () => {
    await queryClient.invalidateQueries({
      queryKey: getGetApiV1RoleAssignmentsQueryKey(),
    });
    await queryClient.invalidateQueries({
      queryKey: getGetApiV1InvitationsQueryKey(),
    });
  };

  // Check for existing role assignments and invitations on mount
  useEffect(() => {
    if (roleAssignmentsData?.data && roleAssignmentsData.data.length > 0) {
      // Store role assignments
      setRoleAssignments(roleAssignmentsData.data);

      // Extract and store organizations, workspaces, and projects
      const organizations = roleAssignmentsData.data
        .filter((ra) => ra.scopeType === 'organization' && ra.organization)
        .map((ra) => ra.organization!)
        .filter((org, index, self) => self.findIndex((o) => o.id === org.id) === index);

      const workspaces = roleAssignmentsData.data
        .filter((ra) => ra.scopeType === 'workspace' && ra.workspace)
        .map((ra) => ra.workspace!)
        .filter((ws, index, self) => self.findIndex((w) => w.id === ws.id) === index);

      const projects = roleAssignmentsData.data
        .filter((ra) => ra.scopeType === 'project' && ra.project)
        .map((ra) => ra.project!)
        .filter((proj, index, self) => self.findIndex((p) => p.id === proj.id) === index);

      if (organizations.length > 0) {
        setOrganizations(organizations);
        setCurrentOrganizationId(organizations[0].id);
      }
      if (workspaces.length > 0) {
        setWorkspaces(workspaces);
      }
      if (projects.length > 0) {
        setProjects(projects);
      }

      // User has role assignments, redirect to dashboard
      if (organizations.length > 0) {
        navigate({ to: '/dashboard' });
      } else if (invitationsData?.data && invitationsData.data.length > 0) {
        setMode('invitations');
      } else {
        setMode('creation');
      }
    } else if (invitationsData?.data && invitationsData.data.length > 0) {
      setMode('invitations');
    } else {
      setMode('creation');
    }
  }, [
    roleAssignmentsData,
    invitationsData,
    navigate,
    setRoleAssignments,
    setOrganizations,
    setWorkspaces,
    setProjects,
    setCurrentOrganizationId,
  ]);

  const invitations = invitationsData?.data || [];

  const progress = Math.round(((currentStep + 1) / steps.length) * 100);
  const CurrentStepComponent = steps[currentStep].component;

  const handleNext = async () => {
    if (currentStep < steps.length - 1) {
      setIsLoading(true);
      try {
        const currentStepData = steps[currentStep];
        const wasAlreadyCompleted = completedSteps.includes(currentStep);

        // Handle step-specific API calls only if not already completed
        if (!wasAlreadyCompleted) {
          switch (currentStepData.id) {
            case 'organization':
              // Only create if not already created
              if (!createdIds.organizationId) {
                await createOrganizationStep();
              }
              break;
            case 'org-invite':
              await sendInvitations('organization');
              break;
            case 'workspace':
              // Only create if not already created
              if (createdIds.organizationId && !createdIds.workspaceId) {
                await createWorkspaceStep(createdIds.organizationId);
              }
              break;
            case 'workspace-invite':
              await sendInvitations('workspace');
              break;
            case 'project':
              // Only create if not already created
              if (createdIds.workspaceId && !createdIds.projectId) {
                await createProjectStep(createdIds.workspaceId);
              }
              break;
            case 'project-invite':
              await sendInvitations('project');
              break;
          }
        }

        // Mark as completed if not already
        if (!completedSteps.includes(currentStep)) {
          setCompletedSteps([...completedSteps, currentStep]);
        }

        // Remove from skipped if it was previously skipped
        setSkippedSteps(skippedSteps.filter((step) => step !== currentStep));
        setCurrentStep(currentStep + 1);

        // If it's the completion step, invalidate queries and redirect
        if (steps[currentStep + 1]?.id === 'completion') {
          await invalidateRoleQueries();

          setTimeout(() => {
            navigate({ to: '/' });
          }, 3000);
        }
      } catch (_error) {
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSkip = () => {
    if (currentStep < steps.length - 1) {
      // Add current step to skipped steps
      const newSkippedSteps = [...skippedSteps];
      if (!newSkippedSteps.includes(currentStep)) {
        newSkippedSteps.push(currentStep);
      }
      setSkippedSteps(newSkippedSteps);

      // Move to next step
      setCurrentStep(currentStep + 1);

      // If skipping to completion step, invalidate queries and redirect
      if (steps[currentStep + 1]?.id === 'completion') {
        invalidateRoleQueries().then(() => {
          setTimeout(() => {
            navigate({ to: '/' });
          }, 3000);
        });
      }
    }
  };

  const handleStepClick = (stepIndex: number) => {
    if (stepIndex <= currentStep || completedSteps.includes(stepIndex)) {
      setCurrentStep(stepIndex);
    }
  };

  const updateOnboardingData = (
    section: string,
    data: Partial<OnboardingData[keyof OnboardingData]>,
  ) => {
    setOnboardingData((prev) => ({
      ...prev,
      [section]: { ...prev[section as keyof OnboardingData], ...data },
    }));
  };

  const handleAcceptInvitation = async (invitationId: string) => {
    setProcessingInvite(invitationId);
    try {
      await updateInvitation.mutateAsync({
        id: invitationId,
        data: { status: 'accepted' },
      });

      // Invalidate all role-related queries
      await invalidateRoleQueries();
      await queryClient.invalidateQueries({
        queryKey: getGetApiV1InvitationsQueryKey(),
      });

      // Check if there are more invitations
      const remainingInvitations = invitations.filter((inv) => inv.id !== invitationId);
      if (remainingInvitations.length === 0) {
        // No more invitations, redirect to dashboard
        navigate({ to: '/' });
      }
    } catch (_error) {
    } finally {
      setProcessingInvite(null);
    }
  };

  const handleDeclineInvitation = async (invitationId: string) => {
    setProcessingInvite(invitationId);
    try {
      await updateInvitation.mutateAsync({
        id: invitationId,
        data: { status: 'rejected' },
      });
      await refetchInvitations();
    } catch (_error) {
    } finally {
      setProcessingInvite(null);
    }
  };

  const startCreationFlow = () => {
    setMode('creation');
    setCurrentStep(0);
  };

  // API functions
  const createOrganizationStep = async () => {
    const orgData = await createOrganization.mutateAsync({
      data: {
        name: onboardingData.organization.name,
        slug: onboardingData.organization.slug,
        description: onboardingData.organization.description || undefined,
        website: onboardingData.organization.website || undefined,
        logoUrl: onboardingData.organization.logoUrl || undefined,
        createdBy: currentUser?.id || '',
        settings: {
          organizationSize: onboardingData.organization.size,
        },
      },
    });

    interface OrgResponse {
      data?: { id: string };
      id?: string;
    }
    const response = orgData as OrgResponse;
    const organizationId = response.data?.id || response.id || '';
    setCreatedIds((prev) => ({ ...prev, organizationId }));
    setCurrentOrganizationId(organizationId);

    // Invalidate role assignments query
    await queryClient.invalidateQueries({
      queryKey: getGetApiV1RoleAssignmentsQueryKey(),
    });

    return organizationId;
  };

  const createWorkspaceStep = async (organizationId: string) => {
    const wsData = await createWorkspace.mutateAsync({
      data: {
        organizationId,
        name: onboardingData.workspace.name,
        slug: onboardingData.workspace.slug,
        description: onboardingData.workspace.description || undefined,
        color: onboardingData.workspace.color || '#8b5cf6',
        icon: onboardingData.workspace.icon || undefined,
        visibility: onboardingData.workspace.visibility as 'public' | 'private',
        settings: {
          type: onboardingData.workspace.type,
        },
        createdBy: currentUser?.id || '',
      },
    });

    interface WsResponse {
      data?: { id: string };
      id?: string;
    }
    const response = wsData as WsResponse;
    const workspaceId = response.data?.id || response.id || '';
    setCreatedIds((prev) => ({ ...prev, workspaceId }));
    setCurrentWorkspaceId(workspaceId);

    // Invalidate role assignments query
    await queryClient.invalidateQueries({
      queryKey: getGetApiV1RoleAssignmentsQueryKey(),
    });

    return workspaceId;
  };

  const createProjectStep = async (workspaceId: string) => {
    // Generate a project key from the name
    const generateProjectKey = (name: string) => {
      const words = name.split(' ');
      if (words.length === 1) {
        return name.substring(0, 3).toUpperCase();
      }
      return words
        .map((word) => word[0])
        .join('')
        .toUpperCase()
        .substring(0, 4);
    };

    const projData = await createProject.mutateAsync({
      data: {
        workspaceId,
        name: onboardingData.project.name,
        key: generateProjectKey(onboardingData.project.name),
        description: onboardingData.project.description || undefined,
        color: onboardingData.project.color || '#6366f1',
        visibility: onboardingData.project.visibility as 'public' | 'private',
        status: 'planning' as const,
        settings: {
          methodology: onboardingData.project.methodology,
        },
        icon: onboardingData.project.icon || undefined,
        createdBy: currentUser?.id || '',
      },
    });

    interface ProjResponse {
      data?: { id: string };
      id?: string;
    }
    const response = projData as ProjResponse;
    const projectId = response.data?.id || response.id || '';
    setCreatedIds((prev) => ({ ...prev, projectId }));
    setCurrentProjectId(projectId);

    // Invalidate role assignments query
    await queryClient.invalidateQueries({
      queryKey: getGetApiV1RoleAssignmentsQueryKey(),
    });

    return projectId;
  };

  const sendInvitations = async (scopeType: 'organization' | 'workspace' | 'project') => {
    const invitations = onboardingData.invitations[scopeType];
    const validInvites = invitations.filter((email) => email);

    if (validInvites.length === 0) {
      return;
    }

    const roles = rolesData?.data || [];
    const memberRole = roles.find((role) => role.name.toLowerCase() === 'member');

    if (!memberRole) {
      return;
    }

    const scopeId =
      scopeType === 'organization'
        ? createdIds.organizationId
        : scopeType === 'workspace'
          ? createdIds.workspaceId
          : createdIds.projectId;

    if (!scopeId) {
      return;
    }

    try {
      await Promise.all(
        validInvites.map((email) =>
          createInvitation.mutateAsync({
            data: {
              email,
              invitedBy: currentUser?.id || '',
              roleId: memberRole.id,
              scopeType,
              scopeId,
            },
          }),
        ),
      );
    } catch (_error) {}
  };

  // Show invitations view if in invitations mode
  if (mode === 'invitations') {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-secondary via-background to-secondary p-4 dark:from-secondary dark:via-background dark:to-secondary">
        <div className="w-full max-w-4xl">
          <div className="mb-8 text-center">
            <h1 className="mb-4 font-semibold text-lg">Welcome back! 👋</h1>
            <p className="text-base text-muted-foreground">
              You have {invitations.length} pending invitation
              {invitations.length !== 1 ? 's' : ''}
            </p>
          </div>

          <div className="mb-8 flex w-full flex-col space-y-4">
            {invitations.map((invitation) => (
              <InvitationCard
                invitation={{
                  id: invitation.id,
                  type: invitation.scopeType,
                  entityName: invitation.organization?.name || 'Organization',
                  inviterName: invitation.inviter?.displayName || 'Someone',
                  inviterEmail: '',
                  role: invitation.role?.name || 'Member',
                  createdAt: invitation.createdAt,
                }}
                isProcessing={processingInvite === invitation.id}
                key={invitation.id}
                onAccept={() => handleAcceptInvitation(invitation.id)}
                onDecline={() => handleDeclineInvitation(invitation.id)}
              />
            ))}
            <Button
              className="self-end "
              onClick={() => {
                navigate({ to: '/user/notification' });
              }}
            >
              More Invitations
            </Button>
          </div>

          {invitations.length === 0 && (
            <Alert className="mb-8">
              <CheckCircle2 className="h-4 w-4" />
              <AlertDescription>
                All invitations processed! You can now create your own organization or continue to
                your dashboard.
              </AlertDescription>
            </Alert>
          )}

          <div className="flex flex-col justify-center gap-4 sm:flex-row">
            <Button className="group" onClick={startCreationFlow} size="lg" variant="outline">
              <Plus className="mr-2 h-5 w-5" />
              Create new organization
            </Button>
            {invitations.length === 0 && (
              <Button
                className="group bg-gradient-to-r from-primary to-primary/80 text-primary-foreground hover:from-primary/80 hover:to-primary/60"
                onClick={() => navigate({ to: '/' })}
                size="lg"
              >
                Continue to dashboard
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Show loading state if still determining mode
  if (mode === 'initial') {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-secondary via-background to-secondary dark:from-secondary dark:via-background dark:to-secondary">
        <div className="text-center">
          <Sparkles className="mx-auto mb-4 h-12 w-12 animate-pulse text-primary" />
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Show creation flow
  return (
    <div className="min-h-screen bg-gradient-to-br from-secondary via-background to-secondary dark:from-secondary dark:via-background dark:to-secondary">
      <div className="flex">
        {/* Vertical Timeline Navigation */}
        <div className="min-h-screen w-80 border-border border-r bg-card/80 p-6 backdrop-blur-sm dark:border-border dark:bg-card/80">
          <div className="mb-4">
            <h2 className="font-semibold text-lg">Quick Setup</h2>
          </div>

          <div className="mb-6">
            <div className="mb-2 flex items-center justify-between">
              <span className="font-medium text-muted-foreground text-sm">Progress</span>
              <span className="font-medium text-muted-foreground text-sm">{progress}%</span>
            </div>
            <Progress className="h-2" value={progress} />
          </div>

          <nav className="space-y-2">
            {steps.map((step, index) => {
              const isActive = index === currentStep;
              const isCompleted = completedSteps.includes(index);
              const isSkipped = skippedSteps.includes(index) && !isCompleted;
              const isAccessible = index <= currentStep || completedSteps.includes(index);
              const Icon = step.icon;

              return (
                <motion.button
                  className={cn(
                    'flex w-full items-center gap-3 rounded-xl p-3 text-left transition-all duration-200',
                    isActive &&
                      'border border-primary/20 bg-gradient-to-r from-secondary to-accent dark:border-primary/20 dark:from-secondary/50 dark:to-accent/50',
                    isCompleted &&
                      !isActive &&
                      'border border-chart-2/30 bg-chart-2/10 dark:border-chart-2/30 dark:bg-chart-2/30',
                    !(isActive || isCompleted) &&
                      isAccessible &&
                      'hover:bg-secondary dark:hover:bg-secondary/50',
                    !isAccessible && 'cursor-not-allowed opacity-50',
                  )}
                  disabled={!isAccessible}
                  key={step.id}
                  onClick={() => handleStepClick(index)}
                  whileHover={isAccessible ? { scale: 1.02 } : {}}
                  whileTap={isAccessible ? { scale: 0.98 } : {}}
                >
                  <div
                    className={cn(
                      'flex h-8 w-8 items-center justify-center rounded-lg transition-colors',
                      isActive &&
                        'bg-gradient-to-br from-primary to-primary/80 text-primary-foreground',
                      isCompleted && !isActive && 'bg-chart-2 text-primary-foreground',
                      !(isActive || isCompleted) &&
                        'bg-muted text-muted-foreground dark:bg-muted dark:text-muted-foreground',
                    )}
                  >
                    {isCompleted && !isActive ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <Icon className="h-4 w-4" />
                    )}
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center gap-2">
                      <h3
                        className={cn(
                          'truncate font-medium text-sm',
                          isActive && 'text-primary dark:text-primary',
                          isCompleted && !isActive && 'text-chart-2 dark:text-chart-2',
                        )}
                      >
                        {step.title}
                      </h3>
                      {isSkipped && (
                        <Badge className="text-xs" variant="secondary">
                          Skipped
                        </Badge>
                      )}
                    </div>
                    <p className="truncate text-muted-foreground text-xs">{step.description}</p>
                  </div>
                  {isActive && <ChevronRight className="h-4 w-4 text-primary" />}
                </motion.button>
              );
            })}
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 ">
          <div className="h-screen w-full overflow-y-auto">
            <AnimatePresence mode="wait">
              <motion.div
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                initial={{ opacity: 0, x: 20 }}
                key={currentStep}
                transition={{ duration: 0.3 }}
              >
                <CurrentStepComponent
                  createdIds={createdIds}
                  data={onboardingData}
                  isFirstStep={currentStep === 0}
                  isLoading={isLoading}
                  key={currentStep}
                  onBack={handleBack}
                  onNext={handleNext}
                  onSkip={handleSkip}
                  roles={
                    rolesData?.data?.map((role) => ({
                      id: role.id,
                      name: role.name,
                      description: role.description || undefined,
                    })) || []
                  }
                  updateData={updateOnboardingData}
                />
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>
    </div>
  );
}
