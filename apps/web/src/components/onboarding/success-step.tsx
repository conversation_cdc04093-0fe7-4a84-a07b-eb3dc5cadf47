import { Button } from '@repo/ui/components/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@repo/ui/components/card';
import { ArrowRight, Briefcase, Building2, FolderOpen, PartyPopper, Rocket } from 'lucide-react';
import type React from 'react';

interface SuccessStepProps {
  organizationData: {
    name: string;
    slug: string;
  };
  workspaceData?: {
    name: string;
    type: string;
    color: string;
  };
  projectData?: {
    name: string;
    key: string;
    methodology: string;
    color: string;
  };
  onComplete: () => void;
}

export const SuccessStep: React.FC<SuccessStepProps> = ({
  organizationData,
  workspaceData,
  projectData,
  onComplete,
}) => {
  return (
    <div className="space-y-8 text-center">
      <div className="space-y-6">
        <div className="inline-flex h-24 w-24 animate-pulse items-center justify-center rounded-full bg-gradient-to-br from-[--tt-color-green-base] to-[--tt-color-green-dec-1]">
          <PartyPopper className="h-12 w-12 text-[--tt-white-color]" />
        </div>

        <div className="space-y-3">
          <h1 className="font-semibold text-2xl">You're all set! 🎉</h1>
          <p className="mx-auto max-w-md text-base text-muted-foreground">
            {organizationData.name} is ready to go. Let's explore your new workspace.
          </p>
        </div>
      </div>

      <Card className="w-full text-left">
        <CardHeader>
          <CardTitle className="font-medium text-base">Your setup summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-[--tt-brand-color-100] dark:bg-[--tt-brand-color-900]/30">
                <Building2 className="h-5 w-5 text-[--tt-brand-color-600] dark:text-[--tt-brand-color-400]" />
              </div>
              <div>
                <p className="font-medium">{organizationData.name}</p>
                <p className="text-muted-foreground text-sm">/{organizationData.slug}</p>
              </div>
            </div>

            {workspaceData && (
              <div className="flex items-center gap-3">
                <div
                  className="flex h-10 w-10 items-center justify-center rounded-lg"
                  style={{
                    backgroundColor: `${workspaceData.color}20`,
                  }}
                >
                  <Briefcase className="h-5 w-5" style={{ color: workspaceData.color }} />
                </div>
                <div>
                  <p className="font-medium">{workspaceData.name}</p>
                  <p className="text-muted-foreground text-sm">{workspaceData.type} workspace</p>
                </div>
              </div>
            )}

            {projectData && (
              <div className="flex items-center gap-3">
                <div
                  className="flex h-10 w-10 items-center justify-center rounded-lg"
                  style={{
                    backgroundColor: `${projectData.color}20`,
                  }}
                >
                  <FolderOpen className="h-5 w-5" style={{ color: projectData.color }} />
                </div>
                <div>
                  <p className="font-medium">{projectData.name}</p>
                  <p className="text-muted-foreground text-sm">
                    {projectData.key} • {projectData.methodology}
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <div className="space-y-4">
        <Button
          className="group bg-gradient-to-r from-[--tt-brand-color-500] to-[--tt-brand-color-600] px-8 text-[--tt-white-color] hover:from-[--tt-brand-color-600] hover:to-[--tt-brand-color-700]"
          onClick={onComplete}
          size="lg"
        >
          <Rocket className="mr-2 h-5 w-5" />
          Go to Dashboard
          <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
        </Button>

        <p className="text-muted-foreground text-sm">
          Press <kbd className="rounded bg-muted px-2 py-1 text-xs">⌘K</kbd> anytime for quick
          actions
        </p>
      </div>
    </div>
  );
};
