import { CardDescription, CardTitle } from '@repo/ui/components/card';
import type React from 'react';

interface StepHeaderProps {
  icon: React.ElementType;
  iconColor: string;
  title: string;
  description: string;
}

export const StepHeader: React.FC<StepHeaderProps> = ({
  icon: Icon,
  iconColor,
  title,
  description,
}) => (
  <div className="flex items-center gap-3">
    <div className={`h-12 w-12 ${iconColor} flex items-center justify-center rounded-xl`}>
      <Icon className="h-6 w-6 text-[--tt-white-color]" />
    </div>
    <div>
      <CardTitle className="text-2xl">{title}</CardTitle>
      <CardDescription>{description}</CardDescription>
    </div>
  </div>
);
