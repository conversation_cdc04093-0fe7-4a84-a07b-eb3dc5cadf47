'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/avatar';
import { Badge } from '@repo/ui/components/badge';
import { But<PERSON> } from '@repo/ui/components/button';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardHeader } from '@repo/ui/components/card';
import { Input } from '@repo/ui/components/input';
import { Label } from '@repo/ui/components/label';
import { Textarea } from '@repo/ui/components/textarea';
import { motion } from 'framer-motion';
import {
  ArrowLeft,
  ArrowRight,
  Building2,
  Camera,
  Globe,
  Hash,
  Link2,
  Palette,
  SkipForward,
  Users,
} from 'lucide-react';
import { useRef, useState } from 'react';
import { ColorPicker } from '../components/color-picker';
import { StepHeader } from '../components/step-header';
import { TeamSizeSelector } from '../components/team-size-selector';

interface OrganizationData {
  name: string;
  slug: string;
  description: string;
  size: string;
  color: string;
  website: string;
  logoUrl?: string;
}

interface WorkspaceData {
  name: string;
  slug: string;
  description: string;
  type: string;
  color: string;
  visibility: string;
  icon?: string;
}

interface ProjectData {
  name: string;
  slug: string;
  description: string;
  methodology: string;
  color: string;
  visibility: string;
  startDate?: string;
  endDate?: string;
}

interface OnboardingData {
  organization: OrganizationData;
  workspace: WorkspaceData;
  project: ProjectData;
  invitations: {
    organization: string[];
    workspace: string[];
    project: string[];
  };
}

interface OrganizationStepProps {
  onNext: () => void;
  onBack: () => void;
  onSkip: () => void;
  data: OnboardingData;
  updateData: (section: string, data: Partial<OrganizationData>) => void;
  isFirstStep: boolean;
  isLoading?: boolean;
}

export default function OrganizationStep({
  onNext,
  onBack,
  onSkip,
  data,
  updateData,
  isFirstStep,
  isLoading = false,
}: OrganizationStepProps) {
  const [formData, setFormData] = useState(data.organization);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const colors = [
    { value: '#6366f1', label: 'Indigo', class: 'bg-[#6366f1]' },
    { value: '#8b5cf6', label: 'Purple', class: 'bg-[#8b5cf6]' },
    { value: '#ec4899', label: 'Pink', class: 'bg-[#ec4899]' },
    { value: '#f43f5e', label: 'Rose', class: 'bg-[#f43f5e]' },
    { value: '#3b82f6', label: 'Blue', class: 'bg-[#3b82f6]' },
    { value: '#06b6d4', label: 'Cyan', class: 'bg-[#06b6d4]' },
    { value: '#10b981', label: 'Emerald', class: 'bg-[#10b981]' },
    { value: '#f59e0b', label: 'Amber', class: 'bg-[#f59e0b]' },
  ];

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  };

  const handleInputChange = (field: string, value: string) => {
    const newData = { ...formData, [field]: value };
    if (field === 'name') {
      newData.slug = generateSlug(value);
    }
    setFormData(newData);
    updateData('organization', newData);
  };

  const handleNext = () => {
    if (formData.name && formData.size) {
      onNext();
    }
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      return;
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('Image size should be less than 5MB');
      return;
    }

    setIsUploadingImage(true);
    try {
      // Convert to base64
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result as string;
        const newData = { ...formData, logoUrl: base64String };
        setFormData(newData);
        updateData('organization', newData);
        setIsUploadingImage(false);
      };
      reader.readAsDataURL(file);
    } catch (_error) {
      setIsUploadingImage(false);
    }
  };

  const getOrgInitials = () => {
    return formData.name
      ? formData.name
          .split(' ')
          .map((word) => word[0])
          .join('')
          .toUpperCase()
          .slice(0, 2)
      : 'ORG';
  };

  return (
    <div className="flex h-screen items-center justify-center">
      <Card className="flex h-full w-full flex-col border-0 bg-card/80 py-0 backdrop-blur-sm dark:bg-card/80">
        <CardHeader className="bg-muted/50 p-4 dark:bg-muted/50">
          <StepHeader
            description="Your organization is the top-level workspace where all your teams collaborate"
            icon={Building2}
            iconColor="bg-primary/10"
            title="Create your organization"
          />
        </CardHeader>

        <CardContent className="flex flex-1 flex-col items-center justify-center overflow-y-auto px-4 pt-15">
          <div className="h-full w-full max-w-3xl space-y-6">
            {/* Organization Logo and Name */}
            <div className="flex flex-wrap items-center justify-center gap-6">
              {/* Logo Upload */}
              <motion.div
                animate={{ opacity: 1, y: 0 }}
                className="flex-shrink-0"
                initial={{ opacity: 0, y: 20 }}
                transition={{ delay: 0.05 }}
              >
                <div className="group relative">
                  <label className="cursor-pointer" htmlFor="org-logo-upload">
                    <Avatar className="h-20 w-20 rounded-lg border-2 border-border">
                      <AvatarImage alt="Organization Logo" src={formData.logoUrl} />
                      <AvatarFallback
                        className="rounded-lg bg-muted text-lg"
                        style={{ backgroundColor: formData.color }}
                      >
                        {getOrgInitials()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="absolute inset-0 flex items-center justify-center rounded-lg bg-foreground/50 opacity-0 transition-opacity group-hover:opacity-100">
                      <Camera className="h-6 w-6 text-background" />
                    </div>
                  </label>
                  <input
                    accept="image/*"
                    className="hidden"
                    disabled={isUploadingImage}
                    id="org-logo-upload"
                    onChange={handleImageUpload}
                    ref={fileInputRef}
                    type="file"
                  />
                </div>
                {/* <p className="text-xs text-muted-foreground mt-2 text-center">
                  
                </p> */}
              </motion.div>

              {/* Organization Name */}
              <motion.div
                animate={{ opacity: 1, y: 0 }}
                className="flex-1 space-y-3"
                initial={{ opacity: 0, y: 20 }}
                transition={{ delay: 0.1 }}
              >
                <Label className="flex items-center gap-2" htmlFor="org-name">
                  Organization name
                  <Badge className="text-xs" variant="secondary">
                    Required
                  </Badge>
                </Label>
                <div className="relative">
                  <Hash className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    className="pl-10"
                    id="org-name"
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Acme Corporation"
                    value={formData.name}
                  />
                </div>
                {formData.name && (
                  <p className="slide-in-from-bottom-2 flex animate-in items-center gap-1 text-muted-foreground text-sm">
                    <Link2 className="h-3 w-3" />
                    spark.app/{formData.slug}
                  </p>
                )}
              </motion.div>
            </div>

            {/* Team Size */}
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className="space-y-3"
              initial={{ opacity: 0, y: 20 }}
              transition={{ delay: 0.2 }}
            >
              <Label className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                How big is your team?
                <Badge className="text-xs" variant="secondary">
                  Required
                </Badge>
              </Label>
              <TeamSizeSelector
                onChange={(value) => handleInputChange('size', value)}
                value={formData.size}
              />
            </motion.div>

            {/* Color Selection */}
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className="space-y-3"
              initial={{ opacity: 0, y: 20 }}
              transition={{ delay: 0.3 }}
            >
              <Label className="flex items-center gap-2">
                <Palette className="h-4 w-4" />
                Choose a color theme
              </Label>
              <ColorPicker
                colors={colors}
                onChange={(value) => handleInputChange('color', value)}
                value={formData.color}
              />
            </motion.div>

            {/* Description */}
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className="space-y-3"
              initial={{ opacity: 0, y: 20 }}
              transition={{ delay: 0.4 }}
            >
              <Label className="" htmlFor="org-description">
                Description (optional)
              </Label>
              <Textarea
                className="h-24 resize-none"
                id="org-description"
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Tell us about your organization..."
                value={formData.description}
              />
            </motion.div>

            {/* Website */}
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className="space-y-3"
              initial={{ opacity: 0, y: 20 }}
              transition={{ delay: 0.5 }}
            >
              <Label className="flex items-center gap-2" htmlFor="org-website">
                <Globe className="h-4 w-4" />
                Website (optional)
              </Label>
              <Input
                id="org-website"
                onChange={(e) => handleInputChange('website', e.target.value)}
                placeholder="https://acme.com"
                value={formData.website}
              />
            </motion.div>
          </div>
        </CardContent>

        <CardFooter className="flex justify-between bg-muted/50 p-4 dark:bg-muted/50">
          <Button
            className={isFirstStep ? 'invisible' : ''}
            disabled={isFirstStep}
            onClick={onBack}
            type="button"
            variant="ghost"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div className="flex gap-2">
            <Button onClick={onSkip} variant="ghost">
              <SkipForward className="mr-2 h-4 w-4" />
              Skip
            </Button>
            <Button
              className="group bg-gradient-to-r from-primary to-primary/80 hover:from-primary/80 hover:to-primary/60"
              disabled={!(formData.name && formData.size) || isLoading}
              onClick={handleNext}
            >
              {isLoading ? 'Creating...' : 'Continue'}
              {!isLoading && (
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              )}
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
