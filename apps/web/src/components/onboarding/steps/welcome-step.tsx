'use client';

import { Badge } from '@repo/ui/components/badge';
import { Button } from '@repo/ui/components/button';
import { Card, CardContent, CardHeader } from '@repo/ui/components/card';
import { motion } from 'framer-motion';
import { ArrowRight, Target, Users, Zap } from 'lucide-react';
import Logo from '@/web/assets/Logo.png';

interface WelcomeStepProps {
  onNext: () => void;
  onSkip: () => void;
}

export default function WelcomeStep({ onNext }: WelcomeStepProps) {
  const features = [
    {
      icon: Zap,
      title: 'Lightning Fast',
      description: 'Built for speed and efficiency',
    },
    {
      icon: Users,
      title: 'Team Collaboration',
      description: 'Work together seamlessly',
    },
    {
      icon: Target,
      title: 'Goal Tracking',
      description: 'Stay focused on what matters',
    },
  ];

  return (
    <div className="flex h-screen items-center justify-center ">
      <Card className="flex h-full w-full flex-col border-0 bg-card/80 backdrop-blur-sm dark:bg-card/80">
        <CardContent className="flex flex-1 flex-col items-center justify-center space-y-4 overflow-y-auto px-4">
          <CardHeader className="w-full items-center justify-center text-center ">
            <motion.div
              animate={{ scale: 1 }}
              className="mx-auto "
              initial={{ scale: 0 }}
              transition={{ type: 'spring', stiffness: 260, damping: 20 }}
            >
              <div className="flex h-20 w-20 items-center justify-center rounded-2xl bg-primary shadow-lg">
                {/* <Sparkles className="w-10 h-10 text-[--tt-white-color]" /> */}
                <img alt="Spark" src={Logo} />
              </div>
            </motion.div>

            <motion.div
              animate={{ opacity: 1, y: 0 }}
              initial={{ opacity: 0, y: 20 }}
              transition={{ delay: 0.2 }}
            >
              <h1 className="mb-4 font-semibold text-2xl">Welcome to Spark</h1>
              <p className="mb-6 text-base text-muted-foreground">
                The modern project management platform that ignites productivity
              </p>
              <Badge className="mb-4" variant="secondary">
                ✨ Let's get you set up in just a few minutes
              </Badge>
            </motion.div>
          </CardHeader>
          <div className="w-full max-w-3xl space-y-8">
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className="grid grid-cols-1 gap-4 md:grid-cols-3"
              initial={{ opacity: 0, y: 20 }}
              transition={{ delay: 0.4 }}
            >
              {features.map((feature, index) => (
                <motion.div
                  animate={{ opacity: 1, y: 0 }}
                  className="rounded-xl border bg-gradient-to-br from-secondary to-accent p-4 text-center dark:from-secondary/50 dark:to-accent/50"
                  initial={{ opacity: 0, y: 20 }}
                  key={feature.title}
                  transition={{ delay: 0.5 + index * 0.1 }}
                >
                  <div className="mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br bg-primary/10">
                    <feature.icon className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="mb-1 font-medium">{feature.title}</h3>
                  <p className="text-muted-foreground text-sm">{feature.description}</p>
                </motion.div>
              ))}
            </motion.div>

            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className="text-center"
              initial={{ opacity: 0, y: 20 }}
              transition={{ delay: 0.8 }}
            >
              <Button onClick={onNext} size="lg">
                Get Started
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Button>
              <p className="mt-4 text-muted-foreground text-sm">
                We'll guide you through setting up your organization, workspace, and first project
              </p>
            </motion.div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
