'use client';

import { Badge } from '@repo/ui/components/badge';
import { Button } from '@repo/ui/components/button';
import { Card, CardDescription, CardFooter, CardHeader, CardTitle } from '@repo/ui/components/card';
import { useNavigate } from '@tanstack/react-router';
import { motion } from 'framer-motion';
import { Briefcase, Check, FolderKanban, Users, X } from 'lucide-react';
import { useState } from 'react';
import { type Invitation, type InvitationType, useOnboarding } from '../onboarding-context';

export default function InvitationsStep() {
  const { invitations, acceptInvitation, declineInvitation, setIsCreatingOwn } = useOnboarding();
  const [processingIds, setProcessingIds] = useState<string[]>([]);
  const navigate = useNavigate();

  const handleAccept = async (invitation: Invitation) => {
    setProcessingIds((prev) => [...prev, invitation.id]);
    try {
      // Simulate API call to accept invitation
      await new Promise((resolve) => setTimeout(resolve, 500));
      acceptInvitation(invitation);
      navigate({
        to: '/dashboard',
      });
    } finally {
      setProcessingIds((prev) => prev.filter((id) => id !== invitation.id));
    }
  };

  const handleDecline = async (invitation: Invitation) => {
    setProcessingIds((prev) => [...prev, invitation.id]);
    try {
      // Simulate API call to decline invitation
      await new Promise((resolve) => setTimeout(resolve, 500));
      declineInvitation(invitation);
    } finally {
      setProcessingIds((prev) => prev.filter((id) => id !== invitation.id));
    }
  };

  const getScopeIcon = (scopeType: InvitationType) => {
    switch (scopeType) {
      case 'organization':
        return <Users className="h-5 w-5" />;
      case 'workspace':
        return <Briefcase className="h-5 w-5" />;
      case 'project':
        return <FolderKanban className="h-5 w-5" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="mb-8 text-center">
        <h2 className="font-semibold text-xl">Pending Invitations</h2>
        <p className="mt-2 text-muted-foreground">
          You've been invited to join the following {invitations.length === 1 ? 'team' : 'teams'}.
        </p>
      </div>

      <div className="space-y-4">
        {invitations.map((invitation, index) => (
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            initial={{ opacity: 0, y: 20 }}
            key={invitation.id}
            transition={{ delay: index * 0.1 }}
          >
            <Card>
              <CardHeader className="flex flex-row items-center gap-4">
                <div className="rounded-full bg-primary/10 p-2">
                  {getScopeIcon(invitation.scopeType)}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <CardTitle>{invitation.scopeName}</CardTitle>
                    <Badge className="capitalize" variant="outline">
                      {invitation.scopeType}
                    </Badge>
                  </div>
                  <CardDescription>
                    Invited by {invitation.inviterName} ({invitation.inviterEmail})
                  </CardDescription>
                </div>
                <Badge>{invitation.role}</Badge>
              </CardHeader>
              <CardFooter className="flex justify-end gap-2">
                <Button
                  disabled={processingIds.includes(invitation.id)}
                  onClick={() => handleDecline(invitation)}
                  variant="outline"
                >
                  <X className="mr-2 h-4 w-4" />
                  Decline
                </Button>
                <Button
                  disabled={processingIds.includes(invitation.id)}
                  onClick={() => handleAccept(invitation)}
                >
                  <Check className="mr-2 h-4 w-4" />
                  Accept
                </Button>
              </CardFooter>
            </Card>
          </motion.div>
        ))}
      </div>

      {invitations.length > 0 && (
        <div className="mt-8 text-center">
          <p className="mb-4 text-muted-foreground">
            Would you like to create your own organization instead?
          </p>

          <Button onClick={() => setIsCreatingOwn(true)} variant="outline">
            Create My Own
          </Button>
        </div>
      )}
    </div>
  );
}
