'use client';

import { <PERSON><PERSON> } from '@repo/ui/components/button';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardHeader } from '@repo/ui/components/card';
import { ArrowLeft, ArrowRight, Briefcase, SkipForward } from 'lucide-react';
import { useState } from 'react';
import { WorkspaceForm } from '../../forms/workspace-form';
import { StepHeader } from '../components/step-header';

interface WorkspaceStepProps {
  onNext: () => void;
  onBack: () => void;
  onSkip: () => void;
  data: any;
  updateData: (section: string, data: any) => void;
}

export default function WorkspaceStep({
  onNext,
  onBack,
  onSkip,
  data,
  updateData,
}: WorkspaceStepProps) {
  const [formData, setFormData] = useState(data.workspace);

  const handleDataChange = (newData: typeof formData) => {
    setFormData(newData);
    updateData('workspace', newData);
  };

  const handleNext = () => {
    if (formData.name && formData.type) {
      onNext();
    }
  };

  return (
    <div className="flex h-screen items-center justify-center">
      <Card className="flex h-full w-full flex-col gap-0 border-0 bg-card/80 py-0 shadow-2xl backdrop-blur-sm dark:bg-card/80">
        <CardHeader className="bg-muted/50 p-4 dark:bg-muted/50">
          <StepHeader
            description="Workspaces help teams organize projects and collaborate"
            icon={Briefcase}
            iconColor="bg-gradient-to-br from-accent-2 to-accent-2/80"
            title="Create a workspace"
          />
        </CardHeader>

        <CardContent className="flex flex-1 flex-col items-center justify-center overflow-y-auto px-4 pt-15">
          <div className="h-full w-full max-w-3xl">
            <WorkspaceForm
              data={formData}
              isOnboarding={true}
              onDataChange={handleDataChange}
              organizationSlug={data.organization.slug}
              showTypeSelector={true}
            />
          </div>
        </CardContent>

        <CardFooter className="flex justify-between bg-muted/50 p-4 dark:bg-muted/50">
          <Button onClick={onBack} variant="ghost">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div className="flex gap-2">
            <Button onClick={onSkip} variant="ghost">
              <SkipForward className="mr-2 h-4 w-4" />
              Skip
            </Button>
            <Button
              className="group bg-gradient-to-r from-primary to-primary/80 hover:from-primary/80 hover:to-primary/60"
              disabled={!(formData.name && formData.type)}
              onClick={handleNext}
            >
              Continue
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
