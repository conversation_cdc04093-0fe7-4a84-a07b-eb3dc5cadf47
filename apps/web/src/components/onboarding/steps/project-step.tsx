'use client';

import { <PERSON><PERSON> } from '@repo/ui/components/button';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardHeader } from '@repo/ui/components/card';
import { ArrowLeft, ArrowRight, FolderKanban, SkipForward } from 'lucide-react';
import { useState } from 'react';
import { ProjectForm } from '../../forms/project-form';
import { StepHeader } from '../components/step-header';

interface ProjectStepProps {
  onNext: () => void;
  onBack: () => void;
  onSkip: () => void;
  data: any;
  updateData: (section: string, data: any) => void;
}

export default function ProjectStep({
  onNext,
  onBack,
  onSkip,
  data,
  updateData,
}: ProjectStepProps) {
  const [formData, setFormData] = useState(data.project);

  const handleDataChange = (newData: typeof formData) => {
    setFormData(newData);
    updateData('project', newData);
  };

  const handleNext = () => {
    if (formData.name) {
      onNext();
    }
  };

  return (
    <div className="flex h-screen items-center justify-center">
      <Card className="flex h-full w-full flex-col border-0 bg-card/80 py-0 shadow-2xl backdrop-blur-sm dark:bg-card/80">
        <CardHeader className="bg-muted/50 p-4 dark:bg-muted/50">
          <StepHeader
            description="Projects help you organize and track your team's work"
            icon={FolderKanban}
            iconColor="bg-primary/10"
            title="Create your first project"
          />
        </CardHeader>

        <CardContent className="flex flex-1 flex-col items-center justify-center overflow-y-auto px-4 pt-15">
          <div className="h-full w-full max-w-3xl">
            <ProjectForm
              data={formData}
              isOnboarding={true}
              onDataChange={handleDataChange}
              organizationSlug={data.organization.slug}
              showMethodology={false}
              showTimeline={true}
              workspaceSlug={data.workspace.slug}
            />
          </div>
        </CardContent>

        <CardFooter className="flex justify-between bg-muted/50 p-4 dark:bg-muted/50">
          <Button onClick={onBack} variant="ghost">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div className="flex gap-2">
            <Button onClick={onSkip} variant="ghost">
              <SkipForward className="mr-2 h-4 w-4" />
              Skip
            </Button>
            <Button
              className="group bg-gradient-to-r from-primary to-primary/80 hover:from-primary/80 hover:to-primary/60"
              disabled={!formData.name}
              onClick={handleNext}
            >
              Continue
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
