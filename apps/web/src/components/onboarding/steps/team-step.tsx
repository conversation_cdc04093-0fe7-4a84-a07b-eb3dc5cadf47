'use client';

import { Badge } from '@repo/ui/components/badge';
import { But<PERSON> } from '@repo/ui/components/button';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/ui/components/table';
import { useForm } from '@tanstack/react-form';
import { motion } from 'framer-motion';
import { Mail, Trash2, UserPlus } from 'lucide-react';
import { useState } from 'react';
import { z } from 'zod';
import { useOnboarding } from '../onboarding-context';

const inviteSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  role: z.string().min(1, 'Please select a role'),
});

type InviteFormValues = z.infer<typeof inviteSchema>;

export default function TeamStep() {
  const { organization, teamMembers, addTeamMember, removeTeamMember } = useOnboarding();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm({
    defaultValues: {
      email: '',
      role: 'member',
    },
    validators: {
      onSubmit: inviteSchema,
    },
    onSubmit: async ({ value }) => {
      await onSubmit(value);
    },
  });

  const onSubmit = async (data: InviteFormValues) => {
    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Check if email already exists
      const exists = teamMembers.some((member) => member.email === data.email);
      if (exists) {
        // For Tanstack Form, we need to handle errors differently
        // We'll show the error through a different mechanism
        alert('This email has already been invited');
      } else {
        addTeamMember(data);
        form.reset();
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRemove = (email: string) => {
    removeTeamMember(email);
  };

  return (
    <motion.div animate={{ opacity: 1 }} className="space-y-6" initial={{ opacity: 0 }}>
      <div>
        <h2 className="font-semibold text-lg">Invite your team</h2>
        <p className="mt-2 text-muted-foreground">
          Invite team members to collaborate with you in {organization?.name || 'your organization'}
          .
        </p>
      </div>

      <Form>
        <form
          className="space-y-4"
          onSubmit={(e) => {
            e.preventDefault();
            form.handleSubmit();
          }}
        >
          <div className="flex flex-col gap-4 md:flex-row">
            <form.Field name="email">
              {(field) => (
                <FormField field={field}>
                  <FormItem className="flex-1">
                    <FormControl>
                      <div className="relative">
                        <Mail className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          className="pl-10"
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="<EMAIL>"
                          value={field.state.value}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>

            <form.Field name="role">
              {(field) => (
                <FormField field={field}>
                  <FormItem className="w-full md:w-[180px]">
                    <FormControl>
                      <Select
                        onValueChange={(value) => field.handleChange(value)}
                        value={field.state.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select role" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="owner">Owner</SelectItem>
                          <SelectItem value="admin">Administrator</SelectItem>
                          <SelectItem value="member">Member</SelectItem>
                          <SelectItem value="readonly">Read-only</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>

            <Button className="w-full md:w-auto" disabled={isSubmitting} type="submit">
              <UserPlus className="mr-2 h-4 w-4" />
              {isSubmitting ? 'Inviting...' : 'Invite'}
            </Button>
          </div>
        </form>
      </Form>

      {teamMembers.length > 0 ? (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Email</TableHead>
                <TableHead>Role</TableHead>
                <TableHead className="w-[80px]" />
              </TableRow>
            </TableHeader>
            <TableBody>
              {teamMembers.map((member, index) => (
                <TableRow key={index}>
                  <TableCell>{member.email}</TableCell>
                  <TableCell>
                    <Badge className="capitalize" variant="outline">
                      {member.role}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Button onClick={() => handleRemove(member.email)} size="icon" variant="ghost">
                      <Trash2 className="h-4 w-4" />
                      <span className="sr-only">Remove</span>
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      ) : (
        <div className="rounded-md border bg-muted/50 py-8 text-center">
          <p className="text-muted-foreground">No team members invited yet</p>
        </div>
      )}

      <div className="text-muted-foreground text-sm">
        <p>You can always invite more team members later.</p>
      </div>
    </motion.div>
  );
}
