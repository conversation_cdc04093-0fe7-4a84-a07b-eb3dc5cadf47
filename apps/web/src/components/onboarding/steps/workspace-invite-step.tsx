'use client';

import { Badge } from '@repo/ui/components/badge';
import { But<PERSON> } from '@repo/ui/components/button';
import { <PERSON>, Card<PERSON>ontent, CardFooter, CardHeader } from '@repo/ui/components/card';
import { Input } from '@repo/ui/components/input';
import { Label } from '@repo/ui/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/ui/components/table';
import { useToast } from '@repo/ui/components/use-toast';
import { motion } from 'framer-motion';
import { ArrowLeft, ArrowRight, Mail, SkipForward, Trash2, UserPlus, Users } from 'lucide-react';
import { useState } from 'react';
import type { PostApiV1InvitationsBody } from '@/web/services/hooks.schemas';
import { usePostApiV1Invitations } from '@/web/services/invitations';
import { useGetApiV1Roles } from '@/web/services/roles';
import { useRootStore } from '@/web/store/store';
import { StepHeader } from '../components/step-header';

interface TeamMember {
  email: string;
  role: string;
  roleId: string;
}

interface WorkspaceInviteStepProps {
  onNext: () => void;
  onBack: () => void;
  onSkip: () => void;
  data: any;
  updateData: (section: string, data: any) => void;
  createdIds?: {
    organizationId: string | null;
    workspaceId: string | null;
    projectId: string | null;
  };
}

export default function WorkspaceInviteStep({
  onNext,
  onBack,
  onSkip,
  data,
  updateData,
  createdIds,
}: WorkspaceInviteStepProps) {
  const [email, setEmail] = useState('');
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [pendingInvitations, setPendingInvitations] = useState<string[]>([]);

  const { toast } = useToast();
  const currentUser = useRootStore((state) => state.currentUser);
  const workspaceId = createdIds?.workspaceId || '';

  // Fetch workspace-level roles
  const { data: rolesData } = useGetApiV1Roles({
    filters: JSON.stringify({ level: { $eq: 'workspace' } }),
  });
  const roles = rolesData?.data || [];

  const invitationMutation = usePostApiV1Invitations({
    mutation: {
      onSuccess: () => {
        toast({
          description: 'Invitation sent successfully',
        });
      },
      onError: (_error) => {
        toast({
          variant: 'destructive',
          description: 'Failed to send invitation. Please try again.',
        });
      },
    },
  });

  // Find the default member role
  const defaultRole = roles.find((r) => r.name.toLowerCase() === 'member') || roles[0];
  const [role, setRole] = useState(defaultRole?.id || '');

  const handleAddMember = async () => {
    if (!email || teamMembers.some((member) => member.email === email)) {
      return;
    }

    if (!workspaceId) {
      toast({
        variant: 'destructive',
        description: 'Workspace not created yet. Please complete the previous step first.',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Send invitation via API
      const invitationData: PostApiV1InvitationsBody = {
        email,
        roleId: role,
        invitedBy: currentUser?.id || '',
        scopeId: workspaceId,
        scopeType: 'workspace',
      };

      await invitationMutation.mutateAsync({ data: invitationData });

      // Update local state after successful invitation
      const selectedRole = roles.find((r) => r.id === role);
      const newMember = {
        email,
        role: selectedRole?.name || 'Member',
        roleId: role,
      };
      const updatedMembers = [...teamMembers, newMember];
      setTeamMembers(updatedMembers);
      setPendingInvitations([...pendingInvitations, email]);

      updateData('invitations', {
        ...data.invitations,
        workspace: updatedMembers,
      });

      setEmail('');
      setRole(defaultRole?.id || '');
    } catch {
      // Error is handled by mutation onError
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRemoveMember = (emailToRemove: string) => {
    const updatedMembers = teamMembers.filter((member) => member.email !== emailToRemove);
    setTeamMembers(updatedMembers);
    setPendingInvitations(pendingInvitations.filter((email) => email !== emailToRemove));
    updateData('invitations', {
      ...data.invitations,
      workspace: updatedMembers,
    });
  };

  return (
    <div className="flex h-screen items-center justify-center">
      <Card className="flex h-full w-full flex-col border-0 bg-card/80 py-0 shadow-2xl backdrop-blur-sm dark:bg-card/80">
        <CardHeader className="bg-muted/50 p-4 dark:bg-muted/50">
          <StepHeader
            description={`Invite team members to collaborate in ${data.workspace.name || 'your workspace'}`}
            icon={Users}
            iconColor="bg-primary/10"
            title="Invite collaborators"
          />
        </CardHeader>

        <CardContent className="flex flex-1 flex-col items-center justify-center overflow-y-auto px-4">
          <div className="w-full max-w-3xl space-y-8">
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className="space-y-4"
              initial={{ opacity: 0, y: 20 }}
              transition={{ delay: 0.1 }}
            >
              <div className="flex flex-col gap-4 md:flex-row">
                <div className="flex-1">
                  <Label className="mb-2 block" htmlFor="email">
                    Email address
                  </Label>
                  <div className="relative">
                    <Mail className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      className="py-6 pl-10"
                      id="email"
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="<EMAIL>"
                      type="email"
                      value={email}
                    />
                  </div>
                </div>

                <div className="w-full md:w-[180px]">
                  <Label className="mb-2 block">Role</Label>
                  <Select onValueChange={setRole} value={role}>
                    <SelectTrigger className="size-full py-6">
                      <SelectValue placeholder="Select a role" />
                    </SelectTrigger>
                    <SelectContent>
                      {roles.map((r) => (
                        <SelectItem key={r.id} value={r.id}>
                          {r.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-end">
                  <Button
                    className="py-6 "
                    disabled={!email || isSubmitting || invitationMutation.isPending}
                    onClick={handleAddMember}
                  >
                    <UserPlus className="mr-2 h-4 w-4" />
                    {isSubmitting || invitationMutation.isPending ? 'Sending...' : 'Invite'}
                  </Button>
                </div>
              </div>
            </motion.div>

            <motion.div
              animate={{ opacity: 1, y: 0 }}
              initial={{ opacity: 0, y: 20 }}
              transition={{ delay: 0.2 }}
            >
              {teamMembers.length > 0 ? (
                <div className="overflow-hidden rounded-lg border">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-muted/50">
                        <TableHead>Email</TableHead>
                        <TableHead>Role</TableHead>
                        <TableHead className="w-[80px]" />
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {teamMembers.map((member, index) => (
                        <TableRow key={index}>
                          <TableCell className="font-medium">{member.email}</TableCell>
                          <TableCell>
                            <Badge className="capitalize" variant="outline">
                              {member.role}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Button
                              className="h-8 w-8 text-muted-foreground hover:text-destructive"
                              onClick={() => handleRemoveMember(member.email)}
                              size="icon"
                              variant="ghost"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="rounded-lg border-2 border-muted-foreground/25 border-dashed bg-muted/20 py-12 text-center">
                  <Users className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                  <p className="text-muted-foreground">No collaborators invited yet</p>
                  <p className="mt-1 text-muted-foreground text-sm">
                    Add email addresses above to invite your team
                  </p>
                </div>
              )}
            </motion.div>
          </div>
        </CardContent>

        <CardFooter className="flex justify-between bg-muted/50 p-4 dark:bg-muted/50">
          <Button onClick={onBack} variant="ghost">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div className="flex gap-2">
            <Button onClick={onSkip} variant="ghost">
              <SkipForward className="mr-2 h-4 w-4" />
              Skip
            </Button>
            <Button className="" onClick={onNext}>
              Continue
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
