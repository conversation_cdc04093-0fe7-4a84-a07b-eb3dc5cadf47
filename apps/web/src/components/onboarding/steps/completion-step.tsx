'use client';

import { Badge } from '@repo/ui/components/badge';
import { But<PERSON> } from '@repo/ui/components/button';
import { Card, CardContent } from '@repo/ui/components/card';
import { useNavigate } from '@tanstack/react-router';
import { motion } from 'framer-motion';
import { ArrowRight, Sparkles } from 'lucide-react';

import ConfettiExplosion from 'react-confetti-explosion';

interface CompletionStepProps {
  onNext: () => void;
}

export default function CompletionStep({ onNext }: CompletionStepProps) {
  const navigate = useNavigate();
  const handleGetStarted = () => {
    // In a real app, this would redirect to the dashboard
    // console.log("Onboarding completed with data:", data);
    navigate({
      to: '/dashboard',
    });
    onNext();
  };

  return (
    <div className="flex h-screen items-center justify-center">
      <Card className="flex h-full w-full flex-col border-0 bg-primary-foreground/80 shadow-2xl backdrop-blur-sm dark:bg-card/80">
        <CardContent className="flex flex-1 flex-col items-center justify-center overflow-y-auto px-4">
          <div className="w-full max-w-3xl space-y-8 text-center">
            {/* <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="grid gap-6"
            >
              <div className="bg-gradient-to-r from-primary/5 to-primary/10 dark:from-primary/10 dark:to-primary/20 p-6 rounded-xl border border-primary/20 dark:border-primary/30">
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center">
                    <Building2 className="w-6 h-6 text-primary-foreground" />
                  </div>
                  <div>
                    <h3 className="font-medium text-base">Organization</h3>
                    <p className="text-muted-foreground">
                      {data.organization.name}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Badge variant="outline">{data.organization.size}</Badge>
                  <span>•</span>
                  <span>
                    {data.invitations.organization.length} members invited
                  </span>
                </div>
              </div>

              <div className="bg-gradient-to-r from-primary/5 to-primary/10 dark:from-primary/10 dark:to-primary/20 p-6 rounded-xl border border-primary/20 dark:border-primary/30">
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center">
                    <Briefcase className="w-6 h-6 text-primary-foreground" />
                  </div>
                  <div>
                    <h3 className="font-medium text-base">Workspace</h3>
                    <p className="text-muted-foreground">
                      {data.workspace.name}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Badge variant="outline" className="capitalize">
                    {data.workspace.type}
                  </Badge>
                  <span>•</span>
                  <Badge variant="outline" className="capitalize">
                    {data.workspace.visibility}
                  </Badge>
                  <span>•</span>
                  <span>
                    {data.invitations.workspace.length} collaborators invited
                  </span>
                </div>
              </div>

              <div className="bg-gradient-to-r from-primary/5 to-primary/10 dark:from-primary/10 dark:to-primary/20 p-6 rounded-xl border border-primary/20 dark:border-primary/30">
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center">
                    <FolderKanban className="w-6 h-6 text-primary-foreground" />
                  </div>
                  <div>
                    <h3 className="font-medium text-base">Project</h3>
                    <p className="text-muted-foreground">{data.project.name}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Badge variant="outline" className="capitalize">
                    {data.project.methodology}
                  </Badge>
                  <span>•</span>
                  <Badge variant="outline" className="capitalize">
                    {data.project.visibility}
                  </Badge>
                  <span>•</span>
                  <span>
                    {data.invitations.project.length} team members invited
                  </span>
                </div>
              </div>
            </motion.div> */}

            <motion.div
              animate={{ scale: 1 }}
              className="mx-auto mb-6"
              initial={{ scale: 0 }}
              transition={{ type: 'spring', stiffness: 260, damping: 20 }}
            >
              <div className="m-auto h-20 w-20 rounded-2xl text-7xl ">
                {/* <PartyPopper className="w-10 h-10" /> */}
                🎉
                <ConfettiExplosion />
              </div>
            </motion.div>

            <motion.div
              animate={{ opacity: 1, y: 0 }}
              initial={{ opacity: 0, y: 20 }}
              transition={{ delay: 0.2 }}
            >
              <div className="mb-4 bg-gradient-to-r from-chart-2/80 via-chart-2/80 to-chart-2/60 bg-clip-text font-semibold text-2xl text-transparent">
                You're all set!
              </div>
              <p className="mb-6 text-base text-muted-foreground">
                Welcome to Spark! Your App is ready and your team can start collaborating.
              </p>
              <Badge className="mb-4" variant="secondary">
                🎉 Setup completed successfully
              </Badge>
            </motion.div>

            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className="text-center"
              initial={{ opacity: 0, y: 20 }}
              transition={{ delay: 0.6 }}
            >
              <Button className=" group px-8 py-3" onClick={handleGetStarted} size="lg">
                <Sparkles className="mr-2 h-5 w-5" />
                Go to Dashboard
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Button>
              <p className="mt-4 text-muted-foreground text-sm">
                Start managing your projects and collaborating with your team
              </p>
            </motion.div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
