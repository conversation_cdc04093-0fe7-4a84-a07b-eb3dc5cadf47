'use client';

import { cn } from '@repo/ui/lib/utils';
import { motion } from 'framer-motion';
import { Code, Megaphone, MoreHorizontal, Palette, Settings } from 'lucide-react';
import type React from 'react';

interface WorkspaceType {
  value: string;
  label: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  gradient: string;
}

interface WorkspaceTypeSelectorProps {
  value: string;
  onChange: (value: string) => void;
}

const workspaceTypes: WorkspaceType[] = [
  {
    value: 'engineering',
    label: 'Engineering',
    description: 'Code, deploy, iterate',
    icon: Code,
    gradient: 'bg-primary/10',
  },
  {
    value: 'design',
    label: 'Design',
    description: 'Create, prototype, ship',
    icon: Palette,
    gradient: 'bg-primary/10',
  },
  {
    value: 'marketing',
    label: 'Marketing',
    description: 'Plan, execute, measure',
    icon: Megaphone,
    gradient: 'bg-primary/10',
  },
  {
    value: 'operations',
    label: 'Operations',
    description: 'Optimize, scale, grow',
    icon: Settings,
    gradient: 'bg-primary/10',
  },
  {
    value: 'other',
    label: 'Other',
    description: 'Custom workspace',
    icon: MoreHorizontal,
    gradient: 'bg-primary/10',
  },
];

export function WorkspaceTypeSelector({ value, onChange }: WorkspaceTypeSelectorProps) {
  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
      {workspaceTypes.map((type, index) => {
        const Icon = type.icon;
        const isSelected = value === type.value;

        return (
          <motion.button
            animate={{ opacity: 1, y: 0 }}
            className={cn(
              'group rounded-xl border-2 p-4 text-left transition-all duration-200 hover:scale-105',
              isSelected
                ? 'border-primary bg-primary/10 shadow-lg ring-2 ring-primary/20 dark:bg-primary/20'
                : 'border-border hover:border-primary/50 hover:shadow-md',
            )}
            initial={{ opacity: 0, y: 20 }}
            key={type.value}
            onClick={() => onChange(type.value)}
            transition={{ delay: index * 0.1 }}
          >
            <div className=" flex items-center justify-start gap-4">
              <div
                className={cn(
                  'flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br',
                  type.gradient,
                  'shadow-md transition-shadow group-hover:shadow-lg',
                )}
              >
                <Icon className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h3 className="font-medium ">{type.label}</h3>
                <p className="text-muted-foreground text-sm">{type.description}</p>
              </div>
            </div>
          </motion.button>
        );
      })}
    </div>
  );
}
