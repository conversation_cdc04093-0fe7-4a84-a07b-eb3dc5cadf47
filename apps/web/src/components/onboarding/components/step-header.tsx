'use client';

import { cn } from '@repo/ui/lib/utils';
import type { LucideIcon } from 'lucide-react';

interface StepHeaderProps {
  icon: LucideIcon;
  iconColor: string;
  title: string;
  description: string;
}

export function StepHeader({ icon: Icon, iconColor, title, description }: StepHeaderProps) {
  return (
    <div className=" flex items-center gap-2 space-y-2">
      <div
        className={cn(
          'flex h-16 w-16 items-center justify-center rounded-2xl shadow-lg',
          iconColor,
        )}
      >
        <Icon className="h-8 w-8 text-[--tt-white-color]" />
      </div>

      <div>
        <div className="font-semibold text-lg">{title}</div>
        <p className="text-muted-foreground">{description}</p>
      </div>
    </div>
  );
}
