'use client';

import { cn } from '@repo/ui/lib/utils';
import { motion } from 'framer-motion';
import { Check, Palette } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

interface Color {
  value: string;
  label: string;
  class: string;
}

interface ColorPickerProps {
  colors: Color[];
  value: string;
  onChange: (value: string) => void;
}

export function ColorPicker({ colors, value, onChange }: ColorPickerProps) {
  const colorInputRef = useRef<HTMLInputElement>(null);
  const [lastCustomColor, setLastCustomColor] = useState<string | null>(null);

  const isCustomColor = !colors.some((color) => color.value === value);
  const showLastCustomColor = lastCustomColor && !colors.some((c) => c.value === lastCustomColor);

  useEffect(() => {
    // If current value is a custom color, save it
    if (isCustomColor && value) {
      setLastCustomColor(value);
    }
  }, [value, isCustomColor]);

  const handleCustomColorChange = (newColor: string) => {
    setLastCustomColor(newColor);
    onChange(newColor);
  };

  return (
    <div className="flex flex-wrap gap-3">
      {colors.map((color, index) => (
        <motion.button
          animate={{ opacity: 1, scale: 1 }}
          className={cn(
            'group flex h-10 w-10 items-center justify-center rounded-xl border-2 transition-all duration-200 hover:scale-110',
            color.class,
            value === color.value
              ? 'border-background shadow-lg ring-2 ring-muted ring-offset-2 dark:ring-muted-foreground'
              : 'border-transparent hover:border-background/50',
          )}
          initial={{ opacity: 0, scale: 0 }}
          key={color.value}
          onClick={() => onChange(color.value)}
          title={color.label}
          transition={{ delay: index * 0.05 }}
        >
          {value === color.value && <Check className="h-4 w-4 text-background" />}
        </motion.button>
      ))}

      {/* Show last custom color if it exists and is not currently selected */}
      {showLastCustomColor && (
        <motion.button
          animate={{ opacity: 1, scale: 1 }}
          className={cn(
            'flex h-10 w-10 items-center justify-center rounded-xl border-2 transition-all duration-200 hover:scale-110',
            value === lastCustomColor
              ? 'border-background shadow-lg ring-2 ring-muted ring-offset-2 dark:ring-muted-foreground'
              : 'border-transparent hover:border-background/50',
          )}
          initial={{ opacity: 0, scale: 0 }}
          onClick={() => onChange(lastCustomColor)}
          style={{ backgroundColor: lastCustomColor }}
          title="Previous custom color"
          transition={{ delay: colors.length * 0.05 }}
        >
          {value === lastCustomColor && (
            <Check className="h-4 w-4 text-background drop-shadow-md" />
          )}
        </motion.button>
      )}

      {/* Custom Color Picker */}
      <motion.div
        animate={{ opacity: 1, scale: 1 }}
        className="relative"
        initial={{ opacity: 0, scale: 0 }}
        transition={{
          delay: (colors.length + (showLastCustomColor ? 1 : 0)) * 0.05,
        }}
      >
        <button
          className={cn(
            'flex h-10 w-10 items-center justify-center rounded-xl border-2 transition-all duration-200 hover:scale-110',
            'bg-gradient-to-br from-destructive via-success to-accent',
            isCustomColor && value !== lastCustomColor
              ? 'border-background shadow-lg ring-2 ring-muted ring-offset-2 dark:ring-muted-foreground'
              : 'border-transparent hover:border-background/50',
          )}
          onClick={() => colorInputRef.current?.click()}
          style={isCustomColor && value !== lastCustomColor ? { background: value } : undefined}
          title="Choose custom color"
          type="button"
        >
          {isCustomColor && value !== lastCustomColor ? (
            <Check className="h-4 w-4 text-background drop-shadow-md" />
          ) : (
            <Palette className="h-4 w-4 text-background drop-shadow-md" />
          )}
        </button>
        <input
          aria-label="Custom color picker"
          className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
          onChange={(e) => handleCustomColorChange(e.target.value)}
          ref={colorInputRef}
          type="color"
          value={lastCustomColor || '#000000'}
        />
      </motion.div>
    </div>
  );
}
