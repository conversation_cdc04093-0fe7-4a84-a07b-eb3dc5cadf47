'use client';

import { cn } from '@repo/ui/lib/utils';

import { motion } from 'framer-motion';
import { Building, Globe, User, Users } from 'lucide-react';
import type React from 'react';

interface TeamSizeOption {
  value: string;
  label: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  gradient: string;
}

interface TeamSizeSelectorProps {
  value: string;
  onChange: (value: string) => void;
}

const teamSizes: TeamSizeOption[] = [
  {
    value: 'personal',
    label: 'Just me',
    description: 'Personal projects and tasks',
    icon: User,
    gradient: 'bg-primary/10',
  },
  {
    value: 'small',
    label: 'Small team',
    description: '2-10 people',
    icon: Users,
    gradient: 'bg-primary/10',
  },
  {
    value: 'medium',
    label: 'Medium team',
    description: '11-50 people',
    icon: Building,
    gradient: 'bg-primary/10',
  },
  {
    value: 'large',
    label: 'Large organization',
    description: '50+ people',
    icon: Globe,
    gradient: 'bg-primary/10',
  },
];

export function TeamSizeSelector({ value, onChange }: TeamSizeSelectorProps) {
  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
      {teamSizes.map((size, index) => {
        const Icon = size.icon;
        const isSelected = value === size.value;

        return (
          <motion.button
            animate={{ opacity: 1, y: 0 }}
            className={cn(
              'group rounded-xl border-2 p-4 text-left transition-all duration-200 hover:scale-105',
              isSelected
                ? 'border-primary bg-primary/10 shadow-lg ring-2 ring-primary/20 dark:bg-primary/20'
                : 'border-border hover:border-primary/50 hover:shadow-md',
            )}
            initial={{ opacity: 0, y: 20 }}
            key={size.value}
            onClick={() => onChange(size.value)}
            transition={{ delay: index * 0.1 }}
          >
            <div className="flex items-start gap-4">
              <div
                className={cn(
                  'flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br',
                  size.gradient,
                  'shadow-md transition-shadow group-hover:shadow-lg',
                )}
              >
                <Icon className="h-6 w-6 text-primary" />
              </div>
              <div className="flex-1">
                <h3 className="mb-1 font-semibold text-sm">{size.label}</h3>
                <p className="text-muted-foreground text-sm">{size.description}</p>
              </div>
            </div>
          </motion.button>
        );
      })}
    </div>
  );
}
