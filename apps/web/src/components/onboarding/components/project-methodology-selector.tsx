'use client';

import { cn } from '@repo/ui/lib/utils';

import { motion } from 'framer-motion';
import { Kanban, RotateCcw, Settings, Workflow, Zap } from 'lucide-react';
import type React from 'react';

interface ProjectMethodology {
  value: string;
  label: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  gradient: string;
}

interface ProjectMethodologySelectorProps {
  value: string;
  onChange: (value: string) => void;
}

const methodologies: ProjectMethodology[] = [
  {
    value: 'agile',
    label: 'Agile',
    description: 'Iterative development with sprints',
    icon: Zap,
    gradient: 'bg-primary/10',
  },
  {
    value: 'scrum',
    label: 'Scrum',
    description: 'Structured sprints with ceremonies',
    icon: RotateCcw,
    gradient: 'bg-primary/10',
  },
  {
    value: 'kanban',
    label: 'Kanban',
    description: 'Visual workflow management',
    icon: Kanban,
    gradient: 'bg-primary/10',
  },
  {
    value: 'waterfall',
    label: 'Waterfall',
    description: 'Sequential project phases',
    icon: Workflow,
    gradient: 'bg-primary/10',
  },
  {
    value: 'custom',
    label: 'Custom',
    description: 'Your own workflow',
    icon: Settings,
    gradient: 'bg-primary/10',
  },
];

export function ProjectMethodologySelector({ value, onChange }: ProjectMethodologySelectorProps) {
  return (
    <div className="flex flex-row flex-wrap items-center justify-start gap-3">
      {methodologies.map((methodology, index) => {
        const Icon = methodology.icon;
        const isSelected = value === methodology.value;

        return (
          <motion.button
            animate={{ opacity: 1, y: 0 }}
            className={cn(
              'group rounded-xl border-2 p-4 text-left transition-all duration-200 hover:scale-105',
              isSelected
                ? 'border-primary bg-primary/10 shadow-lg ring-2 ring-primary/20 dark:bg-primary/20'
                : 'border-border hover:border-primary/50 hover:shadow-md',
            )}
            initial={{ opacity: 0, y: 20 }}
            key={methodology.value}
            onClick={() => onChange(methodology.value)}
            transition={{ delay: index * 0.1 }}
          >
            <div className="flex items-center justify-center gap-4 ">
              <div
                className={cn(
                  'flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br',
                  methodology.gradient,
                  'shadow-md transition-shadow group-hover:shadow-lg',
                )}
              >
                <Icon className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h3 className="mb-1 font-medium">{methodology.label}</h3>
                <p className="text-muted-foreground text-sm">{methodology.description}</p>
              </div>
            </div>
          </motion.button>
        );
      })}
    </div>
  );
}
