import { Check } from 'lucide-react';
import type React from 'react';

interface ColorPickerProps {
  colors: Array<{
    value: string;
    label: string;
    class: string;
  }>;
  value: string;
  onChange: (value: string) => void;
}

export const ColorPicker: React.FC<ColorPickerProps> = ({ colors, value, onChange }) => (
  <div className="flex flex-wrap gap-3">
    {colors.map((color) => (
      <button
        className={`group relative h-12 w-12 rounded-xl transition-all ${color.class} ${
          value === color.value
            ? 'scale-110 ring-2 ring-offset-2 ring-offset-background'
            : 'hover:scale-110'
        }`}
        key={color.value}
        onClick={() => onChange(color.value)}
        title={color.label}
        type="button"
      >
        {value === color.value && (
          <Check className="absolute inset-0 m-auto h-5 w-5 text-[--tt-white-color]" />
        )}
      </button>
    ))}
  </div>
);
