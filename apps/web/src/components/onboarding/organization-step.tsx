import { Badge } from '@repo/ui/components/badge';
import { <PERSON><PERSON> } from '@repo/ui/components/button';
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@repo/ui/components/card';
import { Input } from '@repo/ui/components/input';
import { Label } from '@repo/ui/components/label';
import {
  ArrowRight,
  Building,
  Building2,
  Globe,
  Lightbulb,
  Link2,
  Mail,
  Plus,
  Smile,
  TrendingUp,
  Upload,
  Users,
} from 'lucide-react';
import type React from 'react';
import { StepHeader } from './step-header';

interface OrganizationStepProps {
  formData: {
    name: string;
    slug: string;
    description: string;
    website: string;
    logoUrl: string;
    billingEmail: string;
    billingAddress: string;
    size: string;
  };
  onInputChange: (field: string, value: string) => void;
  onNext: () => void;
  isLoading?: boolean;
  uploadedLogo: string | null;
  setUploadedLogo: (url: string | null) => void;
}

const organizationSizes = [
  { value: '1-10', label: 'Just me', icon: Smile },
  { value: '11-50', label: 'Small team', icon: Users },
  { value: '51-200', label: 'Growing company', icon: TrendingUp },
  { value: '200+', label: 'Large organization', icon: Building },
];

export const OrganizationStep: React.FC<OrganizationStepProps> = ({
  formData,
  onInputChange,
  onNext,
  isLoading = false,
  uploadedLogo,
  setUploadedLogo,
}) => {
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  };

  const handleNameChange = (value: string) => {
    onInputChange('name', value);
    onInputChange('slug', generateSlug(value));
  };

  return (
    <Card className="mx-auto w-full max-w-2xl">
      <CardHeader className="space-y-4">
        <StepHeader
          description="This is your company's shared workspace"
          icon={Building2}
          iconColor="bg-gradient-to-br from-[--tt-brand-color-500] to-[--tt-brand-color-600]"
          title="Create your organization"
        />
      </CardHeader>

      <CardContent className="max-h-[60vh] space-y-6 overflow-y-auto">
        {/* Logo Upload */}
        <div className="flex items-center gap-6">
          <div className="group relative">
            <div className="flex h-24 w-24 items-center justify-center overflow-hidden rounded-2xl bg-gradient-to-br from-[--tt-gray-light-100] to-[--tt-gray-light-200] dark:from-[--tt-gray-dark-800] dark:to-[--tt-gray-dark-700]">
              {uploadedLogo ? (
                <img alt="Logo" className="h-full w-full object-cover" src={uploadedLogo} />
              ) : (
                <Upload className="h-8 w-8 text-[--tt-gray-light-400] dark:text-[--tt-gray-dark-400]" />
              )}
            </div>
            <Button
              className="-bottom-2 -right-2 absolute h-8 w-8 rounded-full p-0 opacity-0 transition-opacity group-hover:opacity-100"
              onClick={() => setUploadedLogo('/api/placeholder/96/96')}
              size="sm"
              type="button"
              variant="secondary"
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex-1">
            <Label>Organization logo</Label>
            <p className="mt-1 text-muted-foreground text-sm">
              Upload your company logo or choose one later
            </p>
          </div>
        </div>

        {/* Organization Name */}
        <div className="space-y-3">
          <Label className="flex items-center gap-2" htmlFor="org-name">
            Organization name
            <Badge className="text-xs" variant="secondary">
              Required
            </Badge>
          </Label>
          <div className="relative">
            <Building className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
            <Input
              className="pl-10"
              id="org-name"
              onChange={(e) => handleNameChange(e.target.value)}
              placeholder="Acme Corporation"
              value={formData.name}
            />
          </div>
          {formData.name && (
            <p className="slide-in-from-bottom-2 flex animate-in items-center gap-1 text-muted-foreground text-sm">
              <Link2 className="h-3 w-3" />
              workhub.com/{formData.slug}
            </p>
          )}
        </div>

        {/* Organization Size */}
        <div className="space-y-3">
          <Label>How many people are in your organization?</Label>
          <div className="grid grid-cols-2 gap-3">
            {organizationSizes.map((size) => {
              const Icon = size.icon;
              return (
                <button
                  className={`rounded-xl border-2 p-4 transition-all ${
                    formData.size === size.value
                      ? 'border-[--tt-brand-color-500] bg-[--tt-brand-color-50] dark:bg-[--tt-brand-color-950]/30'
                      : 'border-[--tt-gray-light-200] hover:border-[--tt-gray-light-300] dark:border-[--tt-gray-dark-700]'
                  }`}
                  key={size.value}
                  onClick={() => onInputChange('size', size.value)}
                  type="button"
                >
                  <Icon
                    className={`mb-2 h-5 w-5 ${
                      formData.size === size.value
                        ? 'text-[--tt-brand-color-600] dark:text-[--tt-brand-color-400]'
                        : 'text-muted-foreground'
                    }`}
                  />
                  <div className="font-medium text-sm">{size.label}</div>
                </button>
              );
            })}
          </div>
        </div>

        {/* Additional Details */}
        <div className="grid gap-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="flex items-center gap-1" htmlFor="org-website">
                <Globe className="h-4 w-4" />
                Website
              </Label>
              <Input
                id="org-website"
                onChange={(e) => onInputChange('website', e.target.value)}
                placeholder="https://example.com"
                type="url"
                value={formData.website}
              />
            </div>
            <div className="space-y-2">
              <Label className="flex items-center gap-1" htmlFor="org-email">
                <Mail className="h-4 w-4" />
                Billing email
              </Label>
              <Input
                id="org-email"
                onChange={(e) => onInputChange('billingEmail', e.target.value)}
                placeholder="<EMAIL>"
                type="email"
                value={formData.billingEmail}
              />
            </div>
          </div>
        </div>
      </CardContent>

      <CardFooter className="flex justify-between border-t pt-6">
        <div className="flex items-center gap-2 text-muted-foreground text-sm">
          <Lightbulb className="h-4 w-4" />
          You can always change these settings later
        </div>
        <Button
          className="group"
          disabled={!(formData.name && formData.size) || isLoading}
          onClick={onNext}
        >
          Continue
          <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
        </Button>
      </CardFooter>
    </Card>
  );
};
