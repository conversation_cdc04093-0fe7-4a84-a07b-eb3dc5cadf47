import { Alert, AlertDescription } from '@repo/ui/components/alert';
import { Button } from '@repo/ui/components/button';
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@repo/ui/components/card';
import { Input } from '@repo/ui/components/input';
import { Label } from '@repo/ui/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { ArrowLeft, ArrowRight, Info, Plus, Send, UserPlus, Users, X } from 'lucide-react';
import type React from 'react';
import { StepHeader } from './step-header';

interface TeamMember {
  email: string;
  roleId: string;
}

interface InviteTeamStepProps {
  organizationName: string;
  inviteMembers: TeamMember[];
  onUpdateMember: (index: number, field: keyof TeamMember, value: string) => void;
  onAddMember: () => void;
  onRemoveMember: (index: number) => void;
  onNext: () => void;
  onBack: () => void;
  onSkip: () => void;
  isLoading?: boolean;
  roles: Array<{ id: string; name: string; description?: string }>;
}

export const InviteTeamStep: React.FC<InviteTeamStepProps> = ({
  organizationName,
  inviteMembers,
  onUpdateMember,
  onAddMember,
  onRemoveMember,
  onNext,
  onBack,
  onSkip,
  isLoading = false,
  roles,
}) => {
  return (
    <Card className="mx-auto w-full max-w-2xl">
      <CardHeader className="space-y-4">
        <StepHeader
          description="Add team members to your organization"
          icon={Users}
          iconColor="bg-gradient-to-br from-[--tt-color-yellow-base] to-[--tt-color-yellow-dec-1]"
          title="Invite your team"
        />
      </CardHeader>

      <CardContent className="max-h-[60vh] space-y-6 overflow-y-auto">
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            Invited members will have access to {organizationName || 'your organization'}. You can
            assign them to specific workspaces and projects later.
          </AlertDescription>
        </Alert>

        <div className="space-y-4">
          <Label>Team members</Label>

          {inviteMembers.map((member, index) => (
            <div className="slide-in-from-bottom-2 flex animate-in gap-2" key={index}>
              <div className="relative flex-1">
                <UserPlus className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                <Input
                  className="pl-10"
                  onChange={(e) => onUpdateMember(index, 'email', e.target.value)}
                  placeholder="<EMAIL>"
                  type="email"
                  value={member.email}
                />
              </div>
              <Select
                onValueChange={(value) => onUpdateMember(index, 'roleId', value)}
                value={member.roleId}
              >
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map((role) => (
                    <SelectItem key={role.id} value={role.id}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {inviteMembers.length > 1 && (
                <Button
                  className="w-12"
                  onClick={() => onRemoveMember(index)}
                  size="icon"
                  variant="ghost"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          ))}

          <Button className="w-full" onClick={onAddMember} variant="outline">
            <Plus className="mr-2 h-4 w-4" />
            Add another
          </Button>
        </div>

        <div className="space-y-3 rounded-xl bg-muted/50 p-4">
          <div className="flex items-start gap-3">
            <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-lg bg-primary/10">
              <Send className="h-4 w-4 text-primary" />
            </div>
            <div>
              <p className="font-medium text-sm">Invitations will be sent</p>
              <p className="text-muted-foreground text-sm">
                Team members will receive an email invitation to join {organizationName}
              </p>
            </div>
          </div>
        </div>
      </CardContent>

      <CardFooter className="flex justify-between border-t pt-6">
        <Button onClick={onBack} variant="ghost">
          <ArrowLeft className="mr-1 h-4 w-4" />
          Back
        </Button>
        <div className="flex gap-2">
          <Button onClick={onSkip} variant="ghost">
            Skip for now
          </Button>
          <Button className="group" disabled={isLoading} onClick={onNext}>
            Send invites & continue
            <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};
