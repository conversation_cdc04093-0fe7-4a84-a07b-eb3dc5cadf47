import { Button } from '@repo/ui/components/button';
import { <PERSON><PERSON>, <PERSON>alogContent, Di<PERSON>Header, DialogTitle } from '@repo/ui/components/dialog';
import { cn } from '@repo/ui/lib/utils';
import {
  Download,
  Maximize2,
  Minimize2,
  Move,
  RotateCw,
  Shrink,
  X,
  ZoomIn,
  ZoomOut,
} from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';

interface ImagePreviewProps {
  src: string;
  alt?: string;
  trigger?: React.ReactNode;
  className?: string;
  onDownload?: () => void;
  fileName?: string;
}

export function ImagePreview({
  src,
  alt = 'Image preview',
  trigger,
  className,
  onDownload,
  fileName = 'image',
}: ImagePreviewProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [scale, setScale] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  const handleZoomIn = useCallback(() => {
    setScale((prev) => Math.min(prev + 0.25, 3));
  }, []);

  const handleZoomOut = useCallback(() => {
    setScale((prev) => Math.max(prev - 0.25, 0.5));
  }, []);

  const handleRotate = useCallback(() => {
    setRotation((prev) => (prev + 90) % 360);
  }, []);

  const handleDownload = () => {
    if (onDownload) {
      onDownload();
    } else {
      // Default download behavior
      const link = document.createElement('a');
      link.href = src;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // Reset transformations when closing
  useEffect(() => {
    if (!isOpen) {
      setScale(1);
      setRotation(0);
      setIsFullscreen(false);
      setImageError(false);
      setIsDragging(false);
      setPosition({ x: 0, y: 0 });
    }
  }, [isOpen]);

  // Handle mouse wheel zoom
  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      if (!containerRef.current?.contains(e.target as Node)) {
        return;
      }

      e.preventDefault();
      const delta = e.deltaY > 0 ? -0.1 : 0.1;
      setScale((prev) => Math.max(0.5, Math.min(3, prev + delta)));
    };

    if (isOpen) {
      window.addEventListener('wheel', handleWheel, { passive: false });
      return () => window.removeEventListener('wheel', handleWheel);
    }
  }, [isOpen]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) {
        return;
      }

      switch (e.key) {
        case '+':
        case '=':
          e.preventDefault();
          handleZoomIn();
          break;
        case '-':
        case '_':
          e.preventDefault();
          handleZoomOut();
          break;
        case 'r':
        case 'R':
          e.preventDefault();
          handleRotate();
          break;
        case '0':
          e.preventDefault();
          setScale(1);
          setPosition({ x: 0, y: 0 });
          break;
      }
    };

    if (isOpen) {
      window.addEventListener('keydown', handleKeyDown);
      return () => window.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen, handleZoomIn, handleZoomOut, handleRotate]);

  return (
    <>
      {trigger ? (
        <div
          className="cursor-pointer"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setIsOpen(true);
          }}
        >
          {trigger}
        </div>
      ) : (
        <img
          alt={alt}
          className={cn('cursor-pointer', className)}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setIsOpen(true);
          }}
          onError={() => setImageError(true)}
          src={src}
        />
      )}

      <Dialog modal={true} onOpenChange={setIsOpen} open={isOpen}>
        <DialogContent
          className={cn(
            'w-[90vw] max-w-6xl overflow-hidden p-0',
            isFullscreen && 'm-0 h-screen max-w-full',
          )}
          onOpenAutoFocus={(e) => e.preventDefault()}
        >
          <DialogHeader className="absolute top-0 right-0 left-0 z-10 border-b bg-background/95 p-4 backdrop-blur-sm">
            <div className="flex items-center justify-between">
              <DialogTitle className="text-foreground">{alt}</DialogTitle>
              <div className="flex items-center gap-2 rounded-lg bg-background/80 p-1">
                <Button
                  className="h-8 w-8 p-0"
                  disabled={scale <= 0.5}
                  onClick={handleZoomOut}
                  size="sm"
                  variant="secondary"
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>
                <span className="min-w-[3rem] px-2 text-center font-medium text-foreground text-sm">
                  {Math.round(scale * 100)}%
                </span>
                <Button
                  className="h-8 w-8 p-0"
                  disabled={scale >= 3}
                  onClick={handleZoomIn}
                  size="sm"
                  variant="secondary"
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>
                <div className="mx-1 h-6 w-px bg-border" />
                <Button
                  className="h-8 w-8 p-0"
                  onClick={() => {
                    setScale(1);
                    setPosition({ x: 0, y: 0 });
                  }}
                  size="sm"
                  title="Fit to window (0)"
                  variant="secondary"
                >
                  <Shrink className="h-4 w-4" />
                </Button>
                <Button
                  className="h-8 w-8 p-0"
                  onClick={handleRotate}
                  size="sm"
                  title="Rotate (R)"
                  variant="secondary"
                >
                  <RotateCw className="h-4 w-4" />
                </Button>
                <Button
                  className="h-8 w-8 p-0"
                  onClick={toggleFullscreen}
                  size="sm"
                  title="Toggle fullscreen"
                  variant="secondary"
                >
                  {isFullscreen ? (
                    <Minimize2 className="h-4 w-4" />
                  ) : (
                    <Maximize2 className="h-4 w-4" />
                  )}
                </Button>
                <Button
                  className="h-8 w-8 p-0"
                  onClick={handleDownload}
                  size="sm"
                  title="Download"
                  variant="secondary"
                >
                  <Download className="h-4 w-4" />
                </Button>
                <div className="mx-1 h-6 w-px bg-border" />
                <Button
                  className="h-8 w-8 p-0 transition-colors hover:bg-destructive hover:text-background"
                  onClick={() => setIsOpen(false)}
                  size="sm"
                  title="Close (Esc)"
                  variant="secondary"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </DialogHeader>

          <div
            className={cn(
              'relative overflow-hidden bg-muted/50',
              isFullscreen ? 'h-screen pt-16' : 'h-[70vh] max-h-[600px] pt-16',
            )}
            onMouseDown={(e) => {
              if (scale > 1 && e.button === 0) {
                setIsDragging(true);
                setDragStart({
                  x: e.clientX - position.x,
                  y: e.clientY - position.y,
                });
              }
            }}
            onMouseLeave={() => setIsDragging(false)}
            onMouseMove={(e) => {
              if (isDragging && scale > 1) {
                setPosition({
                  x: e.clientX - dragStart.x,
                  y: e.clientY - dragStart.y,
                });
              }
            }}
            onMouseUp={() => setIsDragging(false)}
            ref={containerRef}
            style={{
              cursor: scale > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default',
            }}
          >
            {imageError ? (
              <div className="flex h-full items-center justify-center">
                <p className="text-muted-foreground">Failed to load image</p>
              </div>
            ) : (
              <div className="flex h-full w-full items-center justify-center">
                <img
                  alt={alt}
                  className="select-none"
                  draggable={false}
                  onError={() => setImageError(true)}
                  ref={imageRef}
                  src={src}
                  style={{
                    transform: `translate(${position.x}px, ${position.y}px) scale(${scale}) rotate(${rotation}deg)`,
                    transformOrigin: 'center',
                    transition: isDragging ? 'none' : 'transform 0.2s ease-in-out',
                    maxWidth: scale <= 1 ? '90%' : 'none',
                    maxHeight: scale <= 1 ? '90%' : 'none',
                    objectFit: 'contain',
                  }}
                />
              </div>
            )}
          </div>

          {/* Instructions */}
          {scale > 1 && (
            <div className="-translate-x-1/2 absolute bottom-4 left-1/2 transform rounded-full bg-foreground/70 px-3 py-1.5 text-background text-xs">
              <Move className="mr-1 inline h-3 w-3" />
              Drag to pan • Mouse wheel to zoom
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}

// Thumbnail component for consistent image previews across the app
interface ImageThumbnailProps {
  src: string;
  alt?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  onDownload?: () => void;
  fileName?: string;
}

export function ImageThumbnail({
  src,
  alt = 'Image',
  size = 'md',
  className,
  onDownload,
  fileName,
}: ImageThumbnailProps) {
  const [imageError, setImageError] = useState(false);

  const sizeClasses = {
    sm: 'h-12 w-12',
    md: 'h-20 w-20',
    lg: 'h-32 w-32',
  };

  if (imageError) {
    return (
      <div
        className={cn(
          'flex items-center justify-center rounded-md bg-muted',
          sizeClasses[size],
          className,
        )}
      >
        <span className="text-muted-foreground text-xs">No preview</span>
      </div>
    );
  }

  return (
    <ImagePreview
      alt={alt}
      fileName={fileName}
      onDownload={onDownload}
      src={src}
      trigger={
        <div
          className={cn(
            'relative cursor-pointer overflow-hidden rounded-md bg-muted transition-opacity hover:opacity-80',
            sizeClasses[size],
            className,
          )}
        >
          <img
            alt={alt}
            className="h-full w-full object-cover"
            onError={() => setImageError(true)}
            src={src}
          />
        </div>
      }
    />
  );
}
