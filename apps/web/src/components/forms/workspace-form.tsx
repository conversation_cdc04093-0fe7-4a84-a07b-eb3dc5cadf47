'use client';

import { Ava<PERSON>, AvatarFallback, AvatarImage } from '@repo/ui/components/avatar';
import { Badge } from '@repo/ui/components/badge';
import { Input } from '@repo/ui/components/input';
import { Label } from '@repo/ui/components/label';
import { RadioGroup, RadioGroupItem } from '@repo/ui/components/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { Textarea } from '@repo/ui/components/textarea';
import { motion } from 'framer-motion';
import { Briefcase, Building2, Camera, Eye, Globe2, Lock, Rocket, Users, Zap } from 'lucide-react';
import { useRef, useState } from 'react';
import { useRootStore } from '@/web/store/store';
import { ColorPicker } from '../onboarding/components/color-picker';

interface WorkspaceFormData {
  name: string;
  slug: string;
  description?: string;
  type: string;
  color: string;
  visibility: 'public' | 'private';
  icon?: string;
  organizationId?: string;
}

interface WorkspaceFormProps {
  data: WorkspaceFormData;
  onDataChange: (data: WorkspaceFormData) => void;
  organizationSlug?: string;
  isOnboarding?: boolean;
  showTypeSelector?: boolean;
}

const workspaceTypes = [
  {
    value: 'engineering',
    label: 'Engineering',
    icon: Zap,
    description: 'Software development teams',
  },
  {
    value: 'marketing',
    label: 'Marketing',
    icon: Rocket,
    description: 'Marketing and growth teams',
  },
  {
    value: 'operations',
    label: 'Operations',
    icon: Users,
    description: 'Business operations teams',
  },
  {
    value: 'general',
    label: 'General',
    icon: Briefcase,
    description: 'Cross-functional teams',
  },
];

const colors = [
  { value: '#8b5cf6', label: 'Purple', class: 'bg-[#8b5cf6]' },
  { value: '#6366f1', label: 'Indigo', class: 'bg-[#6366f1]' },
  { value: '#3b82f6', label: 'Blue', class: 'bg-[#3b82f6]' },
  { value: '#06b6d4', label: 'Cyan', class: 'bg-[#06b6d4]' },
  { value: '#10b981', label: 'Emerald', class: 'bg-[#10b981]' },
  { value: '#f59e0b', label: 'Amber', class: 'bg-[#f59e0b]' },
  { value: '#ec4899', label: 'Pink', class: 'bg-[#ec4899]' },
  { value: '#f43f5e', label: 'Rose', class: 'bg-[#f43f5e]' },
];

export function WorkspaceForm({
  data,
  onDataChange,
  // organizationSlug,
  isOnboarding = false,
  showTypeSelector = false,
}: WorkspaceFormProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploadingImage, setIsUploadingImage] = useState(false);

  // Get current organization and organizations list from store
  const currentOrganizationId = useRootStore((state) => state.currentOrganizationId);
  const organizations = useRootStore((state) => state.organizations);

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  };

  const handleInputChange = (field: keyof WorkspaceFormData, value: string) => {
    const newData = { ...data, [field]: value };
    if (field === 'name') {
      newData.slug = generateSlug(value);
    }
    onDataChange(newData);
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      return;
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('Image size should be less than 5MB');
      return;
    }

    setIsUploadingImage(true);
    try {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result as string;
        handleInputChange('icon', base64String);
        setIsUploadingImage(false);
      };
      reader.readAsDataURL(file);
    } catch (_error) {
      setIsUploadingImage(false);
    }
  };

  const getWorkspaceInitials = () => {
    return data.name
      ? data.name
          .split(' ')
          .map((word) => word[0])
          .join('')
          .toUpperCase()
          .slice(0, 2)
      : 'WS';
  };

  const animationDelay = isOnboarding ? 0.1 : 0;

  return (
    <div className="space-y-6">
      {/* Workspace Icon and Name */}
      <div className="flex items-start gap-6">
        {/* Icon Upload */}
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className="flex-shrink-0"
          initial={{ opacity: 0, y: 20 }}
          transition={{ delay: animationDelay + 0.05 }}
        >
          <div className="group relative">
            <Avatar
              className="h-20 w-20 rounded-lg border-2 border-border"
              onClick={() => fileInputRef.current?.click()}
            >
              <AvatarImage alt={data.name || 'Workspace'} src={data.icon} />
              <AvatarFallback
                className="rounded-lg font-semibold text-lg"
                style={{ backgroundColor: data.color }}
              >
                {getWorkspaceInitials()}
              </AvatarFallback>
            </Avatar>
            <div
              className="absolute inset-0 flex cursor-pointer items-center justify-center rounded-lg bg-foreground/60 opacity-0 transition-opacity group-hover:opacity-100"
              onClick={() => fileInputRef.current?.click()}
            >
              <Camera className="h-6 w-6 text-background" />
            </div>
            <input
              accept="image/*"
              className="hidden"
              disabled={isUploadingImage}
              id="ws-icon-upload"
              onChange={handleImageUpload}
              ref={fileInputRef}
              type="file"
            />
          </div>
        </motion.div>

        {/* Workspace Name */}
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className="flex-1 space-y-3"
          initial={{ opacity: 0, y: 20 }}
          transition={{ delay: animationDelay + 0.1 }}
        >
          <Label className="flex items-center gap-2" htmlFor="ws-name">
            Workspace name
            <Badge className="text-xs" variant="secondary">
              Required
            </Badge>
          </Label>
          <div className="relative">
            {/* <Hash className="absolute left-3 top-3.5 w-4 h-4 text-muted-foreground" /> */}
            <Input
              id="ws-name"
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Engineering Team"
              value={data.name}
              // className="pl-10 py-6"
            />
          </div>
          {/* {data.name && organizationSlug && (
            <p className="text-sm text-muted-foreground flex items-center gap-1 animate-in slide-in-from-bottom-2">
              <Link2 className="w-3 h-3" />
              {organizationSlug}/{data.slug}
            </p>
          )} */}
        </motion.div>
      </div>

      {/* Organization Selector */}
      {!isOnboarding && (
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className="space-y-3"
          initial={{ opacity: 0, y: 20 }}
          transition={{ delay: animationDelay + 0.12 }}
        >
          <Label className="flex items-center gap-2" htmlFor="ws-organization">
            <Building2 className="h-4 w-4" />
            Organization
            <Badge className="text-xs" variant="secondary">
              Required
            </Badge>
          </Label>
          <Select
            onValueChange={(value) => handleInputChange('organizationId', value)}
            value={data.organizationId || currentOrganizationId || ''}
          >
            <SelectTrigger className="w-full" id="ws-organization">
              <SelectValue placeholder="Select organization" />
            </SelectTrigger>
            <SelectContent>
              {organizations.map((org) => (
                <SelectItem key={org.id} value={org.id}>
                  {org.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </motion.div>
      )}

      {/* Workspace Type Selector */}
      {showTypeSelector && (
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className="space-y-3"
          initial={{ opacity: 0, y: 20 }}
          transition={{ delay: animationDelay }}
        >
          <Label>
            What type of workspace is this?
            <Badge className="ml-2 text-xs" variant="secondary">
              Required
            </Badge>
          </Label>
          <div className="grid grid-cols-2 gap-3">
            {workspaceTypes.map((type) => {
              const Icon = type.icon;
              return (
                <button
                  className={`rounded-xl border-2 p-4 transition-all ${
                    data.type === type.value
                      ? 'border-primary bg-secondary'
                      : 'border-border hover:border-muted-foreground'
                  }`}
                  key={type.value}
                  onClick={() => handleInputChange('type', type.value)}
                  type="button"
                >
                  <Icon
                    className={`mb-2 h-5 w-5 ${
                      data.type === type.value ? 'text-primary' : 'text-muted-foreground'
                    }`}
                  />
                  <div className="font-medium text-sm">{type.label}</div>
                  <div className="mt-1 text-muted-foreground text-xs">{type.description}</div>
                </button>
              );
            })}
          </div>
        </motion.div>
      )}

      {/* Color Selection */}
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className="space-y-3"
        initial={{ opacity: 0, y: 20 }}
        transition={{ delay: animationDelay + 0.15 }}
      >
        <Label>Workspace color</Label>
        <ColorPicker
          colors={colors}
          onChange={(value) => handleInputChange('color', value)}
          value={data.color}
        />
      </motion.div>

      {/* Visibility */}
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className="space-y-3"
        initial={{ opacity: 0, y: 20 }}
        transition={{ delay: animationDelay + 0.2 }}
      >
        <Label className="flex items-center gap-2">
          <Eye className="h-4 w-4" />
          Who can see this workspace?
        </Label>
        <RadioGroup
          onValueChange={(value) => handleInputChange('visibility', value as 'public' | 'private')}
          value={data.visibility}
        >
          <div className="grid gap-3">
            <label
              className={`flex cursor-pointer items-start gap-3 rounded-xl border-2 p-4 transition-all ${
                data.visibility === 'private'
                  ? 'border-primary bg-secondary'
                  : 'border-border hover:border-muted-foreground'
              }`}
              htmlFor="ws-private"
            >
              <RadioGroupItem className="mt-1" id="ws-private" value="private" />
              <div className="flex-1">
                <div className="mb-1 flex items-center gap-2 font-medium">
                  <Lock className="h-4 w-4" />
                  Private
                </div>
                <div className="text-muted-foreground text-sm">
                  Only workspace members can view and contribute
                </div>
              </div>
            </label>

            <label
              className={`flex cursor-pointer items-start gap-3 rounded-xl border-2 p-4 transition-all ${
                data.visibility === 'public'
                  ? 'border-primary bg-secondary'
                  : 'border-border hover:border-muted-foreground'
              }`}
              htmlFor="ws-public"
            >
              <RadioGroupItem className="mt-1" id="ws-public" value="public" />
              <div className="flex-1">
                <div className="mb-1 flex items-center gap-2 font-medium">
                  <Globe2 className="h-4 w-4" />
                  Public
                </div>
                <div className="text-muted-foreground text-sm">
                  Anyone in your organization can view
                </div>
              </div>
            </label>
          </div>
        </RadioGroup>
      </motion.div>

      {/* Description */}
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className="space-y-3"
        initial={{ opacity: 0, y: 20 }}
        transition={{ delay: animationDelay + 0.25 }}
      >
        <Label htmlFor="ws-description">Description (optional)</Label>
        <Textarea
          className="h-24 resize-none"
          id="ws-description"
          onChange={(e) => handleInputChange('description', e.target.value)}
          placeholder="What is this workspace for?"
          value={data.description || ''}
        />
      </motion.div>
    </div>
  );
}
