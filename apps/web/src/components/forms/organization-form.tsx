'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/avatar';
import { Badge } from '@repo/ui/components/badge';
import { Input } from '@repo/ui/components/input';
import { Label } from '@repo/ui/components/label';
import { Textarea } from '@repo/ui/components/textarea';
import { motion } from 'framer-motion';
import { Building2, Camera, Globe, Hash, Link2, Mail, MapPin, Palette, Users } from 'lucide-react';
import { useRef, useState } from 'react';
import { ColorPicker } from '../onboarding/components/color-picker';
import { TeamSizeSelector } from '../onboarding/components/team-size-selector';

interface OrganizationFormData {
  name: string;
  slug: string;
  description?: string;
  size?: string;
  color: string;
  website?: string;
  logoUrl?: string;
  billingEmail?: string;
  billingAddress?: string;
}

interface OrganizationFormProps {
  data: OrganizationFormData;
  onDataChange: (data: OrganizationFormData) => void;
  isOnboarding?: boolean;
  showTeamSize?: boolean;
}

const colors = [
  { value: '#6366f1', label: 'Indigo', class: 'bg-[#6366f1]' },
  { value: '#8b5cf6', label: 'Purple', class: 'bg-[#8b5cf6]' },
  { value: '#ec4899', label: 'Pink', class: 'bg-[#ec4899]' },
  { value: '#f43f5e', label: 'Rose', class: 'bg-[#f43f5e]' },
  { value: '#3b82f6', label: 'Blue', class: 'bg-[#3b82f6]' },
  { value: '#06b6d4', label: 'Cyan', class: 'bg-[#06b6d4]' },
  { value: '#10b981', label: 'Emerald', class: 'bg-[#10b981]' },
  { value: '#f59e0b', label: 'Amber', class: 'bg-[#f59e0b]' },
];

export function OrganizationForm({
  data,
  onDataChange,
  isOnboarding = false,
  showTeamSize = true,
}: OrganizationFormProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploadingImage, setIsUploadingImage] = useState(false);

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  };

  const handleInputChange = (field: keyof OrganizationFormData, value: string) => {
    const newData = { ...data, [field]: value };
    if (field === 'name') {
      newData.slug = generateSlug(value);
    }
    onDataChange(newData);
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      return;
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('Image size should be less than 5MB');
      return;
    }

    setIsUploadingImage(true);
    try {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result as string;
        handleInputChange('logoUrl', base64String);
        setIsUploadingImage(false);
      };
      reader.readAsDataURL(file);
    } catch (_error) {
      setIsUploadingImage(false);
    }
  };

  const getOrgInitials = () => {
    return data.name
      ? data.name
          .split(' ')
          .map((word) => word[0])
          .join('')
          .toUpperCase()
          .slice(0, 2)
      : 'ORG';
  };

  const animationDelay = isOnboarding ? 0.1 : 0;

  return (
    <div className="space-y-6">
      {/* Organization Logo and Name */}
      <div className="flex items-start gap-6">
        {/* Logo Upload */}
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className="flex-shrink-0"
          initial={{ opacity: 0, y: 20 }}
          transition={{ delay: animationDelay + 0.05 }}
        >
          <div className="group relative">
            <Avatar
              className="h-20 w-20 cursor-pointer rounded-lg border-2 border-border"
              onClick={() => fileInputRef.current?.click()}
            >
              <AvatarImage alt={data.name || 'Organization'} src={data.logoUrl} />
              <AvatarFallback
                className="rounded-lg font-semibold text-lg"
                style={{ backgroundColor: data.color }}
              >
                {getOrgInitials()}
              </AvatarFallback>
            </Avatar>
            <div
              className="absolute inset-0 flex cursor-pointer items-center justify-center rounded-lg bg-foreground/60 opacity-0 transition-opacity group-hover:opacity-100"
              onClick={() => fileInputRef.current?.click()}
            >
              <Camera className="h-6 w-6 text-background" />
            </div>
            <input
              accept="image/*"
              className="hidden"
              disabled={isUploadingImage}
              onChange={handleImageUpload}
              ref={fileInputRef}
              type="file"
            />
          </div>
        </motion.div>

        {/* Organization Name */}
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className="flex-1 space-y-3"
          initial={{ opacity: 0, y: 20 }}
          transition={{ delay: animationDelay + 0.1 }}
        >
          <Label className="flex items-center gap-2" htmlFor="org-name">
            Organization name
            <Badge className="text-xs" variant="secondary">
              Required
            </Badge>
          </Label>
          <div className="relative">
            <Hash className="absolute top-3.5 left-3 h-4 w-4 text-muted-foreground" />
            <Input
              className="py-6 pl-10"
              id="org-name"
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Acme Corporation"
              value={data.name}
            />
          </div>
          {data.name && (
            <p className="slide-in-from-bottom-2 flex animate-in items-center gap-1 text-muted-foreground text-sm">
              <Link2 className="h-3 w-3" />
              spark.app/{data.slug}
            </p>
          )}
        </motion.div>
      </div>

      {/* Team Size */}
      {showTeamSize && (
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className="space-y-3"
          initial={{ opacity: 0, y: 20 }}
          transition={{ delay: animationDelay + 0.2 }}
        >
          <Label className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            How big is your team?
            <Badge className="text-xs" variant="secondary">
              Required
            </Badge>
          </Label>
          <TeamSizeSelector
            onChange={(value) => handleInputChange('size', value)}
            value={data.size || ''}
          />
        </motion.div>
      )}

      {/* Color Selection */}
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className="space-y-3"
        initial={{ opacity: 0, y: 20 }}
        transition={{ delay: animationDelay + 0.3 }}
      >
        <Label className="flex items-center gap-2">
          <Palette className="h-4 w-4" />
          Choose a color theme
        </Label>
        <ColorPicker
          colors={colors}
          onChange={(value) => handleInputChange('color', value)}
          value={data.color}
        />
      </motion.div>

      {/* Description */}
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className="space-y-3"
        initial={{ opacity: 0, y: 20 }}
        transition={{ delay: animationDelay + 0.4 }}
      >
        <Label htmlFor="org-description">Description (optional)</Label>
        <Textarea
          className="h-24 resize-none"
          id="org-description"
          onChange={(e) => handleInputChange('description', e.target.value)}
          placeholder="Tell us about your organization..."
          value={data.description || ''}
        />
      </motion.div>

      {/* Contact Information */}
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className="space-y-4"
        initial={{ opacity: 0, y: 20 }}
        transition={{ delay: animationDelay + 0.5 }}
      >
        <div className="flex items-center gap-2 font-medium text-base">
          <Building2 className="h-4 w-4" />
          Contact Information
        </div>

        {/* Website */}
        <div className="space-y-2">
          <Label className="flex items-center gap-2" htmlFor="org-website">
            <Globe className="h-4 w-4" />
            Website (optional)
          </Label>
          <Input
            id="org-website"
            onChange={(e) => handleInputChange('website', e.target.value)}
            placeholder="https://acme.com"
            type="url"
            value={data.website || ''}
          />
        </div>

        {/* Billing Email */}
        {!isOnboarding && (
          <>
            <div className="space-y-2">
              <Label className="flex items-center gap-2" htmlFor="org-billing-email">
                <Mail className="h-4 w-4" />
                Billing Email (optional)
              </Label>
              <Input
                id="org-billing-email"
                onChange={(e) => handleInputChange('billingEmail', e.target.value)}
                placeholder="<EMAIL>"
                type="email"
                value={data.billingEmail || ''}
              />
            </div>

            {/* Billing Address */}
            <div className="space-y-2">
              <Label className="flex items-center gap-2" htmlFor="org-billing-address">
                <MapPin className="h-4 w-4" />
                Billing Address (optional)
              </Label>
              <Textarea
                className="h-20 resize-none"
                id="org-billing-address"
                onChange={(e) => handleInputChange('billingAddress', e.target.value)}
                placeholder="123 Main St, Suite 100&#10;San Francisco, CA 94105"
                value={data.billingAddress || ''}
              />
            </div>
          </>
        )}
      </motion.div>
    </div>
  );
}
