'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/avatar';
import { Badge } from '@repo/ui/components/badge';
import { Input } from '@repo/ui/components/input';
import { Label } from '@repo/ui/components/label';
import { RadioGroup, RadioGroupItem } from '@repo/ui/components/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { Textarea } from '@repo/ui/components/textarea';
import { motion } from 'framer-motion';
import {
  Calendar,
  Camera,
  Eye,
  Flag,
  FolderKanban,
  GitBranch,
  Globe2,
  Lock,
  Palette,
  Target,
} from 'lucide-react';
import { useRef } from 'react';
import type { PatchApiV1ProjectsIdBody, PostApiV1ProjectsBody } from '@/web/services/hooks.schemas';
import { useGetApiV1Workflows } from '@/web/services/workflows';
import { useRootStore } from '@/web/store/store';
import { ColorPicker } from '../onboarding/components/color-picker';

interface ProjectFormProps {
  data: PostApiV1ProjectsBody | PatchApiV1ProjectsIdBody;
  onDataChange: (data: PostApiV1ProjectsBody | PatchApiV1ProjectsIdBody) => void;
  organizationSlug?: string;
  workspaceSlug?: string;
  isOnboarding?: boolean;
  showMethodology?: boolean;
  showTimeline?: boolean;
}

// const methodologies = [
//   { value: "agile", label: "Agile", icon: Zap },
//   { value: "waterfall", label: "Waterfall", icon: TrendingUp },
//   { value: "kanban", label: "Kanban", icon: FolderKanban },
//   { value: "custom", label: "Custom", icon: Sparkles },
// ];

const colors = [
  { value: '#ec4899', label: 'Pink', class: 'bg-[#ec4899]' },
  { value: '#8b5cf6', label: 'Purple', class: 'bg-[#8b5cf6]' },
  { value: '#6366f1', label: 'Indigo', class: 'bg-[#6366f1]' },
  { value: '#f43f5e', label: 'Rose', class: 'bg-[#f43f5e]' },
  { value: '#3b82f6', label: 'Blue', class: 'bg-[#3b82f6]' },
  { value: '#06b6d4', label: 'Cyan', class: 'bg-[#06b6d4]' },
  { value: '#10b981', label: 'Emerald', class: 'bg-[#10b981]' },
  { value: '#f59e0b', label: 'Amber', class: 'bg-[#f59e0b]' },
];

export function ProjectForm({
  data,
  onDataChange,
  // organizationSlug,
  // workspaceSlug,
  isOnboarding = false,
  // showMethodology = false,
  showTimeline = false,
}: ProjectFormProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const workspaces = useRootStore((state) => state.workspaces);
  const currentOrganizationId = useRootStore((state) => state.currentOrganizationId);

  // Fetch workflows for the current organization
  const { data: workflowsData } = useGetApiV1Workflows({
    filters: currentOrganizationId
      ? JSON.stringify({ organizationId: { $eq: currentOrganizationId } })
      : undefined,
    limit: 100,
  });

  const workflows = workflowsData?.data || [];
  const generateKey = (name: string) => {
    const words = name.split(/\s+/);
    let key = '';

    if (words.length === 1) {
      key = words[0].substring(0, 4);
    } else {
      key = words
        .map((word) => word.charAt(0))
        .join('')
        .substring(0, 4);
    }

    return key.toUpperCase().replace(/[^A-Z0-9]/g, '');
  };

  const handleInputChange = (
    field: keyof (PostApiV1ProjectsBody | PatchApiV1ProjectsIdBody),
    value: string,
  ) => {
    const newData = { ...data, [field]: value };
    if (field === 'name') {
      newData.key = generateKey(value);
    }
    onDataChange(newData);
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('Image size should be less than 5MB');
        return;
      }

      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result as string;
        handleInputChange('icon', base64String);
      };
      reader.readAsDataURL(file);
    }
  };

  const getProjectInitials = () => {
    return data.name ? data.name.charAt(0).toUpperCase() : 'P';
  };

  const animationDelay = isOnboarding ? 0.1 : 0;

  return (
    <div className="space-y-6">
      {/* Project Name and Icon */}
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className="space-y-3"
        initial={{ opacity: 0, y: 20 }}
        transition={{ delay: animationDelay + 0.05 }}
      >
        <div className="flex items-start gap-4">
          {/* Project Icon Upload */}
          <div
            className="group relative flex-shrink-0 cursor-pointer"
            onClick={() => fileInputRef.current?.click()}
          >
            <Avatar className="h-20 w-20 rounded-lg border-2 border-border">
              <AvatarImage alt={data.name || 'Project'} src={data.icon || ''} />
              <AvatarFallback
                className="rounded-lg font-semibold text-lg"
                style={{ backgroundColor: data.color || '' }}
              >
                {getProjectInitials()}
              </AvatarFallback>
            </Avatar>
            <div className="absolute inset-0 flex items-center justify-center rounded-lg bg-foreground/60 opacity-0 transition-opacity group-hover:opacity-100">
              <Camera className="h-6 w-6 text-background" />
            </div>
            <input
              accept="image/*"
              className="hidden"
              onChange={handleFileSelect}
              ref={fileInputRef}
              type="file"
            />
          </div>

          {/* Project Name and Key Input */}
          <div className="flex-1 space-y-2">
            <Label className="flex items-center " htmlFor="project-name">
              Project name
              <Badge className="text-xs" variant="secondary">
                Required
              </Badge>
            </Label>
            <div className="flex gap-3">
              <div className="flex-1">
                <div className="relative">
                  {/* <Hash className="absolute  w-4 h-4 text-muted-foreground" /> */}
                  <Input
                    id="project-name"
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Website Redesign"
                    value={data.name}
                  />
                </div>
              </div>
              <div className="w-24">
                <Input
                  className="text-center font-mono uppercase"
                  maxLength={4}
                  onChange={(e) => handleInputChange('key', e.target.value.toUpperCase())}
                  placeholder="KEY"
                  value={data.key}
                />
              </div>
            </div>

            {/* {data.name && organizationSlug && workspaceSlug && (
              <p className="text-sm text-muted-foreground flex items-center gap-1 animate-in slide-in-from-bottom-2">
                <Link2 className="w-3 h-3" />
                {organizationSlug}/{workspaceSlug}/{data.key || "KEY"}
              </p>
            )} */}
            {/* <p className="text-xs text-muted-foreground">
              Issues will be labeled like {data.key || "KEY"}-1,{" "}
              {data.key || "KEY"}-2
            </p> */}
          </div>
        </div>
      </motion.div>

      {/* Project Methodology */}
      {/* {showMethodology && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: animationDelay }}
          className="space-y-3"
        >
          <Label>How does your team work?</Label>
          <div className="grid grid-cols-2 gap-3">
            {methodologies.map((method) => {
              const Icon = method.icon;
              return (
                <button
                  key={method.value}
                  onClick={() => handleInputChange("methodology", method.value)}
                  className={`p-4 rounded-xl border-2 transition-all ${
                    data.methodology === method.value
                      ? "border-primary bg-secondary"
                      : "border-border hover:border-muted-foreground"
                  }`}
                  type="button"
                >
                  <Icon
                    className={`w-5 h-5 mb-2 ${
                      data.methodology === method.value
                        ? "text-primary"
                        : "text-muted-foreground"
                    }`}
                  />
                  <div className="text-sm font-medium">{method.label}</div>
                </button>
              );
            })}
          </div>
        </motion.div>
      )} */}

      {/* Timeline */}
      {showTimeline && (
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className="space-y-3"
          initial={{ opacity: 0, y: 20 }}
          transition={{ delay: animationDelay + 0.1 }}
        >
          <Label className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Project timeline
            <Badge className="text-xs" variant="outline">
              Optional
            </Badge>
          </Label>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm" htmlFor="proj-start">
                Start date
              </Label>
              <div className="relative">
                <Flag className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                <Input
                  className="pl-10"
                  id="proj-start"
                  onChange={(e) => handleInputChange('startDate', e.target.value)}
                  type="date"
                  value={data.startDate as string}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label className="text-sm" htmlFor="proj-target">
                Target date
              </Label>
              <div className="relative">
                <Target className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                <Input
                  className="pl-10"
                  id="proj-target"
                  onChange={(e) => handleInputChange('targetDate', e.target.value)}
                  type="date"
                  value={data.targetDate as string}
                />
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* workspace Selection */}
      {!isOnboarding && (
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className="space-y-3"
          initial={{ opacity: 0, y: 20 }}
          transition={{ delay: animationDelay + 0.1 }}
        >
          <Label className="flex items-center gap-2">
            <FolderKanban className="h-4 w-4" />
            Workspace
          </Label>
          <Select value={data.workspaceId}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a workspace" />
            </SelectTrigger>
            <SelectContent>
              {workspaces.map((workspace) => (
                <SelectItem
                  key={workspace.id}
                  onSelect={() => {
                    // Handle workspace selection if needed
                  }}
                  value={workspace.id}
                >
                  {workspace.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </motion.div>
      )}

      {/* Workflow Selection */}
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className="space-y-3"
        initial={{ opacity: 0, y: 20 }}
        transition={{ delay: animationDelay + 0.12 }}
      >
        <Label className="flex items-center gap-2">
          <GitBranch className="h-4 w-4" />
          Workflow
          <Badge className="text-xs" variant="secondary">
            Required
          </Badge>
        </Label>
        <Select
          onValueChange={(value) => handleInputChange('workflowId', value)}
          value={data.workflowId || ''}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select a workflow" />
          </SelectTrigger>
          <SelectContent>
            {workflows.map((workflow) => (
              <SelectItem key={workflow.id} value={workflow.id}>
                {workflow.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {workflows.length === 0 && (
          <p className="text-muted-foreground text-sm">
            No workflows available. Please create a workflow in the organization settings first.
          </p>
        )}
      </motion.div>

      {/* Color Selection */}
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className="space-y-3"
        initial={{ opacity: 0, y: 20 }}
        transition={{ delay: animationDelay + 0.15 }}
      >
        <Label className="flex items-center gap-2">
          <Palette className="h-4 w-4" />
          Project color
        </Label>
        <ColorPicker
          colors={colors.slice(0, 4)}
          onChange={(value) => handleInputChange('color', value)}
          value={data.color || ''}
        />
      </motion.div>

      {/* Visibility */}
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className="space-y-3"
        initial={{ opacity: 0, y: 20 }}
        transition={{ delay: animationDelay + 0.2 }}
      >
        <Label className="flex items-center gap-2">
          <Eye className="h-4 w-4" />
          Who can see this project?
        </Label>
        <RadioGroup
          onValueChange={(value) => handleInputChange('visibility', value as 'public' | 'private')}
          value={data.visibility}
        >
          <div className="grid gap-3">
            <label
              className={`flex cursor-pointer items-start gap-3 rounded-xl border-2 p-4 transition-all ${
                data.visibility === 'private'
                  ? 'border-primary bg-secondary'
                  : 'border-border hover:border-muted-foreground'
              }`}
              htmlFor="project-private"
            >
              <RadioGroupItem className="mt-1" id="project-private" value="private" />
              <div className="flex-1">
                <div className="mb-1 flex items-center gap-2 font-medium">
                  <Lock className="h-4 w-4" />
                  Private
                </div>
                <div className="text-muted-foreground text-sm">Only project members can access</div>
              </div>
            </label>

            <label
              className={`flex cursor-pointer items-start gap-3 rounded-xl border-2 p-4 transition-all ${
                data.visibility === 'public'
                  ? 'border-primary bg-secondary'
                  : 'border-border hover:border-muted-foreground'
              }`}
              htmlFor="project-public"
            >
              <RadioGroupItem className="mt-1" id="project-public" value="public" />
              <div className="flex-1">
                <div className="mb-1 flex items-center gap-2 font-medium">
                  <Globe2 className="h-4 w-4" />
                  Public
                </div>
                <div className="text-muted-foreground text-sm">
                  Anyone in your workspace can view
                </div>
              </div>
            </label>
          </div>
        </RadioGroup>
      </motion.div>

      {/* Description */}
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className="space-y-3"
        initial={{ opacity: 0, y: 20 }}
        transition={{ delay: animationDelay + 0.25 }}
      >
        <Label htmlFor="project-description">Description (optional)</Label>
        <Textarea
          className="h-24 resize-none"
          id="project-description"
          onChange={(e) => handleInputChange('description', e.target.value)}
          placeholder="What is this project about?"
          value={data.description || ''}
        />
      </motion.div>
    </div>
  );
}
