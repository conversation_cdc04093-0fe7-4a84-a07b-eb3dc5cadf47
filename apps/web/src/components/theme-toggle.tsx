import { SidebarMenuButton } from '@repo/ui/components/sidebar';
import { Moon, Sun } from 'lucide-react';
import { useTheme } from '../hooks/use-theme';

export function ThemeToggle() {
  const { theme, toggleTheme } = useTheme();

  return (
    <SidebarMenuButton
      className="px-2.5 md:px-2"
      onClick={toggleTheme}
      tooltip={{
        children: theme === 'light' ? 'Dark mode' : 'Light mode',
        hidden: false,
      }}
    >
      {theme === 'light' ? <Sun className="size-4" /> : <Moon className="size-4" />}
    </SidebarMenuButton>
  );
}
