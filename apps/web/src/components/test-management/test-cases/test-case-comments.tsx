import { Button } from '@repo/ui/components/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@repo/ui/components/dropdown-menu';
import { Textarea } from '@repo/ui/components/textarea';
import { cn } from '@repo/ui/lib/utils';
import { useQueryClient } from '@tanstack/react-query';
import { formatDistanceToNow } from 'date-fns';
import { Edit2, MoreHorizontal, Reply, Trash2 } from 'lucide-react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import Man from '@/web/assets/man.png';
import type { TestCaseComment, User } from '@/web/services/hooks.schemas';
import {
  useDeleteApiV1TestCaseCommentsId,
  useGetApiV1TestCaseComments,
  usePatchApiV1TestCaseCommentsId,
  usePostApiV1TestCaseComments,
} from '@/web/services/test-case-comments';
import { useGetApiV1Users } from '@/web/services/users';
import { useRootStore } from '@/web/store/store';

interface TestCaseCommentsProps {
  testCaseId: string;
}

interface CommentWithReplies extends TestCaseComment {
  replies?: TestCaseComment[];
}

interface CommentItemProps {
  comment: CommentWithReplies;
  onReply: (parentId: string) => void;
  onRefresh: () => void;
  depth?: number;
  usersMap: Map<string, User>;
}

// Extract user IDs from content
const extractMentionedUserIds = (content: string): string[] => {
  const regex = /@\[user:([a-f0-9-]{36})\]/g;
  const userIds: string[] = [];
  let match;
  while ((match = regex.exec(content)) !== null) {
    userIds.push(match[1]);
  }
  return [...new Set(userIds)]; // Remove duplicates
};

// Helper function to render content with highlighted mentions
const renderContentWithMentions = (
  content: string,
  users: Map<string, User>,
): React.ReactNode[] => {
  // Split content by mention pattern and render
  const parts = content.split(/(@\[user:[a-f0-9-]{36}\])/g);

  return parts.map((part, index) => {
    const mentionMatch = part.match(/@\[user:([a-f0-9-]{36})\]/);
    if (mentionMatch) {
      const userId = mentionMatch[1];
      const user = users.get(userId);
      const displayName = user ? user.displayName || user.email || 'Unknown User' : 'Unknown User';

      return (
        <span className="rounded px-1 font-bold text-primary" key={index}>
          @{displayName}
        </span>
      );
    }
    return <span key={index}>{part}</span>;
  });
};

const CommentItem: React.FC<CommentItemProps> = ({
  comment,
  onReply,
  onRefresh,
  depth = 0,
  usersMap,
}) => {
  const currentUser = useRootStore((state) => state.currentUser);
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(comment.content);

  const updateCommentMutation = usePatchApiV1TestCaseCommentsId({
    mutation: {
      onSuccess: () => {
        setIsEditing(false);
        onRefresh();
      },
    },
  });

  const deleteCommentMutation = useDeleteApiV1TestCaseCommentsId({
    mutation: {
      onSuccess: () => {
        onRefresh();
      },
    },
  });

  const handleUpdate = () => {
    updateCommentMutation.mutate({
      id: comment.id,
      data: { content: editContent, isEdited: true },
    });
  };

  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this comment?')) {
      deleteCommentMutation.mutate({ id: comment.id });
    }
  };

  const isOwner = currentUser?.id === comment.authorId;

  return (
    <div className={cn('flex gap-3', depth > 0 && 'mt-3 ml-12')}>
      <img
        alt={comment.author?.displayName || 'User'}
        className="h-8 w-8 flex-shrink-0 rounded-full"
        src={comment.author?.avatarUrl || Man}
      />
      <div className="flex-1">
        <div className="rounded-lg bg-muted p-3">
          <div className="mb-2 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="font-medium text-sm">
                {comment.author?.displayName || comment.author?.email || 'Unknown User'}
              </span>
              <span className="text-muted-foreground text-xs">
                {formatDistanceToNow(new Date(comment.createdAt), {
                  addSuffix: true,
                })}
              </span>
              {comment.isEdited && <span className="text-muted-foreground text-xs">(edited)</span>}
            </div>
            {isOwner && !comment.isDeleted && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button className="h-6 w-6 p-0" size="sm" variant="ghost">
                    <MoreHorizontal className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setIsEditing(true)}>
                    <Edit2 className="mr-2 h-3 w-3" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-destructive" onClick={handleDelete}>
                    <Trash2 className="mr-2 h-3 w-3" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
          {isEditing ? (
            <div className="space-y-2">
              <Textarea
                className="min-h-[60px]"
                onChange={(e) => setEditContent(e.target.value)}
                value={editContent}
              />
              <div className="flex gap-2">
                <Button
                  disabled={updateCommentMutation.isPending}
                  onClick={handleUpdate}
                  size="sm"
                  type="button"
                >
                  {updateCommentMutation.isPending ? 'Saving...' : 'Save'}
                </Button>
                <Button
                  onClick={() => {
                    setIsEditing(false);
                    setEditContent(comment.content);
                  }}
                  size="sm"
                  variant="outline"
                >
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            <>
              {comment.isDeleted ? (
                <p className="text-muted-foreground text-sm italic">
                  This comment has been deleted
                </p>
              ) : (
                <div className="text-sm">
                  {renderContentWithMentions(comment.content, usersMap)}
                </div>
              )}
              {!comment.isDeleted && depth === 0 && (
                <Button
                  className="mt-2 h-7 text-xs"
                  onClick={() => onReply(comment.id)}
                  size="sm"
                  type="button"
                  variant="ghost"
                >
                  <Reply className="mr-1 h-3 w-3" />
                  Reply
                </Button>
              )}
            </>
          )}
        </div>
        {comment.replies && comment.replies.length > 0 && (
          <div className="mt-2">
            {comment.replies.map((reply: TestCaseComment) => (
              <CommentItem
                comment={reply}
                depth={depth + 1}
                key={reply.id}
                onRefresh={onRefresh}
                onReply={onReply}
                usersMap={usersMap}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export const TestCaseComments: React.FC<TestCaseCommentsProps> = ({ testCaseId }) => {
  const queryClient = useQueryClient();
  const currentUser = useRootStore((state) => state.currentUser);
  const currentProjectId = useRootStore((state) => state.currentProjectId);

  const [visualContent, setVisualContent] = useState(''); // What user sees
  const [replyToId, setReplyToId] = useState<string | null>(null);
  const [showMentions, setShowMentions] = useState(false);
  const [mentionSearch, setMentionSearch] = useState('');
  const [mentionIndex, setMentionIndex] = useState(0);
  const [cursorPosition, setCursorPosition] = useState(0);
  const [mentionPositions, setMentionPositions] = useState<
    Map<number, { userId: string; length: number }>
  >(new Map());
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const { data: commentsData, refetch } = useGetApiV1TestCaseComments(
    {
      filters: JSON.stringify({ testCaseId: { $eq: testCaseId } }),
      limit: 100,
    },
    {
      query: {
        enabled: !!testCaseId,
      },
    },
  );

  // Get users for mentions - you might want to filter by project/workspace
  const { data: usersData } = useGetApiV1Users(undefined, {
    query: {
      enabled: !!currentProjectId,
    },
  });

  const createCommentMutation = usePostApiV1TestCaseComments({
    mutation: {
      onSuccess: async () => {
        setVisualContent('');
        setMentionPositions(new Map());
        setReplyToId(null);
        refetch();

        // Invalidate test case history query to reflect the new comment activity
        await queryClient.invalidateQueries({
          queryKey: ['test-case-history', testCaseId],
        });
      },
    },
  });

  // Convert visual content to content with UUIDs
  const convertToActualContent = (visual: string): string => {
    let result = visual;

    // Sort positions by index in descending order to avoid position shifts
    const sortedPositions = Array.from(mentionPositions.entries()).sort(([a], [b]) => b - a);

    for (const [position, { userId, length }] of sortedPositions) {
      const mentionEnd = position + length;
      if (mentionEnd <= result.length) {
        const before = result.substring(0, position);
        const after = result.substring(mentionEnd);
        result = `${before}@[user:${userId}]${after}`;
      }
    }

    return result;
  };

  const handleSubmitComment = (e?: React.FormEvent) => {
    e?.preventDefault();
    e?.stopPropagation();

    const contentToSubmit = visualContent.trim();
    if (!(contentToSubmit && currentUser)) {
      return;
    }

    // Convert visual content to actual content with UUIDs
    const actualContent = convertToActualContent(contentToSubmit);

    createCommentMutation.mutate({
      data: {
        testCaseId,
        content: actualContent,
        authorId: currentUser.id,
        parentId: replyToId || undefined,
      },
    });
  };

  const allComments = commentsData?.data || [];
  const rootComments = allComments.filter((c) => !c.parentId);

  // Build comment tree
  const commentTree = rootComments.map((comment) => {
    const replies = allComments.filter((c) => c.parentId === comment.id);
    return { ...comment, replies };
  });

  // Extract all mentioned user IDs from all comments
  const allMentionedUserIds = new Set<string>();
  allComments.forEach((comment) => {
    extractMentionedUserIds(comment.content).forEach((id) => allMentionedUserIds.add(id));
  });

  // Build users map for efficient lookup
  const usersMap = new Map<string, User>();
  usersData?.data?.forEach((user) => {
    usersMap.set(user.id, user);
  });

  // Filter users based on mention search
  const filteredUsers = useMemo(() => {
    return (
      usersData?.data
        ?.filter((user) => {
          const searchTerm = mentionSearch.toLowerCase();
          return (
            user.displayName?.toLowerCase().includes(searchTerm) ||
            user.email?.toLowerCase().includes(searchTerm) ||
            user.firstName?.toLowerCase().includes(searchTerm) ||
            user.lastName?.toLowerCase().includes(searchTerm)
          );
        })
        .slice(0, 5) || []
    );
  }, [usersData?.data, mentionSearch]);

  // Handle text input changes for mention detection
  const handleTextChange = (value: string) => {
    setVisualContent(value);

    if (textareaRef.current) {
      const cursorPos = textareaRef.current.selectionStart;
      setCursorPosition(cursorPos);

      // Check for @ symbol
      const textBeforeCursor = value.substring(0, cursorPos);
      const lastAtIndex = textBeforeCursor.lastIndexOf('@');

      if (
        (lastAtIndex !== -1 && lastAtIndex === textBeforeCursor.length - 1) ||
        (lastAtIndex !== -1 && /^@\w*$/.test(textBeforeCursor.substring(lastAtIndex)))
      ) {
        const search = textBeforeCursor.substring(lastAtIndex + 1);
        setMentionSearch(search);
        setShowMentions(true);
        setMentionIndex(0);
      } else {
        setShowMentions(false);
      }
    }
  };

  // Handle mention selection
  const selectMention = useCallback(
    (user: User) => {
      if (textareaRef.current) {
        const cursorPos = cursorPosition;
        const textBeforeCursor = visualContent.substring(0, cursorPos);
        const lastAtIndex = textBeforeCursor.lastIndexOf('@');

        if (lastAtIndex !== -1) {
          const beforeMention = visualContent.substring(0, lastAtIndex);
          const afterMention = visualContent.substring(cursorPos);
          const mentionText = `@${user.displayName || user.email}`;
          const newVisualText = `${beforeMention + mentionText} ${afterMention}`;

          // Update visual content
          setVisualContent(newVisualText);

          // Track mention position for conversion later
          const newPositions = new Map(mentionPositions);
          newPositions.set(lastAtIndex, {
            userId: user.id,
            length: mentionText.length,
          });
          setMentionPositions(newPositions);

          setShowMentions(false);

          // Set cursor position after mention
          setTimeout(() => {
            if (textareaRef.current) {
              const newCursorPos = lastAtIndex + mentionText.length + 1;
              textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
              textareaRef.current.focus();
            }
          }, 0);
        }
      }
    },
    [cursorPosition, visualContent, mentionPositions],
  );

  // Handle keyboard navigation for mentions
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (showMentions && filteredUsers.length > 0) {
        if (e.key === 'ArrowDown') {
          e.preventDefault();
          setMentionIndex((prev) => (prev + 1) % filteredUsers.length);
        } else if (e.key === 'ArrowUp') {
          e.preventDefault();
          setMentionIndex((prev) => (prev - 1 + filteredUsers.length) % filteredUsers.length);
        } else if (e.key === 'Enter') {
          e.preventDefault();
          selectMention(filteredUsers[mentionIndex]);
        } else if (e.key === 'Escape') {
          setShowMentions(false);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [showMentions, filteredUsers, mentionIndex, selectMention]);

  return (
    <div className="flex h-full flex-col">
      {/* Scrollable Comments Area */}
      <div className="min-h-0 flex-1 space-y-4 overflow-auto px-4 py-4">
        {rootComments.length === 0 && (
          <p className="py-8 text-center text-muted-foreground text-sm">
            No comments yet. Be the first to comment!
          </p>
        )}

        {commentTree.map((comment) => (
          <CommentItem
            comment={comment}
            key={comment.id}
            onRefresh={() => refetch()}
            onReply={(parentId) => {
              setReplyToId(parentId);
              textareaRef.current?.focus();
            }}
            usersMap={usersMap}
          />
        ))}
      </div>

      {/* Fixed Add Comment Section */}
      <div className="flex-shrink-0 border-t bg-background p-4">
        <div className="flex gap-3">
          <img
            alt={currentUser?.displayName || 'User'}
            className="h-8 w-8 flex-shrink-0 rounded-full"
            src={currentUser?.avatarUrl || Man}
          />
          <div className="flex-1 space-y-2">
            <div className="mb-2 flex items-center gap-2">
              {replyToId && (
                <span className="text-muted-foreground text-sm">Replying to comment...</span>
              )}
              <span className="ml-auto text-muted-foreground text-xs">Use @ to mention users</span>
            </div>

            <div className="relative">
              <Textarea
                className="min-h-[80px]"
                onChange={(e) => handleTextChange(e.target.value)}
                placeholder={replyToId ? 'Write a reply...' : 'Write a comment...'}
                ref={textareaRef}
                value={visualContent}
              />
              {showMentions && filteredUsers.length > 0 && (
                <div className="absolute right-0 bottom-full left-0 z-50 mb-2 max-h-48 overflow-y-auto rounded-md border bg-background shadow-lg">
                  {filteredUsers.map((user, index) => (
                    <button
                      className={cn(
                        'flex w-full items-center gap-2 px-3 py-2 text-left hover:bg-muted',
                        index === mentionIndex && 'bg-muted',
                      )}
                      key={user.id}
                      onClick={() => selectMention(user)}
                      onMouseEnter={() => setMentionIndex(index)}
                      type="button"
                    >
                      <img
                        alt={user.displayName || 'User'}
                        className="h-6 w-6 rounded-full"
                        src={user.avatarUrl || Man}
                      />
                      <div className="min-w-0 flex-1">
                        <p className="truncate font-medium text-sm">
                          {user.displayName || user.email}
                        </p>
                        {user.displayName && (
                          <p className="truncate text-muted-foreground text-xs">{user.email}</p>
                        )}
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>

            <div className="flex gap-2">
              <Button
                disabled={!visualContent.trim() || createCommentMutation.isPending}
                onClick={handleSubmitComment}
                size="sm"
                type="button"
              >
                {createCommentMutation.isPending ? 'Posting...' : replyToId ? 'Reply' : 'Comment'}
              </Button>
              {(visualContent.trim() || replyToId) && (
                <Button
                  onClick={() => {
                    setVisualContent('');
                    setMentionPositions(new Map());
                    setReplyToId(null);
                  }}
                  size="sm"
                  type="button"
                  variant="outline"
                >
                  Cancel
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
