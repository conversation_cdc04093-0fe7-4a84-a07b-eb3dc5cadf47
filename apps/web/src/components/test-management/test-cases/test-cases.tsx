import { Button } from '@repo/ui/components/button';
import { Confirmation } from '@repo/ui/components/custom/confirmDialog';
import CustomDataGrid, { type MuiColumn } from '@repo/ui/components/custom/customDataGrid';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@repo/ui/components/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { useToast } from '@repo/ui/components/use-toast';
import { Edit, PlusIcon, Trash2 } from 'lucide-react';
import { useState } from 'react';
import type { TestCase, TestSuite } from '@/web/services/hooks.schemas';
import { useGetApiV1Priorities } from '@/web/services/priorities';
import { useGetApiV1Statuses } from '@/web/services/statuses';
import {
  type TestCaseWithSuiteProperties,
  useDeleteApiV1TestSuitesIdTestCasesTestCaseId,
  useGetApiV1TestSuitesIdTestCases,
  usePatchApiV1TestSuitesIdTestCasesTestCaseId,
} from '@/web/services/test-suite-test-cases';
import { useRootStore } from '@/web/store/store';
import { LinkTestCasesDialog } from './link-test-cases-dialog';
import { TestCaseForm } from './test-case-form';
import TestCaseView from './test-case-view';

interface TestCasesProps {
  testSuite: TestSuite;
}

export const TestCases: React.FC<TestCasesProps> = ({ testSuite }) => {
  const { toast } = useToast();
  const { data: testCasesData, refetch: refetchTestCases } = useGetApiV1TestSuitesIdTestCases(
    testSuite.id,
  );
  const deleteTestCaseFromSuite = useDeleteApiV1TestSuitesIdTestCasesTestCaseId();
  const updateTestCaseInSuite = usePatchApiV1TestSuitesIdTestCasesTestCaseId();

  const currentOrganizationId = useRootStore((state) => state.currentOrganizationId);

  const { data: statusesData } = useGetApiV1Statuses({
    filters: JSON.stringify({
      organizationId: {
        $or: {
          $eq: currentOrganizationId,
          $null: true,
        },
      },
    }),
  });

  const { data: prioritiesData } = useGetApiV1Priorities({
    filters: JSON.stringify({
      organizationId: {
        $or: {
          $eq: currentOrganizationId,
          $null: true,
        },
      },
    }),
  });

  // Convert the array response to match the expected format
  const testCases = testCasesData
    ? { data: testCasesData, meta: { totalItems: testCasesData.length } }
    : undefined;

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isLinkDialogOpen, setIsLinkDialogOpen] = useState(false);
  const [testCaseToEdit, setTestCaseToEdit] = useState<TestCaseWithSuiteProperties | null>(null);
  const [testCaseToView, setTestCaseToView] = useState<TestCaseWithSuiteProperties | null>(null);

  const handleCreateTestCase = () => {
    setTestCaseToEdit(null);
    setIsCreateDialogOpen(true);
  };

  const handleEditTestCase = (testCase: TestCaseWithSuiteProperties) => {
    setTestCaseToEdit(testCase);
    setIsCreateDialogOpen(true);
  };

  const handleViewTestCase = (testCase: TestCaseWithSuiteProperties) => {
    setTestCaseToView(testCase);
  };

  const handleClose = () => {
    setIsCreateDialogOpen(false);
    setTestCaseToEdit(null);
    refetchTestCases();
  };

  const handleStatusChange = async (testCaseId: string, statusId: string | null) => {
    try {
      await updateTestCaseInSuite.mutateAsync({
        id: testSuite.id,
        testCaseId,
        data: { statusId },
      });
      toast({
        title: 'Success',
        description: 'Test case status updated successfully',
      });
      refetchTestCases();
    } catch (error) {
      console.error(error);
      toast({
        title: 'Error',
        description: 'Failed to update test case status',
        variant: 'destructive',
      });
    }
  };

  const handlePriorityChange = async (testCaseId: string, priorityId: string | null) => {
    try {
      await updateTestCaseInSuite.mutateAsync({
        id: testSuite.id,
        testCaseId,
        data: { priorityId },
      });
      toast({
        title: 'Success',
        description: 'Test case priority updated successfully',
      });
      refetchTestCases();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error?.message || 'Failed to update test case priority',
        variant: 'destructive',
      });
    }
  };

  const columns: MuiColumn<TestCaseWithSuiteProperties>[] = [
    {
      field: 'title',
      headerName: 'Title',
      width: 200,
      sortable: true,
      renderCell: (params) => (
        <span
          className=" cursor-pointer hover:underline"
          onClick={() => handleViewTestCase(params.row.original)}
        >
          {params.row.original.title || 'Untitled'}
        </span>
      ),
    },
    {
      field: 'suiteStatus',
      headerName: 'Suite Status',
      width: 150,
      sortable: true,
      renderCell(params) {
        const testCase = params.row.original;
        return (
          <Select
            disabled={updateTestCaseInSuite.isPending}
            onValueChange={(value) =>
              handleStatusChange(testCase.id, value === '__no_status__' ? null : value)
            }
            value={testCase.suiteStatus?.id || '__no_status__'}
          >
            <SelectTrigger className="h-8 w-full border-0 bg-transparent p-1 text-xs hover:bg-muted">
              <SelectValue placeholder="No status">
                {testCase.suiteStatus?.name || 'No status'}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="__no_status__">No status</SelectItem>
              {statusesData?.data.map((status) => (
                <SelectItem key={status.id} value={status.id}>
                  {status.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      },
    },
    {
      field: 'suitePriority',
      headerName: 'Suite Priority',
      width: 150,
      sortable: true,
      renderCell(params) {
        const testCase = params.row.original;
        return (
          <Select
            disabled={updateTestCaseInSuite.isPending}
            onValueChange={(value) =>
              handlePriorityChange(testCase.id, value === '__no_priority__' ? null : value)
            }
            value={testCase.suitePriority?.id || '__no_priority__'}
          >
            <SelectTrigger className="h-8 w-full border-0 bg-transparent p-1 text-xs hover:bg-muted">
              <SelectValue placeholder="No priority">
                {testCase.suitePriority?.name || 'No priority'}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="__no_priority__">No priority</SelectItem>
              {prioritiesData?.data.map((priority) => (
                <SelectItem key={priority.id} value={priority.id}>
                  {priority.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      },
    },
    {
      field: 'id',
      headerName: 'Actions',
      width: 200,
      sortable: false,
      renderCell: (params) => (
        <div className="flex space-x-2">
          <Button onClick={() => handleEditTestCase(params.row.original)} size="sm" variant="ghost">
            <Edit className="h-4 w-4" />
          </Button>
          <Confirmation
            cancelButton={{
              name: 'Cancel',
              onClick: () => {},
              variant: 'secondary',
            }}
            confirmButton={{
              name: 'Remove',
              onClick: async () => {
                try {
                  await deleteTestCaseFromSuite.mutateAsync({
                    id: testSuite.id,
                    testCaseId: params.row.original.id,
                  });
                  toast({
                    title: 'Success',
                    description: 'Test case removed from suite successfully',
                  });
                  refetchTestCases();
                } catch (error) {
                  let errorMessage = 'Failed to remove test case from suite';
                  if (error && typeof error === 'object') {
                    // The axios interceptor returns error.response.data directly
                    const errorData = error as {
                      message?: string;
                      error?: string;
                    };
                    errorMessage = errorData.message || errorData.error || errorMessage;
                  }

                  toast({
                    title: 'Error',
                    description: errorMessage,
                    variant: 'destructive',
                  });
                }
              },
              variant: 'destructive',
            }}
            description="This will remove the test case from this test suite. The test case will still exist and can be added back later."
            title="Remove from Suite?"
          >
            <Button size="sm" variant="ghost">
              <Trash2 className="h-4 w-4" />
            </Button>
          </Confirmation>
        </div>
      ),
    },
  ];

  return (
    <div className="h-[50rem] p-4">
      <div className="mb-4 flex items-center justify-between">
        <div>
          <h1 className="font-semibold text-xl">{testSuite.name}</h1>
          <p className="text-muted-foreground">
            {testSuite.description || 'Manage test cases for this suite'}
          </p>
          <p className="text-muted-foreground text-sm">
            Status and Priority are suite-specific - the same test case can have different values in
            different suites.
          </p>
        </div>
      </div>
      <CustomDataGrid<TestCaseWithSuiteProperties>
        addButton={
          <div className="flex gap-2">
            <Button onClick={() => setIsLinkDialogOpen(true)} size="sm" variant="outline">
              <PlusIcon className="mr-2" size={16} />
              Link Existing
            </Button>
            <Button onClick={handleCreateTestCase} size="sm" variant="default">
              <PlusIcon className="mr-2" size={16} />
              Create New
            </Button>
          </div>
        }
        autoHeight={false}
        checkboxSelection
        columns={columns}
        loading={!testCases}
        pageSize={10}
        pageSizeOptions={[5, 10, 20, 50]}
        rows={testCases?.data || []}
      />

      {/* Create/Edit Dialog */}
      {isCreateDialogOpen && (
        <Dialog onOpenChange={setIsCreateDialogOpen} open={isCreateDialogOpen}>
          <DialogContent className="min-h-[80vh] max-w-4xl">
            <DialogHeader>
              <DialogTitle>{testCaseToEdit ? 'Edit Test Case' : 'Create Test Case'}</DialogTitle>
            </DialogHeader>
            <TestCaseForm
              editPayload={
                testCaseToEdit
                  ? ({
                      id: testCaseToEdit.id,
                      title: testCaseToEdit.title,
                      description: testCaseToEdit.description,
                      preconditions: testCaseToEdit.preconditions,
                      steps: testCaseToEdit.steps,
                      tags: testCaseToEdit.tags,
                      estimate: testCaseToEdit.estimate,
                      order: testCaseToEdit.order,
                      isActive: testCaseToEdit.isActive,
                      createdBy: testCaseToEdit.createdBy,
                      createdAt: testCaseToEdit.createdAt,
                      updatedAt: testCaseToEdit.updatedAt,
                      projectId: testCaseToEdit.projectId,
                      assigneeId: testCaseToEdit.assigneeId,
                      reporterId: testCaseToEdit.reporterId,
                      workItemId: testCaseToEdit.workItemId,
                      copiedFromId: testCaseToEdit.copiedFromId,
                      testSuiteId: testSuite.id,
                      statusId: testCaseToEdit.suiteStatus?.id || null,
                      priorityId: testCaseToEdit.suitePriority?.id || null,
                    } as TestCase)
                  : null
              }
              onCancel={handleClose}
              onSubmit={handleClose}
              testSuiteId={testSuite.id}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* View Test Case Dialog */}
      {testCaseToView && (
        <Dialog onOpenChange={() => setTestCaseToView(null)} open={!!testCaseToView}>
          <DialogContent className="max-h-[80vh] max-w-4xl ">
            <DialogHeader>
              <DialogTitle className="flex items-center justify-between">
                <span>{testCaseToView.title}</span>
              </DialogTitle>
            </DialogHeader>
            <TestCaseView testCaseId={testCaseToView.id} />
          </DialogContent>
        </Dialog>
      )}

      {/* Link Test Cases Dialog */}
      <LinkTestCasesDialog
        onOpenChange={setIsLinkDialogOpen}
        onSuccess={refetchTestCases}
        open={isLinkDialogOpen}
        testSuiteId={testSuite.id}
      />
    </div>
  );
};
