import { insertTestCaseSchema, patchTestCaseSchema } from '@repo/db/schema';
import { But<PERSON> } from '@repo/ui/components/button';
import { DialogFooter } from '@repo/ui/components/dialog';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@repo/ui/components/tabs';
import { Textarea } from '@repo/ui/components/textarea';
import { RichTextEditor } from '@repo/ui/components/tiptap/rich-text-editor-controlled';
import { useForm } from '@tanstack/react-form';
import { useQueryClient } from '@tanstack/react-query';
import { useRef, useState } from 'react';
import { toast } from 'sonner';
import type {
  PatchApiV1TestCasesIdBody,
  PostApiV1TestCasesBody,
  TestCase,
} from '@/web/services/hooks.schemas';
import { useGetApiV1Priorities } from '@/web/services/priorities';
import { useGetApiV1Statuses } from '@/web/services/statuses';
import { usePatchApiV1TestCasesId, usePostApiV1TestCases } from '@/web/services/test-cases';
import { usePostApiV1TestSuitesIdTestCasesLink } from '@/web/services/test-suite-test-cases';
import { useRootStore } from '@/web/store/store';
import {
  type PendingFile,
  TestCaseAttachments,
  type TestCaseAttachmentsRef,
} from './test-case-attachments';
import { TestCaseComments } from './test-case-comments';
import { TestCaseHistory } from './test-case-history';
import { TestCaseLinks } from './test-case-links';

interface TestCaseFormProps {
  testSuiteId?: string;
  editPayload?: TestCase | null;
  onSubmit?: () => void;
  onCancel?: () => void;
}

export const TestCaseForm: React.FC<TestCaseFormProps> = ({
  testSuiteId,
  editPayload: testCase,

  onSubmit,
  onCancel,
}) => {
  const queryClient = useQueryClient();
  const createTestCaseMutation = usePostApiV1TestCases();
  const updateTestCaseMutation = usePatchApiV1TestCasesId();
  const linkToSuiteMutation = usePostApiV1TestSuitesIdTestCasesLink();

  const currentOrganizationId = useRootStore((state) => state.currentOrganizationId);

  const { data: prioritiesData } = useGetApiV1Priorities({
    filters: JSON.stringify({
      organizationId: {
        $or: {
          $eq: currentOrganizationId,
          $null: true,
        },
      },
    }),
  });

  const { data: statusesData } = useGetApiV1Statuses({
    filters: JSON.stringify({
      organizationId: {
        $or: {
          $eq: currentOrganizationId,
          $null: true,
        },
      },
    }),
  });

  const currentProjectId = useRootStore((state) => state.currentProjectId);
  const isEditMode = !!testCase;
  const schema = isEditMode ? patchTestCaseSchema : insertTestCaseSchema;

  const [pendingFiles, setPendingFiles] = useState<PendingFile[]>([]);
  const [pendingDeletions, setPendingDeletions] = useState<string[]>([]);
  const attachmentsRef = useRef<TestCaseAttachmentsRef>(null);

  const defaultValues: PostApiV1TestCasesBody | PatchApiV1TestCasesIdBody =
    (testCase as PatchApiV1TestCasesIdBody) || {
      title: '',
      description: '',
      preconditions: '',
      priorityId: null,
      statusId: null,
      projectId: currentProjectId!,
    };

  const form = useForm({
    defaultValues,
    validators: {
      onSubmit: schema,
    },
    onSubmit: async ({ value }) => {
      try {
        // Clean up the payload to remove undefined values
        const payload: any = {
          ...value,
        };

        // Remove undefined values
        Object.keys(payload).forEach((key) => {
          if (payload[key as keyof typeof payload] === undefined) {
            delete payload[key as keyof typeof payload];
          }
        });

        if (isEditMode) {
          await updateTestCaseMutation.mutateAsync({
            id: testCase.id,
            data: payload as PatchApiV1TestCasesIdBody,
          });

          if (pendingFiles.length > 0) {
            try {
              await attachmentsRef.current?.uploadFiles(testCase.id);
            } catch (uploadError: any) {
              toast.error(
                uploadError?.message || 'Test case updated but some attachments failed to upload',
              );
            }
          }

          if (pendingDeletions.length > 0) {
            try {
              await attachmentsRef.current?.deleteAttachments();
            } catch (deleteError: any) {
              toast.error(deleteError?.message || 'Some attachments could not be deleted');
            }
          }

          toast.success('Test case updated successfully');
          queryClient.invalidateQueries({
            queryKey: ['getApiV1TestCases'],
          });
          onSubmit?.();
        } else {
          const response = await createTestCaseMutation.mutateAsync({
            data: payload as PostApiV1TestCasesBody,
          });

          // The response type is PostApiV1TestCases201 which has data.id
          const testCaseId = response.data.id;

          // If we have a testSuiteId, link the test case to the suite
          if (testSuiteId && testCaseId) {
            try {
              await linkToSuiteMutation.mutateAsync({
                id: testSuiteId,
                data: {
                  testCaseIds: [testCaseId],
                  statusId: payload.statusId,
                  priorityId: payload.priorityId,
                },
              });
            } catch (linkError) {
              console.log('linkError', linkError);
              toast.error('Test case created but failed to link to suite');
            }
          }

          if (pendingFiles.length > 0 && testCaseId) {
            try {
              await attachmentsRef.current?.uploadFiles(testCaseId);
            } catch (_uploadError) {
              console.log('_uploadError', _uploadError);
              toast.error('Test case created but some attachments failed to upload');
            }
          }

          toast.success('Test case created successfully');
          queryClient.invalidateQueries({
            queryKey: ['getApiV1TestCases'],
          });
          queryClient.invalidateQueries({
            queryKey: [`/api/v1/test-suites/${testSuiteId}/test-cases`],
          });
          onSubmit?.();
        }
      } catch (error: any) {
        if (error?.issues) {
          const firstError = error.issues[0];
          toast.error(`Validation error: ${firstError.path.join('.')} - ${firstError.message}`);
        } else {
          toast.error('Failed to save test case');
        }
      }
    },
  });

  return (
    <>
      <div className="flex flex-1 flex-col overflow-hidden">
        <form
          className="flex flex-1 flex-col overflow-hidden"
          id="test-case-form"
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
        >
          {/* Main content container */}
          <div className="flex flex-1 overflow-hidden">
            {/* Left side - Main content area */}
            <div className="flex w-full flex-1 flex-col overflow-auto">
              {/* Details section - Always visible */}
              <div className="border-b p-4">
                <h3 className="mb-4 font-semibold text-lg">Test Case Details</h3>
                <div className="flex-col space-y-3">
                  <form.Field name="title">
                    {(field) => (
                      <FormField field={field} required>
                        <FormItem>
                          <FormLabel>Title</FormLabel>
                          <FormControl>
                            <Input
                              onBlur={field.handleBlur}
                              onChange={(e) => field.handleChange(e.target.value)}
                              placeholder="Test Case Title"
                              value={field.state.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      </FormField>
                    )}
                  </form.Field>
                  <form.Field name="description">
                    {(field) => (
                      <FormField field={field}>
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <div className="h-100 max-w-full">
                              <RichTextEditor
                                className="rounded-md"
                                onChange={(value) => field.handleChange(value)}
                                value={field.state.value || ''}
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      </FormField>
                    )}
                  </form.Field>
                </div>
              </div>

              {/* Tabbed content area - Takes remaining space */}
              <div className="flex-1">
                <Tabs className="flex h-full flex-col" defaultValue="details">
                  <div className="bg-muted px-4 pt-2">
                    <TabsList>
                      <TabsTrigger value="details">Details</TabsTrigger>
                      <TabsTrigger value="comments">Comments</TabsTrigger>
                      <TabsTrigger value="history">History</TabsTrigger>
                      <TabsTrigger value="links">Links</TabsTrigger>
                      <TabsTrigger value="attachments">Attachments</TabsTrigger>
                    </TabsList>
                  </div>

                  <div className="flex-1 overflow-hidden">
                    <TabsContent className="h-full overflow-auto p-4" value="details">
                      <div className="space-y-4">
                        <form.Field name="preconditions">
                          {(field) => (
                            <FormField field={field}>
                              <FormItem>
                                <FormLabel>Preconditions</FormLabel>
                                <FormControl>
                                  <Textarea
                                    className="min-h-[100px] resize-none"
                                    onChange={(e) => field.handleChange(e.target.value)}
                                    placeholder="Enter any preconditions for this test case"
                                    value={field.state.value as string}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            </FormField>
                          )}
                        </form.Field>
                      </div>
                    </TabsContent>

                    <TabsContent className="h-full overflow-auto p-4" value="comments">
                      {testCase?.id ? (
                        <TestCaseComments testCaseId={testCase.id} />
                      ) : (
                        <div className="flex h-full items-center justify-center">
                          <p className="text-center text-muted-foreground text-sm">
                            Save the test case to add comments
                          </p>
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent className="h-full overflow-hidden" value="history">
                      {testCase?.id ? (
                        <TestCaseHistory testCaseId={testCase.id} />
                      ) : (
                        <div className="flex h-full items-center justify-center">
                          <p className="text-center text-muted-foreground text-sm">
                            Save the test case to view history
                          </p>
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent className="h-full overflow-auto p-4" value="links">
                      {testCase?.id ? (
                        <TestCaseLinks testCase={testCase} testCaseId={testCase.id} />
                      ) : (
                        <div className="flex h-full items-center justify-center">
                          <p className="text-center text-muted-foreground text-sm">
                            Save the test case to add links
                          </p>
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent className="h-full overflow-auto p-4" value="attachments">
                      <TestCaseAttachments
                        initialPendingDeletions={pendingDeletions}
                        initialPendingFiles={pendingFiles}
                        isEditing={true}
                        onPendingDeletionsChange={setPendingDeletions}
                        onPendingFilesChange={setPendingFiles}
                        ref={attachmentsRef}
                        testCaseId={testCase?.id}
                      />
                    </TabsContent>
                  </div>
                </Tabs>
              </div>
            </div>

            {/* Right sidebar - Properties */}
            <div className="w-60 overflow-auto border-l bg-muted p-4">
              <div className="space-y-4">
                <h3 className="mb-4 font-semibold text-lg">Properties</h3>

                {testSuiteId ? (
                  <>
                    <form.Field name="priorityId">
                      {(field) => (
                        <FormField field={field}>
                          <FormItem>
                            <FormLabel>Suite Priority</FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={field.handleChange}
                                value={field.state.value || ''}
                              >
                                <SelectTrigger className="w-full">
                                  <SelectValue placeholder="Select status" />
                                </SelectTrigger>
                                <SelectContent>
                                  {prioritiesData?.data.map((priority) => (
                                    <SelectItem key={priority.id} value={priority.id}>
                                      {priority.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                            <p className="mt-1 text-muted-foreground text-xs">
                              Priority for this test case in the suite
                            </p>
                          </FormItem>
                        </FormField>
                      )}
                    </form.Field>
                    <form.Field name="statusId">
                      {(field) => (
                        <FormField field={field}>
                          <FormItem>
                            <FormLabel>Suite Status</FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={field.handleChange}
                                value={field.state.value || ''}
                              >
                                <SelectTrigger className="w-full">
                                  <SelectValue placeholder="Select status" />
                                </SelectTrigger>
                                <SelectContent>
                                  {statusesData?.data.map((status) => (
                                    <SelectItem key={status.id} value={status.id}>
                                      {status.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                            <p className="mt-1 text-muted-foreground text-xs">
                              Status for this test case in the suite
                            </p>
                          </FormItem>
                        </FormField>
                      )}
                    </form.Field>
                  </>
                ) : (
                  <div className="text-muted-foreground text-sm">
                    <p>
                      Priority and status can be set at the suite level when this test case is added
                      to a test suite.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </form>
      </div>

      {/* Footer - Outside the main content, always visible */}
      <DialogFooter className="sticky bottom-0 border-t bg-background">
        {onCancel && (
          <Button onClick={onCancel} type="button" variant="outline">
            Cancel
          </Button>
        )}
        <Button
          disabled={createTestCaseMutation.isPending || updateTestCaseMutation.isPending}
          form="test-case-form"
          onClick={() => {}}
          type="submit"
        >
          {createTestCaseMutation.isPending || updateTestCaseMutation.isPending
            ? 'Saving...'
            : isEditMode
              ? 'Update'
              : 'Create'}
        </Button>
      </DialogFooter>
    </>
  );
};
