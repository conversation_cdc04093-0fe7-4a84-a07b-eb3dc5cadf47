import { Button } from '@repo/ui/components/button';
import { DialogFooter } from '@repo/ui/components/dialog';
import { Label } from '@repo/ui/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { useForm } from '@tanstack/react-form';
import { useQueryClient } from '@tanstack/react-query';
import { Loader2, Search } from 'lucide-react';
import { useMemo, useState } from 'react';
import { toast } from 'sonner';
import { z } from 'zod';
import type {
  PostApiV1TestCaseWorkItemLinksBody,
  TestCaseWorkItemLinkLinkType,
} from '@/web/services/hooks.schemas';
import {
  getGetApiV1TestCaseWorkItemLinksQueryKey,
  useGetApiV1TestCaseWorkItemLinks,
  usePostApiV1TestCaseWorkItemLinks,
} from '@/web/services/test-case-work-item-links';
import { useGetApiV1WorkItems } from '@/web/services/work-items';
import { useRootStore } from '@/web/store/store';

interface TestCaseWorkItemLinkFormProps {
  onSubmit?: () => void;
  onCancel?: () => void;
  testCaseId: string;
}

const linkTypes: { value: TestCaseWorkItemLinkLinkType; label: string }[] = [
  { value: 'tests', label: 'Tests' },
  { value: 'tested_by', label: 'Tested by' },
  { value: 'validates', label: 'Validates' },
  { value: 'verifies', label: 'Verifies' },
  { value: 'relates_to', label: 'Relates to' },
];

const formSchema = z.object({
  linkType: z.enum(['tests', 'tested_by', 'validates', 'verifies', 'relates_to']),
  workItemId: z.string().uuid('Please select a work item'),
});

export const TestCaseWorkItemLinkForm: React.FC<TestCaseWorkItemLinkFormProps> = ({
  onSubmit,
  onCancel,
  testCaseId,
}) => {
  const queryClient = useQueryClient();
  const currentUser = useRootStore((state) => state.currentUser);
  const currentProjectId = useRootStore((state) => state.currentProjectId);
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch existing links to prevent duplicates
  const { data: existingLinks } = useGetApiV1TestCaseWorkItemLinks(
    {
      filters: JSON.stringify({ testCaseId: { $eq: testCaseId } }),
      limit: 100,
    },
    {
      query: {
        enabled: !!testCaseId,
      },
    },
  );

  // Fetch work items for the current project
  const { data: workItemsData, isLoading: isLoadingWorkItems } = useGetApiV1WorkItems(
    {
      filters: currentProjectId
        ? JSON.stringify({ projectId: { $eq: currentProjectId } })
        : undefined,
      limit: 100,
    },
    {
      query: {
        enabled: !!currentProjectId,
      },
    },
  );

  const createMutation = usePostApiV1TestCaseWorkItemLinks({
    mutation: {
      onSuccess: () => {
        toast.success('Work item linked successfully');
        queryClient.invalidateQueries({
          queryKey: getGetApiV1TestCaseWorkItemLinksQueryKey(),
        });
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (error) => {
        // Check for unique constraint violation
        if (error.message?.includes('unique constraint')) {
          toast.error('This link already exists');
        } else {
          toast.error(error?.message || 'Failed to create link');
        }
      },
    },
  });

  const form = useForm({
    defaultValues: {
      linkType: 'tests' as TestCaseWorkItemLinkLinkType,
      workItemId: '',
    },
    validators: {
      onChange: formSchema,
    },
    onSubmit: async ({ value }) => {
      if (!currentUser) {
        toast.error('User not authenticated');
        return;
      }

      const data: PostApiV1TestCaseWorkItemLinksBody = {
        testCaseId,
        workItemId: value.workItemId,
        linkType: value.linkType,
        createdById: currentUser.id,
      };

      createMutation.mutate({ data });
    },
  });

  // Filter work items based on search and exclude already linked items
  const filteredWorkItems = useMemo(() => {
    const existingWorkItemIds = new Set(existingLinks?.data?.map((link) => link.workItemId) || []);

    return (
      workItemsData?.data?.filter((item) => {
        // Exclude already linked items
        if (existingWorkItemIds.has(item.id)) {
          return false;
        }

        // Filter by search term
        if (!searchTerm) {
          return true;
        }

        const searchLower = searchTerm.toLowerCase();
        return (
          item.title.toLowerCase().includes(searchLower) ||
          item.id.toLowerCase().includes(searchLower) ||
          item.project?.key?.toLowerCase().includes(searchLower)
        );
      }) || []
    );
  }, [workItemsData?.data, existingLinks?.data, searchTerm]);

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <div className="space-y-6 py-4">
        <div className="space-y-2">
          <Label htmlFor="linkType">Link Type</Label>
          <form.Field name="linkType">
            {(field) => (
              <Select
                onValueChange={(value) => field.setValue(value as TestCaseWorkItemLinkLinkType)}
                value={field.state.value}
              >
                <SelectTrigger id="linkType">
                  <SelectValue placeholder="Select link type" />
                </SelectTrigger>
                <SelectContent>
                  {linkTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </form.Field>
        </div>

        <div className="space-y-2">
          <Label htmlFor="workItem">Work Item</Label>

          {/* Search input */}
          <div className="relative">
            <Search className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
            <input
              className="w-full rounded-md border py-2 pr-3 pl-10 text-sm"
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search work items..."
              type="text"
              value={searchTerm}
            />
          </div>

          <form.Field name="workItemId">
            {(field) => (
              <>
                <Select
                  disabled={isLoadingWorkItems}
                  onValueChange={field.setValue}
                  value={field.state.value}
                >
                  <SelectTrigger id="workItem">
                    <SelectValue
                      placeholder={
                        isLoadingWorkItems ? 'Loading work items...' : 'Select a work item'
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {filteredWorkItems.length === 0 ? (
                      <div className="py-6 text-center text-muted-foreground text-sm">
                        {searchTerm ? 'No work items found' : 'No available work items'}
                      </div>
                    ) : (
                      filteredWorkItems.map((item) => (
                        <SelectItem key={item.id} value={item.id}>
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-muted-foreground">
                              {item.project?.key
                                ? `${item.project.key}-${item.id.slice(0, 8)}`
                                : `WI-${item.id.slice(0, 8)}`}
                            </span>
                            <span className="max-w-[400px] truncate text-sm">{item.title}</span>
                          </div>
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                {field.state.meta.errors && (
                  <p className="mt-1 text-destructive text-sm">
                    {field.state.meta.errors.join(', ')}
                  </p>
                )}
              </>
            )}
          </form.Field>
        </div>
      </div>

      <DialogFooter>
        <Button
          disabled={createMutation.isPending}
          onClick={onCancel}
          type="button"
          variant="outline"
        >
          Cancel
        </Button>
        <Button disabled={createMutation.isPending || !form.state.isValid} type="submit">
          {createMutation.isPending ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Linking...
            </>
          ) : (
            'Link Work Item'
          )}
        </Button>
      </DialogFooter>
    </form>
  );
};
