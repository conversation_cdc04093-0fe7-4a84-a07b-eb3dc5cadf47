import { But<PERSON> } from '@repo/ui/components/button';
import { Checkbox } from '@repo/ui/components/checkbox';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@repo/ui/components/dialog';
import { Input } from '@repo/ui/components/input';
import { Label } from '@repo/ui/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { Skeleton } from '@repo/ui/components/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@repo/ui/components/tabs';
import { useToast } from '@repo/ui/components/use-toast';
import { Search } from 'lucide-react';
import { useState } from 'react';
import { useGetApiV1TestSuitesAllTestCases } from '@/web/services/default';
import { useGetApiV1Priorities } from '@/web/services/priorities';
import { useGetApiV1Statuses } from '@/web/services/statuses';
import {
  useGetApiV1TestSuitesIdAvailableTestCases,
  usePostApiV1TestSuitesIdTestCasesLink,
} from '@/web/services/test-suite-test-cases';

interface LinkTestCasesDialogProps {
  testSuiteId: string;
  projectId?: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export const LinkTestCasesDialog: React.FC<LinkTestCasesDialogProps> = ({
  testSuiteId,
  projectId,
  open,
  onOpenChange,
  onSuccess,
}) => {
  const { toast } = useToast();
  const [selectedTestCaseIds, setSelectedTestCaseIds] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatusId, setSelectedStatusId] = useState<string | null>(null);
  const [selectedPriorityId, setSelectedPriorityId] = useState<string | null>(null);
  const [linkType, setLinkType] = useState<'available' | 'all'>('available');

  const { data: availableTestCases, isLoading } = useGetApiV1TestSuitesIdAvailableTestCases(
    testSuiteId,
    projectId,
  );

  // API call for all test cases across all test suites
  const { data: allTestCases, isLoading: allLoading } = useGetApiV1TestSuitesAllTestCases({
    search: searchQuery,
    projectId,
    limit: 100,
  });

  const { data: statuses } = useGetApiV1Statuses({
    filters: JSON.stringify({ isActive: { $eq: true } }),
  });
  const { data: priorities } = useGetApiV1Priorities({
    filters: JSON.stringify({ isActive: { $eq: true } }),
  });
  const linkTestCases = usePostApiV1TestSuitesIdTestCasesLink();

  const currentTestCases = linkType === 'available' ? availableTestCases : allTestCases?.data;
  const filteredTestCases = currentTestCases?.filter((tc) =>
    tc.title.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  const handleToggleTestCase = (testCaseId: string) => {
    setSelectedTestCaseIds((prev) =>
      prev.includes(testCaseId) ? prev.filter((id) => id !== testCaseId) : [...prev, testCaseId],
    );
  };

  const handleSelectAll = () => {
    if (selectedTestCaseIds.length === filteredTestCases?.length) {
      setSelectedTestCaseIds([]);
    } else {
      setSelectedTestCaseIds(filteredTestCases?.map((tc) => tc.id) || []);
    }
  };

  const handleLink = async () => {
    if (selectedTestCaseIds.length === 0) {
      toast({
        title: 'Error',
        description: 'Please select at least one test case',
        variant: 'destructive',
      });
      return;
    }

    try {
      await linkTestCases.mutateAsync({
        id: testSuiteId,
        data: {
          testCaseIds: selectedTestCaseIds,
          statusId: selectedStatusId,
          priorityId: selectedPriorityId,
        },
      });

      toast({
        title: 'Success',
        description: `${selectedTestCaseIds.length} test case(s) linked successfully`,
      });

      onOpenChange(false);
      onSuccess?.();
    } catch (error) {
      console.log('error', error);
      toast({
        title: 'Error',
        description: 'Failed to link test cases',
        variant: 'destructive',
      });
    }
  };

  return (
    <Dialog onOpenChange={onOpenChange} open={open}>
      <DialogContent className="max-h-[80vh] max-w-4xl">
        <DialogHeader>
          <DialogTitle>Link Existing Test Cases</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <Tabs
            onValueChange={(value) => setLinkType(value as 'available' | 'all')}
            value={linkType}
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="available">Available Test Cases</TabsTrigger>
              <TabsTrigger value="all">All Test Cases</TabsTrigger>
            </TabsList>

            {/* Search */}
            <div className="relative mt-4">
              <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-muted-foreground" />
              <Input
                className="pl-10"
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={
                  linkType === 'available'
                    ? 'Search available test cases...'
                    : 'Search all test cases across all test suites...'
                }
                value={searchQuery}
              />
            </div>

            {/* Default Status and Priority */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Default Status</Label>
                <Select onValueChange={setSelectedStatusId} value={selectedStatusId || undefined}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statuses?.data?.map((status) => (
                      <SelectItem key={status.id} value={status.id}>
                        {status.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Default Priority</Label>
                <Select
                  onValueChange={setSelectedPriorityId}
                  value={selectedPriorityId || undefined}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    {priorities?.data?.map((priority) => (
                      <SelectItem key={priority.id} value={priority.id}>
                        {priority.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <TabsContent className="mt-4" value="available">
              <div className="max-h-[400px] overflow-y-auto rounded-md border p-4">
                {isLoading ? (
                  <div className="space-y-2">
                    {[1, 2, 3].map((i) => (
                      <Skeleton className="h-12 w-full" key={i} />
                    ))}
                  </div>
                ) : filteredTestCases?.length === 0 ? (
                  <p className="py-8 text-center text-muted-foreground">
                    No available test cases found
                  </p>
                ) : (
                  <div className="space-y-2">
                    {/* Select All */}
                    <div className="flex items-center space-x-3 border-b p-2">
                      <Checkbox
                        checked={
                          selectedTestCaseIds.length === filteredTestCases?.length &&
                          filteredTestCases?.length > 0
                        }
                        id="select-all"
                        onCheckedChange={handleSelectAll}
                      />
                      <Label className="cursor-pointer font-medium" htmlFor="select-all">
                        Select All ({filteredTestCases?.length})
                      </Label>
                    </div>

                    {/* Test Cases */}
                    {filteredTestCases?.map((testCase) => (
                      <div
                        className="flex items-center space-x-3 rounded p-2 hover:bg-accent"
                        key={testCase.id}
                      >
                        <Checkbox
                          checked={selectedTestCaseIds.includes(testCase.id)}
                          id={testCase.id}
                          onCheckedChange={() => handleToggleTestCase(testCase.id)}
                        />
                        <Label className="flex-1 cursor-pointer" htmlFor={testCase.id}>
                          <div>
                            <p className="font-medium">{testCase.title}</p>
                            {testCase.description && (
                              <p className="line-clamp-1 text-muted-foreground text-sm">
                                {testCase.description}
                              </p>
                            )}
                            {testCase.project && (
                              <p className="text-muted-foreground text-xs">
                                Project: {testCase.project.name}
                              </p>
                            )}
                            {linkType === 'all' &&
                              (testCase as any).testSuites &&
                              (testCase as any).testSuites.length > 0 && (
                                <p className="mt-1 text-muted-foreground text-xs">
                                  Used in:{' '}
                                  {(testCase as any).testSuites
                                    .map((suite: any) => suite.name)
                                    .join(', ')}
                                </p>
                              )}
                          </div>
                        </Label>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent className="mt-4" value="all">
              <div className="max-h-[400px] overflow-y-auto rounded-md border p-4">
                {allLoading ? (
                  <div className="space-y-2">
                    {[1, 2, 3].map((i) => (
                      <Skeleton className="h-12 w-full" key={i} />
                    ))}
                  </div>
                ) : filteredTestCases?.length === 0 ? (
                  <p className="py-8 text-center text-muted-foreground">No test cases found</p>
                ) : (
                  <div className="space-y-2">
                    {/* Select All */}
                    <div className="flex items-center space-x-3 border-b p-2">
                      <Checkbox
                        checked={
                          selectedTestCaseIds.length === filteredTestCases?.length &&
                          filteredTestCases?.length > 0
                        }
                        id="select-all-all"
                        onCheckedChange={handleSelectAll}
                      />
                      <Label className="cursor-pointer font-medium" htmlFor="select-all-all">
                        Select All ({filteredTestCases?.length})
                      </Label>
                    </div>

                    {/* Test Cases */}
                    {filteredTestCases?.map((testCase) => (
                      <div
                        className="flex items-center space-x-3 rounded p-2 hover:bg-accent"
                        key={testCase.id}
                      >
                        <Checkbox
                          checked={selectedTestCaseIds.includes(testCase.id)}
                          id={`all-${testCase.id}`}
                          onCheckedChange={() => handleToggleTestCase(testCase.id)}
                        />
                        <Label className="flex-1 cursor-pointer" htmlFor={`all-${testCase.id}`}>
                          <div>
                            <p className="font-medium">{testCase.title}</p>
                            {testCase.description && (
                              <p className="line-clamp-1 text-muted-foreground text-sm">
                                {testCase.description}
                              </p>
                            )}
                            {testCase.project && (
                              <p className="text-muted-foreground text-xs">
                                Project: {testCase.project.name}
                              </p>
                            )}
                            {linkType === 'all' &&
                              (testCase as any).testSuites &&
                              (testCase as any).testSuites.length > 0 && (
                                <p className="mt-1 text-muted-foreground text-xs">
                                  Used in:{' '}
                                  {(testCase as any).testSuites
                                    .map((suite: any) => suite.name)
                                    .join(', ')}
                                </p>
                              )}
                          </div>
                        </Label>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter>
          <Button onClick={() => onOpenChange(false)} variant="outline">
            Cancel
          </Button>
          <Button
            disabled={linkTestCases.isPending || selectedTestCaseIds.length === 0}
            onClick={handleLink}
          >
            {linkTestCases.isPending
              ? 'Linking...'
              : `Link ${selectedTestCaseIds.length} Test Case(s)`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
