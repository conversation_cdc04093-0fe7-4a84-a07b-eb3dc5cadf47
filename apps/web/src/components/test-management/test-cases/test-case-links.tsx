'use client';

import { Badge } from '@repo/ui/components/badge';
import { But<PERSON> } from '@repo/ui/components/button';
import { Card, CardContent } from '@repo/ui/components/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@repo/ui/components/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@repo/ui/components/dropdown-menu';
import { useQueryClient } from '@tanstack/react-query';
import { ArrowRight, Link2, Loader2, MoreHorizontal, Plus, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import type { TestCase, TestCaseWorkItemLink, WorkItem } from '@/web/services/hooks.schemas';
import {
  getGetApiV1TestCaseWorkItemLinksQueryKey,
  useDeleteApiV1TestCaseWorkItemLinksId,
  useGetApiV1TestCaseWorkItemLinks,
} from '@/web/services/test-case-work-item-links';
import { TestCaseWorkItemLinkForm } from './test-case-work-item-link-form';

interface TestCaseLinksProps {
  testCaseId: string;
  testCase?: TestCase;
}

const linkTypeLabels: Record<string, { label: string; color: string }> = {
  tests: { label: 'Tests', color: 'bg-primary/10 text-primary' },
  tested_by: { label: 'Tested by', color: 'bg-primary/10 text-primary' },
  validates: { label: 'Validates', color: 'bg-success/10 text-success' },
  verifies: { label: 'Verifies', color: 'bg-success/10 text-success' },
  relates_to: {
    label: 'Relates to',
    color: 'bg-muted dark:bg-muted/20 text-muted-foreground',
  },
};

export const TestCaseLinks: React.FC<TestCaseLinksProps> = ({ testCaseId }) => {
  const queryClient = useQueryClient();
  const [isAddLinkDialogOpen, setIsAddLinkDialogOpen] = useState(false);

  // Fetch links for this test case
  const {
    data: linksData,
    isLoading,
    refetch,
  } = useGetApiV1TestCaseWorkItemLinks(
    {
      filters: JSON.stringify({ testCaseId: { $eq: testCaseId } }),
      limit: 100,
    },
    {
      query: {
        enabled: !!testCaseId,
      },
    },
  );

  const deleteLinkMutation = useDeleteApiV1TestCaseWorkItemLinksId({
    mutation: {
      onSuccess: () => {
        toast.success('Link removed successfully');
        refetch();
        queryClient.invalidateQueries({
          queryKey: getGetApiV1TestCaseWorkItemLinksQueryKey(),
        });
      },
      onError: (error: Error) => {
        toast.error(error?.message || 'Failed to remove link');
      },
    },
  });

  const handleDeleteLink = (linkId: string) => {
    if (confirm('Are you sure you want to remove this link?')) {
      deleteLinkMutation.mutate({ id: linkId });
    }
  };

  const handleCloseDialog = () => {
    setIsAddLinkDialogOpen(false);
  };

  const handleFormSubmit = () => {
    handleCloseDialog();
    refetch();
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="font-medium text-sm">Work Item Links</h3>
          <Button disabled size="sm" variant="outline">
            <Loader2 className="mr-1 h-4 w-4 animate-spin" />
            Loading...
          </Button>
        </div>
        <Card>
          <CardContent className="py-8 text-center text-muted-foreground">
            <Loader2 className="mx-auto mb-3 h-8 w-8 animate-spin" />
            Loading links...
          </CardContent>
        </Card>
      </div>
    );
  }

  const links = linksData?.data || [];

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-medium text-sm">Work Item Links</h3>
        <Button
          onClick={() => setIsAddLinkDialogOpen(true)}
          size="sm"
          type="button"
          variant="outline"
        >
          <Plus className="mr-1 h-3 w-3" />
          Add Link
        </Button>
      </div>

      {links.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-8 text-center">
            <Link2 className="mb-3 h-8 w-8 text-muted-foreground" />
            <p className="text-muted-foreground text-sm">No work item links yet</p>
            <p className="mt-1 text-muted-foreground text-xs">
              Link this test case to related work items
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {links.map((link: TestCaseWorkItemLink) => {
            const linkConfig = linkTypeLabels[link.linkType] || {
              label: link.linkType,
              color: 'bg-muted text-muted-foreground',
            };

            const workItem = (link as any).workItem as WorkItem | undefined;

            return (
              <Card key={link.id}>
                <CardContent className="py-0">
                  <div className="flex items-center justify-between">
                    <div className="flex min-w-0 flex-1 items-center gap-3">
                      <div className="flex items-center gap-2">
                        <Badge className={`${linkConfig.color} border-0`} variant="outline">
                          {linkConfig.label}
                        </Badge>
                        <ArrowRight className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <div className="min-w-0 flex-1">
                        {workItem ? (
                          <div>
                            <div className="flex items-center gap-2">
                              <span className="font-medium text-muted-foreground text-sm">
                                {(workItem as any).project?.key
                                  ? `${(workItem as any).project.key}-${link.workItemId.slice(0, 8)}`
                                  : `WI-${link.workItemId.slice(0, 8)}`}
                              </span>
                              <span className="truncate text-foreground text-sm">
                                {workItem.title}
                              </span>
                            </div>
                            {workItem.status && (
                              <div className="mt-1 flex items-center gap-2">
                                <Badge className="text-xs" variant="secondary">
                                  {workItem.status.name}
                                </Badge>
                                {workItem.assignee && (
                                  <span className="text-muted-foreground text-xs">
                                    Assigned to{' '}
                                    {workItem.assignee.displayName || workItem.assignee.email}
                                  </span>
                                )}
                              </div>
                            )}
                          </div>
                        ) : (
                          <div>
                            <span className="text-muted-foreground text-sm">
                              Work Item ID: {link.workItemId.slice(0, 8)}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button className="h-8 w-8 p-0" size="sm" type="button" variant="ghost">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            className="text-destructive"
                            onClick={() => handleDeleteLink(link.id)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Remove Link
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      <Dialog onOpenChange={handleCloseDialog} open={isAddLinkDialogOpen}>
        <DialogContent
          className="max-w-2xl"
          onClick={(e) => e.stopPropagation()}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.stopPropagation();
            }
          }}
        >
          <DialogHeader>
            <DialogTitle>Link Work Item</DialogTitle>
          </DialogHeader>
          <TestCaseWorkItemLinkForm
            onCancel={handleCloseDialog}
            onSubmit={handleFormSubmit}
            testCaseId={testCaseId}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};
