import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@repo/ui/components/alert-dialog';
import { Button } from '@repo/ui/components/button';
import { Input } from '@repo/ui/components/input';
import { cn } from '@repo/ui/lib/utils';
import { format } from 'date-fns';
import { Download, Eye, File, FileText, Image, Paperclip, Trash2, Upload, X } from 'lucide-react';
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { toast } from 'sonner';
import { ImagePreview, ImageThumbnail } from '@/web/components/image-previewer/image-preview';
import axiosInstance from '@/web/services/axiosClient';
import {
  useDeleteApiV1AttachmentsId,
  useGetApiV1AttachmentsByEntity,
} from '@/web/services/default';
import type { Attachment } from '@/web/services/hooks.schemas';

export interface PendingFile {
  id: string;
  file: File;
  preview?: string;
}

export interface TestCaseAttachmentsRef {
  uploadFiles: (testCaseId: string) => Promise<void>;
  getPendingFiles: () => PendingFile[];
  clearPendingFiles: () => void;
  deleteAttachments: () => Promise<void>;
  getPendingDeletions: () => string[];
}

interface TestCaseAttachmentsProps {
  testCaseId?: string;
  isEditing: boolean;
  onPendingFilesChange?: (files: PendingFile[]) => void;
  onPendingDeletionsChange?: (deletions: string[]) => void;
  initialPendingFiles?: PendingFile[];
  initialPendingDeletions?: string[];
}

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export const TestCaseAttachments = forwardRef<TestCaseAttachmentsRef, TestCaseAttachmentsProps>(
  (
    {
      testCaseId,
      isEditing,
      onPendingFilesChange,
      onPendingDeletionsChange,
      initialPendingFiles = [],
      initialPendingDeletions = [],
    },
    ref,
  ) => {
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [pendingFiles, setPendingFiles] = useState<PendingFile[]>(initialPendingFiles);
    const [pendingDeletions, setPendingDeletions] = useState<string[]>(initialPendingDeletions);
    const [deleteConfirmId, setDeleteConfirmId] = useState<string | null>(null);
    const [deletePendingId, setDeletePendingId] = useState<string | null>(null);
    const [isUploading, setIsUploading] = useState(false);

    // API hooks
    const { data: attachmentsData, refetch: refetchAttachments } = useGetApiV1AttachmentsByEntity(
      {
        entityType: 'test_case',
        entityId: testCaseId || '',
      },
      {
        query: {
          enabled: !!testCaseId,
        },
      },
    );

    const deleteMutation = useDeleteApiV1AttachmentsId();

    // Notify parent of pending files changes
    useEffect(() => {
      onPendingFilesChange?.(pendingFiles);
    }, [pendingFiles, onPendingFilesChange]);

    // Notify parent of pending deletions changes
    useEffect(() => {
      onPendingDeletionsChange?.(pendingDeletions);
    }, [pendingDeletions, onPendingDeletionsChange]);

    // Expose methods to parent via ref
    useImperativeHandle(ref, () => ({
      uploadFiles: async (testCaseId: string) => {
        if (pendingFiles.length === 0) {
          return;
        }

        setIsUploading(true);
        const errors: string[] = [];

        try {
          for (const pendingFile of pendingFiles) {
            try {
              const formData = new FormData();
              formData.append('file', pendingFile.file);

              await axiosInstance.post(
                `/api/v1/attachments/upload?entityType=test_case&entityId=${testCaseId}`,
                formData,
              );
            } catch (_error: any) {
              errors.push(`Failed to upload ${pendingFile.file.name}`);
            }
          }

          if (errors.length > 0) {
            toast.error(`Some files failed to upload: ${errors.join(', ')}`);
          } else {
            toast.success('All attachments uploaded successfully');
          }
          setPendingFiles([]);

          // Add a small delay before refetching to ensure backend processing is complete
          setTimeout(async () => {
            await refetchAttachments();
          }, 500);
        } finally {
          setIsUploading(false);
        }
      },
      getPendingFiles: () => pendingFiles,
      clearPendingFiles: () => setPendingFiles([]),
      deleteAttachments: async () => {
        if (pendingDeletions.length === 0) {
          return;
        }

        const errors: string[] = [];

        for (const attachmentId of pendingDeletions) {
          try {
            await deleteMutation.mutateAsync({ id: attachmentId });
          } catch (_error) {
            errors.push(`Failed to delete attachment ${attachmentId}`);
          }
        }

        if (errors.length > 0) {
          toast.error(`Some attachments failed to delete: ${errors.join(', ')}`);
        } else {
          toast.success('Attachments deleted successfully');
        }

        // Clear pending deletions after processing
        setPendingDeletions([]);
        refetchAttachments();
      },
      getPendingDeletions: () => pendingDeletions,
    }));

    const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(e.target.files || []);

      // Validate file sizes
      const validFiles = files.filter((file) => {
        if (file.size > MAX_FILE_SIZE) {
          toast.error(`${file.name} exceeds the 10MB size limit`);
          return false;
        }
        return true;
      });

      const newPendingFiles = validFiles.map((file) => ({
        id: `${file.name}-${Date.now()}-${Math.random()}`,
        file,
        preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined,
      }));

      setPendingFiles((prev) => [...prev, ...newPendingFiles]);

      // Clear input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }, []);

    const getFileIcon = (fileType: string | null) => {
      if (!fileType) {
        return <File className="h-4 w-4" />;
      }
      if (fileType.startsWith('image/')) {
        return <Image className="h-4 w-4" />;
      }
      if (fileType === 'application/pdf') {
        return <FileText className="h-4 w-4" />;
      }
      return <File className="h-4 w-4" />;
    };

    const formatFileSize = (bytes: number | null) => {
      if (!bytes) {
        return 'Unknown size';
      }
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(1024));
      return `${(bytes / 1024 ** i).toFixed(2)} ${sizes[i]}`;
    };

    const handleDownload = async (attachment: Attachment) => {
      try {
        const response = await axiosInstance.get(`/api/v1/attachments/${attachment.id}/signed-url`);
        if (response.data?.url) {
          window.open(response.data.url, '_blank');
        }
      } catch (_error) {
        toast.error('Failed to get download URL');
      }
    };

    const removePendingFile = (fileId: string) => {
      setPendingFiles((prev) => {
        const file = prev.find((f) => f.id === fileId);
        if (file?.preview) {
          URL.revokeObjectURL(file.preview);
        }
        return prev.filter((f) => f.id !== fileId);
      });
      setDeletePendingId(null);
    };

    const attachments = (attachmentsData || []).filter(
      (attachment) => !pendingDeletions.includes(attachment.id),
    );
    const totalFiles = attachments.length + pendingFiles.length;

    if (!isEditing && attachments.length === 0 && pendingFiles.length === 0) {
      return (
        <div className="py-12 text-center text-muted-foreground">
          <Paperclip className="mx-auto mb-3 h-8 w-8 text-muted-foreground" />
          <p className="text-sm">No attachments</p>
        </div>
      );
    }

    return (
      <div className="flex h-full flex-col gap-4">
        {isEditing && (
          <div className="flex flex-shrink-0 items-center gap-2">
            <Input
              accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.txt"
              className="hidden"
              disabled={isUploading}
              multiple
              onChange={handleFileSelect}
              ref={fileInputRef}
              type="file"
            />
            <Button
              disabled={isUploading}
              onClick={() => fileInputRef.current?.click()}
              size="sm"
              type="button"
              variant="outline"
            >
              <Upload className="mr-2 h-4 w-4" />
              Upload Files
            </Button>
            <p className="text-muted-foreground text-xs">
              Max file size: 10MB. Supported: Images, PDF, Word, Excel, Text
            </p>
            {!testCaseId && (
              <p className="text-secondary text-xs">
                Files will be uploaded when you save the test case
              </p>
            )}
          </div>
        )}

        {/* Combined list header */}
        {totalFiles > 0 && (
          <div className="flex flex-shrink-0 items-center justify-between">
            <h4 className="font-medium text-muted-foreground text-sm">
              Attachments ({totalFiles})
            </h4>
            <div className="flex gap-3">
              {pendingFiles.length > 0 && (
                <span className="text-secondary text-xs">{pendingFiles.length} pending upload</span>
              )}
              {pendingDeletions.length > 0 && (
                <span className="text-destructive text-xs">
                  {pendingDeletions.length} pending deletion
                </span>
              )}
            </div>
          </div>
        )}

        {/* Scrollable content area */}
        <div className="flex-1 overflow-y-auto overflow-x-hidden pr-2">
          <div className="space-y-4">
            {/* Pending Files (to be uploaded) */}
            {pendingFiles.length > 0 && (
              <div className="space-y-2">
                {pendingFiles.map((pendingFile) => {
                  const isImage = pendingFile.file.type.startsWith('image/');
                  const previewUrl =
                    pendingFile.preview || (isImage ? URL.createObjectURL(pendingFile.file) : null);

                  return (
                    <div
                      className="flex max-w-full items-center gap-3 rounded-lg border border-secondary/20 bg-secondary/5 p-3"
                      key={pendingFile.id}
                    >
                      <div className="flex min-w-0 flex-1 items-center gap-3">
                        <div className="flex-shrink-0">
                          {isImage && previewUrl ? (
                            <ImageThumbnail
                              alt={pendingFile.file.name}
                              fileName={pendingFile.file.name}
                              size="sm"
                              src={previewUrl}
                            />
                          ) : (
                            getFileIcon(pendingFile.file.type)
                          )}
                        </div>
                        <div className="min-w-0 max-w-[300px] flex-1">
                          <p
                            className="block truncate font-medium text-sm"
                            title={pendingFile.file.name}
                          >
                            {pendingFile.file.name}
                          </p>
                          <p className="block truncate text-muted-foreground text-xs">
                            {formatFileSize(pendingFile.file.size)} • Pending upload
                          </p>
                        </div>
                      </div>
                      {isEditing && (
                        <Button
                          className="h-8 w-8 flex-shrink-0 p-0 hover:border-destructive/30 hover:bg-destructive/5 hover:text-destructive"
                          disabled={isUploading}
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            setDeletePendingId(pendingFile.id);
                          }}
                          size="sm"
                          type="button"
                          variant="outline"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  );
                })}
              </div>
            )}

            {/* Existing Attachments */}
            {attachmentsData && attachmentsData.length > 0 && (
              <div className="space-y-2">
                {attachmentsData.map((attachment) => {
                  const isImage = attachment.fileType?.startsWith('image/');
                  const isMarkedForDeletion = pendingDeletions.includes(attachment.id);

                  if (isMarkedForDeletion && !isEditing) {
                    return null; // Hide in view mode if marked for deletion
                  }

                  return (
                    <div
                      className={cn(
                        'flex max-w-full items-center gap-3 rounded-lg p-3 transition-colors',
                        isMarkedForDeletion
                          ? 'border border-destructive/20 bg-destructive/5 opacity-60'
                          : 'bg-muted hover:bg-muted',
                      )}
                      key={attachment.id}
                    >
                      <div className="flex min-w-0 flex-1 items-center gap-3">
                        <div className="flex-shrink-0">
                          {isImage ? (
                            <ImageThumbnail
                              alt={attachment.fileName}
                              fileName={attachment.fileName}
                              onDownload={() => handleDownload(attachment)}
                              size="sm"
                              src={attachment.url}
                            />
                          ) : (
                            getFileIcon(attachment.fileType)
                          )}
                        </div>
                        <div className="min-w-0 max-w-[300px] flex-1">
                          <p
                            className="block truncate font-medium text-sm"
                            title={attachment.fileName}
                          >
                            {attachment.fileName}
                          </p>
                          <p
                            className="block truncate text-muted-foreground text-xs"
                            title={`${formatFileSize(attachment.size)} • ${format(new Date(attachment.uploadedAt), "MMM d, yyyy 'at' h:mm a")}${attachment.uploader ? ` • ${attachment.uploader.displayName}` : ''}${isMarkedForDeletion ? ' • Marked for deletion' : ''}`}
                          >
                            {formatFileSize(attachment.size)} •{' '}
                            {format(new Date(attachment.uploadedAt), "MMM d, yyyy 'at' h:mm a")}
                            {attachment.uploader && <> • {attachment.uploader.displayName}</>}
                            {isMarkedForDeletion && (
                              <span className="font-medium text-destructive">
                                {' '}
                                • Marked for deletion
                              </span>
                            )}
                          </p>
                        </div>
                      </div>
                      <div className="flex flex-shrink-0 items-center gap-1">
                        {isImage && (
                          <ImagePreview
                            alt={attachment.fileName}
                            fileName={attachment.fileName}
                            onDownload={() => handleDownload(attachment)}
                            src={attachment.url}
                            trigger={
                              <Button
                                className="h-8 w-8 p-0"
                                size="sm"
                                title="Preview"
                                type="button"
                                variant="outline"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            }
                          />
                        )}
                        <Button
                          className="h-8 w-8 p-0"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleDownload(attachment);
                          }}
                          size="sm"
                          title="Download"
                          type="button"
                          variant="outline"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                        {isEditing &&
                          (isMarkedForDeletion ? (
                            <Button
                              className="h-8 w-8 p-0 hover:border-success/30 hover:bg-success/5 hover:text-success"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                setPendingDeletions((prev) =>
                                  prev.filter((id) => id !== attachment.id),
                                );
                                toast.info('Deletion cancelled');
                              }}
                              size="sm"
                              title="Undo Delete"
                              type="button"
                              variant="outline"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          ) : (
                            <Button
                              className="h-8 w-8 p-0 hover:border-destructive/30 hover:bg-destructive/5 hover:text-destructive"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                setDeleteConfirmId(attachment.id);
                              }}
                              size="sm"
                              title="Delete"
                              type="button"
                              variant="outline"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>

        {/* Delete Pending File Confirmation */}
        <AlertDialog onOpenChange={() => setDeletePendingId(null)} open={!!deletePendingId}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Remove File</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to remove this file? It has not been uploaded yet.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => {
                  if (deletePendingId) {
                    removePendingFile(deletePendingId);
                  }
                }}
              >
                Remove
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Delete Existing Attachment Confirmation */}
        <AlertDialog onOpenChange={() => setDeleteConfirmId(null)} open={!!deleteConfirmId}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Attachment</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete this attachment? This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => {
                  if (deleteConfirmId) {
                    setPendingDeletions((prev) => [...prev, deleteConfirmId]);
                    setDeleteConfirmId(null);
                    toast.info('Attachment marked for deletion. Save changes to confirm.');
                  }
                }}
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    );
  },
);

TestCaseAttachments.displayName = 'TestCaseAttachments';
