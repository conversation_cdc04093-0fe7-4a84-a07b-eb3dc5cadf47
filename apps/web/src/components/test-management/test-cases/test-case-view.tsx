'use client';

import { useRouter } from '@tanstack/react-router';
import { useGetApiV1Priorities } from '@/web/services/priorities';
import { useGetApiV1Statuses } from '@/web/services/statuses';
import { useGetApiV1TestCasesId } from '@/web/services/test-cases';
import { useRootStore } from '@/web/store/store';
import { TestCaseForm } from './test-case-form';

export default function TestCaseView({
  testCaseId,
  isInDialog = false,
  onCancel,
}: {
  testCaseId?: string;
  isInDialog?: boolean;
  onCancel?: () => void;
}) {
  const router = useRouter();
  const currentOrganizationId = useRootStore((state) => state.currentOrganizationId);

  // Fetch test case data
  const { data: testCaseData } = useGetApiV1TestCasesId(testCaseId || '', {
    query: {
      enabled: !!testCaseId,
    },
  });

  // Fetch all required data for the form
  const { data: statusesData } = useGetApiV1Statuses({
    filters: JSON.stringify({
      organizationId: { $or: { $eq: currentOrganizationId, $null: true } },
    }),
  });
  const { data: prioritiesData } = useGetApiV1Priorities({
    filters: JSON.stringify({
      organizationId: { $or: { $eq: currentOrganizationId, $null: true } },
    }),
  });

  const handleBack = () => {
    if (onCancel) {
      onCancel();
    } else {
      router.history.back();
    }
  };

  const handleSubmit = () => {
    // Refresh the test case data after update
    router.invalidate();
  };

  if (!(testCaseData && statusesData && prioritiesData)) {
    return <div className="p-6 text-muted-foreground">Loading...</div>;
  }

  const testCase = testCaseData.data;

  return (
    <div className={`flex w-full flex-col bg-background ${isInDialog ? 'h-full' : 'h-screen'}`}>
      <TestCaseForm editPayload={testCase} onCancel={handleBack} onSubmit={handleSubmit} />
    </div>
  );
}
