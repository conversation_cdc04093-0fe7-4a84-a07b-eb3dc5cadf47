import { Button } from '@repo/ui/components/button';
import { Confirmation } from '@repo/ui/components/custom/confirmDialog';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@repo/ui/components/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@repo/ui/components/dropdown-menu';
import { useToast } from '@repo/ui/components/use-toast';
import { Copy, Edit, Folder, FolderInput, MoreVertical, PlusIcon, Trash2 } from 'lucide-react';
import { useState } from 'react';
import type { TestSuite } from '@/web/services/hooks.schemas';
import { useGetApiV1TestPlansId } from '@/web/services/test-plans';
import { deleteApiV1TestSuitesId, useGetApiV1TestSuites } from '@/web/services/test-suites';
import { TestCases } from '../test-cases/test-cases';
import { TestPlanForm } from '../test-plans/test-plan-form';
import { TestSuiteCopyFormEnhanced } from './test-suite-copy-form-enhanced';
import { TestSuiteForm } from './test-suite-form';
import { TestSuiteImportFormEnhanced } from './test-suite-import-form-enhanced';

interface TestSuitesProps {
  testPlanId: string;
}

export const TestSuites: React.FC<TestSuitesProps> = ({ testPlanId }) => {
  const { toast } = useToast();
  const { data: testPlanData } = useGetApiV1TestPlansId(testPlanId || '');
  const { data: testSuites, refetch: refetchTestSuites } = useGetApiV1TestSuites({
    filters: JSON.stringify({
      testPlanId: {
        $eq: testPlanId,
      },
    }),

    limit: 100,
  });

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [testSuiteToEdit, setTestSuiteToEdit] = useState<TestSuite | null>(null);
  const [selectedTestSuite, setSelectedTestSuite] = useState<TestSuite | null>(null);
  const [isCopyDialogOpen, setIsCopyDialogOpen] = useState(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [testSuiteToCopy, setTestSuiteToCopy] = useState<TestSuite | null>(null);
  const [testSuiteToImportInto, setTestSuiteToImportInto] = useState<TestSuite | null>(null);

  const handleCreateTestSuite = () => {
    setTestSuiteToEdit(null);
    setIsCreateDialogOpen(true);
  };

  const handleEditTestSuite = (testSuite: TestSuite) => {
    setTestSuiteToEdit(testSuite);
    setIsCreateDialogOpen(true);
  };

  const handleClose = () => {
    setIsCreateDialogOpen(false);
    setTestSuiteToEdit(null);
    refetchTestSuites();
  };

  const handleCopyTestSuite = (testSuite: TestSuite) => {
    setTestSuiteToCopy(testSuite);
    setIsCopyDialogOpen(true);
  };

  const handleImportTestCases = (testSuite: TestSuite) => {
    setTestSuiteToImportInto(testSuite);
    setIsImportDialogOpen(true);
  };

  const renderSuiteRow = (suite: TestSuite): React.ReactNode => {
    return (
      <div
        className={`border-b transition-colors ${
          selectedTestSuite?.id === suite.id
            ? 'bg-primary/5 hover:bg-primary/10'
            : 'border-border hover:bg-muted'
        }`}
        key={suite.id}
      >
        <div className="flex items-center justify-start py-3">
          <div
            className="flex flex-1 cursor-pointer items-center px-4"
            onClick={() => setSelectedTestSuite(suite)}
          >
            <div className="mr-2 rounded p-1">
              <Folder
                className={`h-4 w-4 ${
                  selectedTestSuite?.id === suite.id ? 'text-primary' : 'text-muted-foreground'
                }`}
              />
            </div>
            <div className="min-w-0 flex-1">
              <p className="truncate font-medium text-foreground text-sm">{suite.name}</p>
              <p className="text-muted-foreground text-xs">
                {suite.description || 'No description'}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-1 pr-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  className="h-8 w-8 p-0 hover:bg-muted"
                  onClick={(e) => e.stopPropagation()}
                  size="sm"
                  variant="ghost"
                >
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEditTestSuite(suite);
                  }}
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCopyTestSuite(suite);
                  }}
                >
                  <Copy className="mr-2 h-4 w-4" />
                  Copy
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    handleImportTestCases(suite);
                  }}
                >
                  <FolderInput className="mr-2 h-4 w-4" />
                  Import Test Cases
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <Confirmation
                  cancelButton={{
                    name: 'Cancel',
                    onClick: () => {},
                    variant: 'secondary',
                  }}
                  confirmButton={{
                    name: 'Delete',
                    onClick: async () => {
                      try {
                        await deleteApiV1TestSuitesId(suite.id);
                        if (selectedTestSuite?.id === suite.id) {
                          setSelectedTestSuite(null);
                        }
                        toast({
                          title: 'Success',
                          description: 'Test suite deleted successfully',
                        });
                        refetchTestSuites();
                      } catch (error) {
                        let errorMessage = 'Failed to delete test suite';
                        if (error && typeof error === 'object') {
                          // The axios interceptor returns error.response.data directly
                          const errorData = error as {
                            message?: string;
                            error?: string;
                          };
                          errorMessage = errorData.message || errorData.error || errorMessage;
                        }

                        toast({
                          title: 'Error',
                          description: errorMessage,
                          variant: 'destructive',
                        });
                      }
                    },
                    variant: 'destructive',
                  }}
                  description="This action cannot be undone. This will permanently delete the test suite and all associated test cases."
                  title="Delete Test Suite?"
                >
                  <DropdownMenuItem
                    className="text-destructive focus:text-destructive"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </Confirmation>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    );
  };

  const suites = testSuites?.data || [];

  const [isEditTestPlanOpen, setIsEditTestPlanOpen] = useState(false);

  // const handleBack = () => {
  //   router.history.back();
  // };

  const testPlan = testPlanData?.data;

  return (
    <div className="flex h-full flex-col">
      {/* Header */}
      {testPlan && (
        <div className="border-b bg-background px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {/* <Button
                variant="ghost"
                size="sm"
                onClick={handleBack}
                className="-ml-2"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button> 
              <div className="h-8 w-px bg-muted" /> */}
              <div>
                <h1 className="font-semibold text-foreground text-xl">{testPlan.name}</h1>
                <p className="text-muted-foreground text-sm">
                  {testPlan.description || 'Manage test suites and test cases'}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <span
                className={`inline-flex items-center rounded-full border px-2.5 py-1 font-medium text-xs ${
                  testPlan.isActive
                    ? 'border-success/20 bg-success/10 text-success-foreground'
                    : 'border-border bg-muted text-muted-foreground'
                }`}
              >
                {testPlan.isActive ? 'Active' : 'Inactive'}
              </span>
              <Button onClick={() => setIsEditTestPlanOpen(true)} size="sm" variant="outline">
                <Edit className="mr-2 h-4 w-4" />
                Edit Plan
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Content */}
      <div className="flex flex-1">
        {/* Left side - Test Suites list */}
        <div className="flex w-80 flex-col border-r bg-background">
          <div className="border-b px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-base text-foreground">Test Suites</h3>
                <p className="mt-0.5 text-muted-foreground text-xs">Organize test cases</p>
              </div>
              <Button onClick={handleCreateTestSuite} size="sm">
                <PlusIcon className="mr-1.5 h-3.5 w-3.5" />
                New
              </Button>
            </div>
          </div>
          <div className="flex-1 overflow-y-auto">
            {suites.length === 0 ? (
              <div className="px-6 py-12 text-center">
                <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-muted">
                  <Folder className="h-6 w-6 text-muted-foreground" />
                </div>
                <p className="font-medium text-foreground text-sm">No test suites</p>
                <p className="mt-1 text-muted-foreground text-xs">
                  Create your first test suite to get started
                </p>
              </div>
            ) : (
              <div>{suites.map((suite) => renderSuiteRow(suite))}</div>
            )}
          </div>
        </div>

        {/* Right side - Test Cases */}
        <div className="flex-1">
          {selectedTestSuite ? (
            <TestCases testSuite={selectedTestSuite} />
          ) : (
            <div className="flex h-full items-center justify-center bg-muted">
              <div className="text-center">
                <div className="mx-auto mb-4 flex h-14 w-14 items-center justify-center rounded-full bg-muted">
                  <Folder className="h-7 w-7 text-muted-foreground" />
                </div>
                <h3 className="mb-1 font-medium text-base text-foreground">Select a test suite</h3>
                <p className="mx-auto max-w-sm text-muted-foreground text-sm">
                  Choose a test suite from the sidebar to view and manage its test cases
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Create/Edit Dialog */}
        {isCreateDialogOpen && (
          <Dialog onOpenChange={setIsCreateDialogOpen} open={isCreateDialogOpen}>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>
                  {testSuiteToEdit ? 'Edit Test Suite' : 'Create Test Suite'}
                </DialogTitle>
              </DialogHeader>
              <TestSuiteForm
                onCancel={handleClose}
                onSubmit={handleClose}
                testPlanId={testPlanId}
                testSuite={testSuiteToEdit}
              />
            </DialogContent>
          </Dialog>
        )}

        {/* Copy Dialog */}
        {isCopyDialogOpen && testSuiteToCopy && (
          <Dialog onOpenChange={setIsCopyDialogOpen} open={isCopyDialogOpen}>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Copy Test Suite</DialogTitle>
              </DialogHeader>
              <TestSuiteCopyFormEnhanced
                onCancel={() => {
                  setIsCopyDialogOpen(false);
                  setTestSuiteToCopy(null);
                }}
                onSubmit={() => {
                  setIsCopyDialogOpen(false);
                  setTestSuiteToCopy(null);
                  refetchTestSuites();
                }}
                testSuite={testSuiteToCopy}
              />
            </DialogContent>
          </Dialog>
        )}

        {/* Import Dialog */}
        {isImportDialogOpen && testSuiteToImportInto && (
          <Dialog onOpenChange={setIsImportDialogOpen} open={isImportDialogOpen}>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Import Test Cases</DialogTitle>
              </DialogHeader>
              <TestSuiteImportFormEnhanced
                onCancel={() => {
                  setIsImportDialogOpen(false);
                  setTestSuiteToImportInto(null);
                }}
                onSubmit={() => {
                  setIsImportDialogOpen(false);
                  setTestSuiteToImportInto(null);
                  // Refresh test cases if this is the selected suite
                  if (selectedTestSuite?.id === testSuiteToImportInto.id) {
                    // The TestCases component will refresh automatically via react-query
                  }
                }}
                testPlanId={testPlanId}
                testSuite={testSuiteToImportInto}
              />
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* Edit Test Plan Dialog */}
      {isEditTestPlanOpen && testPlan && (
        <Dialog onOpenChange={setIsEditTestPlanOpen} open={isEditTestPlanOpen}>
          <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Test Plan</DialogTitle>
            </DialogHeader>
            <TestPlanForm
              onCancel={() => setIsEditTestPlanOpen(false)}
              onSubmit={() => setIsEditTestPlanOpen(false)}
              testPlan={testPlan}
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};
