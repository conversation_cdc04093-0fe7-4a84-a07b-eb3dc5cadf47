import { Button } from '@repo/ui/components/button';
import { Checkbox } from '@repo/ui/components/checkbox';
import { DialogFooter } from '@repo/ui/components/dialog';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { Skeleton } from '@repo/ui/components/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@repo/ui/components/tabs';
import { useForm } from '@tanstack/react-form';
import { useQueryClient } from '@tanstack/react-query';
import { Check, Loader2, Search } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';
import type { TestSuite } from '@/web/services/hooks.schemas';
import {
  getGetApiV1TestSuitesIdTestCasesQueryKey,
  useGetApiV1TestSuitesIdTestCases,
  usePostApiV1TestSuitesIdTestCasesLink,
} from '@/web/services/test-suite-test-cases';
import { useGetApiV1TestSuites } from '@/web/services/test-suites';

interface TestSuiteImportFormEnhancedProps {
  testSuite: TestSuite;
  testPlanId: string;
  onSubmit?: () => void;
  onCancel?: () => void;
}

export const TestSuiteImportFormEnhanced: React.FC<TestSuiteImportFormEnhancedProps> = ({
  testSuite,
  testPlanId,
  onSubmit,
  onCancel,
}) => {
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTestCaseIds, setSelectedTestCaseIds] = useState<string[]>([]);
  const [importMode, setImportMode] = useState<'all' | 'selected'>('all');

  // Fetch all test suites in the same test plan
  const { data: testSuitesData, isLoading: isSuitesLoading } = useGetApiV1TestSuites({
    filters: JSON.stringify({
      testPlanId: {
        $eq: testPlanId,
      },
    }),
    limit: 100,
  });

  const form = useForm({
    defaultValues: {
      sourceSuiteId: '',
    },
    validators: {
      onChange: ({ value }) => {
        if (!value.sourceSuiteId) {
          return {
            fields: {
              sourceSuiteId: 'Please select a test suite to import from',
            },
          };
        }
        return;
      },
    },
    onSubmit: async ({ value }) => {
      console.log('value', value);
      try {
        const testCasesToImport =
          importMode === 'all' ? sourceTestCases?.map((tc) => tc.id) || [] : selectedTestCaseIds;

        if (testCasesToImport.length === 0) {
          toast.error('No test cases selected to import');
          return;
        }

        await linkMutation.mutateAsync({
          id: testSuite.id,
          data: {
            testCaseIds: testCasesToImport,
            statusId: null,
            priorityId: null,
          },
        });

        toast.success(
          `Successfully imported ${testCasesToImport.length} test case${testCasesToImport.length !== 1 ? 's' : ''}`,
        );
        queryClient.invalidateQueries({
          queryKey: getGetApiV1TestSuitesIdTestCasesQueryKey(testSuite.id),
        });

        if (onSubmit) {
          onSubmit();
        }
      } catch (error) {
        console.log('error', error);
        toast.error('Failed to import test cases');
      }
    },
  });

  const selectedSourceSuiteId = form.state.values.sourceSuiteId;

  // Fetch test cases from the selected source suite
  const { data: sourceTestCases, isLoading: isTestCasesLoading } = useGetApiV1TestSuitesIdTestCases(
    selectedSourceSuiteId,
    {
      query: {
        enabled: !!selectedSourceSuiteId,
      },
    },
  );

  const linkMutation = usePostApiV1TestSuitesIdTestCasesLink();

  // Filter out the current suite from the list
  const availableSuites = testSuitesData?.data?.filter((suite) => suite.id !== testSuite.id) || [];

  // Filter test cases based on search query
  const filteredTestCases = useMemo(() => {
    if (!(sourceTestCases && searchQuery)) {
      return sourceTestCases || [];
    }
    return sourceTestCases.filter(
      (tc) =>
        tc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        tc.description?.toLowerCase().includes(searchQuery.toLowerCase()),
    );
  }, [sourceTestCases, searchQuery]);

  // Reset selected test cases when source suite changes
  useEffect(() => {
    setSelectedTestCaseIds([]);
    setSearchQuery('');
  }, [selectedSourceSuiteId]);

  const handleToggleTestCase = (testCaseId: string) => {
    setSelectedTestCaseIds((prev) =>
      prev.includes(testCaseId) ? prev.filter((id) => id !== testCaseId) : [...prev, testCaseId],
    );
  };

  const handleSelectAll = () => {
    if (selectedTestCaseIds.length === filteredTestCases?.length) {
      setSelectedTestCaseIds([]);
    } else {
      setSelectedTestCaseIds(filteredTestCases?.map((tc) => tc.id) || []);
    }
  };

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <div className="space-y-4 p-3">
        <div className="rounded-lg border bg-muted/30 p-3">
          <p className="text-muted-foreground text-sm">
            Import test cases from another test suite into <strong>{testSuite.name}</strong>.
          </p>
        </div>

        <form.Field name="sourceSuiteId">
          {(field) => (
            <FormField field={field} required>
              <FormItem>
                <FormLabel>Source Test Suite</FormLabel>
                <FormControl>
                  <Select
                    disabled={isSuitesLoading || availableSuites.length === 0}
                    onValueChange={field.setValue}
                    value={field.state.value}
                  >
                    <SelectTrigger>
                      <SelectValue
                        placeholder={
                          isSuitesLoading
                            ? 'Loading test suites...'
                            : availableSuites.length === 0
                              ? 'No other test suites available'
                              : 'Select a test suite to import from'
                        }
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {availableSuites.map((suite) => (
                        <SelectItem key={suite.id} value={suite.id}>
                          <div>
                            <div className="font-medium">{suite.name}</div>
                            {suite.description && (
                              <div className="text-muted-foreground text-xs">
                                {suite.description}
                              </div>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>

        {selectedSourceSuiteId && (
          <div className="space-y-3">
            {/* <FormLabel>Test Cases to Import</FormLabel> */}
            <Tabs onValueChange={(v) => setImportMode(v as 'all' | 'selected')} value={importMode}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="all">Import All ({sourceTestCases?.length || 0})</TabsTrigger>
                <TabsTrigger value="selected">Select Specific</TabsTrigger>
              </TabsList>

              <TabsContent className="mt-3" value="all">
                <div className="rounded-lg border bg-muted/30 p-3">
                  <p className="text-muted-foreground text-sm">
                    All {sourceTestCases?.length || 0} test cases from the selected suite will be
                    linked. Each test case can have different status and priority in this suite.
                  </p>
                </div>
              </TabsContent>

              <TabsContent className="mt-3 space-y-3" value="selected">
                {/* Search */}
                <div className="relative">
                  <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    className="pl-10"
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search test cases..."
                    value={searchQuery}
                  />
                </div>

                {/* Test Cases List */}
                <div className="max-h-[300px] overflow-y-auto rounded-md border p-2">
                  {isTestCasesLoading ? (
                    <div className="space-y-2">
                      {[1, 2, 3].map((i) => (
                        <Skeleton className="h-10 w-full" key={i} />
                      ))}
                    </div>
                  ) : filteredTestCases?.length === 0 ? (
                    <p className="py-4 text-center text-muted-foreground text-sm">
                      No test cases found in the selected suite
                    </p>
                  ) : (
                    <div className="space-y-1">
                      {/* Select All */}
                      <div className="flex items-center space-x-3 border-b p-2">
                        <Checkbox
                          checked={
                            selectedTestCaseIds.length === filteredTestCases?.length &&
                            filteredTestCases?.length > 0
                          }
                          id="select-all"
                          onCheckedChange={handleSelectAll}
                        />
                        <label className="cursor-pointer font-medium text-sm" htmlFor="select-all">
                          Select All ({filteredTestCases?.length})
                        </label>
                      </div>

                      {/* Test Cases */}
                      {filteredTestCases?.map((testCase) => (
                        <div
                          className="flex items-start space-x-3 rounded p-2 hover:bg-accent"
                          key={testCase.id}
                        >
                          <Checkbox
                            checked={selectedTestCaseIds.includes(testCase.id)}
                            id={`import-${testCase.id}`}
                            onCheckedChange={() => handleToggleTestCase(testCase.id)}
                          />
                          <label
                            className="flex-1 cursor-pointer"
                            htmlFor={`import-${testCase.id}`}
                          >
                            <div>
                              <p className="font-medium text-sm">{testCase.title}</p>
                              {testCase.description && (
                                <p className="line-clamp-1 text-muted-foreground text-xs">
                                  {testCase.description}
                                </p>
                              )}
                              <div className="mt-1 flex gap-4">
                                {testCase.suiteStatus && (
                                  <p className="text-muted-foreground text-xs">
                                    Status: {testCase.suiteStatus.name}
                                  </p>
                                )}
                                {testCase.suitePriority && (
                                  <p className="text-muted-foreground text-xs">
                                    Priority: {testCase.suitePriority.name}
                                  </p>
                                )}
                              </div>
                            </div>
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {selectedTestCaseIds.length > 0 && (
                  <p className="text-muted-foreground text-sm">
                    <Check className="mr-1 inline h-3 w-3" />
                    {selectedTestCaseIds.length} test case(s) selected
                  </p>
                )}
              </TabsContent>
            </Tabs>
          </div>
        )}

        <div className="rounded-lg border border-warning/50 bg-warning/10 p-3">
          <p className="text-sm text-warning-foreground">
            <strong>Important:</strong> Test cases will be linked to this suite (same test case
            IDs). You can set different status/priority for each test case in this suite.
          </p>
        </div>
      </div>

      <DialogFooter>
        <Button
          disabled={linkMutation.isPending}
          onClick={onCancel}
          type="button"
          variant="outline"
        >
          Cancel
        </Button>
        <Button
          disabled={
            linkMutation.isPending ||
            !form.state.isValid ||
            availableSuites.length === 0 ||
            (importMode === 'selected' && selectedTestCaseIds.length === 0)
          }
          type="submit"
        >
          {linkMutation.isPending ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Importing...
            </>
          ) : (
            <>
              Import Test Cases
              {importMode === 'selected' &&
                selectedTestCaseIds.length > 0 &&
                ` (${selectedTestCaseIds.length})`}
            </>
          )}
        </Button>
      </DialogFooter>
    </form>
  );
};
