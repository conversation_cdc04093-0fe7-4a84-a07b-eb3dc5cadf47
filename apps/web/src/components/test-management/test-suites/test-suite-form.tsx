import { insertTestSuiteSchema, patchTestSuiteSchema } from '@repo/db/schema';
import { Button } from '@repo/ui/components/button';
import { DialogFooter } from '@repo/ui/components/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
// } from "@repo/ui/components/select";
import { Textarea } from '@repo/ui/components/textarea';
import { useForm } from '@tanstack/react-form';
import { toast } from 'sonner';
import { z } from 'zod';
import type {
  PatchApiV1TestSuitesIdBody,
  PostApiV1TestSuitesBody,
  TestSuite,
} from '@/web/services/hooks.schemas';
import { usePatchApiV1TestSuitesId, usePostApiV1TestSuites } from '@/web/services/test-suites';

interface TestSuiteFormProps {
  testPlanId: string;
  testSuite?: TestSuite | null;
  onSubmit?: () => void;
  onCancel?: () => void;
}

export const TestSuiteForm: React.FC<TestSuiteFormProps> = ({
  testPlanId,
  testSuite,
  onSubmit,
  onCancel,
}) => {
  const createTestSuiteMutation = usePostApiV1TestSuites();
  const updateTestSuiteMutation = usePatchApiV1TestSuitesId();

  const isEditMode = !!testSuite;
  const schema = isEditMode ? patchTestSuiteSchema : insertTestSuiteSchema;

  const defaultValues: PostApiV1TestSuitesBody | PatchApiV1TestSuitesIdBody =
    (testSuite as PatchApiV1TestSuitesIdBody) || {
      testPlanId,
      name: '',
      description: '',
    };

  const form = useForm({
    defaultValues,
    validators: {
      onSubmit: schema,
    },
    onSubmit: async ({ value }) => {
      try {
        if (isEditMode) {
          await updateTestSuiteMutation.mutateAsync({
            id: testSuite.id,
            data: value as z.infer<typeof patchTestSuiteSchema>,
          });
          toast.success('Test suite updated successfully');
        } else {
          await createTestSuiteMutation.mutateAsync({
            data: value as z.infer<typeof insertTestSuiteSchema>,
          });
          toast.success('Test suite created successfully');
        }
        if (onSubmit) {
          onSubmit();
        }
      } catch (error) {
        if (error instanceof z.ZodError) {
          toast.error('Please check the form for errors');
          return;
        }
        toast.error(isEditMode ? 'Failed to update test suite' : 'Failed to create test suite');
        throw error;
      }
    },
  });

  return (
    <>
      <Form
        className="space-y-4 p-3"
        id="test-suite-form"
        onSubmit={(e) => {
          e.preventDefault();
          form.handleSubmit();
        }}
      >
        <form.Field name="name">
          {(field) => (
            <FormField field={field} required>
              <FormItem>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Enter test suite name"
                    value={field.state.value}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>

        <form.Field name="description">
          {(field) => (
            <FormField field={field}>
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea
                    className="min-h-[120px] resize-none"
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Enter test suite description"
                    value={field.state.value || ''}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>
      </Form>
      <DialogFooter>
        <Button
          disabled={createTestSuiteMutation.isPending || updateTestSuiteMutation.isPending}
          onClick={onCancel}
          type="button"
          variant="outline"
        >
          Cancel
        </Button>
        <Button
          disabled={createTestSuiteMutation.isPending || updateTestSuiteMutation.isPending}
          form="test-suite-form"
          type="submit"
        >
          {createTestSuiteMutation.isPending || updateTestSuiteMutation.isPending
            ? 'Saving...'
            : isEditMode
              ? 'Update Test Suite'
              : 'Create Test Suite'}
        </Button>
      </DialogFooter>
    </>
  );
};
