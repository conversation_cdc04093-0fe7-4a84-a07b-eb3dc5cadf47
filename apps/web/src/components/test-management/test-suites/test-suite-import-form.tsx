import { But<PERSON> } from '@repo/ui/components/button';
import { Checkbox } from '@repo/ui/components/checkbox';
import { DialogFooter } from '@repo/ui/components/dialog';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
import { Label } from '@repo/ui/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { Skeleton } from '@repo/ui/components/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@repo/ui/components/tabs';
import { useForm } from '@tanstack/react-form';
import { useQueryClient } from '@tanstack/react-query';
import { Loader2, Search } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { useGetApiV1TestSuitesAllTestCases } from '@/web/services/default';
import type { TestSuite } from '@/web/services/hooks.schemas';
import { getGetApiV1TestSuitesIdTestCasesQueryKey } from '@/web/services/test-suite-test-cases';
import { useGetApiV1TestSuites, usePatchApiV1TestSuitesId } from '@/web/services/test-suites';

interface TestSuiteImportFormProps {
  testSuite: TestSuite;
  testPlanId: string;
  onSubmit?: () => void;
  onCancel?: () => void;
}

export const TestSuiteImportForm: React.FC<TestSuiteImportFormProps> = ({
  testSuite,
  testPlanId,
  onSubmit,
  onCancel,
}) => {
  const queryClient = useQueryClient();
  const [importType, setImportType] = useState<'suite' | 'individual'>('suite');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTestCaseIds, setSelectedTestCaseIds] = useState<string[]>([]);

  // Fetch all test suites in the same test plan
  const { data: testSuitesData, isLoading } = useGetApiV1TestSuites({
    filters: JSON.stringify({
      testPlanId: {
        $eq: testPlanId,
      },
    }),
    limit: 100,
  });

  // For individual test case selection across all test suites
  const { data: allTestCases, isLoading: allTestCasesLoading } = useGetApiV1TestSuitesAllTestCases({
    search: searchQuery,
    limit: 100,
  });

  const importMutation = usePatchApiV1TestSuitesId({
    mutation: {
      onSuccess: (data) => {
        const imported = (data as any)?.imported || 0;
        toast.success(`Successfully imported ${imported} test case${imported !== 1 ? 's' : ''}`);
        queryClient.invalidateQueries({
          queryKey: getGetApiV1TestSuitesIdTestCasesQueryKey(testSuite.id),
        });
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (error) => {
        toast.error(error?.message || 'Failed to import test cases');
      },
    },
  });

  const form = useForm({
    defaultValues: {
      sourceSuiteId: '',
    },
    validators: {
      onChange: ({ value }) => {
        if (importType === 'suite' && !value.sourceSuiteId) {
          return {
            fields: {
              sourceSuiteId: 'Please select a test suite to import from',
            },
          };
        }
        if (importType === 'individual' && selectedTestCaseIds.length === 0) {
          return {
            form: 'Please select at least one test case to import',
          };
        }
        return;
      },
    },
    onSubmit: async ({ value }) => {
      const submitData: any = {
        id: testSuite.id,
        data: {},
      };

      if (importType === 'suite') {
        submitData.data.sourceSuiteId = value.sourceSuiteId;
      } else {
        submitData.data.selectedTestCaseIds = selectedTestCaseIds;
      }

      importMutation.mutate(submitData);
    },
  });

  // Filter out the current suite from the list
  const availableSuites = testSuitesData?.data?.filter((suite) => suite.id !== testSuite.id) || [];

  const handleToggleTestCase = (testCaseId: string) => {
    setSelectedTestCaseIds((prev) =>
      prev.includes(testCaseId) ? prev.filter((id) => id !== testCaseId) : [...prev, testCaseId],
    );
  };

  const handleSelectAll = () => {
    const allIds = allTestCases?.data?.map((tc) => tc.id) || [];
    setSelectedTestCaseIds(selectedTestCaseIds.length === allIds.length ? [] : allIds);
  };

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <div className="space-y-4 p-3">
        <div className="rounded-lg border bg-muted/30 p-3">
          <p className="text-muted-foreground text-sm">
            Import test cases from another test suite or select individual test cases to import into{' '}
            <strong>{testSuite.name}</strong>.
          </p>
        </div>

        <Tabs
          onValueChange={(value) => setImportType(value as 'suite' | 'individual')}
          value={importType}
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="suite">Import from Test Suite</TabsTrigger>
            <TabsTrigger value="individual">Select Individual Test Cases</TabsTrigger>
          </TabsList>

          <TabsContent className="mt-4 space-y-4" value="suite">
            <form.Field name="sourceSuiteId">
              {(field) => (
                <FormField field={field} required>
                  <FormItem>
                    <FormLabel>Source Test Suite</FormLabel>
                    <FormControl>
                      <Select
                        disabled={isLoading || availableSuites.length === 0}
                        onValueChange={field.setValue}
                        value={field.state.value}
                      >
                        <SelectTrigger>
                          <SelectValue
                            placeholder={
                              isLoading
                                ? 'Loading test suites...'
                                : availableSuites.length === 0
                                  ? 'No other test suites available'
                                  : 'Select a test suite to import from'
                            }
                          />
                        </SelectTrigger>
                        <SelectContent>
                          {availableSuites.map((suite) => (
                            <SelectItem key={suite.id} value={suite.id}>
                              <div>
                                <div className="font-medium">{suite.name}</div>
                                {suite.description && (
                                  <div className="text-muted-foreground text-xs">
                                    {suite.description}
                                  </div>
                                )}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>
          </TabsContent>

          <TabsContent className="mt-4 space-y-4" value="individual">
            <div className="space-y-3">
              <div className="relative">
                <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-muted-foreground" />
                <Input
                  className="pl-10"
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search test cases across all test suites..."
                  value={searchQuery}
                />
              </div>

              <div className="max-h-[300px] overflow-y-auto rounded-md border p-4">
                {allTestCasesLoading ? (
                  <div className="space-y-2">
                    {[1, 2, 3].map((i) => (
                      <Skeleton className="h-12 w-full" key={i} />
                    ))}
                  </div>
                ) : allTestCases?.data?.length === 0 ? (
                  <div className="py-4 text-center text-muted-foreground">
                    <p>No test cases found.</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {/* Select All */}
                    <div className="flex items-center space-x-3 border-b p-2">
                      <Checkbox
                        checked={
                          selectedTestCaseIds.length === allTestCases?.data?.length &&
                          allTestCases?.data?.length > 0
                        }
                        id="select-all-individual"
                        onCheckedChange={handleSelectAll}
                      />
                      <Label className="cursor-pointer font-medium" htmlFor="select-all-individual">
                        Select All ({allTestCases?.data?.length || 0})
                      </Label>
                    </div>

                    {/* Test Cases */}
                    {allTestCases?.data?.map((testCase) => (
                      <div
                        className="flex items-center space-x-3 rounded p-2 hover:bg-accent"
                        key={testCase.id}
                      >
                        <Checkbox
                          checked={selectedTestCaseIds.includes(testCase.id)}
                          id={`individual-${testCase.id}`}
                          onCheckedChange={() => handleToggleTestCase(testCase.id)}
                        />
                        <Label
                          className="flex-1 cursor-pointer"
                          htmlFor={`individual-${testCase.id}`}
                        >
                          <div>
                            <p className="font-medium">{testCase.title}</p>
                            {testCase.description && (
                              <p className="line-clamp-1 text-muted-foreground text-sm">
                                {testCase.description}
                              </p>
                            )}
                            {testCase.project && (
                              <p className="text-muted-foreground text-xs">
                                Project: {testCase.project.name}
                              </p>
                            )}
                            {(testCase as any).testSuites &&
                              (testCase as any).testSuites.length > 0 && (
                                <p className="mt-1 text-muted-foreground text-xs">
                                  Used in:{' '}
                                  {(testCase as any).testSuites
                                    .map((suite: any) => suite.name)
                                    .join(', ')}
                                </p>
                              )}
                          </div>
                        </Label>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {selectedTestCaseIds.length > 0 && (
                <div className="text-muted-foreground text-sm">
                  {selectedTestCaseIds.length} test case(s) selected
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>

        <div className="rounded-lg border border-warning/50 bg-warning/10 p-3">
          <p className="text-sm text-warning-foreground">
            <strong>Important:</strong> Test cases will be linked to this suite (same test case
            IDs). You can set different status/priority for each suite.
          </p>
        </div>
      </div>

      <DialogFooter>
        <Button
          disabled={importMutation.isPending}
          onClick={onCancel}
          type="button"
          variant="outline"
        >
          Cancel
        </Button>
        <Button
          disabled={
            importMutation.isPending ||
            !form.state.isValid ||
            (importType === 'suite' && availableSuites.length === 0) ||
            (importType === 'individual' && selectedTestCaseIds.length === 0)
          }
          type="submit"
        >
          {importMutation.isPending ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Importing...
            </>
          ) : (
            `Import ${
              importType === 'suite' ? 'Test Cases' : `${selectedTestCaseIds.length} Test Case(s)`
            }`
          )}
        </Button>
      </DialogFooter>
    </form>
  );
};
