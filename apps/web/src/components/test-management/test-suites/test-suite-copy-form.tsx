import { Button } from '@repo/ui/components/button';
import { Checkbox } from '@repo/ui/components/checkbox';
import { DialogFooter } from '@repo/ui/components/dialog';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
import { Textarea } from '@repo/ui/components/textarea';
import { useForm } from '@tanstack/react-form';
import { useQueryClient } from '@tanstack/react-query';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import type { TestSuite } from '@/web/services/hooks.schemas';
import { getGetApiV1TestSuitesQueryKey, usePostApiV1TestSuites } from '@/web/services/test-suites';

interface TestSuiteCopyFormProps {
  testSuite: TestSuite;
  onSubmit?: () => void;
  onCancel?: () => void;
}

export const TestSuiteCopyForm: React.FC<TestSuiteCopyFormProps> = ({
  testSuite,
  onSubmit,
  onCancel,
}) => {
  const queryClient = useQueryClient();

  const copyMutation = usePostApiV1TestSuites({
    mutation: {
      onSuccess: () => {
        toast.success('Test suite copied successfully');
        queryClient.invalidateQueries({ queryKey: getGetApiV1TestSuitesQueryKey() });
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (error) => {
        toast.error(error?.message || 'Failed to copy test suite');
      },
    },
  });

  const form = useForm({
    defaultValues: {
      name: `${testSuite.name} (Copy)`,
      description: testSuite.description || '',
      testPlanId: testSuite.testPlanId,
      includeTestCases: true,
    },
    validators: {
      onChange: ({ value }) => {
        if (!value.name || value.name.trim().length === 0) {
          return {
            fields: {
              name: 'Name is required',
            },
          };
        }
        return;
      },
    },
    onSubmit: async ({ value }) => {
      const { includeTestCases, ...suiteData } = value;
      copyMutation.mutate({
        data: {
          ...suiteData,
          description: suiteData.description || null,
          copiedFromId: testSuite.id,
          includeTestCases,
        },
      });
    },
  });

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <div className="space-y-4 p-3">
        <form.Field name="name">
          {(field) => (
            <FormField field={field} required>
              <FormItem>
                <FormLabel>New Suite Name</FormLabel>
                <FormControl>
                  <Input
                    onBlur={field.handleBlur}
                    onChange={(e) => field.setValue(e.target.value)}
                    placeholder="Enter name for the copied suite"
                    value={field.state.value}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>

        <form.Field name="description">
          {(field) => (
            <FormField field={field}>
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea
                    className="min-h-[80px] resize-none"
                    onBlur={field.handleBlur}
                    onChange={(e) => field.setValue(e.target.value)}
                    placeholder="Enter description (optional)"
                    value={field.state.value || ''}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>

        <form.Field name="includeTestCases">
          {(field) => (
            <FormField field={field}>
              <FormItem>
                <div className="flex items-center space-x-2">
                  <FormControl>
                    <Checkbox
                      checked={field.state.value}
                      onCheckedChange={(checked) => field.setValue(!!checked)}
                    />
                  </FormControl>
                  <FormLabel className="cursor-pointer font-normal text-sm">
                    Link all test cases from the original suite
                  </FormLabel>
                </div>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>

        <div className="rounded-lg border bg-muted/30 p-3">
          <p className="text-muted-foreground text-sm">
            <strong>Note:</strong> This will create a new test suite with the same structure.
            {form.state.values.includeTestCases
              ? ' All test cases will be linked to the new suite (same test case IDs, but you can set different status/priority for each suite).'
              : ' The suite will be created empty without test cases.'}
          </p>
        </div>
      </div>

      <DialogFooter>
        <Button
          disabled={copyMutation.isPending}
          onClick={onCancel}
          type="button"
          variant="outline"
        >
          Cancel
        </Button>
        <Button disabled={copyMutation.isPending} type="submit">
          {copyMutation.isPending ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Copying...
            </>
          ) : (
            'Copy Suite'
          )}
        </Button>
      </DialogFooter>
    </form>
  );
};
