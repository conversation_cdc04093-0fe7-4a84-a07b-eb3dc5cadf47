import { Button } from '@repo/ui/components/button';
import { Checkbox } from '@repo/ui/components/checkbox';
import { DialogFooter } from '@repo/ui/components/dialog';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
import { Skeleton } from '@repo/ui/components/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@repo/ui/components/tabs';
import { Textarea } from '@repo/ui/components/textarea';
import { useForm } from '@tanstack/react-form';
import { useQueryClient } from '@tanstack/react-query';
import { Check, Loader2, Search } from 'lucide-react';
import { useMemo, useState } from 'react';
import { toast } from 'sonner';
import type { TestSuite } from '@/web/services/hooks.schemas';
import {
  useGetApiV1TestSuitesIdTestCases,
  usePostApiV1TestSuitesIdTestCasesLink,
} from '@/web/services/test-suite-test-cases';
import { getGetApiV1TestSuitesQueryKey, usePostApiV1TestSuites } from '@/web/services/test-suites';

interface TestSuiteCopyFormEnhancedProps {
  testSuite: TestSuite;
  onSubmit?: () => void;
  onCancel?: () => void;
}

export const TestSuiteCopyFormEnhanced: React.FC<TestSuiteCopyFormEnhancedProps> = ({
  testSuite,
  onSubmit,
  onCancel,
}) => {
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTestCaseIds, setSelectedTestCaseIds] = useState<string[]>([]);
  const [copyMode, setCopyMode] = useState<'all' | 'selected'>('all');

  // Fetch test cases from the source suite
  const { data: testCases, isLoading } = useGetApiV1TestSuitesIdTestCases(testSuite.id);

  const copyMutation = usePostApiV1TestSuites();
  const linkMutation = usePostApiV1TestSuitesIdTestCasesLink();

  // Filter test cases based on search query
  const filteredTestCases = useMemo(() => {
    if (!(testCases && searchQuery)) return testCases || [];
    return testCases.filter(
      (tc) =>
        tc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        tc.description?.toLowerCase().includes(searchQuery.toLowerCase()),
    );
  }, [testCases, searchQuery]);

  const form = useForm({
    defaultValues: {
      name: `${testSuite.name} (Copy)`,
      description: testSuite.description || '',
      testPlanId: testSuite.testPlanId,
    },
    validators: {
      onChange: ({ value }) => {
        if (!value.name || value.name.trim().length === 0) {
          return {
            fields: {
              name: 'Name is required',
            },
          };
        }
        return;
      },
    },
    onSubmit: async ({ value }) => {
      try {
        // Create the new suite
        const response = await copyMutation.mutateAsync({
          data: {
            ...value,
            description: value.description || null,
            copiedFromId: testSuite.id,
            includeTestCases: false, // We'll manually link selected test cases
          },
        });

        const newSuiteId = response.data.id;

        // Link selected test cases if any
        if (copyMode === 'all' && testCases && testCases.length > 0) {
          const testCaseIds = testCases.map((tc) => tc.id);
          await linkMutation.mutateAsync({
            id: newSuiteId,
            data: {
              testCaseIds,
              // Preserve the status and priority from the original suite
              statusId: null,
              priorityId: null,
            },
          });
        } else if (copyMode === 'selected' && selectedTestCaseIds.length > 0) {
          await linkMutation.mutateAsync({
            id: newSuiteId,
            data: {
              testCaseIds: selectedTestCaseIds,
              statusId: null,
              priorityId: null,
            },
          });
        }

        toast.success('Test suite copied successfully');
        queryClient.invalidateQueries({
          queryKey: getGetApiV1TestSuitesQueryKey(),
        });
        if (onSubmit) {
          onSubmit();
        }
      } catch (error: any) {
        toast.error(error?.message || 'Failed to copy test suite');
      }
    },
  });

  const handleToggleTestCase = (testCaseId: string) => {
    setSelectedTestCaseIds((prev) =>
      prev.includes(testCaseId) ? prev.filter((id) => id !== testCaseId) : [...prev, testCaseId],
    );
  };

  const handleSelectAll = () => {
    if (selectedTestCaseIds.length === filteredTestCases?.length) {
      setSelectedTestCaseIds([]);
    } else {
      setSelectedTestCaseIds(filteredTestCases?.map((tc) => tc.id) || []);
    }
  };

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <div className="space-y-4 p-3">
        <form.Field name="name">
          {(field) => (
            <FormField field={field} required>
              <FormItem>
                <FormLabel>New Suite Name</FormLabel>
                <FormControl>
                  <Input
                    onBlur={field.handleBlur}
                    onChange={(e) => field.setValue(e.target.value)}
                    placeholder="Enter name for the copied suite"
                    value={field.state.value}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>

        <form.Field name="description">
          {(field) => (
            <FormField field={field}>
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea
                    className="min-h-[80px] resize-none"
                    onBlur={field.handleBlur}
                    onChange={(e) => field.setValue(e.target.value)}
                    placeholder="Enter description (optional)"
                    value={field.state.value || ''}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>

        <div className="space-y-3">
          {/* <FormLabel>Test Cases</FormLabel> */}
          <Tabs onValueChange={(v) => setCopyMode(v as 'all' | 'selected')} value={copyMode}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="all">Copy All ({testCases?.length || 0})</TabsTrigger>
              <TabsTrigger value="selected">Select Specific</TabsTrigger>
            </TabsList>

            <TabsContent className="mt-3" value="all">
              <div className="rounded-lg border bg-muted/30 p-3">
                <p className="text-muted-foreground text-sm">
                  All {testCases?.length || 0} test cases will be linked to the new suite. Each test
                  case can have different status and priority in the new suite.
                </p>
              </div>
            </TabsContent>

            <TabsContent className="mt-3 space-y-3" value="selected">
              {/* Search */}
              <div className="relative">
                <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-muted-foreground" />
                <Input
                  className="pl-10"
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search test cases..."
                  value={searchQuery}
                />
              </div>

              {/* Test Cases List */}
              <div className="max-h-[300px] overflow-y-auto rounded-md border p-2">
                {isLoading ? (
                  <div className="space-y-2">
                    {[1, 2, 3].map((i) => (
                      <Skeleton className="h-10 w-full" key={i} />
                    ))}
                  </div>
                ) : filteredTestCases?.length === 0 ? (
                  <p className="py-4 text-center text-muted-foreground text-sm">
                    No test cases found
                  </p>
                ) : (
                  <div className="space-y-1">
                    {/* Select All */}
                    <div className="flex items-center space-x-3 border-b p-2">
                      <Checkbox
                        checked={
                          selectedTestCaseIds.length === filteredTestCases?.length &&
                          filteredTestCases?.length > 0
                        }
                        id="select-all"
                        onCheckedChange={handleSelectAll}
                      />
                      <label className="cursor-pointer font-medium text-sm" htmlFor="select-all">
                        Select All ({filteredTestCases?.length})
                      </label>
                    </div>

                    {/* Test Cases */}
                    {filteredTestCases?.map((testCase) => (
                      <div
                        className="flex items-start space-x-3 rounded p-2 hover:bg-accent"
                        key={testCase.id}
                      >
                        <Checkbox
                          checked={selectedTestCaseIds.includes(testCase.id)}
                          id={testCase.id}
                          onCheckedChange={() => handleToggleTestCase(testCase.id)}
                        />
                        <label className="flex-1 cursor-pointer" htmlFor={testCase.id}>
                          <div>
                            <p className="font-medium text-sm">{testCase.title}</p>
                            {testCase.suiteStatus && (
                              <p className="text-muted-foreground text-xs">
                                Status: {testCase.suiteStatus.name}
                              </p>
                            )}
                            {testCase.suitePriority && (
                              <p className="text-muted-foreground text-xs">
                                Priority: {testCase.suitePriority.name}
                              </p>
                            )}
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {selectedTestCaseIds.length > 0 && (
                <p className="text-muted-foreground text-sm">
                  <Check className="mr-1 inline h-3 w-3" />
                  {selectedTestCaseIds.length} test case(s) selected
                </p>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </div>

      <DialogFooter>
        <Button
          disabled={copyMutation.isPending || linkMutation.isPending}
          onClick={onCancel}
          type="button"
          variant="outline"
        >
          Cancel
        </Button>
        <Button
          disabled={
            copyMutation.isPending ||
            linkMutation.isPending ||
            (copyMode === 'selected' && selectedTestCaseIds.length === 0)
          }
          type="submit"
        >
          {copyMutation.isPending || linkMutation.isPending ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Copying...
            </>
          ) : (
            <>
              Copy Suite
              {copyMode === 'selected' &&
                selectedTestCaseIds.length > 0 &&
                ` (${selectedTestCaseIds.length} test cases)`}
            </>
          )}
        </Button>
      </DialogFooter>
    </form>
  );
};
