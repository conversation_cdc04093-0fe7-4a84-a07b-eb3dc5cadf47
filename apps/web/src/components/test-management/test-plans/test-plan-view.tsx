import { useParams } from '@tanstack/react-router';
import { useGetApiV1TestPlansId } from '@/web/services/test-plans';
import { TestSuites } from '../test-suites/test-suites';

interface TestPlanViewProps {
  testPlanId?: string;
}

export const TestPlanView: React.FC<TestPlanViewProps> = ({ testPlanId: propsTestPlanId }) => {
  const { tpId } = useParams({
    from: '/__mainLayout/project/testPlans/detail/$tpId',
  });
  const testPlanId = propsTestPlanId || tpId;

  const { data: testPlanData, isLoading } = useGetApiV1TestPlansId(testPlanId || '', {
    query: {
      enabled: !!testPlanId,
    },
  });

  if (isLoading) {
    return <div className="p-6 text-muted-foreground">Loading...</div>;
  }

  if (!testPlanData?.data) {
    return (
      <div className="flex h-full items-center justify-center">
        <p className="text-muted-foreground">Test plan not found</p>
      </div>
    );
  }

  const testPlan = testPlanData.data;

  return (
    <div className="flex h-screen flex-col bg-background">
      <TestSuites testPlanId={testPlan.id} />
    </div>
  );
};
