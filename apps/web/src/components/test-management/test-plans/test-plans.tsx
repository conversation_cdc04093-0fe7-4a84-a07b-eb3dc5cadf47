import { Button } from '@repo/ui/components/button';
import { Confirmation } from '@repo/ui/components/custom/confirmDialog';
import CustomDataGrid, { type MuiColumn } from '@repo/ui/components/custom/customDataGrid';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@repo/ui/components/dialog';
import { useToast } from '@repo/ui/components/use-toast';
import { Link } from '@tanstack/react-router';
import { format } from 'date-fns';
import { Edit, PlusIcon, Trash2 } from 'lucide-react';
import { useState } from 'react';
import type { TestPlan } from '@/web/services/hooks.schemas';
import { deleteApiV1TestPlansId, useGetApiV1TestPlans } from '@/web/services/test-plans';
import { useRootStore } from '@/web/store/store';
import { TestPlanForm } from './test-plan-form';

export const TestPlans = () => {
  const { toast } = useToast();
  const currentProjectId = useRootStore((state) => state.currentProjectId);
  const { data: testPlans, refetch: refetchTestPlans } = useGetApiV1TestPlans({
    filters: JSON.stringify({
      projectId: {
        $eq: currentProjectId,
      },
    }),
    limit: 100,
  });

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [testPlanToEdit, setTestPlanToEdit] = useState<TestPlan | null>(null);

  const handleCreateTestPlan = () => {
    setTestPlanToEdit(null);
    setIsCreateDialogOpen(true);
  };

  const handleEditTestPlan = (testPlan: TestPlan) => {
    setTestPlanToEdit(testPlan);
    setIsCreateDialogOpen(true);
  };

  const handleClose = () => {
    setIsCreateDialogOpen(false);
    setTestPlanToEdit(null);
    refetchTestPlans();
  };

  const columns: MuiColumn<TestPlan>[] = [
    {
      field: 'name',
      headerName: 'Name',
      width: 250,
      sortable: true,
      renderCell: (params) => (
        <Link
          className="text-primary hover:underline"
          params={{ tpId: params.row.original.id }}
          to="/project/testPlans/detail/$tpId"
        >
          {params.row.original.name || 'Untitled'}
        </Link>
      ),
    },
    {
      field: 'description',
      headerName: 'Description',
      width: 350,
    },
    {
      field: 'isActive',
      headerName: 'Status',
      width: 120,
      sortable: true,
      valueFormatter: (params) => (params.value ? 'Active' : 'Inactive'),
    },
    {
      field: 'createdAt',
      headerName: 'Created',
      width: 150,
      sortable: true,
      valueFormatter: (params) =>
        params.value ? format(new Date(params.value as string), 'MMM d, yyyy') : '-',
    },
    {
      field: 'id',
      headerName: 'Actions',
      width: 150,
      sortable: false,
      renderCell: (params) => (
        <div className="flex space-x-2">
          <Button onClick={() => handleEditTestPlan(params.row.original)} size="sm" variant="ghost">
            <Edit className="h-4 w-4" />
          </Button>
          <Confirmation
            cancelButton={{
              name: 'Cancel',
              onClick: () => {},
              variant: 'secondary',
            }}
            confirmButton={{
              name: 'Delete',
              onClick: async () => {
                try {
                  await deleteApiV1TestPlansId(params.row.original.id);
                  toast({
                    title: 'Success',
                    description: 'Test plan deleted successfully',
                  });
                  refetchTestPlans();
                } catch (error) {
                  let errorMessage = 'Failed to delete test plan';
                  if (error && typeof error === 'object') {
                    // The axios interceptor returns error.response.data directly
                    const errorData = error as {
                      message?: string;
                      error?: string;
                    };
                    errorMessage = errorData.message || errorData.error || errorMessage;
                  }

                  toast({
                    title: 'Error',
                    description: errorMessage,
                    variant: 'destructive',
                  });
                }
              },
              variant: 'destructive',
            }}
            description="This action cannot be undone. This will permanently delete the test plan and all associated test suites and test cases."
            title="Are You Sure?"
          >
            <Button size="sm" variant="ghost">
              <Trash2 className="h-4 w-4" />
            </Button>
          </Confirmation>
        </div>
      ),
    },
  ];

  return (
    <div className="h-screen p-4">
      {/* <div className="flex items-center justify-between mb-4">
        <div>
          <h1 className="text-xl font-semibold">Test Plans</h1>
          <p className="text-muted-foreground">
            Organize and manage your test cases with comprehensive test plans
          </p>
        </div>
      </div> */}
      <CustomDataGrid<TestPlan>
        addButton={
          <Button onClick={handleCreateTestPlan} size="sm" variant="default">
            <PlusIcon className="mr-2" size={16} />
            Create Test Plan
          </Button>
        }
        autoHeight={false}
        checkboxSelection={false}
        columns={columns}
        loading={!testPlans}
        pageSize={10}
        pageSizeOptions={[5, 10, 20, 50]}
        rows={testPlans?.data || []}
      />

      {isCreateDialogOpen && (
        <Dialog onOpenChange={setIsCreateDialogOpen} open={isCreateDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>{testPlanToEdit ? 'Edit Test Plan' : 'Create Test Plan'}</DialogTitle>
            </DialogHeader>
            <TestPlanForm onCancel={handleClose} onSubmit={handleClose} testPlan={testPlanToEdit} />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};
