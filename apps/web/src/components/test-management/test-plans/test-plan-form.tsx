import { insertTestPlanSchema, patchTestPlanSchema } from '@repo/db/schema';
import { Button } from '@repo/ui/components/button';
import { DialogFooter } from '@repo/ui/components/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
import { Switch } from '@repo/ui/components/switch';
import { Textarea } from '@repo/ui/components/textarea';
import { useForm } from '@tanstack/react-form';
import { toast } from 'sonner';
import { z } from 'zod';
import type {
  PatchApiV1TestPlansIdBody,
  PostApiV1TestPlansBody,
  TestPlan,
} from '@/web/services/hooks.schemas';
import { usePatchApiV1TestPlansId, usePostApiV1TestPlans } from '@/web/services/test-plans';
import { useRootStore } from '@/web/store/store';

interface TestPlanFormProps {
  testPlan?: TestPlan | null;
  onSubmit?: () => void;
  onCancel?: () => void;
}

export const TestPlanForm: React.FC<TestPlanFormProps> = ({ testPlan, onSubmit, onCancel }) => {
  const currentProjectId = useRootStore((state) => state.currentProjectId);
  const currentUser = useRootStore((state) => state.currentUser);

  const createTestPlanMutation = usePostApiV1TestPlans();
  const updateTestPlanMutation = usePatchApiV1TestPlansId();

  const isEditMode = !!testPlan;
  const schema = isEditMode ? patchTestPlanSchema : insertTestPlanSchema;

  const defaultValues: PostApiV1TestPlansBody | PatchApiV1TestPlansIdBody = testPlan || {
    projectId: currentProjectId || '',
    name: '',
    description: '',
    isActive: true,
    createdById: currentUser?.id || '',
  };

  const form = useForm({
    defaultValues,
    validators: {
      onSubmit: schema,
    },
    onSubmit: async ({ value }) => {
      try {
        if (isEditMode) {
          await updateTestPlanMutation.mutateAsync({
            id: testPlan.id,
            data: value as z.infer<typeof patchTestPlanSchema>,
          });
          toast.success('Test plan updated successfully');
        } else {
          await createTestPlanMutation.mutateAsync({
            data: value as z.infer<typeof insertTestPlanSchema>,
          });
          toast.success('Test plan created successfully');
        }
        if (onSubmit) {
          onSubmit();
        }
      } catch (error) {
        if (error instanceof z.ZodError) {
          toast.error('Please check the form for errors');
          return;
        }
        toast.error(isEditMode ? 'Failed to update test plan' : 'Failed to create test plan');
        throw error;
      }
    },
  });

  return (
    <>
      <Form
        className="space-y-4 p-3"
        id="test-plan-form"
        onSubmit={(e) => {
          e.preventDefault();
          form.handleSubmit();
        }}
      >
        <form.Field name="name">
          {(field) => (
            <FormField field={field} required>
              <FormItem>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Enter test plan name"
                    value={field.state.value}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>

        <form.Field name="description">
          {(field) => (
            <FormField field={field}>
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea
                    className="min-h-[120px] resize-none"
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Enter test plan description"
                    value={field.state.value || ''}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>

        <form.Field name="isActive">
          {(field) => (
            <FormField field={field}>
              <FormItem>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <FormLabel>Active Status</FormLabel>
                    <div className="text-muted-foreground text-sm">
                      Inactive test plans won't be available for test execution
                    </div>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.state.value}
                      onCheckedChange={(checked) => field.handleChange(checked)}
                    />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>
      </Form>
      <DialogFooter>
        <Button
          disabled={createTestPlanMutation.isPending || updateTestPlanMutation.isPending}
          onClick={onCancel}
          type="button"
          variant="outline"
        >
          Cancel
        </Button>
        <Button
          disabled={createTestPlanMutation.isPending || updateTestPlanMutation.isPending}
          form="test-plan-form"
          type="submit"
        >
          {createTestPlanMutation.isPending || updateTestPlanMutation.isPending
            ? 'Saving...'
            : isEditMode
              ? 'Update Test Plan'
              : 'Create Test Plan'}
        </Button>
      </DialogFooter>
    </>
  );
};
