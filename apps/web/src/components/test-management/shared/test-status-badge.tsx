import { Alert<PERSON>ircle, CheckCircle2, SkipForward, XCir<PERSON> } from 'lucide-react';

export type TestExecutionStatus = 'passed' | 'failed' | 'blocked' | 'skipped' | 'not_executed';

interface TestStatusBadgeProps {
  status: TestExecutionStatus;
  showIcon?: boolean;
  size?: 'default' | 'sm';
}

const statusConfig = {
  passed: {
    className:
      'bg-primary/10 text-primary-foreground border-primary/20 dark:bg-primary/20 dark:text-primary dark:border-primary/80',
    label: 'Passed',
    icon: CheckCircle2,
  },
  failed: {
    className:
      'bg-destructive/10 text-destructive border-destructive/20 dark:bg-destructive/10 dark:text-destructive-foreground dark:border-destructive/30',
    label: 'Failed',
    icon: XCircle,
  },
  blocked: {
    className:
      'bg-warning/10 text-warning-foreground border-warning/20 dark:bg-warning/20 dark:text-warning dark:border-warning/80',
    label: 'Blocked',
    icon: AlertCircle,
  },
  skipped: {
    className: 'bg-muted text-foreground border-border',
    label: 'Skipped',
    icon: SkipForward,
  },
  not_executed: {
    className: 'bg-muted text-muted-foreground border-border',
    label: 'Not Executed',
    icon: null,
  },
};

export const TestStatusBadge: React.FC<TestStatusBadgeProps> = ({
  status,
  showIcon = false,
  size = 'default',
}) => {
  const config = statusConfig[status] || statusConfig.not_executed;
  const Icon = config.icon;
  const sizeClasses = size === 'sm' ? 'px-2 py-0.5 text-xs' : 'px-2.5 py-1 text-xs';
  const iconSize = size === 'sm' ? 'h-3 w-3' : 'h-3.5 w-3.5';

  return (
    <span
      className={`inline-flex items-center gap-1 rounded-full border font-medium ${config.className} ${sizeClasses}`}
    >
      {showIcon && Icon && <Icon className={iconSize} />}
      <span>{config.label}</span>
    </span>
  );
};
