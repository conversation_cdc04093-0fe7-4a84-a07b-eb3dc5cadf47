// import {
//   usePatchV1ProjectGroupId,
//   usePostV1ProjectGroup,
// } from "@/web/services/project-group/project-group";
// import {
//   GetV1ProjectGroup200ItemsItem,
//   PatchV1ProjectGroupIdBody,
//   PostV1ProjectGroupBody,
// } from "@/web/services/types";
// import { useRootStore } from "@/web/store/store";
// import {
//   insertProjectGroupSchema,
//   patchProjectGroupSchema,
// } from "@repo/db/schema";
// import { Button } from "@repo/ui/components/button";
// import { DialogFooter } from "@repo/ui/components/dialog";
// import {
//   Form,
//   FormControl,
//   FormField,
//   FormItem,
//   FormLabel,
//   FormMessage,
// } from "@repo/ui/components/form";
// import { Input } from "@repo/ui/components/input";
// import { Textarea } from "@repo/ui/components/textarea";
// import { useForm } from "@tanstack/react-form";

// interface ProjectGroupFormProps {
//   onSubmit?: () => void;
//   onCancel?: () => void;
//   editPayload?: GetV1ProjectGroup200ItemsItem | null;
// }

// export const ProjectGroupForm: React.FC<ProjectGroupFormProps> = ({
//   onSubmit,
//   onCancel,
//   editPayload,
// }) => {
//   const currentProject = useRootStore((state) => state.currentProjectId);

//   const defaultValues: PatchV1ProjectGroupIdBody | PostV1ProjectGroupBody =
//     editPayload || {
//       projectId: currentProject,
//       name: "",
//       description: "",
//     };

//   const requiredFields = ["projectId", "name"];

//   const projectGroupMutation = usePostV1ProjectGroup({
//     mutation: {
//       onSuccess: () => {
//         if (onSubmit) onSubmit();
//       },
//       onError: (error) => {
//         console.error("Project group creation failed:", error);
//       },
//     },
//   });

//   const projectGroupUpdateMutation = usePatchV1ProjectGroupId({
//     mutation: {
//       onSuccess: () => {
//         if (onSubmit) onSubmit();
//       },
//       onError: (error) => {
//         console.error("Project group update failed:", error);
//       },
//     },
//   });

//   const schemaForForm = editPayload
//     ? patchProjectGroupSchema
//     : insertProjectGroupSchema;

//   const form = useForm({
//     defaultValues,
//     validators: {
//       onSubmit: schemaForForm,
//     },
//     onSubmit: async ({ value }) => {
//       if (editPayload) {
//         await projectGroupUpdateMutation.mutateAsync({
//           data: value,
//           id: editPayload.id,
//         });
//       } else {
//         await projectGroupMutation.mutateAsync({
//           data: value as PostV1ProjectGroupBody,
//         });
//       }
//     },
//   });

//   return (
//     <>
//       <div className="overflow-auto">
//         <Form
//           id="project-group-form"
//           onSubmit={(e) => {
//             e.preventDefault();
//             form.handleSubmit();
//           }}
//           className="flex flex-col gap-y-3 p-6"
//         >
//           <div className="grid gap-3">
//             {/* <form.Field name="projectId">
//               {(field) => (
//                 <FormField
//                   field={field}
//                   required={requiredFields.includes("projectId")}
//                 >
//                   <FormItem>
//                     <FormLabel>Project</FormLabel>
//                     <FormControl>
//                       <Select
//                         value={field.state.value}
//                         onValueChange={(value) => field.handleChange(value)}
//                       >
//                         <SelectTrigger>
//                           <SelectValue placeholder="Select a project" />
//                         </SelectTrigger>
//                         <SelectContent>
//                           {projects?.items?.map((project) => (
//                             <SelectItem key={project.id} value={project.id}>
//                               {project.name}
//                             </SelectItem>
//                           ))}
//                         </SelectContent>
//                       </Select>
//                     </FormControl>
//                     <FormMessage />
//                   </FormItem>
//                 </FormField>
//               )}
//             </form.Field> */}

//             <form.Field name="name">
//               {(field) => (
//                 <FormField
//                   field={field}
//                   required={requiredFields.includes("name")}
//                 >
//                   <FormItem>
//                     <FormLabel>Name</FormLabel>
//                     <FormControl>
//                       <Input
//                         placeholder="Project group name"
//                         value={field.state.value}
//                         onChange={(e) => field.handleChange(e.target.value)}
//                         onBlur={field.handleBlur}
//                       />
//                     </FormControl>
//                     <FormMessage />
//                   </FormItem>
//                 </FormField>
//               )}
//             </form.Field>

//             <form.Field name="description">
//               {(field) => (
//                 <FormField
//                   field={field}
//                   required={requiredFields.includes("description")}
//                 >
//                   <FormItem>
//                     <FormLabel>Description</FormLabel>
//                     <FormControl>
//                       <Textarea
//                         placeholder="Project group description"
//                         value={field.state.value || ""}
//                         onChange={(e) => field.handleChange(e.target.value)}
//                         onBlur={field.handleBlur}
//                       />
//                     </FormControl>
//                     <FormMessage />
//                   </FormItem>
//                 </FormField>
//               )}
//             </form.Field>
//           </div>
//         </Form>
//       </div>
//       <DialogFooter className="">
//         <Button variant="outline" onClick={onCancel}>
//           Cancel
//         </Button>
//         <Button form="project-group-form" type="submit">
//           {editPayload ? "Update" : "Create"}
//         </Button>
//       </DialogFooter>
//     </>
//   );
// };

export const ProjectGroupForm = () => {
  return (
    <div className="p-6">
      <h2 className="font-semibold text-lg">Project Group Form</h2>
      <p className="mt-2 text-muted-foreground text-sm">
        This is a placeholder for the project group form component.
      </p>
    </div>
  );
};
