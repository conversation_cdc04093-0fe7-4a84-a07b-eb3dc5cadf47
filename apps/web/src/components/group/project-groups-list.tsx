// import {
//   deleteV1ProjectGroupId,
//   useGetV1ProjectGroup,
// } from "@/web/services/project-group/project-group";
// import { useGetV1Project } from "@/web/services/project/project"; // Import project hook
// import { useRootStore } from "@/web/store/store"; // Import store for workspaceId
// import { GetV1ProjectGroup200ItemsItem } from "@/web/services/types";
// import { Button } from "@repo/ui/components/button";
// import { Confirmation } from "@repo/ui/components/custom/confirmDialog";
// import CustomDataGrid, {
//   MuiColumn,
// } from "@repo/ui/components/custom/customDataGrid";
// import {
//   Dialog,
//   DialogContent,
//   DialogHeader,
//   DialogTitle,
//   DialogTrigger,
// } from "@repo/ui/components/dialog";
// import { useState } from "react";
// import { ProjectGroupForm } from "./project-group-form";

// export default function ProjectGroupList() {
//   const currentWorkspaceId = useRootStore((state) => state.currentWorkspaceId); // Get workspaceId
//   const [pagination, setPagination] = useState({
//     page: 1,
//     pageSize: 5,
//   });
//   const [sorting, setSorting] = useState({
//     field: "name",
//     order: "ASC",
//   });
//   const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

//   // Fetch project groups
//   const { data, isLoading, refetch } = useGetV1ProjectGroup({
//     pagination: JSON.stringify(pagination),
//     sort: JSON.stringify(sorting),
//   });

//   // Fetch projects for the current workspace
//   const { data: projects, isLoading: isProjectsLoading } = useGetV1Project({
//     filters: JSON.stringify({
//       workspaceId: {
//         $eq: currentWorkspaceId,
//       },
//     }),
//   });

//   const handleRowSelectionChange = (selection: Record<string, boolean>) => {
//     console.log("Selected rows:", selection);
//   };

//   // Map projectId to project name
//   const getProjectName = (projectId: string) => {
//     const project = projects?.items.find((p) => p.id === projectId);
//     return project ? project.name : projectId; // Fallback to projectId if name not found
//   };

//   const columns: MuiColumn<GetV1ProjectGroup200ItemsItem>[] = [
//     {
//       field: "name",
//       headerName: "Name",
//       width: 200,
//       sortable: true,
//     },
//     {
//       field: "projectId",
//       headerName: "Project",
//       width: 200,
//       sortable: true,
//       renderCell: (params) => getProjectName(params.row.original.projectId), // Display project name
//     },
//     {
//       field: "description",
//       headerName: "Description",
//       width: 300,
//     },
//     {
//       field: "createdAt",
//       headerName: "Created At",
//       width: 150,
//       sortable: true,
//       renderCell: (params) =>
//         new Date(params.row.original.createdAt).toLocaleDateString(),
//     },
//     {
//       field: "updatedAt",
//       headerName: "Updated At",
//       width: 150,
//       sortable: true,
//       renderCell: (params) =>
//         new Date(params.row.original.updatedAt).toLocaleDateString(),
//     },
//     {
//       field: "id",
//       headerName: "Actions",
//       sortable: false,
//       width: 200,
//       renderCell: (params) => (
//         <div className="flex space-x-2">
//           <Dialog>
//             <DialogTrigger asChild>
//               <Button variant="outline" size="sm">
//                 Edit
//               </Button>
//             </DialogTrigger>
//             <DialogContent>
//               <DialogHeader>
//                 <DialogTitle>Edit Project Group</DialogTitle>
//               </DialogHeader>
//               <ProjectGroupForm
//                 editPayload={params.row.original}
//                 onSubmit={() => refetch()} // Refresh to update table
//                 onCancel={() => setIsAddDialogOpen(false)}
//               />
//             </DialogContent>
//           </Dialog>
//           <Confirmation
//             title="Are you sure?"
//             description="This action cannot be undone."
//             cancelButton={{
//               name: "Cancel",
//               onClick: () => console.log("Cancelled!"),
//               variant: "secondary",
//             }}
//             confirmButton={{
//               name: "Delete",
//               size: "sm",
//               onClick: async () => {
//                 await deleteV1ProjectGroupId(params.row.original.id);
//                 refetch();
//               },
//               variant: "destructive",
//             }}
//           >
//             <Button variant="destructive" size="sm">
//               Delete
//             </Button>
//           </Confirmation>
//         </div>
//       ),
//     },
//   ];

//   return (
//     <div className="container p-4 h-full">
//       <div className="flex justify-end">
//         <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
//           <DialogContent>
//             <DialogHeader>
//               <DialogTitle>Create Project Group</DialogTitle>
//             </DialogHeader>
//             <ProjectGroupForm
//               onSubmit={() => {
//                 setIsAddDialogOpen(false);
//                 refetch();
//               }}
//               onCancel={() => setIsAddDialogOpen(false)}
//             />
//           </DialogContent>
//         </Dialog>
//       </div>
//       <CustomDataGrid<GetV1ProjectGroup200ItemsItem>
//         rows={data?.items}
//         totalRows={data?.meta.totalItems}
//         columns={columns}
//         pageSize={pagination.pageSize}
//         pageSizeOptions={[5, 10, 20]}
//         checkboxSelection
//         onRowSelectionChange={handleRowSelectionChange}
//         disableSelectionOnClick={false}
//         autoHeight={false}
//         loading={isLoading || isProjectsLoading} // Account for projects loading
//         rowHeight={52}
//         initialState={{
//           sorting: [{ id: "name", desc: false }],
//         }}
//         onSortModelChange={(model) => {
//           setSorting({
//             field: model[0]?.id || "name",
//             order: model[0]?.desc ? "DESC" : "ASC",
//           });
//           setPagination((prev) => ({ ...prev, page: 1 }));
//         }}
//         onFilterModelChange={(model) => console.log("Filter changed:", model)}
//         onPageChange={(page) => {
//           console.log("Page changed:", page);
//           setPagination((prev) => ({ ...prev, page: page + 1 }));
//         }}
//         onPageSizeChange={(size) =>
//           setPagination((prev) => ({ ...prev, pageSize: size }))
//         }
//         addButton={{
//           onClick: () => setIsAddDialogOpen(true),
//           title: "Add Project Group",
//           size: "sm",
//           variant: "default",
//         }}
//       />
//     </div>
//   );
// }

export const ProjectGroupsList = () => {
  return (
    <div className="p-6">
      <h2 className="font-semibold text-lg">Project Groups List</h2>
      <p className="mt-2 text-muted-foreground text-sm">
        This component will list all project groups associated with the current workspace.
      </p>
      {/* Placeholder for project groups list */}
      <div className="mt-4">
        {/* Render project groups here */}
        {/* Example: <ProjectGroupItem /> */}
      </div>
    </div>
  );
};
