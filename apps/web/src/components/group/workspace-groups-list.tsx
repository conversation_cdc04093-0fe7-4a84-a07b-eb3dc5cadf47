// import { GetV1WorkspaceGroup200ItemsItem } from "@/web/services/types";
// import {
//   deleteV1WorkspaceGroupId,
//   useGetV1WorkspaceGroup,
// } from "@/web/services/workspace-group/workspace-group";
// import { Button } from "@repo/ui/components/button";
// import { Confirmation } from "@repo/ui/components/custom/confirmDialog";
// import CustomDataGrid, {
//   MuiColumn,
// } from "@repo/ui/components/custom/customDataGrid";
// import {
//   Dialog,
//   DialogContent,
//   DialogHeader,
//   DialogTitle,
//   DialogTrigger,
// } from "@repo/ui/components/dialog";
// import { useState } from "react";
// import { WorkspaceGroupForm } from "./workspace-group-form";

// export default function WorkspaceGroupList() {
//   const [pagination, setPagination] = useState({
//     page: 1,
//     pageSize: 5,
//   });
//   const [sorting, setSorting] = useState({
//     field: "name",
//     order: "ASC",
//   });
//   const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
//   const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

//   const { data, isLoading, refetch } = useGetV1WorkspaceGroup({
//     pagination: JSON.stringify(pagination),
//     sort: JSON.stringify(sorting),
//   });

//   const handleRowSelectionChange = (selection: Record<string, boolean>) => {
//     console.log("Selected rows:", selection);
//   };

//   const columns: MuiColumn<GetV1WorkspaceGroup200ItemsItem>[] = [
//     {
//       field: "name",
//       headerName: "Name",
//       width: 200,
//       sortable: true,
//     },
//     {
//       field: "workspaceId",
//       headerName: "Workspace",
//       width: 200,
//       sortable: true,
//     },
//     {
//       field: "description",
//       headerName: "Description",
//       width: 300,
//     },
//     {
//       field: "createdAt",
//       headerName: "Created At",
//       width: 150,
//       sortable: true,
//       renderCell: (params) =>
//         new Date(params.row.original.createdAt).toLocaleDateString(),
//     },
//     {
//       field: "updatedAt",
//       headerName: "Updated At",
//       width: 150,
//       sortable: true,
//       renderCell: (params) =>
//         new Date(params.row.original.updatedAt).toLocaleDateString(),
//     },
//     {
//       field: "id",
//       headerName: "Actions",
//       sortable: false,
//       width: 200,
//       renderCell: (params) => (
//         <div className="flex space-x-2">
//           <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
//             <DialogTrigger asChild>
//               <Button variant="outline" size="sm">
//                 Edit
//               </Button>
//             </DialogTrigger>
//             <DialogContent>
//               <DialogHeader>
//                 <DialogTitle>Edit Workspace Group</DialogTitle>
//               </DialogHeader>
//               <WorkspaceGroupForm
//                 editPayload={params.row.original}
//                 onSubmit={() => {
//                   setIsEditDialogOpen(false);
//                   refetch();
//                 }}
//                 onCancel={() => setIsEditDialogOpen(false)}
//               />
//             </DialogContent>
//           </Dialog>
//           <Confirmation
//             title="Are you sure?"
//             description="This action cannot be undone."
//             cancelButton={{
//               name: "Cancel",
//               onClick: () => console.log("Cancelled!"),
//               variant: "secondary",
//             }}
//             confirmButton={{
//               name: "Delete",
//               size: "sm",
//               onClick: async () => {
//                 await deleteV1WorkspaceGroupId(params.row.original.id);
//                 refetch();
//               },
//               variant: "destructive",
//             }}
//           >
//             <Button variant="destructive" size="sm">
//               Delete
//             </Button>
//           </Confirmation>
//         </div>
//       ),
//     },
//   ];

//   return (
//     <div className="container p-4 h-full">
//       <CustomDataGrid<GetV1WorkspaceGroup200ItemsItem>
//         rows={data?.items}
//         totalRows={data?.meta.totalItems}
//         columns={columns}
//         pageSize={pagination.pageSize}
//         pageSizeOptions={[5, 10, 20]}
//         checkboxSelection
//         onRowSelectionChange={handleRowSelectionChange}
//         disableSelectionOnClick={false}
//         autoHeight={false}
//         loading={isLoading}
//         rowHeight={52}
//         initialState={{
//           sorting: [{ id: "name", desc: false }],
//         }}
//         onSortModelChange={(model) => {
//           setSorting({
//             field: model[0]?.id || "name",
//             order: model[0]?.desc ? "DESC" : "ASC",
//           });
//           setPagination((prev) => ({ ...prev, page: 1 }));
//         }}
//         onFilterModelChange={(model) => console.log("Filter changed:", model)}
//         onPageChange={(page) => {
//           console.log("Page changed:", page);
//           setPagination((prev) => ({ ...prev, page: page + 1 }));
//         }}
//         onPageSizeChange={(size) =>
//           setPagination((prev) => ({ ...prev, pageSize: size }))
//         }
//         addButton={{
//           onClick: () => setIsAddDialogOpen(true),
//           title: "Add Workspace Group",
//           size: "sm",
//           variant: "default",
//         }}
//       />
//       <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
//         <DialogContent>
//           <DialogHeader>
//             <DialogTitle>Create Workspace Group</DialogTitle>
//           </DialogHeader>
//           <WorkspaceGroupForm
//             onSubmit={() => {
//               setIsAddDialogOpen(false);
//               refetch();
//             }}
//             onCancel={() => setIsAddDialogOpen(false)}
//           />
//         </DialogContent>
//       </Dialog>
//     </div>
//   );
// }

export const WorkspaceGroupsList = () => {
  return (
    <div className="container h-full p-4">
      <h2 className="mb-4 font-semibold text-lg">Workspace Groups List</h2>
      <p className="mb-6 text-muted-foreground text-sm">
        This is a placeholder for the Workspace Groups List component.
      </p>
      {/* Add list items and logic here */}
    </div>
  );
};
