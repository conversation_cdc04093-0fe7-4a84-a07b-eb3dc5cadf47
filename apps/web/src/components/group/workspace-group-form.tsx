// import {
//   GetV1WorkspaceGroup200ItemsItem,
//   PatchV1WorkspaceGroupIdBody,
//   PostV1WorkspaceGroupBody,
// } from "@/web/services/types";
// import {
//   usePatchV1WorkspaceGroupId,
//   usePostV1WorkspaceGroup,
// } from "@/web/services/workspace-group/workspace-group";
// import { useGetV1Workspace } from "@/web/services/workspace/workspace";
// import { useRootStore } from "@/web/store/store";
// import {
//   insertWorkspaceGroupSchema,
//   patchWorkspaceGroupSchema,
// } from "@repo/db/schema";
// import { Button } from "@repo/ui/components/button";
// import { DialogFooter } from "@repo/ui/components/dialog";
// import {
//   Form,
//   FormControl,
//   FormField,
//   FormItem,
//   FormLabel,
//   FormMessage,
// } from "@repo/ui/components/form";
// import { Input } from "@repo/ui/components/input";
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
// } from "@repo/ui/components/select";
// import { Textarea } from "@repo/ui/components/textarea";
// import { useForm } from "@tanstack/react-form";

// interface WorkspaceGroupFormProps {
//   onSubmit?: () => void;
//   onCancel?: () => void;
//   editPayload?: GetV1WorkspaceGroup200ItemsItem | null;
// }

// export const WorkspaceGroupForm: React.FC<WorkspaceGroupFormProps> = ({
//   onSubmit,
//   onCancel,
//   editPayload,
// }) => {
//   const { data: workspaces } = useGetV1Workspace();
//   const currentWorkspaceId = useRootStore((state) => state.currentWorkspaceId);

//   const defaultValues: PatchV1WorkspaceGroupIdBody | PostV1WorkspaceGroupBody =
//     editPayload || {
//       workspaceId: currentWorkspaceId,
//       name: "",
//       description: "",
//     };

//   const requiredFields = ["workspaceId", "name"];

//   const workspaceGroupMutation = usePostV1WorkspaceGroup({
//     mutation: {
//       onSuccess: () => {
//         if (onSubmit) onSubmit();
//       },
//       onError: (error) => {
//         console.error("Workspace group creation failed:", error);
//       },
//     },
//   });

//   const workspaceGroupUpdateMutation = usePatchV1WorkspaceGroupId({
//     mutation: {
//       onSuccess: () => {
//         if (onSubmit) onSubmit();
//       },
//       onError: (error) => {
//         console.error("Workspace group update failed:", error);
//       },
//     },
//   });

//   const schemaForForm = editPayload
//     ? patchWorkspaceGroupSchema
//     : insertWorkspaceGroupSchema;

//   const form = useForm({
//     defaultValues,
//     validators: {
//       onSubmit: schemaForForm,
//     },
//     onSubmit: async ({ value }) => {
//       if (editPayload) {
//         await workspaceGroupUpdateMutation.mutateAsync({
//           data: value,
//           id: editPayload.id,
//         });
//       } else {
//         await workspaceGroupMutation.mutateAsync({
//           data: value as PostV1WorkspaceGroupBody,
//         });
//       }
//     },
//   });

//   return (
//     <>
//       <div className="overflow-auto">
//         <Form
//           id="workspace-group-form"
//           onSubmit={(e) => {
//             e.preventDefault();
//             form.handleSubmit();
//           }}
//           className="flex flex-col gap-y-3 p-6"
//         >
//           <div className="grid gap-3">
//             <form.Field name="workspaceId">
//               {(field) => (
//                 <FormField
//                   field={field}
//                   required={requiredFields.includes("workspaceId")}
//                 >
//                   <FormItem>
//                     <FormLabel>Workspace</FormLabel>
//                     <FormControl>
//                       <Select
//                         value={field.state.value}
//                         onValueChange={(value) => field.handleChange(value)}
//                       >
//                         <SelectTrigger>
//                           <SelectValue placeholder="Select a workspace" />
//                         </SelectTrigger>
//                         <SelectContent>
//                           {workspaces?.items?.map((workspace) => (
//                             <SelectItem key={workspace.id} value={workspace.id}>
//                               {workspace.name}
//                             </SelectItem>
//                           ))}
//                         </SelectContent>
//                       </Select>
//                     </FormControl>
//                     <FormMessage />
//                   </FormItem>
//                 </FormField>
//               )}
//             </form.Field>

//             <form.Field name="name">
//               {(field) => (
//                 <FormField
//                   field={field}
//                   required={requiredFields.includes("name")}
//                 >
//                   <FormItem>
//                     <FormLabel>Name</FormLabel>
//                     <FormControl>
//                       <Input
//                         placeholder="Workspace group name"
//                         value={field.state.value}
//                         onChange={(e) => field.handleChange(e.target.value)}
//                         onBlur={field.handleBlur}
//                       />
//                     </FormControl>
//                     <FormMessage />
//                   </FormItem>
//                 </FormField>
//               )}
//             </form.Field>

//             <form.Field name="description">
//               {(field) => (
//                 <FormField
//                   field={field}
//                   required={requiredFields.includes("description")}
//                 >
//                   <FormItem>
//                     <FormLabel>Description</FormLabel>
//                     <FormControl>
//                       <Textarea
//                         placeholder="Workspace group description"
//                         value={field.state.value || ""}
//                         onChange={(e) => field.handleChange(e.target.value)}
//                         onBlur={field.handleBlur}
//                       />
//                     </FormControl>
//                     <FormMessage />
//                   </FormItem>
//                 </FormField>
//               )}
//             </form.Field>
//           </div>
//         </Form>
//       </div>
//       <DialogFooter className="">
//         <Button variant="outline" onClick={onCancel}>
//           Cancel
//         </Button>
//         <Button form="workspace-group-form" type="submit">
//           {editPayload ? "Update" : "Create"}
//         </Button>
//       </DialogFooter>
//     </>
//   );
// };

export const WorkspaceGroupForm = () => {
  return (
    <div className="p-6">
      <h2 className="mb-4 font-semibold text-lg">Workspace Group Form</h2>
      <p className="mb-6 text-muted-foreground text-sm">
        This is a placeholder for the Workspace Group Form component.
      </p>
      {/* Add form fields and logic here */}
    </div>
  );
};
