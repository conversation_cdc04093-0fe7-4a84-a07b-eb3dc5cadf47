import { Checkbox } from '@repo/ui/components/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { useToast } from '@repo/ui/components/use-toast';
import { THEME_VARIANTS } from '../constants/theme-variants';
import { useTheme } from '../hooks/use-theme';

export function ThemeVariantSelector() {
  const { variant, setVariant, isAutoRotating, setIsAutoRotating } = useTheme();
  const { toast } = useToast();

  // Stop auto-rotation when user manually selects a theme
  const handleManualChange = (newVariant: string) => {
    setIsAutoRotating(false);
    setVariant(newVariant);

    const selectedTheme = THEME_VARIANTS.find((theme) => theme.identifier === newVariant);

    toast({
      title: 'Theme Selected',
      description: `Manually switched to ${selectedTheme?.label || newVariant}`,
      duration: 3000,
    });
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="font-medium text-foreground text-sm">Theme Variant</label>
        <Select onValueChange={handleManualChange} value={variant}>
          <SelectTrigger className="mt-2 w-full">
            <SelectValue placeholder="Select a theme variant" />
          </SelectTrigger>
          <SelectContent>
            {THEME_VARIANTS.map((themeVariant) => (
              <SelectItem key={themeVariant.identifier} value={themeVariant.identifier}>
                {themeVariant.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          checked={isAutoRotating}
          id="auto-rotate"
          onCheckedChange={(checked) => {
            setIsAutoRotating(checked as boolean);
            if (checked) {
              toast({
                title: 'Auto-rotation enabled',
                description: 'Theme will change every 2 seconds',
                duration: 2000,
              });
            }
          }}
        />
        <label
          className="font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          htmlFor="auto-rotate"
        >
          Auto-rotate themes every 2 seconds
        </label>
      </div>
    </div>
  );
}
