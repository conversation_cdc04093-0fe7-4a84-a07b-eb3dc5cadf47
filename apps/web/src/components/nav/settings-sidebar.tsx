'use client';

import { buttonVariants } from '@repo/ui/components/button';
import { cn } from '@repo/ui/lib/utils';
import { Link, useRouter } from '@tanstack/react-router';
import { useEffect, useState } from 'react';

interface SidebarNavProps extends React.HTMLAttributes<HTMLElement> {
  items: {
    href: string;
    title: string;
  }[];
}

export function SidebarNav({ className, items, ...props }: SidebarNavProps) {
  const router = useRouter({});
  const [currentUrl, setCurrentUrl] = useState(router.state.location.pathname);

  useEffect(() => {
    // Subscribe to router state changes
    const unsubscribe = router.subscribe('onLoad', (state) => {
      // console.log("Router state changed to:", state.toLocation.pathname);
      setCurrentUrl(state.toLocation.pathname); // Update local state
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, [router]);

  return (
    <nav
      className={cn(' flex space-x-2 lg:flex-col lg:space-x-0 lg:space-y-1', className)}
      {...props}
    >
      {items.map((item) => (
        <Link
          className={cn(
            buttonVariants({ variant: 'ghost' }),
            currentUrl === item.href
              ? 'bg-muted hover:bg-muted'
              : 'hover:bg-transparent hover:underline',
            'w-full justify-start',
          )}
          key={item.href}
          to={item.href}
        >
          {item.title}
        </Link>
      ))}
    </nav>
  );
}
