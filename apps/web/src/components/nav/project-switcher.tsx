/* eslint-disable react-hooks/exhaustive-deps */
'use client';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@repo/ui/components/dropdown-menu';
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@repo/ui/components/sidebar';
import { useNavigate } from '@tanstack/react-router';
import { ChevronsUpDown, Plus } from 'lucide-react';
import * as React from 'react';
import { useRootStore } from '@/web/store/store';

export function ProjectSwitcher({
  teams,
  type = 'workspace',
  onAdd = () => {},
}: {
  teams: {
    id: string;
    name: string;
    logoUrl: string | null | undefined;
    description: string | null;
    orgId?: string; // Only for workspaces
    orgName?: string; // Only for workspaces
  }[];
  type?: 'workspace' | 'project';
  onAdd?: () => void;
}) {
  const navigate = useNavigate();
  const { isMobile } = useSidebar();
  const [activeTeam, setActiveTeam] = React.useState(() => {
    if (type === 'project') {
      return null; // No default project selection
    }
    return teams[0];
  });
  const setCurrentWorkspaceId = useRootStore((state) => state.setCurrentWorkspaceId);
  const setCurrentProjectId = useRootStore((state) => state.setCurrentProjectId);
  const setCurrentOrganizationId = useRootStore((state) => state.setCurrentOrganizationId);
  const currentWorkspaceId = useRootStore((state) => state.currentWorkspaceId);
  const currentProjectId = useRootStore((state) => state.currentProjectId);

  React.useEffect(() => {
    if (type === 'workspace' && !currentWorkspaceId && teams.length > 0) {
      setCurrentWorkspaceId(teams[0]?.id);
      setCurrentOrganizationId(teams[0]?.orgId || '');
    }
    // Don't auto-set project - let user explicitly select
  }, [teams, currentWorkspaceId, setCurrentOrganizationId, setCurrentWorkspaceId, type]);

  React.useEffect(() => {
    if (type === 'workspace') {
      setActiveTeam(teams.find((team) => team.id === currentWorkspaceId) || teams[0]);
    } else {
      // For projects, only set activeTeam if there's a valid currentProjectId
      if (currentProjectId && currentProjectId !== 'undefined') {
        const foundTeam = teams.find((team) => team.id === currentProjectId);
        setActiveTeam(foundTeam || null);
      } else {
        setActiveTeam(null);
      }
    }
  }, [currentWorkspaceId, currentProjectId, teams, type]);

  // Default display for no selection
  const displayTeam = activeTeam || {
    id: `no-${type}`,
    name: type === 'project' ? 'Select a project' : `No ${type}s`,
    logoUrl: null,
    description: null,
    orgId: '',
    orgName: '',
  };

  // Helper function to get the first letter of the name
  const getAvatarLetter = (name: string) => {
    return name ? name[0]?.toUpperCase() : '?';
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild className="">
            <SidebarMenuButton
              className={`${
                type === 'workspace' ? 'mt-2 h-8 w-8 p-0' : ''
              } group data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground`}
              size="lg"
            >
              <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                {displayTeam?.logoUrl ? (
                  <img
                    alt={`${type}-logo`}
                    className="h-6 w-6 rounded-sm"
                    src={displayTeam.logoUrl}
                  />
                ) : (
                  <div className="flex size-6 items-center justify-center rounded-md bg-sidebar-accent font-medium text-sidebar-accent-foreground text-sm">
                    {activeTeam ? getAvatarLetter(displayTeam?.name) : <Plus className="size-4" />}
                  </div>
                )}
              </div>
              {type !== 'workspace' && (
                <>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-medium">{displayTeam?.name}</span>
                    <span className="truncate text-xs">{displayTeam?.description}</span>
                  </div>
                  <ChevronsUpDown className="ml-auto" />
                </>
              )}
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="start"
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            side={isMobile ? 'bottom' : 'right'}
            sideOffset={4}
          >
            <DropdownMenuLabel className="text-muted-foreground text-xs">
              {type === 'workspace' ? 'Workspace' : 'Project'}
            </DropdownMenuLabel>
            {teams.map((team, index) => (
              <DropdownMenuItem
                className={`${
                  activeTeam?.id === team?.id
                    ? 'bg-sidebar-primary text-sidebar-primary-foreground'
                    : ''
                } gap-2 p-2`}
                key={team.id}
                onClick={() => {
                  setActiveTeam(team);
                  if (type === 'workspace') {
                    setCurrentWorkspaceId(team?.id);
                    setCurrentOrganizationId(team?.orgId || '');
                    navigate({
                      to: '/workspace/$wsId',
                      params: { wsId: team?.id },
                    });
                  } else {
                    setCurrentProjectId(team?.id);
                    navigate({
                      to: '/project/$projId',
                      params: { projId: team?.id },
                    });
                  }
                }}
              >
                <div className="flex size-6 items-center justify-center rounded-md border">
                  {team.logoUrl ? (
                    <img alt={`${type}-logo`} className="h-6 w-6 rounded-sm" src={team.logoUrl} />
                  ) : (
                    <div className="flex size-6 items-center justify-center rounded-md bg-sidebar-accent font-medium text-sidebar-accent-foreground text-sm">
                      {activeTeam ? getAvatarLetter(team?.name) : <Plus className="size-4" />}
                    </div>
                  )}
                </div>
                <div className="flex flex-col">
                  <span>{team.name}</span>
                  {type === 'workspace' && team.orgName && (
                    <span className="text-muted-foreground text-xs">{team.orgName}</span>
                  )}
                </div>
                <DropdownMenuShortcut>⌘{index + 1}</DropdownMenuShortcut>
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator />
            <DropdownMenuItem className="gap-2 p-2">
              <div className="flex size-6 items-center justify-center rounded-md border bg-transparent">
                <Plus className="size-4" />
              </div>
              <div className="font-medium text-muted-foreground" onClick={onAdd}>
                Add {type === 'workspace' ? 'workspace' : 'project'}
              </div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
