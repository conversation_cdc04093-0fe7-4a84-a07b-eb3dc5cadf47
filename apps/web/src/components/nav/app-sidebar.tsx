'use client';

import { <PERSON><PERSON> } from '@repo/ui/components/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@repo/ui/components/dialog';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from '@repo/ui/components/sidebar';
import {
  Link,
  // useNavigate,
  useParams,
  useRouter,
} from '@tanstack/react-router';
import { Briefcase, CircuitBoard, Handshake, Home, Plus, Settings, Users } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { NavUser } from '@/web/components/nav/nav-user';
import { ProjectSwitcher } from '@/web/components/nav/project-switcher';
import { ThemeToggle } from '@/web/components/theme-toggle';
// Removed direct API imports - will use store data instead
import { useAppDataRefresh } from '@/web/hooks/use-app-data';
import { useRefreshSidebarData } from '@/web/hooks/use-refresh-sidebar-data';
import type { Project } from '@/web/services/hooks.schemas';
import { useRootStore } from '@/web/store/store';
import { ProjectForm } from '../project/project-form'; // Assume this exists
import { UserInvitationEnhancedForm } from '../user/invite-user-enhanced-form';
import { WorkspaceForm } from '../workspace/workspace-form';
import { NavProjects } from './nav-projects';

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const [openWorkspaceForm, setOpenWorkspaceForm] = useState(false);
  const [openProjectForm, setOpenProjectForm] = useState(false);
  const [openInviteUser, setOpenInviteUser] = useState(false);

  const currentWorkspaceId = useRootStore((state) => state.currentWorkspaceId);
  const currentProjectId = useRootStore((state) => state.currentProjectId);
  const currentOrganizationId = useRootStore((state) => state.currentOrganizationId);

  const setCurrentWorkspaceId = useRootStore((state) => state.setCurrentWorkspaceId);
  const setCurrentProjectId = useRootStore((state) => state.setCurrentProjectId);
  const setCurrentOrganizationId = useRootStore((state) => state.setCurrentOrganizationId);
  const currentUser = useRootStore((state) => state.currentUser);
  const { refetchWorkspaces, refetchProjects, refetchRoleAssignments } = useAppDataRefresh();
  const { refreshSidebarData } = useRefreshSidebarData();

  // Get role-based data from store
  const getOrganizationsFromRoleAssignments = useRootStore(
    (state) => state.getOrganizationsFromRoleAssignments,
  );
  const getWorkspacesFromRoleAssignments = useRootStore(
    (state) => state.getWorkspacesFromRoleAssignments,
  );
  const getProjectsFromRoleAssignments = useRootStore(
    (state) => state.getProjectsFromRoleAssignments,
  );
  // const getUserRoleAssignmentsByScope = useRootStore(
  //   (state) => state.getUserRoleAssignmentsByScope
  // );
  const isLoadingRoleAssignments = useRootStore((state) => state.isLoadingRoleAssignments);

  // Subscribe to actual store data to trigger re-renders
  const organizations = useRootStore((state) => state.organizations);
  const workspaces = useRootStore((state) => state.workspaces);
  const projects = useRootStore((state) => state.projects);
  // const roleAssignments = useRootStore((state) => state.roleAssignments);

  // Get organizations, workspaces, and projects from role assignments
  const userOrganizations = React.useMemo(() => {
    if (!currentUser?.id || isLoadingRoleAssignments) {
      return [];
    }
    const orgs = getOrganizationsFromRoleAssignments(currentUser.id);
    console.log(
      '[Sidebar] User organizations:',
      orgs.length,
      'from',
      organizations.length,
      'total',
    );
    return orgs;
  }, [
    currentUser?.id,
    getOrganizationsFromRoleAssignments,
    organizations,
    isLoadingRoleAssignments,
  ]);

  const userWorkspaces = React.useMemo(() => {
    if (!currentUser?.id || isLoadingRoleAssignments) {
      return [];
    }
    const ws = getWorkspacesFromRoleAssignments(currentUser.id);
    console.log('[Sidebar] User workspaces:', ws.length, 'from', workspaces.length, 'total');
    return ws;
  }, [currentUser?.id, getWorkspacesFromRoleAssignments, workspaces, isLoadingRoleAssignments]);

  const userProjects = React.useMemo(() => {
    if (!currentUser?.id || isLoadingRoleAssignments) {
      return [];
    }
    const proj = getProjectsFromRoleAssignments(currentUser.id);
    console.log('[Sidebar] User projects:', proj.length, 'from', projects.length, 'total');
    return proj;
  }, [currentUser?.id, getProjectsFromRoleAssignments, projects, isLoadingRoleAssignments]);

  // const workspaceRoleAssignments = React.useMemo(() => {
  //   if (!currentUser?.id) return [];
  //   return getUserRoleAssignmentsByScope(currentUser.id, "workspace");
  // }, [currentUser?.id, getUserRoleAssignmentsByScope, roleAssignments]);

  const { wsId } = useParams({ strict: false });

  // const navigate = useNavigate();s
  // const { isMobile } = useSidebar();

  const router = useRouter({});
  const [currentUrl, setCurrentUrl] = useState(router.state.location.pathname);

  useEffect(() => {
    // Subscribe to router state changes
    const unsubscribe = router.subscribe('onLoad', (state) => {
      // console.log("Router state changed to:", state.toLocation.pathname);
      setCurrentUrl(state.toLocation.pathname); // Update local state
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, [router]);

  // Get projects for current workspace (filtered by role assignments)
  const workspaceProjects = React.useMemo(() => {
    if (currentWorkspaceId) {
      // Filter projects that user has access to in the current workspace
      return userProjects.filter((proj) => proj.workspaceId === currentWorkspaceId);
    }
    return [];
  }, [currentWorkspaceId, userProjects]);

  // Combine workspaces with organization details
  const workspaceTeams = React.useMemo(() => {
    console.log('[Sidebar] Computing workspaceTeams:', {
      userWorkspaces: userWorkspaces.length,
      userOrganizations: userOrganizations.length,
      currentUserId: currentUser?.id,
    });
    return userWorkspaces.map((workspace) => {
      // Find the organization for this workspace
      const org = userOrganizations.find((o) => o.id === workspace.organizationId);
      return {
        id: workspace.id,
        name: workspace.name,
        logoUrl: workspace.icon,
        description: workspace.description || '',
        orgId: workspace.organizationId,
        orgName: org?.name || '',
      };
    });
  }, [userWorkspaces, userOrganizations, currentUser?.id]);

  // Sample data for navigation (unchanged)
  const data = {
    user: currentUser,
    navMain: [
      ...(wsId && wsId !== 'undefined'
        ? [
            {
              title: 'Workspace settings',
              url: `/workspace/${currentWorkspaceId}`,
              isActive: true,
              items: [
                { title: 'Overview', url: `/workspace/${currentWorkspaceId}` },
                {
                  title: 'Projects',
                  url: `/workspace/projects/${currentWorkspaceId}`,
                },
                {
                  title: 'Users',
                  url: `/workspace/users/${currentWorkspaceId}`,
                },
              ],
            },
          ]
        : []),
    ],
    projects: [
      ...(currentProjectId && currentProjectId !== 'undefined'
        ? [
            {
              name: 'Project Settings',
              url: `/project/${currentProjectId}`,
              icon: Briefcase,
              isActive: true,
              items: [
                { name: 'Overview', url: `/project/${currentProjectId}` },
                // { name: "Groups", url: `/project/groups/${currentProjectId}` },
                { name: 'Users', url: `/project/users/${currentProjectId}` },
              ],
            },
            {
              name: 'Boards',
              url: `/project/boards/${currentProjectId}`,
              icon: CircuitBoard,
              isActive: true,
              items: [
                {
                  name: 'Work Items',
                  url: `/project/boards/workItems/${currentProjectId}`,
                },
                {
                  name: 'Agile Boards',
                  url: `/project/boards/agileBoard/${currentProjectId}`,
                },
                {
                  name: 'Sprints',
                  url: `/project/boards/sprints/${currentProjectId}`,
                },
                {
                  name: 'Test Plans',
                  url: `/project/testPlans/${currentProjectId}`,
                },
              ],
            },
          ]
        : []),
    ],
  };

  // Navigate to the first workspace if none is selected
  useEffect(() => {
    if (workspaceTeams.length && !workspaceTeams.find((ws) => ws.id === currentWorkspaceId)) {
      setCurrentWorkspaceId(workspaceTeams[0]?.id || '');
      setCurrentOrganizationId(workspaceTeams[0]?.orgId || '');
      // Don't navigate automatically - let user choose
    }
  }, [workspaceTeams, currentWorkspaceId, setCurrentOrganizationId, setCurrentWorkspaceId]);

  // Only set current project if explicitly navigated to a project route
  useEffect(() => {
    // Extract project ID from current URL if it's a project route
    const projectMatch = currentUrl.match(/\/project\/([^/]+)/);
    if (projectMatch?.[1] && projectMatch[1] !== 'undefined') {
      const projectId = projectMatch[1];
      if (projects?.find((p) => p.id === projectId)) {
        setCurrentProjectId(projectId);
      }
    }
  }, [currentUrl, projects, setCurrentProjectId]);

  useEffect(() => {
    // If no project is selected, set to first available project in current workspace
    if (!currentOrganizationId && userOrganizations.length > 0) {
      setCurrentOrganizationId(userOrganizations[0].id);
    }
  }, [userOrganizations, currentOrganizationId, setCurrentOrganizationId]);

  return (
    <>
      <Sidebar
        className="overflow-hidden [&>[data-sidebar=sidebar]]:flex-row"
        collapsible="icon"
        {...props}
      >
        <Sidebar className="!w-[calc(var(--sidebar-width-icon)_+_1px)] border-r" collapsible="none">
          <SidebarHeader>
            <SidebarMenu>
              {!isLoadingRoleAssignments && workspaceTeams.length > 0 ? (
                <ProjectSwitcher
                  onAdd={() => setOpenWorkspaceForm(true)}
                  teams={workspaceTeams}
                  type="workspace"
                />
              ) : (
                <div className="px-2 py-1">
                  {isLoadingRoleAssignments ? (
                    <div className="text-muted-foreground text-sm">Loading...</div>
                  ) : (
                    <Button
                      className="h-8 w-full justify-start gap-2"
                      disabled={!currentOrganizationId}
                      onClick={() => setOpenWorkspaceForm(true)}
                      size="sm"
                      variant="ghost"
                    >
                      <Plus className="h-4 w-4" />
                      Add Workspace
                    </Button>
                  )}
                </div>
              )}
            </SidebarMenu>
          </SidebarHeader>
          <SidebarContent>
            <SidebarGroup>
              <SidebarGroupContent className="px-1.5 md:px-0">
                <SidebarMenu>
                  <SidebarMenuItem>
                    {currentWorkspaceId && currentWorkspaceId !== 'undefined' ? (
                      <Link params={{ wsId: currentWorkspaceId }} to="/workspace/$wsId">
                        <SidebarMenuButton
                          className="px-2.5 md:px-2"
                          isActive={currentUrl === `/workspace/${currentWorkspaceId}`}
                          tooltip={{ children: 'Overview', hidden: false }}
                        >
                          <Home />
                        </SidebarMenuButton>
                      </Link>
                    ) : (
                      <Link to="/">
                        <SidebarMenuButton
                          className="px-2.5 md:px-2"
                          isActive={currentUrl === '/'}
                          tooltip={{ children: 'Home', hidden: false }}
                        >
                          <Home />
                        </SidebarMenuButton>
                      </Link>
                    )}
                  </SidebarMenuItem>
                  {currentWorkspaceId && currentWorkspaceId !== 'undefined' && (
                    <>
                      <SidebarMenuItem>
                        <Link params={{ wsId: currentWorkspaceId }} to="/workspace/projects/$wsId">
                          {' '}
                          <SidebarMenuButton
                            isActive={currentUrl === `/workspace/projects/${currentWorkspaceId}`}
                            tooltip={{ children: 'Projects', hidden: false }}
                          >
                            <Handshake />
                          </SidebarMenuButton>
                        </Link>
                      </SidebarMenuItem>
                      <SidebarMenuItem>
                        <Link params={{ wsId: currentWorkspaceId }} to="/workspace/users/$wsId">
                          <SidebarMenuButton
                            isActive={currentUrl === `/workspace/users/${currentWorkspaceId}`}
                            tooltip={{ children: 'Users', hidden: false }}
                          >
                            <Users />
                          </SidebarMenuButton>
                        </Link>
                      </SidebarMenuItem>
                    </>
                  )}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </SidebarContent>
          <SidebarFooter>
            <SidebarMenu>
              <SidebarMenuItem>
                <ThemeToggle />
              </SidebarMenuItem>
              <SidebarMenuItem>
                <Link to="/settings">
                  <SidebarMenuButton
                    className="px-2.5 md:px-2"
                    isActive={currentUrl === '/settings' || currentUrl.startsWith('/settings/')}
                    tooltip={{ children: 'Settings', hidden: false }}
                  >
                    <Settings className="size-4" />
                  </SidebarMenuButton>
                </Link>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton
                  className="px-2.5 md:px-2"
                  onClick={() => setOpenInviteUser(true)}
                  tooltip={{ children: 'Invite User', hidden: false }}
                >
                  <Users className="size-4" />
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarFooter>
        </Sidebar>
        <Sidebar className="hidden flex-1 md:flex" collapsible="none">
          <SidebarHeader>
            {workspaceProjects.length > 0 ? (
              <ProjectSwitcher
                onAdd={() => setOpenProjectForm(true)}
                teams={workspaceProjects.map((proj: Project) => ({
                  id: proj.id,
                  name: proj.name,
                  logoUrl: proj.icon,
                  description: proj.description,
                }))}
                type="project"
              />
            ) : (
              <div className="px-4 py-2">
                <Button
                  className="w-full justify-start gap-2"
                  disabled={!currentWorkspaceId}
                  onClick={() => setOpenProjectForm(true)}
                  variant="ghost"
                >
                  <Plus className="h-4 w-4" />
                  Add Project
                </Button>
              </div>
            )}
          </SidebarHeader>
          <SidebarContent>
            {/* <NavMain items={data.navMain} /> */}
            <NavProjects projects={data.projects} projId={currentProjectId} />
          </SidebarContent>
          <SidebarFooter>
            <NavUser user={data.user} />
          </SidebarFooter>
          <SidebarRail />
        </Sidebar>
      </Sidebar>
      <Dialog onOpenChange={setOpenWorkspaceForm} open={openWorkspaceForm}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Workspace</DialogTitle>
          </DialogHeader>
          <WorkspaceForm
            editPayload={null}
            onCancel={() => setOpenWorkspaceForm(false)}
            onSubmit={() => {
              setOpenWorkspaceForm(false);
              refetchWorkspaces();
              refetchRoleAssignments();
              refreshSidebarData();
            }}
          />
        </DialogContent>
      </Dialog>

      <Dialog onOpenChange={setOpenProjectForm} open={openProjectForm}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Project</DialogTitle>
          </DialogHeader>
          <ProjectForm
            editPayload={null}
            onCancel={() => setOpenProjectForm(false)}
            onSubmit={() => {
              setOpenProjectForm(false);
              refetchProjects();
              refetchRoleAssignments();
              refreshSidebarData();
            }}
          />
        </DialogContent>
      </Dialog>
      {openInviteUser && (
        <Dialog onOpenChange={setOpenInviteUser} open={openInviteUser}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Invite User</DialogTitle>
            </DialogHeader>
            <UserInvitationEnhancedForm
              onCancel={() => setOpenInviteUser(false)}
              onSubmit={() => setOpenInviteUser(false)}
              scopeType="organization"
            />
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
