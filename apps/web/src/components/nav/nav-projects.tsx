'use client';

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@repo/ui/components/collapsible';
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from '@repo/ui/components/sidebar';
import { Link, useRouter } from '@tanstack/react-router';
import { ChevronRight, type LucideIcon } from 'lucide-react';
import { useEffect, useState } from 'react';

export function NavProjects({
  projects,
  projId,
}: {
  projects: {
    name: string;
    url: string;
    isActive?: boolean;
    icon: LucideIcon;
    items: {
      name: string;
      url: string;
    }[];
  }[];
  projId?: string;
}) {
  // If projId exists, use the projects array directly
  const projectList = projId ? projects : [];
  const router = useRouter({});
  const [currentUrl, setCurrentUrl] = useState(router.state.location.pathname);

  useEffect(() => {
    // Subscribe to router state changes
    const unsubscribe = router.subscribe('onLoad', (state) => {
      // console.log("Router state changed to:", state.toLocation.pathname);
      setCurrentUrl(state.toLocation.pathname); // Update local state
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, [router]);
  return (
    <SidebarGroup>
      <SidebarGroupLabel>Manage Project</SidebarGroupLabel>

      <SidebarMenu>
        {projectList.map((item) => (
          <Collapsible
            asChild
            className="group/collapsible"
            defaultOpen={item.isActive}
            key={item.name}
          >
            <SidebarMenuItem>
              <CollapsibleTrigger asChild>
                <SidebarMenuButton tooltip={item.name}>
                  <item.icon size={4} />
                  <span>{item.name}</span>
                  <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                </SidebarMenuButton>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <SidebarMenuSub>
                  {item.items.map((subItem) => (
                    <SidebarMenuSubItem key={subItem.name}>
                      <SidebarMenuSubButton asChild isActive={subItem.url === currentUrl}>
                        <Link to={subItem.url}>
                          <span>{subItem.name}</span>
                        </Link>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                  ))}
                </SidebarMenuSub>
              </CollapsibleContent>
            </SidebarMenuItem>
          </Collapsible>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  );
}
