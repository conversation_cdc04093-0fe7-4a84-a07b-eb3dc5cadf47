import { Button } from '@repo/ui/components/button';
import CustomDataGrid, { type MuiColumn } from '@repo/ui/components/custom/customDataGrid';
import { useState } from 'react';
import type { User } from '@/web/services/hooks.schemas';
import { useGetApiV1Users } from '@/web/services/users';

const columns: MuiColumn<User>[] = [
  {
    field: 'displayName',
    headerName: 'Display Name',
    width: 150,
    sortable: true,
  },
  {
    field: 'firstName',
    headerName: 'First Name',
    width: 120,
  },
  {
    field: 'lastName',
    headerName: 'Last Name',
    width: 120,
  },
  {
    field: 'email',
    headerName: 'Email',
    width: 120,
  },
  {
    field: 'phone',
    headerName: 'Phone',
    width: 120,
  },

  {
    field: 'id', // Can reuse a field or use a dummy one
    headerName: 'Actions',
    sortable: false,
    width: 150,
    renderCell: (params) => (
      <div className="flex space-x-2">
        <Button
          onClick={() => alert(`Editing ${params.row.original.displayName}`)}
          size="sm"
          variant="outline"
        >
          Edit
        </Button>
        <Button
          onClick={() => alert(`Deleting ${params.row.original.id}`)}
          size="sm"
          variant="destructive"
        >
          Delete
        </Button>
      </div>
    ),
  },
];

export default function UserList() {
  const handleRowSelectionChange = (_selection: Record<string, boolean>) => {};

  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 5,
  });
  const [sorting, setSorting] = useState({
    field: 'id',
    order: 'ASC',
  });

  const { data, isLoading } = useGetApiV1Users({
    sort: JSON.stringify(sorting),
    page: pagination.page,
    limit: pagination.pageSize,
  });

  return (
    <div className="container h-full p-4">
      <CustomDataGrid<User>
        autoHeight={false}
        checkboxSelection
        columns={columns}
        disableSelectionOnClick={false}
        initialState={{
          sorting: [{ id: 'id', desc: false }],
        }}
        loading={isLoading}
        onFilterModelChange={(_model) => {}}
        onPageChange={(page) => {
          setPagination((prev) => ({ ...prev, page: page + 1 }));
        }}
        onPageSizeChange={(size) => setPagination((prev) => ({ ...prev, pageSize: size }))}
        onRowSelectionChange={handleRowSelectionChange}
        onSortModelChange={(model) => {
          setSorting({
            field: model[0]?.id || 'id',
            order: model[0]?.desc ? 'DESC' : 'ASC',
          });
          setPagination((prev) => ({ ...prev, page: 1 }));
        }}
        pageSize={pagination.pageSize}
        pageSizeOptions={[5, 10, 20]}
        rowHeight={52}
        rows={data?.data}
        totalRows={data?.meta.totalItems}
      />
    </div>
  );
}
