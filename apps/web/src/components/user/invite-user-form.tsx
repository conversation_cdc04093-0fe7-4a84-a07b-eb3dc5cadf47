import { insertInvitationSchema, patchInvitationSchema } from '@repo/db/schema';
import { Button } from '@repo/ui/components/button';
import { DialogFooter } from '@repo/ui/components/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
import { ScrollArea } from '@repo/ui/components/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { useForm } from '@tanstack/react-form';
import type {
  Invitation,
  PatchApiV1InvitationsIdBody,
  PostApiV1InvitationsBody,
} from '@/web/services/hooks.schemas';
import { usePatchApiV1InvitationsId, usePostApiV1Invitations } from '@/web/services/invitations';
import { useGetApiV1Roles } from '@/web/services/roles';
import { useRootStore } from '@/web/store/store';

interface UserInvitationFormProps {
  onSubmit?: () => void;
  onCancel?: () => void;
  editPayload?: Invitation | null;
  scopeType?: 'organization' | 'workspace' | 'project'; // Allow scope type to be passed in
}

export const UserInvitationForm: React.FC<UserInvitationFormProps> = ({
  onSubmit,
  onCancel,
  editPayload,
  scopeType = 'organization', // Default to organization
}) => {
  // Fetch organization-level roles
  const { data: roles } = useGetApiV1Roles({
    filters: JSON.stringify({ level: { $eq: 'organization' } }),
  });

  const currentUser = useRootStore((state) => state.currentUser);
  const currentOrganizationId = useRootStore((state) => state.currentOrganizationId);
  const currentWorkspaceId = useRootStore((state) => state.currentWorkspaceId);
  const currentProjectId = useRootStore((state) => state.currentProjectId);

  const defaultValues: PatchApiV1InvitationsIdBody | PostApiV1InvitationsBody = editPayload || {
    email: '',
    roleId: '',
    invitedBy: currentUser?.id,
    scopeId:
      scopeType === 'organization'
        ? currentOrganizationId
        : scopeType === 'workspace'
          ? currentWorkspaceId
          : currentProjectId,
    scopeType,
  };

  const requiredFields = ['email', 'roleId'];

  const userInvitationMutation = usePostApiV1Invitations({
    mutation: {
      onSuccess: () => {
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (_error) => {},
    },
  });

  const userInvitationUpdateMutation = usePatchApiV1InvitationsId({
    mutation: {
      onSuccess: () => {
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (_error) => {},
    },
  });

  const schemaForForm = editPayload ? patchInvitationSchema : insertInvitationSchema;

  const form = useForm({
    defaultValues,
    validators: {
      onSubmit: schemaForForm,
    },
    onSubmit: async ({ value }) => {
      if (editPayload) {
        await userInvitationUpdateMutation.mutateAsync({
          data: value,
          id: editPayload.id,
        });
      } else {
        await userInvitationMutation.mutateAsync({
          data: value as PostApiV1InvitationsBody,
        });
      }
    },
  });

  return (
    <div className="flex h-full flex-col">
      <ScrollArea className="flex-1 px-6 py-4">
        <Form
          className="flex flex-col gap-y-4"
          id="user-invitation-form"
          onSubmit={(e) => {
            e.preventDefault();
            form.handleSubmit();
          }}
        >
          <div className="grid gap-4">
            <form.Field name="email">
              {(field) => (
                <FormField field={field} required={requiredFields.includes('email')}>
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="<EMAIL>"
                        type="email"
                        value={(field.state.value as string) || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>

            <form.Field name="roleId">
              {(field) => (
                <FormField field={field} required={requiredFields.includes('roleId')}>
                  <FormItem>
                    <FormLabel>Role</FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={(value) => field.handleChange(value)}
                        value={field.state.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select a role" />
                        </SelectTrigger>
                        <SelectContent>
                          {roles?.data?.map((role) => (
                            <SelectItem key={role.id} value={role.id}>
                              {role.name}
                              {role.description && (
                                <span className="ml-2 text-muted-foreground">
                                  - {role.description}
                                </span>
                              )}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>
          </div>
        </Form>
      </ScrollArea>

      <DialogFooter className="border-t px-6 py-4">
        <Button
          disabled={userInvitationMutation.isPending || userInvitationUpdateMutation.isPending}
          onClick={onCancel}
          variant="outline"
        >
          Cancel
        </Button>
        <Button
          disabled={userInvitationMutation.isPending || userInvitationUpdateMutation.isPending}
          form="user-invitation-form"
          isLoading={userInvitationMutation.isPending || userInvitationUpdateMutation.isPending}
          type="submit"
        >
          {editPayload ? 'Update' : 'Send Invitation'}
        </Button>
      </DialogFooter>
    </div>
  );
};
