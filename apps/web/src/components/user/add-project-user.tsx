// import { useGetV1Role } from "@/web/services/role/role";
// import {
//   GetV1User200ItemsItem,
//   GetV1UserProjectRole200ItemsItem,
//   PatchV1UserProjectRoleIdBody,
//   PostV1UserProjectRoleBody,
// } from "@/web/services/types";
// import {
//   usePatchV1UserProjectRoleId,
//   usePostV1UserProjectRole,
// } from "@/web/services/user-project-role/user-project-role";
// import { useGetV1UserWorkspaceRole } from "@/web/services/user-workspace-role/user-workspace-role";
// import { useRootStore } from "@/web/store/store";
// import {
//   insertUserProjectRoleSchema,
//   patchUserProjectRoleSchema,
// } from "@repo/db/schema";
// import { Button } from "@repo/ui/components/button";
// import { DialogFooter } from "@repo/ui/components/dialog";
// import {
//   Form,
//   FormControl,
//   FormField,
//   FormItem,
//   FormLabel,
//   FormMessage,
// } from "@repo/ui/components/form";
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
// } from "@repo/ui/components/select";
// import { useForm } from "@tanstack/react-form";

// interface UserProjectRoleFormProps {
//   onSubmit?: () => void;
//   onCancel?: () => void;
//   editPayload?: GetV1UserProjectRole200ItemsItem | null;
//   users: GetV1User200ItemsItem[];
// }

// export const UserProjectRoleForm: React.FC<UserProjectRoleFormProps> = ({
//   onSubmit,
//   onCancel,
//   editPayload,
//   users = [],
// }) => {
//   const currentWorkspaceId = useRootStore((state) => state.currentWorkspaceId);
//   const { data: workspaceUsers } = useGetV1UserWorkspaceRole({
//     filters: JSON.stringify({ workspaceId: { $eq: currentWorkspaceId } }),
//   });
//   console.log({ editPayload });
//   const { data: roles } = useGetV1Role({
//     filters: JSON.stringify({ level: { $eq: "project" } }),
//   });

//   const currentUser = useRootStore((state) => state.currentUser);
//   const currentProjectId = useRootStore((state) => state.currentProjectId);

//   const defaultValues:
//     | PatchV1UserProjectRoleIdBody
//     | PostV1UserProjectRoleBody = editPayload || {
//     userId: "",
//     projectId: currentProjectId,
//     isActive: true,
//     createdBy: currentUser?.id,
//   };

//   const requiredFields = ["userId", "projectId"];

//   const userProjectRoleMutation = usePostV1UserProjectRole({
//     mutation: {
//       onSuccess: () => {
//         if (onSubmit) onSubmit();
//       },
//       onError: (error) => {
//         console.error("User project role creation failed:", error);
//       },
//     },
//   });

//   const userProjectRoleUpdateMutation = usePatchV1UserProjectRoleId({
//     mutation: {
//       onSuccess: () => {
//         if (onSubmit) onSubmit();
//       },
//       onError: (error) => {
//         console.error("User project role update failed:", error);
//       },
//     },
//   });

//   const schemaForForm = editPayload
//     ? patchUserProjectRoleSchema
//     : insertUserProjectRoleSchema;

//   const form = useForm({
//     defaultValues,
//     validators: {
//       onSubmit: schemaForForm,
//     },
//     onSubmit: async ({ value }) => {
//       if (editPayload) {
//         await userProjectRoleUpdateMutation.mutateAsync({
//           data: value,
//           id: editPayload.id,
//         });
//       } else {
//         await userProjectRoleMutation.mutateAsync({
//           data: value as PostV1UserProjectRoleBody,
//         });
//       }
//     },
//   });

//   return (
//     <>
//       <div className="overflow-auto">
//         <Form
//           id="user-project-role-form"
//           onSubmit={(e) => {
//             e.preventDefault();
//             form.handleSubmit();
//           }}
//           className="flex flex-col gap-y-3 p-6"
//         >
//           <div className="grid gap-3">
//             <form.Field name="userId">
//               {(field) => (
//                 <FormField
//                   field={field}
//                   required={requiredFields.includes("userId")}
//                 >
//                   <FormItem>
//                     <FormLabel>User</FormLabel>
//                     <FormControl>
//                       <Select
//                         value={field.state.value}
//                         onValueChange={(value) => field.handleChange(value)}
//                         disabled={!!editPayload}
//                       >
//                         <SelectTrigger className="w-full">
//                           <SelectValue placeholder="Select a user" />
//                         </SelectTrigger>
//                         <SelectContent>
//                           {workspaceUsers?.items
//                             ?.filter(
//                               (usr) =>
//                                 !users.find(
//                                   (exUser) =>
//                                     exUser.id === usr?.user_data?.id &&
//                                     !editPayload
//                                 )
//                             )
//                             ?.map((user) => (
//                               <SelectItem key={user.userId} value={user.userId}>
//                                 {user.user_data?.firstName}{" "}
//                                 {user.user_data?.lastName}
//                               </SelectItem>
//                             ))}
//                         </SelectContent>
//                       </Select>
//                     </FormControl>
//                     <FormMessage />
//                   </FormItem>
//                 </FormField>
//               )}
//             </form.Field>
//             <form.Field name="roleId">
//               {(field) => (
//                 <FormField
//                   field={field}
//                   required={requiredFields.includes("roleId")}
//                 >
//                   <FormItem>
//                     <FormLabel>Role</FormLabel>
//                     <FormControl>
//                       <Select
//                         value={field.state.value}
//                         onValueChange={(value) => field.handleChange(value)}
//                       >
//                         <SelectTrigger className="w-full">
//                           <SelectValue placeholder="Select a role" />
//                         </SelectTrigger>
//                         <SelectContent>
//                           {roles?.items?.map((role) => (
//                             <SelectItem key={role.id} value={role.id}>
//                               {role.name} - {role.description}
//                             </SelectItem>
//                           ))}
//                         </SelectContent>
//                       </Select>
//                     </FormControl>
//                     <FormMessage />
//                   </FormItem>
//                 </FormField>
//               )}
//             </form.Field>
//           </div>
//         </Form>
//       </div>
//       <DialogFooter className="">
//         <Button variant="outline" onClick={onCancel}>
//           Cancel
//         </Button>
//         <Button form="user-project-role-form" type="submit">
//           {editPayload ? "Update" : "Create"}
//         </Button>
//       </DialogFooter>
//     </>
//   );
// };

export const AddProjectUser = () => {
  return (
    <div className="flex h-screen items-center justify-center">
      <div className="w-full max-w-3xl rounded-lg bg-background p-6 shadow-md">
        <h2 className="mb-4 font-semibold text-xl">Add Project User</h2>
        <p className="mb-6 text-muted-foreground">
          Use the form below to add a user to your project.
        </p>
        {/* UserProjectRoleForm component can be used here */}
      </div>
    </div>
  );
};
