// import { useGetV1Role } from "@/web/services/role/role";
// import {
//   GetV1User200ItemsItem,
//   GetV1UserWorkspaceRole200ItemsItem,
//   PatchV1UserWorkspaceRoleIdBody,
//   PostV1UserWorkspaceRoleBody,
// } from "@/web/services/types";
// import { useGetV1UserOrganizationRole } from "@/web/services/user-organization-role/user-organization-role";
// import {
//   usePatchV1UserWorkspaceRoleId,
//   usePostV1UserWorkspaceRole,
// } from "@/web/services/user-workspace-role/user-workspace-role";
// import { useRootStore } from "@/web/store/store";
// import {
//   insertUserWorkspaceRoleSchema,
//   patchUserWorkspaceRoleSchema,
// } from "@repo/db/schema";
// import { Button } from "@repo/ui/components/button";
// import { DialogFooter } from "@repo/ui/components/dialog";
// import {
//   Form,
//   FormControl,
//   FormField,
//   FormItem,
//   FormLabel,
//   FormMessage,
// } from "@repo/ui/components/form";
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
// } from "@repo/ui/components/select";
// import { useForm } from "@tanstack/react-form";

// interface UserWorkspaceRoleFormProps {
//   onSubmit?: () => void;
//   onCancel?: () => void;
//   editPayload?: GetV1UserWorkspaceRole200ItemsItem | null;
//   users: GetV1User200ItemsItem[];
// }

// export const UserWorkspaceRoleForm: React.FC<UserWorkspaceRoleFormProps> = ({
//   onSubmit,
//   onCancel,
//   editPayload,
//   users = [],
// }) => {
//   const currentOrganizationId = useRootStore(
//     (state) => state.currentOrganizationId
//   );
//   const { data: roles } = useGetV1Role({
//     filters: JSON.stringify({ level: { $eq: "workspace" } }),
//   });
//   const { data: orgUsers } = useGetV1UserOrganizationRole({
//     filters: JSON.stringify({ organizationId: { $eq: currentOrganizationId } }),
//   });

//   const currentUser = useRootStore((state) => state.currentUser);

//   const currentWorkspaceId = useRootStore((state) => state.currentWorkspaceId);

//   const defaultValues:
//     | PatchV1UserWorkspaceRoleIdBody
//     | PostV1UserWorkspaceRoleBody = editPayload || {
//     userId: "",
//     workspaceId: currentWorkspaceId,
//     roleId: "",
//     isActive: true,
//     createdBy: currentUser?.id,
//   };

//   const requiredFields = ["userId", "workspaceId", "roleId"];

//   const userWorkspaceRoleMutation = usePostV1UserWorkspaceRole({
//     mutation: {
//       onSuccess: () => {
//         if (onSubmit) onSubmit();
//       },
//       onError: (error) => {
//         console.error("User workspace role creation failed:", error);
//       },
//     },
//   });

//   const userWorkspaceRoleUpdateMutation = usePatchV1UserWorkspaceRoleId({
//     mutation: {
//       onSuccess: () => {
//         if (onSubmit) onSubmit();
//       },
//       onError: (error) => {
//         console.error("User workspace role update failed:", error);
//       },
//     },
//   });

//   const schemaForForm = editPayload
//     ? patchUserWorkspaceRoleSchema
//     : insertUserWorkspaceRoleSchema;

//   const form = useForm({
//     defaultValues,
//     validators: {
//       onSubmit: schemaForForm,
//     },
//     onSubmit: async ({ value }) => {
//       if (editPayload) {
//         await userWorkspaceRoleUpdateMutation.mutateAsync({
//           data: value,
//           id: editPayload.id,
//         });
//       } else {
//         await userWorkspaceRoleMutation.mutateAsync({
//           data: value as PostV1UserWorkspaceRoleBody,
//         });
//       }
//     },
//   });

//   return (
//     <>
//       <div className="overflow-auto">
//         <Form
//           id="user-workspace-role-form"
//           onSubmit={(e) => {
//             e.preventDefault();
//             form.handleSubmit();
//           }}
//           className="flex flex-col gap-y-3 p-6"
//         >
//           <div className="grid gap-3">
//             <form.Field name="userId">
//               {(field) => (
//                 <FormField
//                   field={field}
//                   required={requiredFields.includes("userId")}
//                 >
//                   <FormItem>
//                     <FormLabel>User</FormLabel>
//                     <FormControl>
//                       <Select
//                         value={field.state.value}
//                         onValueChange={(value) => field.handleChange(value)}
//                         disabled={!!editPayload}
//                       >
//                         <SelectTrigger className="w-full">
//                           <SelectValue placeholder="Select a user" />
//                         </SelectTrigger>
//                         <SelectContent>
//                           {orgUsers?.items
//                             ?.filter(
//                               (usr) =>
//                                 !users.find(
//                                   (exUser) =>
//                                     exUser.id === usr?.user_data?.id &&
//                                     !editPayload
//                                 )
//                             )
//                             ?.map((user) => (
//                               <SelectItem key={user.id} value={user.userId}>
//                                 {user.user_data?.firstName}{" "}
//                                 {user.user_data?.lastName}
//                               </SelectItem>
//                             ))}
//                         </SelectContent>
//                       </Select>
//                     </FormControl>
//                     <FormMessage />
//                   </FormItem>
//                 </FormField>
//               )}
//             </form.Field>

//             <form.Field name="roleId">
//               {(field) => (
//                 <FormField
//                   field={field}
//                   required={requiredFields.includes("roleId")}
//                 >
//                   <FormItem>
//                     <FormLabel>Role</FormLabel>
//                     <FormControl>
//                       <Select
//                         value={field.state.value}
//                         onValueChange={(value) => field.handleChange(value)}
//                       >
//                         <SelectTrigger className="w-full">
//                           <SelectValue placeholder="Select a role" />
//                         </SelectTrigger>
//                         <SelectContent>
//                           {roles?.items?.map((role) => (
//                             <SelectItem key={role.id} value={role.id}>
//                               {role.name} - {role.description}
//                             </SelectItem>
//                           ))}
//                         </SelectContent>
//                       </Select>
//                     </FormControl>
//                     <FormMessage />
//                   </FormItem>
//                 </FormField>
//               )}
//             </form.Field>
//           </div>
//         </Form>
//       </div>
//       <DialogFooter className="">
//         <Button variant="outline" onClick={onCancel}>
//           Cancel
//         </Button>
//         <Button form="user-workspace-role-form" type="submit">
//           {editPayload ? "Update" : "Create"}
//         </Button>
//       </DialogFooter>
//     </>
//   );
// };

export const AddWorkspaceUser = () => {
  return (
    <div className="flex h-screen items-center justify-center">
      <div className="w-full max-w-3xl rounded-lg bg-background p-6 shadow-md">
        <h2 className="mb-4 font-semibold text-xl">Add Workspace User</h2>
        <p className="mb-6 text-muted-foreground">Invite users to collaborate in your workspace.</p>
      </div>
    </div>
  );
};
