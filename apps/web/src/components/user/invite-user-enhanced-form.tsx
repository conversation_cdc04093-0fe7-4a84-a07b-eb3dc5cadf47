import { insertInvitationSchema, patchInvitationSchema } from '@repo/db/schema';
import { Button } from '@repo/ui/components/button';
import { DialogFooter } from '@repo/ui/components/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
import { ScrollArea } from '@repo/ui/components/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { useToast } from '@repo/ui/components/use-toast';
import { useForm } from '@tanstack/react-form';
import { useMemo } from 'react';
import type {
  Invitation,
  PostApiV1InvitationsBody,
  PostApiV1RoleAssignmentsBody,
} from '@/web/services/hooks.schemas';
import { usePatchApiV1InvitationsId, usePostApiV1Invitations } from '@/web/services/invitations';
import {
  useGetApiV1RoleAssignments,
  usePostApiV1RoleAssignments,
} from '@/web/services/role-assignments';
import { useGetApiV1Roles } from '@/web/services/roles';
import { useGetApiV1Users } from '@/web/services/users';
import { useRootStore } from '@/web/store/store';

interface UserInvitationEnhancedFormProps {
  onSubmit?: () => void;
  onCancel?: () => void;
  editPayload?: Invitation | null;
  scopeType?: 'organization' | 'workspace' | 'project';
}

export const UserInvitationEnhancedForm: React.FC<UserInvitationEnhancedFormProps> = ({
  onSubmit,
  onCancel,
  editPayload,
  scopeType = 'organization',
}) => {
  const { toast } = useToast();
  // Fetch roles based on scope type
  const roleLevel =
    scopeType === 'organization'
      ? 'organization'
      : scopeType === 'workspace'
        ? 'workspace'
        : 'project';

  const { data: roles } = useGetApiV1Roles({
    filters: JSON.stringify({ level: { $eq: roleLevel } }),
  });

  const currentUser = useRootStore((state) => state.currentUser);
  const currentOrganizationId = useRootStore((state) => state.currentOrganizationId);
  const currentWorkspaceId = useRootStore((state) => state.currentWorkspaceId);
  const currentProjectId = useRootStore((state) => state.currentProjectId);

  // Determine the scope ID based on scope type
  const scopeId =
    scopeType === 'organization'
      ? currentOrganizationId
      : scopeType === 'workspace'
        ? currentWorkspaceId
        : currentProjectId;

  // Fetch users based on scope type
  // For workspace: get all organization users
  // For project: get all workspace users
  const parentScopeType =
    scopeType === 'workspace' ? 'organization' : scopeType === 'project' ? 'workspace' : null;
  const parentScopeId =
    scopeType === 'workspace'
      ? currentOrganizationId
      : scopeType === 'project'
        ? currentWorkspaceId
        : null;

  // Fetch users who have roles in the parent scope
  const { data: parentRoleAssignments } = useGetApiV1RoleAssignments(
    parentScopeType && parentScopeId
      ? {
          filters: JSON.stringify({
            scopeType: { $eq: parentScopeType },
            scopeId: { $eq: parentScopeId },
            isActive: { $eq: true },
          }),
          limit: 100,
        }
      : undefined,
    {
      query: {
        enabled: scopeType !== 'organization' && !!parentScopeId,
      },
    },
  );

  // Fetch existing role assignments for the current scope to filter them out
  const { data: currentRoleAssignments } = useGetApiV1RoleAssignments({
    filters: JSON.stringify({
      scopeType: { $eq: scopeType },
      scopeId: { $eq: scopeId },
      isActive: { $eq: true },
    }),
    limit: 100,
  });

  // Fetch all users for organization scope
  const { data: allUsers } = useGetApiV1Users(
    scopeType === 'organization' ? { limit: 100 } : undefined,
    {
      query: {
        enabled: scopeType === 'organization',
      },
    },
  );

  // Get available users based on scope type
  const availableUsers = useMemo(() => {
    if (scopeType === 'organization') {
      // For organization, show all users except those already in the organization
      const existingUserIds = new Set(currentRoleAssignments?.data?.map((ra) => ra.userId) || []);
      return allUsers?.data?.filter((user) => !existingUserIds.has(user.id)) || [];
    }
    // For workspace/project, show users from parent scope who aren't already in current scope
    const existingUserIds = new Set(currentRoleAssignments?.data?.map((ra) => ra.userId) || []);
    const parentUsers = parentRoleAssignments?.data?.map((ra) => ra.user) || [];
    return parentUsers.filter((user) => !existingUserIds.has(user.id));
  }, [scopeType, allUsers, parentRoleAssignments, currentRoleAssignments]);

  const defaultValues: any =
    editPayload ||
    (scopeType === 'organization'
      ? {
          email: '',
          roleId: '',
          invitedBy: currentUser?.id || '',
          scopeId,
          scopeType,
        }
      : {
          userId: '',
          roleId: '',
        });

  const requiredFields = scopeType === 'organization' ? ['email', 'roleId'] : ['userId', 'roleId'];

  const userInvitationMutation = usePostApiV1Invitations({
    mutation: {
      onSuccess: () => {
        if (onSubmit) {
          onSubmit();
        }
        toast({
          title: 'Success',
          description: 'Invitation sent successfully',
          duration: 4000,
        });
      },
      onError: (error) => {
        toast({
          title: 'Error',
          description: error.message || 'Failed to send invitation. Please try again.',
          variant: 'destructive',
          duration: 4000,
        });
      },
    },
  });

  const userInvitationUpdateMutation = usePatchApiV1InvitationsId({
    mutation: {
      onSuccess: () => {
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (_error) => {},
    },
  });

  const roleAssignmentMutation = usePostApiV1RoleAssignments({
    mutation: {
      onSuccess: () => {
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (_error) => {},
    },
  });

  const schemaForForm = editPayload ? patchInvitationSchema : insertInvitationSchema;

  const form = useForm({
    defaultValues,
    validators: {
      onSubmit: scopeType === 'organization' ? schemaForForm : undefined,
    },
    onSubmit: async ({ value }) => {
      if (scopeType === 'organization') {
        // For organization, use invitation flow
        if (editPayload) {
          await userInvitationUpdateMutation.mutateAsync({
            data: value,
            id: editPayload.id,
          });
        } else {
          await userInvitationMutation.mutateAsync({
            data: value as PostApiV1InvitationsBody,
          });
        }
      } else {
        // For workspace/project, directly create role assignment
        const roleAssignmentData: PostApiV1RoleAssignmentsBody = {
          userId: (value as any).userId,
          roleId: value.roleId,
          scopeType,
          scopeId: scopeId!,
          isActive: true,
        };
        await roleAssignmentMutation.mutateAsync({
          data: roleAssignmentData,
        });
      }
    },
  });

  return (
    <div className="flex h-full flex-col">
      <ScrollArea className="flex-1 px-6 py-4">
        <Form
          className="flex flex-col gap-y-4"
          id="user-invitation-form"
          onSubmit={(e) => {
            e.preventDefault();
            form.handleSubmit();
          }}
        >
          <div className="grid gap-4">
            {scopeType === 'organization' ? (
              <form.Field name="email">
                {(field) => (
                  <FormField field={field} required={requiredFields.includes('email')}>
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="<EMAIL>"
                          type="email"
                          value={(field.state.value as string) || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  </FormField>
                )}
              </form.Field>
            ) : (
              <form.Field name="userId">
                {(field) => (
                  <FormField field={field} required={requiredFields.includes('userId')}>
                    <FormItem>
                      <FormLabel>User</FormLabel>
                      <FormControl>
                        <Select
                          onValueChange={(value) => field.handleChange(value)}
                          value={field.state.value}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select a user" />
                          </SelectTrigger>
                          <SelectContent>
                            {availableUsers.length === 0 ? (
                              <div className="px-2 py-1 text-muted-foreground text-sm">
                                No available users to add
                              </div>
                            ) : (
                              availableUsers.map((user) => (
                                <SelectItem key={user.id} value={user.id}>
                                  <div className="flex flex-col">
                                    <span>
                                      {`${user.firstName || ''} ${user.lastName || ''}`.trim() ||
                                        user.email}
                                    </span>
                                    {(user.firstName || user.lastName) && (
                                      <span className="text-muted-foreground text-xs">
                                        {user.email}
                                      </span>
                                    )}
                                  </div>
                                </SelectItem>
                              ))
                            )}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  </FormField>
                )}
              </form.Field>
            )}

            <form.Field name="roleId">
              {(field) => (
                <FormField field={field} required={requiredFields.includes('roleId')}>
                  <FormItem>
                    <FormLabel>Role</FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={(value) => field.handleChange(value)}
                        value={field.state.value}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select a role" />
                        </SelectTrigger>
                        <SelectContent>
                          {roles?.data?.map((role) => (
                            <SelectItem key={role.id} value={role.id}>
                              {role.name}
                              {role.description && (
                                <span className="ml-2 text-muted-foreground">
                                  - {role.description}
                                </span>
                              )}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>
          </div>
        </Form>
      </ScrollArea>

      <DialogFooter className="border-t px-6 py-4">
        <Button
          disabled={
            userInvitationMutation.isPending ||
            userInvitationUpdateMutation.isPending ||
            roleAssignmentMutation.isPending
          }
          onClick={onCancel}
          variant="outline"
        >
          Cancel
        </Button>
        <Button
          disabled={
            userInvitationMutation.isPending ||
            userInvitationUpdateMutation.isPending ||
            roleAssignmentMutation.isPending ||
            (scopeType !== 'organization' && availableUsers.length === 0)
          }
          form="user-invitation-form"
          isLoading={
            userInvitationMutation.isPending ||
            userInvitationUpdateMutation.isPending ||
            roleAssignmentMutation.isPending
          }
          type="submit"
        >
          {scopeType === 'organization' ? (editPayload ? 'Update' : 'Send Invitation') : 'Add User'}
        </Button>
      </DialogFooter>
    </div>
  );
};
