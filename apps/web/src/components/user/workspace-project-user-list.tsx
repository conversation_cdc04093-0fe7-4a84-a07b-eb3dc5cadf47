import { Button } from '@repo/ui/components/button';
import CustomDataGrid, { type MuiColumn } from '@repo/ui/components/custom/customDataGrid';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@repo/ui/components/dialog';
import { Edit, PlusIcon, Trash2 } from 'lucide-react';
import { useState } from 'react';
import type { RoleAssignment } from '@/web/services/hooks.schemas';
import { useGetApiV1RoleAssignments } from '@/web/services/role-assignments';
import { useRootStore } from '@/web/store/store';
import { UserInvitationEnhancedForm } from './invite-user-enhanced-form';

interface WorkspaceProjectUserListProps {
  scopeType: 'workspace' | 'project';
  scopeId: string;
}

export default function WorkspaceProjectUserList({
  scopeType,
  scopeId,
}: WorkspaceProjectUserListProps) {
  const [openAddUser, setOpenAddUser] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
  });
  const [sorting, setSorting] = useState({
    field: 'createdAt',
    order: 'DESC',
  });

  const currentWorkspaceId = useRootStore((state) => state.currentWorkspaceId);
  const currentProjectId = useRootStore((state) => state.currentProjectId);

  // Use the provided scopeId or fall back to current context
  const actualScopeId =
    scopeId || (scopeType === 'workspace' ? currentWorkspaceId : currentProjectId);

  const { data, isLoading } = useGetApiV1RoleAssignments({
    filters: JSON.stringify({
      scopeType: { $eq: scopeType },
      scopeId: { $eq: actualScopeId },
      isActive: { $eq: true },
    }),
    sort: JSON.stringify(sorting),
    page: pagination.page,
    limit: pagination.pageSize,
  });

  const columns: MuiColumn<RoleAssignment>[] = [
    {
      field: 'userId',
      headerName: 'Display Name',
      width: 200,
      sortable: true,
      renderCell: (params) => {
        return params.row.original.user?.displayName || 'N/A';
      },
    },
    {
      field: 'userId',
      headerName: 'Email',
      width: 250,
      renderCell: (params) => {
        return params.row.original.user?.email || 'N/A';
      },
    },
    {
      field: 'roleId',
      headerName: 'Role',
      width: 150,
      renderCell: (params) => {
        return params.row.original.role?.name || 'N/A';
      },
    },
    {
      field: 'createdAt',
      headerName: 'Added On',
      width: 200,
      renderCell: (params) => {
        const createdAt = params.row.original.createdAt;
        if (!createdAt) {
          return 'N/A';
        }
        return new Date(createdAt).toLocaleDateString();
      },
    },
    {
      field: 'id',
      headerName: 'Actions',
      sortable: false,
      width: 150,
      renderCell: (params) => (
        <div className="flex space-x-2">
          <Button
            onClick={() => alert(`Editing role for ${params.row.original.user?.displayName}`)}
            size="sm"
            variant="ghost"
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            onClick={() => alert(`Removing ${params.row.original.user?.displayName}`)}
            size="sm"
            variant="ghost"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  const handleRowSelectionChange = (_selection: Record<string, boolean>) => {};

  return (
    <div className="h-full p-4">
      <CustomDataGrid<RoleAssignment>
        addButton={
          <Button onClick={() => setOpenAddUser(true)}>
            <PlusIcon className="mr-2 h-4 w-4" />
            Add User
          </Button>
        }
        autoHeight={false}
        checkboxSelection
        columns={columns}
        disableSelectionOnClick={false}
        initialState={{
          sorting: [{ id: 'createdAt', desc: true }],
        }}
        loading={isLoading}
        onFilterModelChange={(_model) => {}}
        onPageChange={(page) => {
          setPagination((prev) => ({ ...prev, page: page + 1 }));
        }}
        onPageSizeChange={(size) => setPagination((prev) => ({ ...prev, pageSize: size }))}
        onRowSelectionChange={handleRowSelectionChange}
        onSortModelChange={(model) => {
          setSorting({
            field: model[0]?.id || 'createdAt',
            order: model[0]?.desc ? 'DESC' : 'ASC',
          });
          setPagination((prev) => ({ ...prev, page: 1 }));
        }}
        pageSize={pagination.pageSize}
        pageSizeOptions={[5, 10, 20, 50]}
        rowHeight={52}
        rows={data?.data}
        totalRows={data?.meta.totalItems}
      />

      {openAddUser && (
        <Dialog onOpenChange={setOpenAddUser} open={openAddUser}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                Add User to {scopeType === 'workspace' ? 'Workspace' : 'Project'}
              </DialogTitle>
            </DialogHeader>
            <UserInvitationEnhancedForm
              onCancel={() => setOpenAddUser(false)}
              onSubmit={() => {
                setOpenAddUser(false);
                // The data will auto-refresh through React Query
              }}
              scopeType={scopeType}
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
