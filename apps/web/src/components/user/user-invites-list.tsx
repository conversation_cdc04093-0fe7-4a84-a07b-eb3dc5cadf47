import { Badge } from '@repo/ui/components/badge';
import { Button } from '@repo/ui/components/button';
import { Input } from '@repo/ui/components/input';
import { Tabs, TabsList, TabsTrigger } from '@repo/ui/components/tabs';
import { useToast } from '@repo/ui/components/use-toast';
import { cn } from '@repo/ui/lib/utils';
import { formatDistanceToNow } from 'date-fns';
import {
  Briefcase,
  Building2,
  Check,
  CheckCircle,
  Clock,
  Mail,
  Search,
  Users,
  X,
  XCircle,
} from 'lucide-react';
import { useMemo, useState } from 'react';
import { useGetApiV1Invitations, usePatchApiV1InvitationsId } from '@/web/services/invitations';
import { useRootStore } from '@/web/store/store';

export default function UserInvitationList() {
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 200,
  });
  const [sorting] = useState({
    field: 'createdAt',
    order: 'DESC',
  });

  const [searchQuery, setSearchQuery] = useState('');
  const [filter, setFilter] = useState<'all' | 'pending' | 'accepted' | 'rejected' | 'expired'>(
    'all',
  );
  const { toast } = useToast();
  const updateInvitation = usePatchApiV1InvitationsId();
  const currentuser = useRootStore((state) => state.currentUser);

  const { data, isLoading, refetch } = useGetApiV1Invitations({
    page: pagination.page,
    sort: JSON.stringify(sorting),
    limit: pagination.pageSize,
    ...(filter !== 'all' && { status: filter }),
    filters: JSON.stringify({
      email: { $eq: currentuser?.email },
    }),
  });

  const handleAccept = async (invitationId: string) => {
    try {
      await updateInvitation.mutateAsync({
        id: invitationId,
        data: {
          status: 'accepted',
        },
      });

      toast({
        title: 'Invitation accepted',
        description: "You've successfully joined.",
      });
      refetch();
    } catch {
      toast({
        title: 'Error',
        description: 'Failed to accept invitation.',
        variant: 'destructive',
      });
    }
  };

  const handleReject = async (invitationId: string) => {
    try {
      await updateInvitation.mutateAsync({
        id: invitationId,
        data: {
          status: 'rejected',
        },
      });

      toast({
        title: 'Invitation rejected',
        description: 'The invitation has been declined.',
      });
      refetch();
    } catch {
      toast({
        title: 'Error',
        description: 'Failed to reject invitation.',
        variant: 'destructive',
      });
    }
  };

  const getStatusIcon = (status: string, isExpired: boolean) => {
    if (isExpired && status === 'pending') {
      return <Clock className="h-3 w-3 text-muted-foreground" />;
    }
    switch (status) {
      case 'accepted':
        return <CheckCircle className="h-3 w-3 text-success" />;
      case 'rejected':
        return <XCircle className="h-3 w-3 text-destructive" />;
      default:
        return <Mail className="h-3 w-3 text-accent" />;
    }
  };

  const getScopeIcon = (scopeType?: string) => {
    switch (scopeType) {
      case 'organization':
        return <Building2 className="h-3 w-3" />;
      case 'workspace':
        return <Users className="h-3 w-3" />;
      case 'project':
        return <Briefcase className="h-3 w-3" />;
      default:
        return <Building2 className="h-3 w-3" />;
    }
  };

  const { filteredInvitations, counts } = useMemo(() => {
    if (!data?.data) {
      return {
        filteredInvitations: [],
        counts: { all: 0, pending: 0, accepted: 0, rejected: 0, expired: 0 },
      };
    }

    const counts = {
      all: 0,
      pending: 0,
      accepted: 0,
      rejected: 0,
      expired: 0,
    };

    const filtered = data.data.filter((invitation) => {
      const matchesSearch =
        invitation.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        invitation.invitedBy?.toLowerCase().includes(searchQuery.toLowerCase());

      // Check if invitation is expired
      const isExpired = invitation.expiresAt
        ? new Date(invitation.expiresAt as string) < new Date()
        : false;

      // Count statuses
      counts.all++;
      if (isExpired && invitation.status === 'pending') {
        counts.expired++;
      } else if (invitation.status === 'pending') {
        counts.pending++;
      } else if (invitation.status === 'accepted') {
        counts.accepted++;
      } else if (invitation.status === 'rejected') {
        counts.rejected++;
      }

      let matchesFilter = true;
      if (filter !== 'all') {
        if (filter === 'expired') {
          matchesFilter = isExpired && invitation.status === 'pending';
        } else if (filter === 'pending') {
          matchesFilter = invitation.status === 'pending' && !isExpired;
        } else {
          matchesFilter = invitation.status === filter;
        }
      }

      return matchesSearch && matchesFilter;
    });

    return { filteredInvitations: filtered, counts };
  }, [data?.data, searchQuery, filter]);

  return (
    <div className=" p-6 ">
      <div className="flex justify-between align-center">
        <p className="font-semibold text-xl">Invitations</p>
        {/* Tabs */}
        <Tabs className="mb-6" defaultValue="all">
          <TabsList className="grid w-full max-w-[600px] grid-cols-5">
            <TabsTrigger onClick={() => setFilter('all')} value="all">
              All {counts.all > 0 && `(${counts.all})`}
            </TabsTrigger>
            <TabsTrigger onClick={() => setFilter('pending')} value="pending">
              Pending{' '}
              {counts.pending > 0 && (
                <Badge className="ml-1 h-4 px-1 text-[10px]" variant="destructive">
                  {counts.pending}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger onClick={() => setFilter('accepted')} value="accepted">
              Accepted {counts.accepted > 0 && `(${counts.accepted})`}
            </TabsTrigger>
            <TabsTrigger onClick={() => setFilter('rejected')} value="rejected">
              Rejected {counts.rejected > 0 && `(${counts.rejected})`}
            </TabsTrigger>
            <TabsTrigger onClick={() => setFilter('expired')} value="expired">
              Expired{' '}
              {counts.expired > 0 && (
                <Badge className="ml-1 h-4 px-1 text-[10px]" variant="secondary">
                  {counts.expired}
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Search */}
      <div className="relative mb-6">
        <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-muted-foreground" />
        <Input
          className="pl-10"
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Search invitations..."
          value={searchQuery}
        />
      </div>

      {/* Invitation List */}
      <div className="space-y-2">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-muted-foreground">Loading invitations...</div>
          </div>
        ) : filteredInvitations.length === 0 ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-muted-foreground">No invitations found</div>
          </div>
        ) : (
          filteredInvitations.map((invitation) => {
            const isExpired = invitation.expiresAt
              ? new Date(invitation.expiresAt as string) < new Date()
              : false;
            const isPending = invitation.status === 'pending';

            return (
              <div
                className={cn(
                  'flex items-center gap-3 rounded-md border p-3 transition-all',
                  isExpired && 'opacity-60',
                  isPending &&
                    !isExpired &&
                    'border-accent/20 bg-accent/5/50 dark:border-accent/80 dark:bg-accent/20',
                )}
                key={invitation.id}
              >
                <div className="flex-shrink-0">{getStatusIcon(invitation.status, isExpired)}</div>

                <div className="min-w-0 flex-1 space-y-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-sm">{invitation.email}</span>
                    {isPending && !isExpired && (
                      <span className="flex h-1.5 w-1.5 rounded-full bg-accent" />
                    )}
                  </div>

                  <div className="flex items-center gap-2 text-muted-foreground text-xs">
                    {getScopeIcon(invitation.scopeType)}
                    <span>{invitation.organization?.name || 'Organization'}</span>
                    {invitation.role?.name && (
                      <>
                        <span>•</span>
                        <span>{invitation.role.name}</span>
                      </>
                    )}
                    <span>•</span>
                    <span>From {invitation.inviter?.displayName || 'System'}</span>
                    <span>•</span>
                    <span>
                      {formatDistanceToNow(new Date(invitation.createdAt), {
                        addSuffix: true,
                      })}
                    </span>
                  </div>

                  {isExpired && isPending && (
                    <Badge className="text-xs" variant="secondary">
                      Expired{' '}
                      {formatDistanceToNow(new Date(invitation.expiresAt as string), {
                        addSuffix: true,
                      })}
                    </Badge>
                  )}
                </div>

                <div className="flex flex-shrink-0 items-center gap-2">
                  {isPending && !isExpired && (
                    <>
                      <Button
                        className="h-7 px-2 text-xs"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAccept(invitation.id);
                        }}
                        size="sm"
                        variant="ghost"
                      >
                        <Check className="mr-1 h-3 w-3" />
                        Accept
                      </Button>
                      <Button
                        className="h-7 px-2 text-destructive text-xs hover:text-destructive-foreground"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleReject(invitation.id);
                        }}
                        size="sm"
                        variant="ghost"
                      >
                        <X className="mr-1 h-3 w-3" />
                        Reject
                      </Button>
                    </>
                  )}

                  {/* <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-7 w-7">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">Open menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <Confirmation
                        title="Delete invitation?"
                        description="This action cannot be undone. The invitation will be permanently deleted."
                        cancelButton={{
                          name: "Cancel",
                          onClick: () => {},
                          variant: "secondary",
                        }}
                        confirmButton={{
                          name: "Delete",
                          onClick: () => handleDelete(invitation.id),
                          variant: "destructive",
                        }}
                      >
                        <DropdownMenuItem
                          className="text-destructive"
                          onSelect={(e) => e.preventDefault()}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </Confirmation>
                    </DropdownMenuContent>
                  </DropdownMenu> */}
                </div>
              </div>
            );
          })
        )}
      </div>

      {/* Pagination */}
      {data && data.meta.totalItems > pagination.pageSize && (
        <div className="mt-6 flex items-center justify-between">
          <p className="text-muted-foreground text-sm">
            Showing {(pagination.page - 1) * pagination.pageSize + 1} to{' '}
            {Math.min(pagination.page * pagination.pageSize, data.meta.totalItems)} of{' '}
            {data.meta.totalItems} invitations
          </p>
          <div className="flex items-center gap-2">
            <Button
              disabled={pagination.page === 1}
              onClick={() => setPagination((prev) => ({ ...prev, page: prev.page - 1 }))}
              size="sm"
              variant="outline"
            >
              Previous
            </Button>
            <Button
              disabled={pagination.page * pagination.pageSize >= data.meta.totalItems}
              onClick={() => setPagination((prev) => ({ ...prev, page: prev.page + 1 }))}
              size="sm"
              variant="outline"
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
