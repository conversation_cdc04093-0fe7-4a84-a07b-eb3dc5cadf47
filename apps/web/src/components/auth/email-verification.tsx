import { Button } from '@repo/ui/components/button';
import { useToast } from '@repo/ui/components/use-toast';
import { cn } from '@repo/ui/lib/utils';
import { useNavigate, useParams } from '@tanstack/react-router';
import { AlertCircle, CheckCircle2, Loader2, Mail, XCircle } from 'lucide-react';
import { useEffect, useState } from 'react';
import { usePostApiV1AuthVerifyEmail } from '@/web/services/auth';

interface EmailVerificationProps {
  className?: string;
}

export function EmailVerification({ className }: EmailVerificationProps) {
  const { token } = useParams({ from: '/__authLayout/verifyEmail/$token' });
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<
    'pending' | 'success' | 'error' | 'invalid'
  >('pending');

  const verifyEmailMutation = usePostApiV1AuthVerifyEmail({
    mutation: {
      onSuccess: (_data) => {
        setVerificationStatus('success');
        toast({
          title: 'Email Verified Successfully!',
          description: 'Your email has been verified. Redirecting to login...',
          variant: 'default',
        });

        // Redirect to login after a delay
        setTimeout(() => {
          navigate({ to: '/login' });
        }, 2000);
      },
      onError: (error) => {
        setVerificationStatus('error');
        toast({
          title: 'Verification Failed',
          description:
            error.message || 'There was an error verifying your email. Please try again.',
          variant: 'destructive',
        });
      },
    },
  });

  const handleVerifyEmail = async () => {
    if (!token || isVerifying) {
      return;
    }

    setIsVerifying(true);
    try {
      await verifyEmailMutation.mutateAsync({
        data: { token },
      });
    } finally {
      setIsVerifying(false);
    }
  };

  useEffect(() => {
    if (!token) {
      setVerificationStatus('invalid');
    }
  }, [token]);

  if (verificationStatus === 'invalid') {
    return (
      <div
        className={cn(
          'space-y-6 rounded-xl border border-border/50 bg-card/50 p-8 text-center shadow-lg backdrop-blur-sm',
          className,
        )}
      >
        <div>
          <AlertCircle className="mx-auto h-20 w-20 text-warning" />
        </div>
        <div>
          <div className="mb-2 font-semibold text-2xl text-foreground">
            Invalid Verification Link
          </div>
          <p className="text-muted-foreground">The verification link is invalid or has expired.</p>
        </div>
        <Button
          className="hover:bg-primary/5"
          onClick={() => navigate({ to: '/login' })}
          variant="outline"
        >
          Go to Login
        </Button>
      </div>
    );
  }

  return (
    <div
      className={cn(
        'space-y-6 rounded-xl border border-border/50 bg-card/50 p-8 shadow-lg backdrop-blur-sm',
        className,
      )}
    >
      {verificationStatus === 'pending' && (
        <>
          <div className="flex flex-col items-center gap-4 text-center">
            <div>
              <div className="relative">
                <Mail className="h-20 w-20 text-primary" />
                <div className="-top-1 -right-1 absolute">
                  <div className="h-4 w-4 animate-pulse rounded-full bg-primary" />
                </div>
              </div>
            </div>

            <div>
              <div className="font-semibold text-2xl text-foreground">Verify Your Email</div>
              <p className="mt-2 text-muted-foreground">
                Click the button below to verify your email address
              </p>
            </div>
          </div>

          <div className="space-y-4">
            <Button className="w-full" disabled={isVerifying} onClick={handleVerifyEmail} size="lg">
              {isVerifying ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Verifying your email...
                </>
              ) : (
                <>
                  <Mail className="mr-2 h-4 w-4" />
                  Verify Email Address
                </>
              )}
            </Button>

            <p className="text-center text-muted-foreground text-sm">
              By verifying your email, you confirm that you have access to this email address.
            </p>
          </div>
        </>
      )}

      {verificationStatus === 'success' && (
        <div className="space-y-4 text-center">
          <div>
            <CheckCircle2 className="mx-auto h-20 w-20 text-success" />
          </div>

          <div>
            <div className="font-semibold text-2xl text-success">Email Verified Successfully!</div>
            <p className="mt-2 text-muted-foreground">
              Your email has been verified. Redirecting you to login...
            </p>
          </div>

          <div className="pt-4">
            <Loader2 className="mx-auto h-8 w-8 animate-spin text-primary" />
          </div>
        </div>
      )}

      {verificationStatus === 'error' && (
        <div className="space-y-4 text-center">
          <div>
            <XCircle className="mx-auto h-20 w-20 text-destructive" />
          </div>

          <div>
            <div className="font-semibold text-2xl text-destructive">Verification Failed</div>
            <p className="mt-2 text-muted-foreground">
              We couldn't verify your email. The link may have expired or already been used.
            </p>
          </div>

          <div className="space-y-3 pt-4">
            <Button
              className="w-full"
              disabled={isVerifying}
              onClick={handleVerifyEmail}
              variant="outline"
            >
              {isVerifying ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Retrying...
                </>
              ) : (
                'Try Again'
              )}
            </Button>

            <Button className="w-full" onClick={() => navigate({ to: '/login' })} variant="ghost">
              Back to Login
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
