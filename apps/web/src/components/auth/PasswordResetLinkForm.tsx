'use client';

import { But<PERSON> } from '@repo/ui/components/button';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
import { useToast } from '@repo/ui/components/use-toast';
import { cn } from '@repo/ui/lib/utils';
import { useForm } from '@tanstack/react-form';
import { Link } from '@tanstack/react-router';
import { z } from 'zod';
import { usePostApiV1AuthForgotPassword } from '@/web/services/auth';

// Schema for requesting reset link
const resetLinkSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

// Mock API (replace with real API call)
export function PasswordResetLinkForm({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  const { toast } = useToast();

  const sendResetLink = usePostApiV1AuthForgotPassword({
    mutation: {
      onSuccess: () => {
        toast({
          title: 'Success',
          description: 'Password reset link sent to your email.',
        });
      },
      onError: (_error) => {
        toast({
          title: 'Error',
          description: 'Failed to send reset link. Please try again.',
          variant: 'destructive',
        });
      },
    },
  });
  const form = useForm({
    defaultValues: {
      email: '',
    },
    validators: {
      onSubmit: resetLinkSchema,
    },
    onSubmit: async ({ value }) => {
      try {
        await sendResetLink.mutateAsync({ data: value });
        toast({
          title: 'Success',
          description: 'Password reset link sent to your email.',
        });
      } catch (_error) {
        toast({
          title: 'Error',
          description: 'Failed to send reset link. Please try again.',
          variant: 'destructive',
        });
      }
    },
  });

  return (
    <div
      className={cn(
        'flex flex-col gap-6 rounded-xl border border-border/50 bg-card/50 p-6 shadow-lg backdrop-blur-sm',
        className,
      )}
      {...props}
    >
      <div className="flex flex-col items-center gap-2 text-center">
        <div className="font-semibold text-2xl text-foreground">Reset your password</div>
        <p className="text-balance text-muted-foreground text-sm">
          Enter your email to receive a password reset link
        </p>
      </div>
      <form
        className="grid gap-y-3"
        id="reset-link-form"
        onSubmit={(e) => {
          e.preventDefault();
          form.handleSubmit();
        }}
      >
        <form.Field name="email">
          {(field) => (
            <FormField field={field} required>
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input
                    className={cn(field.state.meta.errors[0] && 'border-destructive')}
                    id="email"
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="<EMAIL>"
                    type="email"
                    value={field.state.value}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>

        <Button
          className="w-full"
          disabled={sendResetLink.isPending}
          form="reset-link-form"
          type="submit"
        >
          {sendResetLink.isPending ? 'Sending...' : 'Send reset link'}
        </Button>
      </form>
      <div className="text-center text-muted-foreground text-sm">
        Remember your password?{' '}
        <Link
          className="font-medium text-primary underline-offset-4 transition-colors hover:underline"
          to="/login"
        >
          Login
        </Link>
      </div>
    </div>
  );
}
