'use client';

import { But<PERSON> } from '@repo/ui/components/button';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
import { useToast } from '@repo/ui/components/use-toast';
import { cn } from '@repo/ui/lib/utils';
import { useForm } from '@tanstack/react-form';
import { Link, useNavigate, useParams } from '@tanstack/react-router';
import { z } from 'zod';
import { usePostApiV1AuthResetPassword } from '@/web/services/auth';

// Define the schema for password reset
const resetPasswordSchema = z
  .object({
    password: z.string().min(8, 'Password must be at least 8 characters'),
    confirmPassword: z.string().min(8, 'Password must be at least 8 characters'),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

export function PasswordResetForm({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { token } = useParams({ from: '/__authLayout/reset/$token' });

  const resetPassword = usePostApiV1AuthResetPassword({
    mutation: {
      onSuccess: () => {
        toast({
          title: 'Success',
          description: 'Your password has been reset.',
        });
      },
      onError: (_error) => {
        toast({
          title: 'Error',
          description: 'Failed to reset password. Please try again.',
          variant: 'destructive',
        });
      },
    },
  });
  const form = useForm({
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
    validators: {
      onSubmit: resetPasswordSchema,
    },
    onSubmit: async ({ value }) => {
      try {
        await resetPassword.mutateAsync({
          data: { password: value.password, token: token || '' },
        });
        toast({
          title: 'Success',
          description: 'Your password has been reset.',
        });
        navigate({ to: '/login' });
      } catch (_error) {
        toast({
          title: 'Error',
          description: 'Failed to reset password. Please try again.',
          variant: 'destructive',
        });
      }
    },
  });

  const requiredFields = ['password', 'confirmPassword'];

  return (
    <div
      className={cn(
        'flex flex-col gap-6 rounded-xl border border-border/50 bg-card/50 p-6 shadow-lg backdrop-blur-sm',
        className,
      )}
      {...props}
    >
      <div className="flex flex-col items-center gap-2 text-center">
        <div className="font-semibold text-2xl text-foreground">Set a new password</div>
        <p className="text-balance text-muted-foreground text-sm">Enter your new password below</p>
      </div>
      <form
        className="grid gap-y-3"
        id="password-reset-form"
        onSubmit={(e) => {
          e.preventDefault();
          form.handleSubmit();
        }}
      >
        <form.Field name="password">
          {(field) => (
            <FormField field={field} required={requiredFields.includes('password')}>
              <FormItem>
                <FormLabel>New Password</FormLabel>
                <FormControl>
                  <Input
                    className={cn(field.state.meta.errors[0] && 'border-destructive')}
                    id="password"
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="New Password"
                    type="password"
                    value={field.state.value}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>

        <form.Field name="confirmPassword">
          {(field) => (
            <FormField field={field} required={requiredFields.includes('confirmPassword')}>
              <FormItem>
                <FormLabel>Confirm New Password</FormLabel>
                <FormControl>
                  <Input
                    className={cn(field.state.meta.errors[0] && 'border-destructive')}
                    id="confirmPassword"
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Confirm New Password"
                    type="password"
                    value={field.state.value}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>

        <Button
          className="w-full"
          disabled={resetPassword.isPending}
          form="password-reset-form"
          type="submit"
        >
          {resetPassword.isPending ? 'Resetting...' : 'Reset Password'}
        </Button>
      </form>
      <div className="text-center text-muted-foreground text-sm">
        Back to{' '}
        <Link
          className="font-medium text-primary underline-offset-4 transition-colors hover:underline"
          to="/login"
        >
          Login
        </Link>
      </div>
    </div>
  );
}
