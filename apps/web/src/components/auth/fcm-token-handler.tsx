import { useToast } from '@repo/ui/components/use-toast';
import { getToken, onMessage } from 'firebase/messaging';
import { useEffect } from 'react';
import { usePostApiV1UserFcmTokens } from '@/web/services/user-fcm-tokens';
import { messaging } from '../../lib/firebase';
import { useRootStore } from '../../store/store';

// TODO: Replace with your actual VAPID key from Firebase Console
const VAPID_KEY =
  'BDP9mTaaOq598599Psd9NQOqxq9urf1oyGMLQq_iySntnGFXh8qoQNFXOVtIQGIm7HuHRG_prP0g3XXPKpiok9Y';

// Helper function to detect device info
function getDeviceInfo() {
  const ua = navigator.userAgent;
  let deviceType = 'desktop';
  let os = 'unknown';
  let browser = 'unknown';

  // Detect device type
  if (/Mobile|Android|iPhone|iPad/.test(ua)) {
    deviceType = 'mobile';
  } else if (/Tablet|iPad/.test(ua)) {
    deviceType = 'tablet';
  }

  // Detect OS
  if (/Windows/.test(ua)) {
    os = 'Windows';
  } else if (/Mac/.test(ua)) {
    os = 'macOS';
  } else if (/Linux/.test(ua)) {
    os = 'Linux';
  } else if (/Android/.test(ua)) {
    os = 'Android';
  } else if (/iPhone|iPad/.test(ua)) {
    os = 'iOS';
  }

  // Detect browser
  if (/Chrome/.test(ua) && !/Edge/.test(ua)) {
    browser = 'Chrome';
  } else if (/Firefox/.test(ua)) {
    browser = 'Firefox';
  } else if (/Safari/.test(ua) && !/Chrome/.test(ua)) {
    browser = 'Safari';
  } else if (/Edge/.test(ua)) {
    browser = 'Edge';
  }

  return {
    deviceType,
    os,
    browser,
    deviceModel: (navigator as any).userAgentData?.platform || 'unknown',
  };
}

interface FCMTokenHandlerProps {
  enabled?: boolean;
}

export function FCMTokenHandler({ enabled = true }: FCMTokenHandlerProps) {
  const currentUser = useRootStore((state) => state.currentUser);
  const { toast } = useToast();

  // FCM token registration mutation
  const registerTokenMutation = usePostApiV1UserFcmTokens({
    mutation: {
      onSuccess: () => {},
      onError: (_error) => {
        toast({
          title: 'Notification Setup Failed',
          description: 'Failed to setup push notifications. Please try refreshing the page.',
          variant: 'destructive',
        });
      },
    },
  });

  useEffect(() => {
    if (!(enabled && currentUser && messaging)) {
      return;
    }

    async function setupFCM() {
      try {
        // Request notification permission
        const permission = await Notification.requestPermission();
        if (permission !== 'granted') {
          return;
        }

        // Register service worker
        const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');

        // Get FCM token
        const token = await getToken(messaging!, {
          vapidKey: VAPID_KEY,
          serviceWorkerRegistration: registration,
        });

        if (token) {
          // Register token with backend using the hook
          const deviceInfo = getDeviceInfo();

          registerTokenMutation.mutate({
            data: {
              fcmToken: token,
              ...deviceInfo,
            },
          });
        }

        // Handle foreground messages
        onMessage(messaging!, (payload) => {
          if (payload.notification) {
            toast({
              title: payload.notification.title || 'New Notification',
              description: payload.notification.body || 'You have a new notification',
            });
          }
        });

        // Listen for logout messages from service worker
        navigator.serviceWorker.addEventListener('message', (event) => {
          if (event.data?.type === 'LOGOUT_FROM_FCM') {
            // Clear local storage and redirect to login
            localStorage.removeItem('token');
            localStorage.removeItem('refreshToken');
            window.location.href = '/auth/login';
          }
        });
      } catch (_error) {}
    }

    setupFCM();
  }, [enabled, currentUser, toast, registerTokenMutation.mutate]);

  return null; // This component doesn't render anything
}

export default FCMTokenHandler;
