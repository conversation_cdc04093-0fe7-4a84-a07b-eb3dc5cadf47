import { Button } from '@repo/ui/components/button';
import { useToast } from '@repo/ui/components/use-toast';
import { cn } from '@repo/ui/lib/utils';
import { useNavigate } from '@tanstack/react-router';
import { ArrowLeft, Loader2, Mail, RefreshCw } from 'lucide-react';
import { useState } from 'react';

interface CheckEmailProps {
  className?: string;
  email?: string;
  onResendEmail?: () => Promise<void>;
}

export function CheckEmail({ className, email = 'your inbox', onResendEmail }: CheckEmailProps) {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isResending, setIsResending] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);

  const handleResendEmail = async () => {
    if (isResending || resendCooldown > 0 || !onResendEmail) {
      return;
    }

    setIsResending(true);
    try {
      await onResendEmail();
      toast({
        title: 'Email Resent',
        description: "We've sent another verification email to your inbox.",
        variant: 'default',
      });

      // Set cooldown
      setResendCooldown(60);
      const interval = setInterval(() => {
        setResendCooldown((prev) => {
          if (prev <= 1) {
            clearInterval(interval);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (_error) {
      toast({
        title: 'Failed to Resend',
        description: "We couldn't resend the email. Please try again later.",
        variant: 'destructive',
      });
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div
      className={cn(
        'space-y-6 rounded-xl border border-border/50 bg-card/50 p-8 shadow-lg backdrop-blur-sm',
        className,
      )}
    >
      <div className="flex flex-col items-center gap-4 text-center">
        <div className="relative">
          <div className="rounded-full bg-primary/10 p-4">
            <Mail className="h-16 w-16 text-primary" />
          </div>

          {/* Decorative dot */}
          <div className="-top-1 -right-1 absolute">
            <div className="h-4 w-4 animate-pulse rounded-full bg-success" />
          </div>
        </div>

        <div>
          <div className="font-semibold text-2xl text-foreground">Check Your Email</div>
          <p className="mt-2 text-muted-foreground">We've sent a verification link to</p>
          {email !== 'your inbox' && <p className="mt-1 font-medium text-primary">{email}</p>}
        </div>
      </div>

      <div className="space-y-4">
        <div className="space-y-2 rounded-lg bg-muted/50 p-4">
          <p className="text-muted-foreground text-sm">
            Please check your email and click the verification link to continue.
          </p>
          <p className="text-muted-foreground text-sm">The link will expire in 24 hours.</p>
        </div>

        <div className="space-y-1 text-muted-foreground text-sm">
          <p className="font-medium">Didn't receive the email?</p>
          <ul className="ml-2 list-inside list-disc space-y-1 text-muted-foreground">
            <li>Check your spam or junk folder</li>
            <li>Make sure you entered the correct email address</li>
            <li>Wait a few minutes and try resending</li>
          </ul>
        </div>

        {onResendEmail && (
          <Button
            className="w-full"
            disabled={isResending || resendCooldown > 0}
            onClick={handleResendEmail}
            variant="outline"
          >
            {isResending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Resending...
              </>
            ) : resendCooldown > 0 ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Resend in {resendCooldown}s
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Resend Email
              </>
            )}
          </Button>
        )}

        <Button className="w-full" onClick={() => navigate({ to: '/login' })} variant="ghost">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Login
        </Button>
      </div>

      <div className="text-center">
        <p className="text-muted-foreground text-xs">
          Having trouble? Contact your system administrator for support.
        </p>
      </div>
    </div>
  );
}
