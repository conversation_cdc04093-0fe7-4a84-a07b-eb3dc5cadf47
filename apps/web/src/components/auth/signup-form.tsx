import { registerSchema } from '@repo/db/schema';
import { But<PERSON> } from '@repo/ui/components/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
import { PhoneInput } from '@repo/ui/components/phone-input'; // Import the PhoneInput component
import { useToast } from '@repo/ui/components/use-toast';
import { cn } from '@repo/ui/lib/utils';
import { useForm } from '@tanstack/react-form';
import { Link, useNavigate } from '@tanstack/react-router';
import type React from 'react';
import { useState } from 'react';
import { z } from 'zod';
import { usePostApiV1AuthRegister } from '@/web/services/auth';
import type { PostApiV1AuthRegisterBody } from '@/web/services/hooks.schemas';

// Extend registerSchema to include confirmPassword
const formRegisterSchema = registerSchema
  .extend({
    user: registerSchema.shape.user.extend({
      confirmPassword: z.string().min(1, 'Confirm password is required'),
    }),
    organization: z
      .object({
        name: z.string().optional(),
        slug: z.string().optional(),
      })
      .optional(),
  })
  .refine((data) => data.user.password === data.user.confirmPassword, {
    message: 'Passwords do not match',
    path: ['user.confirmPassword'],
  });

type FormValues = z.infer<typeof formRegisterSchema>;

export function RegistrationForm({ className, ...props }: React.ComponentProps<'form'>) {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [registeredEmail, setRegisteredEmail] = useState('');

  const saveRegistration = usePostApiV1AuthRegister({
    mutation: {
      onSuccess: () => {
        toast({
          title: 'Registration successful!',
          description: 'Please check your email to verify your account.',
          variant: 'default',
        });

        // Navigate to check-email page with the email as a query param
        navigate({
          to: '/check-email',
          search: { email: registeredEmail },
        });
      },
      onError: (error) => {
        toast({
          title: 'Registration failed',
          description: error.message || 'Please try again.',
          variant: 'destructive',
        });
      },
    },
  });
  const defaultValues: FormValues = {
    user: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
      displayName: '',
    },
    organization: {
      name: '',
      slug: '',
    },
  };

  const form = useForm({
    defaultValues,
    validators: {
      onSubmit: formRegisterSchema,
    },
    onSubmit: async ({ value }) => {
      try {
        const { user } = value;
        const { confirmPassword: _, ...userData } = user;

        const requestData: PostApiV1AuthRegisterBody = {
          ...userData,
        };

        // Store email for redirect
        setRegisteredEmail(userData.email);

        await saveRegistration.mutateAsync({ data: requestData });
      } catch (error) {
        if (error instanceof z.ZodError) {
        } else {
        }
      }
    },
  });

  return (
    <div>
      <Form
        className={cn(
          'flex max-h-[70vh] flex-col gap-3 overflow-auto rounded-xl border border-border/50 bg-card/50 p-6 shadow-lg backdrop-blur-sm',
          className,
        )}
        onSubmit={(e) => {
          e.preventDefault();
          form.handleSubmit();
        }}
        {...props}
      >
        <div className="flex flex-col items-center gap-1 text-center">
          <div className="font-semibold text-2xl text-foreground">Create your account</div>
          <p className="text-balance text-muted-foreground text-sm">
            Fill in the details below to register your account
          </p>
        </div>
        <div className="grid gap-3">
          {/* Name Row - Two Columns */}
          <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
            <div>
              <form.Field name="user.firstName">
                {(field) => (
                  <FormField field={field} required>
                    <FormItem>
                      <FormLabel>First Name</FormLabel>
                      <FormControl>
                        <Input
                          id="firstName"
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="John"
                          type="text"
                          value={field.state.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  </FormField>
                )}
              </form.Field>
            </div>
            <div>
              <form.Field name="user.lastName">
                {(field) => (
                  <FormField field={field}>
                    <FormItem>
                      <FormLabel>Last Name</FormLabel>
                      <FormControl>
                        <Input
                          id="lastName"
                          onBlur={field.handleBlur}
                          onChange={(e) => field.handleChange(e.target.value)}
                          placeholder="Doe"
                          type="text"
                          value={field.state.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  </FormField>
                )}
              </form.Field>
            </div>
          </div>

          {/* Full Width Fields */}
          <div>
            <form.Field name="user.displayName">
              {(field) => (
                <FormField field={field}>
                  <FormItem>
                    <FormLabel>Display Name</FormLabel>
                    <FormControl>
                      <Input
                        id="displayName"
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="Jhone"
                        type="text"
                        value={field.state.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>
          </div>
          <div>
            <form.Field name="user.email">
              {(field) => (
                <FormField field={field} required>
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        id="email"
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="<EMAIL>"
                        type="email"
                        value={field.state.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>
          </div>
          <div>
            <form.Field name="user.phone">
              {(field) => (
                <FormField field={field} required>
                  <FormItem>
                    <FormLabel>Phone</FormLabel>
                    <FormControl>
                      <PhoneInput
                        id="phone"
                        onBlur={field.handleBlur}
                        onChange={(value) => field.handleChange(value)}
                        placeholder="************"
                        type="tel"
                        value={field.state.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>
          </div>
          <div>
            <form.Field name="user.password">
              {(field) => (
                <FormField field={field} required>
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input
                        id="password"
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        type="password"
                        value={field.state.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>
          </div>
          <div>
            <form.Field name="user.confirmPassword">
              {(field) => (
                <FormField field={field} required>
                  <FormItem>
                    <FormLabel>Confirm Password</FormLabel>
                    <FormControl>
                      <Input
                        id="confirmPassword"
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        type="password"
                        value={field.state.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>
          </div>
          <div>
            <Button className="w-full" disabled={saveRegistration.isPending} type="submit">
              {saveRegistration.isPending ? 'Registering...' : 'Register'}
            </Button>
          </div>
        </div>
        <div className="text-center text-muted-foreground text-sm">
          Already have an account?{' '}
          <Link
            className="font-medium text-primary underline-offset-4 transition-colors hover:underline"
            to="/login"
          >
            Login
          </Link>
        </div>
      </Form>
    </div>
  );
}
