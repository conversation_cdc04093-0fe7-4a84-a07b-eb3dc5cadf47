import { loginSchema } from '@repo/db/schema';
import { Button } from '@repo/ui/components/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
import { useToast } from '@repo/ui/components/use-toast';
import { cn } from '@repo/ui/lib/utils';
import { useForm } from '@tanstack/react-form';
import { Link, useNavigate } from '@tanstack/react-router';
import { usePostApiV1AuthLogin } from '@/web/services/auth';
import type { GetApiV1UsersId200Data, PostApiV1AuthLoginBody } from '@/web/services/hooks.schemas';
import { useRootStore } from '@/web/store/store';
import { setAuthToken } from '@/web/utils';

export function LoginForm({ className, ...props }: React.ComponentProps<'form'>) {
  const navigate = useNavigate();
  const { toast } = useToast();

  const setCurrentUser = useRootStore((state) => state.setCurrentUser);

  const login = usePostApiV1AuthLogin({
    mutation: {
      onSuccess: async (res) => {
        toast({
          title: 'Login successful',
          description: 'You have successfully logged in.',
        });
        setAuthToken(res.tokens.accessToken);
        if (res.user) {
          setCurrentUser(res.user as GetApiV1UsersId200Data);
        }

        await new Promise((resolve) => setTimeout(resolve, 500));

        // Check if user is empty and redirect accordingly

        navigate({ to: '/' });
      },
      onError: (error) => {
        console.log(error);
        toast({
          title: 'Login failed',
          description: error.message || 'Failed to login. Please try again.',
          variant: 'destructive',
        });
      },
    },
  });

  const form = useForm({
    defaultValues: {
      email: '',
      password: '',
    },
    validators: {
      onSubmit: loginSchema,
    },
    onSubmit: async ({ value }: { value: PostApiV1AuthLoginBody }) => {
      login.mutateAsync({ data: value });
    },
    onSubmitInvalid(props) {
      console.log(props);
    },
  });

  const requiredFields = ['email', 'password'];

  return (
    <div>
      <Form
        className={cn(
          'flex flex-col gap-6 rounded-xl border border-border/50 bg-card/50 p-6 shadow-lg backdrop-blur-sm',
          className,
        )}
        id="login-form"
        onSubmit={(e) => {
          e.preventDefault();
          form.handleSubmit();
        }}
        {...props}
      >
        <div className="flex flex-col items-center gap-2 text-center">
          <div className="font-semibold text-2xl text-foreground">Login to your account</div>
          <p className="text-balance text-muted-foreground text-sm">
            Enter your credentials below to login to your account
          </p>
        </div>
        <div className="grid gap-y-3 p-3">
          <form.Field name="email">
            {(field) => (
              <div>
                <FormField field={field} required={requiredFields.includes('email')}>
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        id="email"
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="<EMAIL>"
                        type="email"
                        value={field.state.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              </div>
            )}
          </form.Field>

          <form.Field name="password">
            {(field) => (
              <div>
                <FormField field={field} required={requiredFields.includes('password')}>
                  <FormItem>
                    <div className="flex items-center">
                      <FormLabel>Password</FormLabel>
                      <Link
                        className="ml-auto text-primary text-sm underline-offset-4 transition-colors hover:underline"
                        to="/resetlink"
                      >
                        Forgot password?
                      </Link>
                    </div>
                    <FormControl>
                      <Input
                        id="password"
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        type="password"
                        value={field.state.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              </div>
            )}
          </form.Field>

          <div>
            <Button className="w-full" disabled={login.isPending} type="submit">
              {login.isPending ? 'Logging in...' : 'Login'}
            </Button>
          </div>

          {/* <motion.div
                initial={{ opacity: 0 }}
                animate={{
                  opacity: 1,
                  transition: { delay: 0.6, duration: 0.6 },
                }}
                className="after:border-border relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t"
              >
                <span className="bg-background text-muted-foreground relative z-10 px-2">
                  Or continue with
                </span>
              </div> 

              <motion.div
                variants={buttonVariants}
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button variant="outline" className="w-full">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    className="mr-2 h-5 w-5"
                  >
                    <path
                      d="M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12"
                      fill="currentColor"
                    />
                  </svg>
                  Login with GitHub
                </Button>
              </div>*/}
        </div>
        <div className="text-center text-muted-foreground text-sm">
          Don't have an account?{' '}
          <Link
            className="font-medium text-primary underline-offset-4 transition-colors hover:underline"
            to="/signup"
          >
            Sign up
          </Link>
        </div>
      </Form>
    </div>
  );
}
