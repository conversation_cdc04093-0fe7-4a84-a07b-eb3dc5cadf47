import { Button } from '@repo/ui/components/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
import { useToast } from '@repo/ui/components/use-toast';
import { cn } from '@repo/ui/lib/utils';
import { useForm } from '@tanstack/react-form';
import { Link, useNavigate, useParams } from '@tanstack/react-router';
import { Briefcase, Building2, Loader2, Users } from 'lucide-react';
import { useEffect, useState } from 'react';
import { z } from 'zod';
import { usePostApiV1AuthRegister } from '@/web/services/auth';
import type { PostApiV1AuthRegisterBody } from '@/web/services/hooks.schemas';
import { useGetApiV1InvitationsId } from '@/web/services/invitations';

// Define form schema - separate from API schema due to confirmPassword
const formSchema = z
  .object({
    email: z.string().email('Invalid email address'),
    password: z.string().min(8, 'Password must be at least 8 characters'),
    confirmPassword: z.string(),
    firstName: z.string().min(1, 'First name is required'),
    lastName: z.string().min(1, 'Last name is required'),
    displayName: z.string().optional(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

interface InviteRegistrationProps {
  className?: string;
}

export function InviteRegistrationForm({ className, ...props }: InviteRegistrationProps) {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { token } = useParams({ from: '/__authLayout/invited/$token' });
  const [invitationData, setInvitationData] = useState<{
    id: string;
    email: string;
    role?: { name: string };
    inviter?: { displayName?: string; email: string };
    organization?: { name: string };
    workspace?: { name: string };
    project?: { name: string };
    scopeType: string;
  } | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // Fetch invitation details by token
  const {
    data: invitationResponse,
    isLoading: isLoadingInvitation,
    error: invitationError,
  } = useGetApiV1InvitationsId(token || '');

  // Register mutation
  const register = usePostApiV1AuthRegister({
    mutation: {
      onSuccess: async (_data) => {
        // Set auth token and user
        // setAuthToken(data.tokens.accessToken);
        // if (data.user) setCurrentUser(data.user);

        toast({
          title: 'Registration successful!',
          description: 'Your account has been created. Please log in to continue.',
        });

        // Navigate to login after a short delay
        setTimeout(() => {
          navigate({ to: '/login' });
        }, 1500);
      },
      onError: (error) => {
        toast({
          title: 'Registration failed',
          description: error.message || 'Failed to register. Please try again.',
          variant: 'destructive',
        });
      },
    },
  });

  useEffect(() => {
    if (invitationResponse?.data) {
      setInvitationData(invitationResponse.data);
    }
  }, [invitationResponse]);

  useEffect(() => {
    if (invitationError) {
      toast({
        title: 'Invalid invitation',
        description: 'This invitation link is invalid or has expired.',
        variant: 'destructive',
      });
      navigate({ to: '/signup' });
    }
  }, [invitationError, navigate, toast]);

  const form = useForm({
    defaultValues: {
      email: invitationData?.email || '',
      password: '',
      confirmPassword: '',
      firstName: '',
      lastName: '',
      displayName: '',
    },
    onSubmit: async ({ value }) => {
      setIsProcessing(true);
      try {
        // Validate form data
        const validatedData = formSchema.parse(value);

        // Extract only the fields needed for the API
        const { confirmPassword, ...formData } = validatedData;
        void confirmPassword; // Explicitly mark as intentionally unused

        // Prepare data for API - include displayName in the payload
        const registerData: PostApiV1AuthRegisterBody = {
          email: formData.email,
          password: formData.password,
          firstName: formData.firstName,
          lastName: formData.lastName,
          displayName: formData.displayName || `${formData.firstName} ${formData.lastName}`,
        };

        await register.mutateAsync({ data: registerData });
      } catch (error) {
        if (error instanceof z.ZodError) {
          // Handle validation errors
          const firstError = error.errors[0];
          toast({
            title: 'Validation error',
            description: firstError?.message || 'Please check your input',
            variant: 'destructive',
          });
        } else {
          throw error;
        }
      } finally {
        setIsProcessing(false);
      }
    },
  });

  // Update form email when invitation data is loaded
  useEffect(() => {
    if (invitationData?.email) {
      form.setFieldValue('email', invitationData.email);
    }
  }, [invitationData, form]);

  if (isLoadingInvitation) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (invitationError || !invitationData) {
    return (
      <div className="space-y-4 rounded-xl border border-border/50 bg-card/50 p-8 text-center shadow-lg backdrop-blur-sm">
        <div className="font-semibold text-2xl text-destructive">Invalid Invitation</div>
        <p className="text-muted-foreground">This invitation link is invalid or has expired.</p>
        <Button
          className="hover:bg-primary/5"
          onClick={() => navigate({ to: '/login' })}
          variant="outline"
        >
          Go to Login
        </Button>
      </div>
    );
  }

  const getScopeIcon = () => {
    switch (invitationData?.scopeType) {
      case 'organization':
        return <Building2 className="h-5 w-5 text-primary" />;
      case 'workspace':
        return <Briefcase className="h-5 w-5 text-primary" />;
      case 'project':
        return <Users className="h-5 w-5 text-primary" />;
      default:
        return null;
    }
  };

  const getScopeName = () => {
    switch (invitationData?.scopeType) {
      case 'organization':
        return invitationData?.organization?.name;
      case 'workspace':
        return invitationData?.workspace?.name;
      case 'project':
        return invitationData?.project?.name;
      default:
        return '';
    }
  };

  const requiredFields = ['email', 'password', 'confirmPassword', 'firstName', 'lastName'];

  return (
    <div
      className={cn(
        'space-y-6 rounded-xl border border-border/50 bg-card/50 p-8 shadow-lg backdrop-blur-sm',
        className,
      )}
    >
      {invitationData && (
        <div className="mb-6 rounded-lg border border-primary/20 bg-primary/5 p-4">
          <div className="flex items-center gap-3">
            <div className="rounded-md bg-primary/10 p-2">{getScopeIcon()}</div>
            <div>
              <p className="font-medium text-muted-foreground text-sm">
                You've been invited to join
              </p>
              <p className="font-semibold text-foreground text-lg">{getScopeName()}</p>
              <p className="text-muted-foreground text-sm">
                as{' '}
                <span className="font-medium text-primary">
                  {invitationData.role?.name || 'a member'}
                </span>{' '}
                by{' '}
                <span className="font-medium">
                  {invitationData.inviter?.displayName || invitationData.inviter?.email}
                </span>
              </p>
            </div>
          </div>
        </div>
      )}

      <Form
        id="invite-register-form"
        onSubmit={(e) => {
          e.preventDefault();
          form.handleSubmit();
        }}
        {...props}
      >
        <div className="flex flex-col items-center gap-2 text-center">
          <div className="font-semibold text-2xl">Complete Your Registration</div>
          <p className="text-balance text-muted-foreground text-sm">
            Create your account to accept the invitation
          </p>
        </div>

        <div className="grid gap-y-3 p-3">
          <form.Field name="email">
            {(field) => (
              <div>
                <FormField field={field} required={requiredFields.includes('email')}>
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        className="bg-muted"
                        disabled={true}
                        id="email"
                        placeholder="<EMAIL>"
                        type="email" // Email is pre-filled from invitation
                        value={field.state.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              </div>
            )}
          </form.Field>

          <form.Field name="firstName">
            {(field) => (
              <div>
                <FormField field={field} required={requiredFields.includes('firstName')}>
                  <FormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input
                        id="firstName"
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="John"
                        value={field.state.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              </div>
            )}
          </form.Field>

          <form.Field name="lastName">
            {(field) => (
              <div>
                <FormField field={field} required={requiredFields.includes('lastName')}>
                  <FormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input
                        id="lastName"
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="Doe"
                        value={field.state.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              </div>
            )}
          </form.Field>

          <form.Field name="displayName">
            {(field) => (
              <div>
                <FormField field={field}>
                  <FormItem>
                    <FormLabel>Display Name (optional)</FormLabel>
                    <FormControl>
                      <Input
                        id="displayName"
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="How you want to be called"
                        value={field.state.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              </div>
            )}
          </form.Field>

          <form.Field name="password">
            {(field) => (
              <div>
                <FormField field={field} required={requiredFields.includes('password')}>
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input
                        id="password"
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        type="password"
                        value={field.state.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              </div>
            )}
          </form.Field>

          <form.Field name="confirmPassword">
            {(field) => (
              <div>
                <FormField field={field} required={requiredFields.includes('confirmPassword')}>
                  <FormItem>
                    <FormLabel>Confirm Password</FormLabel>
                    <FormControl>
                      <Input
                        id="confirmPassword"
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        type="password"
                        value={field.state.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              </div>
            )}
          </form.Field>

          <div>
            <Button className="w-full" disabled={register.isPending || isProcessing} type="submit">
              {register.isPending || isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating account...
                </>
              ) : (
                'Create Account'
              )}
            </Button>
          </div>
        </div>

        <div className="text-center text-sm">
          Already have an account? <Link to="/login">Sign in</Link>
        </div>
      </Form>
    </div>
  );
}
