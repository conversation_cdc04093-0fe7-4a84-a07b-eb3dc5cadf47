import { BarChart3, CalendarDays, CheckCircle2, LayoutDashboard, Users, Zap } from 'lucide-react';
import Logo from '@/web/assets/Logo.png';

// Prop types
interface AuthLeftPanelProps {
  children: React.ReactNode;
}

const AuthLeftPanel: React.FC<AuthLeftPanelProps> = ({ children }) => {
  return (
    <div className="relative min-h-svh overflow-hidden bg-background">
      {/* Left Panel - Equal Half */}
      <div className="absolute inset-0 hidden w-1/2 lg:block">
        <div className="relative h-full bg-gradient-to-br from-primary via-primary/90 to-primary/80">
          {/* Dot Pattern Background - More Visible */}
          <div
            className="absolute inset-0"
            style={{
              backgroundImage:
                'radial-gradient(circle at 2px 2px, var(--background) 1px, transparent 1px)',
              opacity: 0.3,
              backgroundSize: '24px 24px',
            }}
          />

          {/* Diagonal Lines Pattern */}
          <div
            className="absolute inset-0 opacity-20"
            style={{
              backgroundImage: `repeating-linear-gradient(
                -45deg,
                transparent,
                transparent 40px,
                rgba(255,255,255,0.1) 40px,
                rgba(255,255,255,0.1) 41px
              )`,
            }}
          />

          {/* Floating Elements - More Visible */}
          {/* <div className="absolute top-10 left-10 w-48 h-48 bg-background/30 rounded-full blur-xl animate-pulse" />
          <div className="absolute bottom-20 right-20 w-64 h-64 bg-background/25 rounded-full blur-2xl animate-pulse" style={{animationDelay: '1s'}} />
          <div className="absolute top-1/2 left-1/4 w-40 h-40 bg-background/35 rounded-full blur-xl animate-pulse" style={{animationDelay: '2s'}} /> */}

          {/* Content - Centered on the left half */}
          <div className="relative z-10 flex h-full items-center justify-center p-12">
            <div className="w-full max-w-xl">
              {/* Logo */}
              <div className="mb-10 flex items-center justify-center font-semibold text-2xl text-primary-foreground">
                <span className="mr-2">
                  <img alt="Spark logo" className="h-10 drop-shadow-md" src={Logo} />
                </span>
                <Zap className="h-10 w-10 text-primary-foreground" />
                PARK
              </div>

              {/* Main Content */}
              <div className="space-y-10 text-center">
                {/* Title and Subtitle */}
                <div className="space-y-4">
                  <h1 className="font-semibold text-4xl text-primary-foreground leading-tight tracking-tight">
                    Elevate Your Project Management
                  </h1>
                  <p className="mx-auto max-w-md text-base text-primary-foreground leading-relaxed opacity-85">
                    Streamline workflows, enhance collaboration, and deliver exceptional results
                    with our intuitive platform.
                  </p>
                </div>

                {/* Feature Cards */}
                <div className="mx-auto grid w-full max-w-lg grid-cols-2 gap-4">
                  <FeatureCard
                    description="Visualize progress at a glance"
                    icon={<LayoutDashboard className="h-5 w-5" />}
                    title="Intuitive Dashboard"
                  />
                  <FeatureCard
                    description="Work together seamlessly"
                    icon={<Users className="h-5 w-5" />}
                    title="Team Collaboration"
                  />
                  <FeatureCard
                    description="Organize work efficiently"
                    icon={<CalendarDays className="h-5 w-5" />}
                    title="Sprint Planning"
                  />
                  <FeatureCard
                    description="Track performance metrics"
                    icon={<BarChart3 className="h-5 w-5" />}
                    title="Real-time Analytics"
                  />
                </div>

                {/* Bottom Features */}
                <div className="flex items-center justify-center space-x-8 text-primary-foreground text-sm">
                  <div className="flex items-center space-x-2 opacity-80">
                    <Zap className="h-4 w-4" />
                    <span>Lightning Fast</span>
                  </div>
                  <div className="flex items-center space-x-2 opacity-80">
                    <CheckCircle2 className="h-4 w-4" />
                    <span>Enterprise Ready</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Panel - Form Content */}
      <div className="relative flex min-h-svh items-center justify-center p-8 lg:pl-[50%]">
        {/* Dot Pattern Background for Right Side - Theme Adaptive */}

        <div className="relative z-10 w-full max-w-md">{children}</div>
      </div>
    </div>
  );
};

// Feature Card Component
interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ icon, title, description }) => {
  return (
    <div className="group rounded-lg border border-background/30 bg-background/20 p-4 backdrop-blur-md transition-all duration-300 hover:scale-105 hover:border-background/40 hover:bg-background/30">
      <div className="flex items-start space-x-3">
        <div className="rounded-md bg-background/30 p-2 transition-colors group-hover:bg-background/40">
          {icon}
        </div>
        <div className="flex-1 text-primary-foreground">
          <h3 className="mb-1 font-medium text-sm">{title}</h3>
          <p className="text-xs leading-relaxed opacity-90">{description}</p>
        </div>
      </div>
    </div>
  );
};

export default AuthLeftPanel;
