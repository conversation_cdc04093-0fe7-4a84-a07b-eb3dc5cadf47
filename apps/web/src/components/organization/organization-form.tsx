'use client';

import { But<PERSON> } from '@repo/ui/components/button';
import { DialogFooter } from '@repo/ui/components/dialog';
import { useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import type {
  Organization,
  PatchApiV1OrganizationsIdBody,
  PostApiV1OrganizationsBody,
} from '@/web/services/hooks.schemas';
import { getGetApiV1InvitationsQueryKey } from '@/web/services/invitations';
import {
  usePatchApiV1OrganizationsId,
  usePostApiV1Organizations,
} from '@/web/services/organizations';
import { getGetApiV1RoleAssignmentsQueryKey } from '@/web/services/role-assignments';
import { useRootStore } from '@/web/store/store';
import { OrganizationForm as OrganizationFormComponent } from '../forms/organization-form';

interface OrganizationFormProps {
  onSubmit?: () => void;
  onSuccess?: (organization: Organization) => void;
  onCancel?: () => void;
  editPayload?: Organization | null;
}

export const OrganizationForm: React.FC<OrganizationFormProps> = ({
  onSubmit,
  onSuccess,
  onCancel,
  editPayload,
}) => {
  const queryClient = useQueryClient();
  const currentUser = useRootStore((state) => state.currentUser);

  // Define the form data type to match OrganizationFormData from the reusable component
  interface FormData {
    name: string;
    slug: string;
    description?: string;
    size?: string;
    color: string;
    website?: string;
    logoUrl?: string;
    billingEmail?: string;
    billingAddress?: string;
  }

  const [formData, setFormData] = useState<FormData>({
    name: editPayload?.name || '',
    slug: editPayload?.slug || '',
    description: editPayload?.description || undefined,
    size: (editPayload?.settings as { size?: string })?.size || undefined,
    color: (editPayload?.settings as { color?: string })?.color || '#6366f1',
    website: (editPayload?.domains as { website?: string })?.website || undefined,
    logoUrl: editPayload?.logoUrl || undefined,
    billingEmail: editPayload?.billingEmail || undefined,
    billingAddress: editPayload?.billingAddress || undefined,
  });

  const organizationMutation = usePostApiV1Organizations({
    mutation: {
      onSuccess: async (response) => {
        // Invalidate role-related queries
        await queryClient.invalidateQueries({
          queryKey: getGetApiV1RoleAssignmentsQueryKey(),
        });
        await queryClient.invalidateQueries({
          queryKey: getGetApiV1InvitationsQueryKey(),
        });

        if (onSuccess && response.data) {
          onSuccess(response.data);
        }
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (_error) => {},
    },
  });

  const organizationUpdateMutation = usePatchApiV1OrganizationsId({
    mutation: {
      onSuccess: async (response) => {
        // Invalidate role-related queries
        await queryClient.invalidateQueries({
          queryKey: getGetApiV1RoleAssignmentsQueryKey(),
        });
        await queryClient.invalidateQueries({
          queryKey: getGetApiV1InvitationsQueryKey(),
        });

        if (onSuccess && response.data) {
          onSuccess(response.data);
        }
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (_error) => {},
    },
  });

  const handleSubmit = async () => {
    const dataToSubmit: PatchApiV1OrganizationsIdBody | PostApiV1OrganizationsBody = {
      name: formData.name,
      slug: formData.slug,
      description: formData.description || null,
      logoUrl: formData.logoUrl || null,
      isActive: true,
      createdBy: currentUser?.id || null,
      settings: {
        size: formData.size,
        color: formData.color,
      },
      domains: formData.website ? { website: formData.website } : {},
      billingEmail: formData.billingEmail || null,
      billingAddress: formData.billingAddress || null,
      billingPlan: null,
    };

    if (editPayload) {
      await organizationUpdateMutation.mutateAsync({
        data: dataToSubmit,
        id: editPayload.id,
      });
    } else {
      await organizationMutation.mutateAsync({
        data: dataToSubmit as PostApiV1OrganizationsBody,
      });
    }
  };

  const isLoading = organizationMutation.isPending || organizationUpdateMutation.isPending;

  return (
    <>
      <div className="overflow-auto p-6">
        <OrganizationFormComponent
          data={formData}
          isOnboarding={false}
          onDataChange={setFormData}
          showTeamSize={!editPayload} // Show team size selector only for new organizations
        />
      </div>
      <DialogFooter>
        <Button disabled={isLoading} onClick={onCancel} type="button" variant="outline">
          Cancel
        </Button>
        <Button disabled={isLoading || !formData.name || !formData.slug} onClick={handleSubmit}>
          {isLoading ? 'Saving...' : editPayload ? 'Update' : 'Create'}
        </Button>
      </DialogFooter>
    </>
  );
};
