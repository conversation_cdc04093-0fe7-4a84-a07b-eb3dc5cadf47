'use client';

import { But<PERSON> } from '@repo/ui/components/button';
import { DialogFooter } from '@repo/ui/components/dialog';
import { Input } from '@repo/ui/components/input';
import { Label } from '@repo/ui/components/label';
import { Textarea } from '@repo/ui/components/textarea';
import { useToast } from '@repo/ui/components/use-toast';
import { useForm } from '@tanstack/react-form';
import type { StatusCategory } from '@/web/services/hooks.schemas';
import {
  usePatchApiV1StatusCategoriesId,
  usePostApiV1StatusCategories,
} from '../../../services/status-categories';
import { useRootStore } from '../../../store/store';

interface StatusCategoriesFormProps {
  item?: StatusCategory | null;
  onSuccess: () => void;
  onCancel: () => void;
}

export function StatusCategoriesForm({ item, onSuccess, onCancel }: StatusCategoriesFormProps) {
  const currentOrganizationId = useRootStore((state) => state.currentOrganizationId);
  const { toast } = useToast();

  const createMutation = usePostApiV1StatusCategories({
    mutation: {
      onSuccess: () => {
        toast({
          title: 'Success',
          description: 'Status category created successfully',
        });
        onSuccess();
      },
      onError: () => {
        toast({
          title: 'Error',
          description: 'Failed to create status category',
          variant: 'destructive',
        });
      },
    },
  });

  const updateMutation = usePatchApiV1StatusCategoriesId({
    mutation: {
      onSuccess: () => {
        toast({
          title: 'Success',
          description: 'Status category updated successfully',
        });
        onSuccess();
      },
      onError: () => {
        toast({
          title: 'Error',
          description: 'Failed to update status category',
          variant: 'destructive',
        });
      },
    },
  });

  const form = useForm({
    defaultValues: {
      name: item?.name || '',
      description: item?.description || '',
      color: item?.color || '#94a3b8',
    },
    onSubmit: async ({ value }) => {
      const formData = {
        ...value,
        organizationId: currentOrganizationId,
      };

      if (item?.id) {
        updateMutation.mutate({
          id: item.id,
          data: formData,
        });
      } else {
        createMutation.mutate({
          data: formData,
        });
      }
    },
  });

  const isLoading = createMutation.isPending || updateMutation.isPending;

  return (
    <form
      className="space-y-4"
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <div className="grid grid-cols-2 gap-4">
        <form.Field
          children={(field) => (
            <div className="space-y-2">
              <Label htmlFor={field.name}>Name</Label>
              <Input
                id={field.name}
                name={field.name}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Enter category name"
                required
                value={field.state.value}
              />
            </div>
          )}
          name="name"
        />

        <form.Field
          children={(field) => (
            <div className="space-y-2">
              <Label htmlFor={field.name}>Color</Label>
              <div className="flex gap-2">
                <Input
                  className="w-16"
                  onChange={(e) => field.handleChange(e.target.value)}
                  type="color"
                  value={field.state.value}
                />
                <Input
                  className="font-mono"
                  onChange={(e) => field.handleChange(e.target.value)}
                  placeholder="#94a3b8"
                  value={field.state.value}
                />
              </div>
            </div>
          )}
          name="color"
        />
      </div>

      <form.Field
        children={(field) => (
          <div className="space-y-2">
            <Label htmlFor={field.name}>Description</Label>
            <Textarea
              id={field.name}
              name={field.name}
              onBlur={field.handleBlur}
              onChange={(e) => field.handleChange(e.target.value)}
              placeholder="Enter category description"
              required
              value={field.state.value}
            />
          </div>
        )}
        name="description"
      />

      <DialogFooter>
        <Button onClick={onCancel} type="button" variant="outline">
          Cancel
        </Button>
        <Button disabled={isLoading} type="submit">
          {isLoading ? 'Saving...' : item ? 'Update' : 'Create'}
        </Button>
      </DialogFooter>
    </form>
  );
}
