'use client';

import type { StatusCategory } from '@/web/services/hooks.schemas';
import { useGetApiV1StatusCategories } from '../../../services/status-categories';
import { StatusCategoriesList } from './status-categories-list';
export default function StatusCategories() {
  const { data: statusCategories, isLoading } = useGetApiV1StatusCategories();

  const handleEdit = (_item: StatusCategory) => {};

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <div className="font-semibold text-xl">Status Categories</div>
          <p className="text-muted-foreground">Manage high-level status groupings.</p>
        </div>
        {/* <Button onClick={handleAdd} disabled={isLoading}>
          <Plus className="mr-2 h-4 w-4" />
          Add Category
        </Button> */}
      </div>

      <StatusCategoriesList
        data={statusCategories?.data || []}
        isLoading={isLoading}
        onEdit={handleEdit}
      />

      {/* <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {editingItem ? "Edit" : "Add"} Status Category
            </DialogTitle>
            <DialogDescription>
              {editingItem ? "Update the" : "Create a new"} status category.
            </DialogDescription>
          </DialogHeader>

          <StatusCategoriesForm
            item={editingItem}
            onSuccess={handleSuccess}
            onCancel={() => setDialogOpen(false)}
          />
        </DialogContent>
      </Dialog> */}
    </div>
  );
}
