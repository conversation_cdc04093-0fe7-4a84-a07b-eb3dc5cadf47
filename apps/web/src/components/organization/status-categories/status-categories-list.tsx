'use client';

import { But<PERSON> } from '@repo/ui/components/button';
import { Confirmation } from '@repo/ui/components/custom/confirmDialog';
import { DataTable } from '@repo/ui/components/data-table';
import { useToast } from '@repo/ui/components/use-toast';
import type { ColumnDef } from '@tanstack/react-table';
import { Edit, Trash2 } from 'lucide-react';
import type { StatusCategory } from '@/web/services/hooks.schemas';
import { useDeleteApiV1StatusCategoriesId } from '../../../services/status-categories';

interface StatusCategoriesListProps {
  data: StatusCategory[];
  onEdit: (item: StatusCategory) => void;
  isLoading: boolean;
}

export function StatusCategoriesList({ data, onEdit, isLoading }: StatusCategoriesListProps) {
  // const [isDialogOpen, setIsDialogOpen] = useState(false);
  // const [editCategory, setEditCategory] = useState<StatusCategory | undefined>(undefined);
  const { toast } = useToast();

  const deleteMutation = useDeleteApiV1StatusCategoriesId({
    mutation: {
      onSuccess: () => {
        toast({
          title: 'Success',
          description: 'Status category deleted successfully',
        });
        window.location.reload(); // Simple refresh for now
      },
      onError: () => {
        toast({
          title: 'Error',
          description: 'Failed to delete status category',
          variant: 'destructive',
        });
      },
    },
  });

  const columns: ColumnDef<StatusCategory>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'description',
      header: 'Description',
    },
    {
      accessorKey: 'color',
      header: 'Color',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <div
            className="h-4 w-4 rounded-full border"
            style={{ backgroundColor: row.getValue('color') || '#94a3b8' }}
          />
          <span className="font-mono text-sm">{row.getValue('color') || '#94a3b8'}</span>
        </div>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const category = row.original;
        return (
          <div className="flex space-x-2">
            <Button disabled={isLoading} onClick={() => onEdit(category)} size="sm" variant="ghost">
              <Edit className="h-4 w-4" />
            </Button>
            <Confirmation
              cancelButton={{
                name: 'Cancel',
                onClick: () => {},
                variant: 'secondary',
              }}
              confirmButton={{
                name: 'Delete',
                onClick: () => deleteMutation.mutate({ id: category.id }),
                variant: 'destructive',
              }}
              description={`Are you sure you want to delete ${category.name}?`}
              title="Are you sure?"
            >
              <Button disabled={deleteMutation.isPending} size="sm" variant="ghost">
                <Trash2 className="h-4 w-4" />
              </Button>
            </Confirmation>
          </div>
        );
      },
    },
  ];

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <DataTable
      columns={columns}
      data={data}
      // searchKey="name"
      enableFiltering={true}
      enablePagination={false}
      enableRowSelection={false}
      // enableColumnVisibility={true}
      enableSorting={true}
      searchPlaceholder="Search status categories..."
    />
  );
}
