import { Alert, AlertDescription } from '@repo/ui/components/alert';
import { Button } from '@repo/ui/components/button';
import { Confirmation } from '@repo/ui/components/custom/confirmDialog';
import { DataTable } from '@repo/ui/components/data-table';
import { toast } from '@repo/ui/components/use-toast';
import { useNavigate } from '@tanstack/react-router';
import type { ColumnDef } from '@tanstack/react-table';
import { Edit, InfoIcon, Trash2 } from 'lucide-react';
import type { Workflow } from '@/web/services/hooks.schemas';
import { useDeleteApiV1WorkflowsId, useGetApiV1Workflows } from '@/web/services/workflows';
import { useRootStore } from '@/web/store/store';

// Define the type for params in renderCell and valueFormatter

const Workflows = () => {
  const navigate = useNavigate();
  const currentOrganizationId = useRootStore((state) => state.currentOrganizationId);

  const { data: workflows, refetch: refetchWorkflows } = useGetApiV1Workflows(
    currentOrganizationId
      ? {
          filters: JSON.stringify({
            organizationId: { $eq: currentOrganizationId },
          }),
          limit: 100,
        }
      : undefined,
    {
      query: {
        enabled: !!currentOrganizationId,
      },
    },
  );

  const handleAdd = () => {
    if (!currentOrganizationId) {
      toast({
        title: 'No Organization Selected',
        description: 'Please select an organization before creating a workflow.',
        variant: 'destructive',
      });
      return;
    }
    navigate({ to: '/settings/work-flow/create' });
  };

  const handleEdit = (workflow: Workflow) => {
    navigate({
      to: '/settings/work-flow/$workflowId/edit',
      params: { workflowId: workflow.id },
    });
  };

  const deleteMutation = useDeleteApiV1WorkflowsId({
    mutation: {
      onSuccess: () => {
        refetchWorkflows();
      },
      onError: (_error) => {},
    },
  });

  const columns: ColumnDef<Workflow>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'description',
      header: 'Description',
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const workflow = row.original;
        return (
          <div className="flex space-x-2">
            <Button
              onClick={() => handleEdit(workflow)}
              size="sm"
              title="Edit properties"
              variant="ghost"
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Confirmation
              cancelButton={{
                name: 'Cancel',
                onClick: () => {},
                variant: 'secondary',
              }}
              confirmButton={{
                name: 'Delete',
                onClick: () => deleteMutation.mutate({ id: workflow.id }),
                variant: 'destructive',
              }}
              description={`Are you sure you want to delete ${workflow.name}?`}
              title="Are you sure?"
            >
              <Button disabled={deleteMutation.isPending} size="sm" variant="ghost">
                {deleteMutation.isPending ? 'Deleting...' : <Trash2 className="h-4 w-4" />}
              </Button>
            </Confirmation>
          </div>
        );
      },
    },
  ];

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="font-semibold text-xl">Workflows</div>
        <Button onClick={handleAdd} size="sm" variant="default">
          Add Workflow
        </Button>
      </div>

      {currentOrganizationId ? (
        <DataTable
          columns={columns}
          data={workflows?.data || []}
          enableColumnVisibility={true}
          enableFiltering={true}
          enablePagination={false}
          enableRowSelection={false}
          enableSorting={true}
          searchKey="name"
          searchPlaceholder="Search workflows..."
        />
      ) : (
        <Alert>
          <InfoIcon className="h-4 w-4" />
          <AlertDescription>
            Please select an organization to view and manage workflows.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default Workflows;
