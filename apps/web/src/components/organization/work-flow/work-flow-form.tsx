'use client';

import { insertWorkflowSchema, patchWorkflowSchema } from '@repo/db/schema';
import { Button } from '@repo/ui/components/button';
import { DialogFooter } from '@repo/ui/components/dialog';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
import { Switch } from '@repo/ui/components/switch';
import { Textarea } from '@repo/ui/components/textarea';
import { cn } from '@repo/ui/lib/utils';
import { useForm } from '@tanstack/react-form';
import type {
  PatchApiV1WorkflowsIdBody,
  PostApiV1WorkflowsBody,
  Workflow,
} from '@/web/services/hooks.schemas';
import { usePatchApiV1WorkflowsId, usePostApiV1Workflows } from '@/web/services/workflows';
import { useRootStore } from '@/web/store/store';

interface WorkflowFormProps {
  onSubmit?: () => void;
  onCancel?: () => void;
  editPayload?: Workflow | null;
  organizationId?: string;
}

export const WorkflowForm: React.FC<WorkflowFormProps> = ({ onSubmit, onCancel, editPayload }) => {
  const currentUser = useRootStore((state) => state.currentUser);
  const organizationId = useRootStore((state) => state.currentOrganizationId);

  // Normalize defaultValues to ensure valid initialization
  const defaultValues: PostApiV1WorkflowsBody | PatchApiV1WorkflowsIdBody = editPayload || {
    name: '',
    description: '',
    organizationId,
    isDefault: false,
    isActive: true,
    createdBy: currentUser?.id || null,
    settings: {},
  };

  const createMutation = usePostApiV1Workflows({
    mutation: {
      onSuccess: (_data) => {
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (_error) => {},
    },
  });

  const updateMutation = usePatchApiV1WorkflowsId({
    mutation: {
      onSuccess: (_data) => {
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (_error) => {},
    },
  });
  const schemaForForm = editPayload ? patchWorkflowSchema : insertWorkflowSchema;
  const form = useForm({
    defaultValues,
    validators: {
      onSubmit: schemaForForm,
    },
    onSubmit: async ({ value }) => {
      try {
        if (editPayload?.id) {
          await updateMutation.mutateAsync({
            id: editPayload.id,
            data: value,
          });
        } else {
          await createMutation.mutateAsync({
            data: value as PostApiV1WorkflowsBody,
          });
        }
      } catch (_error) {}
    },
  });

  const requiredFields = ['name', 'organizationId'];

  return (
    <>
      <div className="overflow-auto">
        <form
          className="space-y-3 p-3"
          id="workflow-form"
          onSubmit={(e) => {
            e.preventDefault();
            form.handleSubmit();
          }}
        >
          <form.Field name="name">
            {(field) => (
              <FormField field={field} required={requiredFields.includes('name')}>
                <FormItem>
                  <FormLabel>Workflow Name</FormLabel>
                  <FormControl>
                    <Input
                      className={cn(field.state.meta.errors[0] && 'border-destructive')}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                      placeholder="Workflow Name"
                      value={field.state.value}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            )}
          </form.Field>

          <form.Field name="description">
            {(field) => (
              <FormField field={field} required={requiredFields.includes('description')}>
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      className={cn(field.state.meta.errors[0] && 'border-destructive')}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                      placeholder="Workflow description"
                      value={field.state.value || ''}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            )}
          </form.Field>

          <form.Field name="organizationId">
            {(field) => (
              <FormField field={field} required={requiredFields.includes('organizationId')}>
                <FormItem className="hidden">
                  <FormControl>
                    <Input
                      className={cn(field.state.meta.errors[0] && 'border-destructive')}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                      value={field.state.value || ''}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            )}
          </form.Field>

          <form.Field name="isDefault">
            {(field) => (
              <FormField field={field} required={requiredFields.includes('isDefault')}>
                <FormItem className="flex items-center space-x-2">
                  <FormLabel>Is Default</FormLabel>
                  <FormControl>
                    <Switch
                      checked={field.state.value}
                      onCheckedChange={(checked) => field.handleChange(checked)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            )}
          </form.Field>

          <form.Field name="isActive">
            {(field) => (
              <FormField field={field} required={requiredFields.includes('isActive')}>
                <FormItem className="flex items-center space-x-2">
                  <FormLabel>Is Active</FormLabel>
                  <FormControl>
                    <Switch
                      checked={field.state.value}
                      onCheckedChange={(checked) => field.handleChange(checked)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            )}
          </form.Field>

          <form.Field name="createdBy">
            {(field) => (
              <FormField field={field} required={requiredFields.includes('createdBy')}>
                <FormItem className="hidden">
                  <FormControl>
                    <Input
                      className={cn(field.state.meta.errors[0] && 'border-destructive')}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                      value={field.state.value || ''}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            )}
          </form.Field>

          <form.Field name="settings">
            {(field) => (
              <FormField field={field} required={requiredFields.includes('settings')}>
                <FormItem>
                  <FormLabel>Settings (JSON)</FormLabel>
                  <FormControl>
                    <Textarea
                      className={cn(field.state.meta.errors[0] && 'border-destructive')}
                      onBlur={field.handleBlur}
                      onChange={(e) => {
                        try {
                          const json = e.target.value ? JSON.parse(e.target.value) : {};
                          field.handleChange(json);
                        } catch (_error) {}
                      }}
                      placeholder='{"key": "value"}'
                      value={field.state.value ? JSON.stringify(field.state.value, null, 2) : ''}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            )}
          </form.Field>
        </form>
      </div>
      <DialogFooter>
        <Button
          disabled={createMutation.isPending || updateMutation.isPending}
          onClick={onCancel}
          type="button"
          variant="outline"
        >
          Cancel
        </Button>
        <Button
          disabled={createMutation.isPending || updateMutation.isPending}
          form="workflow-form"
          type="submit"
        >
          {createMutation.isPending || updateMutation.isPending
            ? 'Saving...'
            : editPayload
              ? 'Update'
              : 'Create'}
        </Button>
      </DialogFooter>
    </>
  );
};
