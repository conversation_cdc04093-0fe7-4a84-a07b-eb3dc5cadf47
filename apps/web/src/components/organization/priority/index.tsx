import { Dialog, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@repo/ui/components/dialog';
import { useState } from 'react';
import type { Priority } from '@/web/services/hooks.schemas';
import { useGetApiV1Priorities } from '@/web/services/priorities';
import { PriorityForm } from './priority-form';
import PriorityList from './priority-list';

export const Priorities = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editPayload, setEditPayload] = useState<Priority | null>(null);

  // Fetch priorities
  const { data: priorities = { data: [] }, isLoading, refetch } = useGetApiV1Priorities({});

  const handleEdit = (priority: Priority) => {
    setEditPayload(priority);
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditPayload(null);
  };

  const handleFormSubmit = () => {
    setIsDialogOpen(false);
    setEditPayload(null);
    refetch(); // Refresh priority list
  };

  return (
    <div className="h-screen">
      <PriorityList
        isLoading={isLoading}
        onEdit={handleEdit}
        priorities={priorities.data}
        refetch={refetch}
      />
      <Dialog onOpenChange={setIsDialogOpen} open={isDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{editPayload ? 'Edit Priority' : 'Add Priority'}</DialogTitle>
          </DialogHeader>
          <PriorityForm
            editPayload={editPayload}
            onCancel={handleCloseDialog}
            onSubmit={handleFormSubmit}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Priorities;
