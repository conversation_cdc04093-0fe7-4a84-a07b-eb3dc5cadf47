import { Button } from '@repo/ui/components/button';
import { Confirmation } from '@repo/ui/components/custom/confirmDialog';
import { DataTable } from '@repo/ui/components/data-table';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@repo/ui/components/dialog';
import type { ColumnDef } from '@tanstack/react-table';
import { Edit, Plus, Trash2 } from 'lucide-react';
import { useState } from 'react';
import type { Priority } from '@/web/services/hooks.schemas';
import { useDeleteApiV1PrioritiesId } from '@/web/services/priorities';
import { renderPriorityIcon } from '../work-item-icons';
import { PriorityForm } from './priority-form';

interface PriorityListProps {
  priorities: Priority[];
  isLoading: boolean;
  onEdit: (priority: Priority) => void;
  refetch: () => void;
}

const PriorityList: React.FC<PriorityListProps> = ({
  priorities,
  isLoading,

  onEdit,
  refetch,
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editPriority, setEditPriority] = useState<Priority | undefined>(undefined);
  const deleteMutation = useDeleteApiV1PrioritiesId({
    mutation: {
      onSuccess: () => {
        refetch();
      },
      onError: (_error) => {
        // alert(`Failed to delete priority:  "Unknown error"}`);
      },
    },
  });
  const columns: ColumnDef<Priority>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'color',
      header: 'Color',
      cell: ({ row }) => (
        <div
          className="h-6 w-6 rounded-full"
          style={{ backgroundColor: row.getValue('color') || '#FFFFFF' }}
        />
      ),
    },
    {
      accessorKey: 'icon',
      header: 'Icon',
      cell: ({ row }) => {
        return (
          <div className="flex items-center">
            {renderPriorityIcon(row.original.icon, row.original.color)}
          </div>
        );
      },
    },
    {
      accessorKey: 'isActive',
      header: 'Active',
      cell: ({ row }) => (row.getValue('isActive') ? 'Yes' : 'No'),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const priority = row.original;
        return (
          <div className="flex space-x-2">
            <Button onClick={() => onEdit(priority)} size="sm" variant="ghost">
              <Edit className="h-4 w-4" />
            </Button>
            <Confirmation
              cancelButton={{
                name: 'Cancel',
                onClick: () => {},
                variant: 'secondary',
              }}
              confirmButton={{
                name: 'Delete',
                onClick: () => deleteMutation.mutate({ id: priority.id }),
                variant: 'destructive',
              }}
              description={`Are you sure you want to delete ${priority.name}?`}
              title="Are you sure?"
            >
              <Button disabled={deleteMutation.isPending} size="sm" variant="ghost">
                <Trash2 className="h-4 w-4" />
              </Button>
            </Confirmation>
          </div>
        );
      },
    },
  ];

  const handleOpenDialog = (priority?: Priority) => {
    setEditPriority(priority);
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditPriority(undefined);
  };

  const handleFormSubmit = () => {
    handleCloseDialog();
    refetch(); // Refresh priority list
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <div className="font-semibold text-xl">Priorities</div>
          <p className="text-muted-foreground">Manage priority levels for work items.</p>
        </div>
        <Button disabled={isLoading} onClick={() => handleOpenDialog()}>
          <Plus className="mr-2 h-4 w-4" />
          Add Priority
        </Button>
      </div>

      <DataTable
        columns={columns}
        data={priorities}
        enableColumnVisibility={true}
        enableFiltering={true}
        enablePagination={false}
        enableRowSelection={false}
        enableSorting={true}
        searchKey="name"
        searchPlaceholder="Search priorities..."
      />

      <Dialog onOpenChange={setIsDialogOpen} open={isDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{editPriority ? 'Edit Priority' : 'Add Priority'}</DialogTitle>
          </DialogHeader>
          <PriorityForm
            editPayload={editPriority}
            onCancel={handleCloseDialog}
            onSubmit={handleFormSubmit}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PriorityList;
