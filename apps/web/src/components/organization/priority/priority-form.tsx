'use client';

import { insertPrioritySchema, patchPrioritySchema } from '@repo/db/schema';
import { Button } from '@repo/ui/components/button';
import { DialogFooter } from '@repo/ui/components/dialog';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { Switch } from '@repo/ui/components/switch';
import { Textarea } from '@repo/ui/components/textarea';
import { useForm } from '@tanstack/react-form';
import type {
  PatchApiV1PrioritiesIdBody,
  PostApiV1PrioritiesBody,
  Priority,
} from '@/web/services/hooks.schemas';
import { usePatchApiV1PrioritiesId, usePostApiV1Priorities } from '@/web/services/priorities';
import { useRootStore } from '@/web/store/store';
import { prioritytIcons, renderPriorityIcon } from '../work-item-icons';

interface PriorityFormProps {
  onSubmit?: () => void;
  onCancel?: () => void;
  editPayload?: Priority | null;
}

export const PriorityForm: React.FC<PriorityFormProps> = ({ onSubmit, onCancel, editPayload }) => {
  const currentOrganizationId = useRootStore((state) => state.currentOrganizationId);

  const defaultValues: PatchApiV1PrioritiesIdBody | PostApiV1PrioritiesBody = editPayload
    ? {
        ...editPayload,
        description: editPayload.description ?? '',
        color: editPayload.color ?? '#000000',
        icon: editPayload.icon ?? '',
      }
    : {
        name: '',
        description: '',
        color: '#000000',
        icon: '',
        organizationId: currentOrganizationId,
        isActive: true,
      };

  const createMutation = usePostApiV1Priorities({
    mutation: {
      onSuccess: () => {
        onSubmit?.();
      },
      onError: (error) => {
        alert(`Failed to create priority: ${error.message || 'Unknown error'}`);
      },
    },
  });

  const updateMutation = usePatchApiV1PrioritiesId({
    mutation: {
      onSuccess: () => {
        onSubmit?.();
      },
      onError: (error) => {
        alert(`Failed to update priority: ${error.message || 'Unknown error'}`);
      },
    },
  });

  const isEditMode = !!editPayload;

  const schemaForForm = editPayload ? patchPrioritySchema : insertPrioritySchema;

  const form = useForm({
    defaultValues,
    validators: {
      onSubmit: schemaForForm,
    },
    onSubmit: async ({ value }) => {
      if (isEditMode && editPayload?.id) {
        await updateMutation.mutateAsync({
          id: editPayload.id,
          data: value,
        });
      } else {
        await createMutation.mutateAsync({
          data: value as PostApiV1PrioritiesBody,
        });
      }
    },
  });

  const requiredFields = ['name', 'organizationId'];

  return (
    <div className="overflow-auto">
      <form
        className="space-y-3 p-3"
        id="priority-form"
        onSubmit={(e) => {
          e.preventDefault();
          form.handleSubmit();
        }}
      >
        <form.Field name="name">
          {(field) => (
            <FormField field={field} required={requiredFields.includes('name')}>
              <FormItem>
                <FormLabel>Priority Name</FormLabel>
                <FormControl>
                  <Input
                    className={field.state.meta.errors[0] && 'border-destructive'}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Priority Name"
                    value={field.state.value}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>

        <form.Field name="description">
          {(field) => (
            <FormField field={field} required={requiredFields.includes('description')}>
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea
                    className={field.state.meta.errors[0] && 'border-destructive'}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value || '')}
                    placeholder="Priority description"
                    value={field.state.value ?? ''}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>

        <form.Field name="organizationId">
          {(field) => (
            <FormField field={field} required={requiredFields.includes('organizationId')}>
              <FormItem className="hidden">
                <FormControl>
                  <Input
                    className={field.state.meta.errors[0] && 'border-destructive'}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    value={field.state.value || ''}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>

        <form.Field name="color">
          {(field) => (
            <FormField field={field} required={requiredFields.includes('color')}>
              <FormItem>
                <FormLabel>Color</FormLabel>
                <FormControl>
                  <Input
                    className={field.state.meta.errors[0] && 'border-destructive'}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value || '')}
                    type="color"
                    value={field.state.value ?? '#000000'}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>

        <form.Field name="icon">
          {(field) => (
            <FormField field={field} required={requiredFields.includes('icon')}>
              <FormItem>
                <FormLabel>Icon</FormLabel>
                <FormControl>
                  {/* <Input
                    placeholder="Icon name or URL"
                    value={field.state.value ?? ""}
                    onChange={(e) => field.handleChange(e.target.value || "")}
                    onBlur={field.handleBlur}
                    className={field.state.meta.errors[0] && "border-destructive"}
                  /> */}
                  <div className="flex items-center space-x-2">
                    <Select
                      onValueChange={(value) => field.handleChange(value || null)}
                      value={field.state.value as string}
                    >
                      <SelectTrigger className="w-[200px]">
                        <SelectValue placeholder="Select an icon">
                          {field.state.value
                            ? prioritytIcons.find((icon) => icon.name === field.state.value)?.label
                            : 'Select an icon'}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        {prioritytIcons.map((icon) => (
                          <SelectItem key={icon.name} value={icon.name}>
                            <div className="flex items-center space-x-2">
                              <icon.component className="h-5 w-5" />
                              <span>{icon.label}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {renderPriorityIcon(
                      field.state.value || null,
                      field.state.value ? '#000' : null,
                    )}
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>
        {/* {JSON.stringify(form.state.errors, null, 2)} */}

        <form.Field name="isActive">
          {(field) => (
            <FormField field={field} required={requiredFields.includes('isActive')}>
              <FormItem className="flex items-center space-x-2">
                <FormLabel>Active</FormLabel>
                <FormControl>
                  <Switch
                    checked={field.state.value}
                    onCheckedChange={(checked) => field.handleChange(checked)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>
      </form>
      <DialogFooter>
        <Button
          disabled={createMutation.isPending || updateMutation.isPending}
          onClick={onCancel}
          type="button"
          variant="outline"
        >
          Cancel
        </Button>
        <Button
          disabled={createMutation.isPending || updateMutation.isPending}
          form="priority-form"
          type="submit"
        >
          {createMutation.isPending || updateMutation.isPending
            ? 'Saving...'
            : isEditMode
              ? 'Update'
              : 'Create'}
        </Button>
      </DialogFooter>
    </div>
  );
};
