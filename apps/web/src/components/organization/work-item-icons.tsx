// Import lucide-react icons
import {
  AlertCircle,
  AlertTriangle,
  Bug,
  CheckCircle,
  ChevronDown,
  ChevronsDown,
  ChevronsUp,
  ChevronUp,
  Clipboard,
  FileText,
  Flag,
  Lightbulb,
  Minus,
  Rocket,
  Star,
  Tag,
} from 'lucide-react';

export const workItemIcons = [
  { name: 'check-circle', component: CheckCircle, label: 'Task' },
  { name: 'bug', component: Bug, label: 'Bug' },
  { name: 'star', component: Star, label: 'Feature' },
  { name: 'flag', component: Flag, label: 'Priority' },
  { name: 'clipboard', component: Clipboard, label: 'Story' },
  { name: 'file-text', component: FileText, label: 'Document' },
  { name: 'lightbulb', component: Lightbulb, label: 'Idea' },
  { name: 'alert-triangle', component: AlertTriangle, label: 'Risk' },
  { name: 'rocket', component: Rocket, label: 'Epic' },
  { name: 'tag', component: Tag, label: 'Tag' },
];

export const prioritytIcons = [
  { name: 'flag', component: Flag, label: 'Low' },
  { name: 'star', component: Star, label: 'Medium' },
  { name: 'alert-triangle', component: AlertTriangle, label: 'High' },
  { name: 'bug', component: Bug, label: 'Critical' },
  { name: 'alert-circle', component: AlertCircle, label: 'Urgent' },
  { name: 'arrow-up', component: ChevronUp, label: 'Improvement' },
  { name: 'arrows-up', component: ChevronsUp, label: 'Enhancement' },
  { name: 'minus', component: Minus, label: 'Minor' },
  { name: 'arrow-down', component: ChevronDown, label: 'Downgrade' },
  { name: 'arrows-down', component: ChevronsDown, label: 'Deprioritize' },
];

export const renderIcon = (iconName: string | null, color: string | null) => {
  const IconComponent = workItemIcons.find((icon) => icon.name === iconName)?.component;
  return IconComponent ? <IconComponent className="h-4 w-4" color={color || '#000'} /> : null;
};

export const renderPriorityIcon = (priorityName: string | null, color: string | null) => {
  const IconComponent = prioritytIcons.find(
    (icon) => icon.name.toLowerCase() === priorityName?.toLowerCase(),
  )?.component;
  return IconComponent ? <IconComponent className="h-5 w-5" color={color || '#000'} /> : null;
};
