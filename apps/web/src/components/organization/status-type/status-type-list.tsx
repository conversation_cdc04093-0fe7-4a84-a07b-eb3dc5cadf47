import { Button } from '@repo/ui/components/button';
import { Confirmation } from '@repo/ui/components/custom/confirmDialog';
import { DataTable } from '@repo/ui/components/data-table';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@repo/ui/components/dialog';
import type { ColumnDef } from '@tanstack/react-table';
import { Edit, Plus, Trash2 } from 'lucide-react';
import { useState } from 'react';
import type { StatusCategory } from '@/web/services/hooks.schemas';
import { useDeleteApiV1StatusCategoriesId } from '@/web/services/status-categories';
import { StatusTypeForm } from './status-type-form';

// Define the StatusType interface based on the schema

interface StatusTypesListProps {
  statusTypes: StatusCategory[];
  isLoading: boolean;
  onEdit: (statusType: StatusCategory) => void;
  refetch: () => void;
}

const StatusTypesList: React.FC<StatusTypesListProps> = ({
  statusTypes,
  isLoading,

  onEdit,
  refetch,
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editStatusType, setEditStatusType] = useState<StatusCategory | undefined>(undefined);
  const deleteMutation = useDeleteApiV1StatusCategoriesId({
    mutation: {
      onSuccess: () => {
        refetch();
      },
      onError: (_error) => {
        // alert(`Failed to delete status type: ${error.message || "Unknown error"}`);
      },
    },
  });
  const columns: ColumnDef<StatusCategory>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'color',
      header: 'Color',
      cell: ({ row }) => (
        <div
          className="h-6 w-6 rounded-full"
          style={{ backgroundColor: row.getValue('color') || '#FFFFFF' }}
        />
      ),
    },
    {
      accessorKey: 'isActive',
      header: 'Active',
      cell: ({ row }) => (row.getValue('isActive') ? 'Yes' : 'No'),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const statusType = row.original;
        return (
          <div className="flex space-x-2">
            <Button onClick={() => onEdit(statusType)} size="sm" variant="ghost">
              <Edit className="h-4 w-4" />
            </Button>
            <Confirmation
              cancelButton={{
                name: 'Cancel',
                onClick: () => {},
                variant: 'secondary',
              }}
              confirmButton={{
                name: 'Delete',
                onClick: () => deleteMutation.mutate({ id: statusType.id }),
                variant: 'destructive',
              }}
              description={`Are you sure you want to delete ${statusType.name}?`}
              title="Are you sure?"
            >
              <Button disabled={deleteMutation.isPending} size="sm" variant="ghost">
                <Trash2 className="h-4 w-4" />
              </Button>
            </Confirmation>
          </div>
        );
      },
    },
  ];

  const handleOpenDialog = (statusType?: StatusCategory) => {
    setEditStatusType(statusType);
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditStatusType(undefined);
  };

  const handleFormSubmit = () => {
    handleCloseDialog();
    refetch(); // Refresh status type list
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="font-semibold text-2xl tracking-tight">Status Types</h2>
          <p className="text-muted-foreground">Manage status type categories.</p>
        </div>
        <Button disabled={isLoading} onClick={() => handleOpenDialog()}>
          <Plus className="mr-2 h-4 w-4" />
          Add Status Type
        </Button>
      </div>

      <DataTable
        columns={columns}
        data={statusTypes}
        enableColumnVisibility={true}
        enableFiltering={true}
        enablePagination={false}
        enableRowSelection={false}
        enableSorting={true}
        searchKey="name"
        searchPlaceholder="Search status types..."
      />

      <Dialog onOpenChange={setIsDialogOpen} open={isDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{editStatusType ? 'Edit Status Type' : 'Add Status Type'}</DialogTitle>
          </DialogHeader>
          <StatusTypeForm
            editPayload={editStatusType}
            onCancel={handleCloseDialog}
            onSubmit={handleFormSubmit}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default StatusTypesList;
