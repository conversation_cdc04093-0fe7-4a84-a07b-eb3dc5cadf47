import { insertStatusCategorySchema, patchStatusCategorySchema } from '@repo/db/schema';
import { Button } from '@repo/ui/components/button';
import { DialogFooter } from '@repo/ui/components/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
import { Switch } from '@repo/ui/components/switch';
import { Textarea } from '@repo/ui/components/textarea';
import { useForm } from '@tanstack/react-form';
import type {
  PatchApiV1StatusCategoriesIdBody,
  PostApiV1StatusCategoriesBody,
  StatusCategory,
} from '@/web/services/hooks.schemas';
import {
  usePatchApiV1StatusCategoriesId,
  usePostApiV1StatusCategories,
} from '@/web/services/status-categories';

interface StatusTypeFormProps {
  onSubmit?: () => void;
  onCancel?: () => void;
  editPayload?: StatusCategory | null;
}

export const StatusTypeForm: React.FC<StatusTypeFormProps> = ({
  onSubmit,
  onCancel,
  editPayload,
}) => {
  const defaultValues: PatchApiV1StatusCategoriesIdBody | PostApiV1StatusCategoriesBody =
    editPayload || {
      name: '',
      description: null,
      color: '#000000',
      isActive: true,
    };

  const requiredFields = ['name', 'category', 'organizationId'];

  const statusTypeMutation = usePostApiV1StatusCategories({
    mutation: {
      onSuccess: () => {
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (_error) => {},
    },
  });

  const statusTypeUpdateMutation = usePatchApiV1StatusCategoriesId({
    mutation: {
      onSuccess: () => {
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (_error) => {},
    },
  });
  const schemaForForm = editPayload ? patchStatusCategorySchema : insertStatusCategorySchema;
  const form = useForm({
    defaultValues,
    validators: {
      onSubmit: schemaForForm,
    },
    onSubmit: async ({ value }) => {
      if (editPayload) {
        await statusTypeUpdateMutation.mutateAsync({
          data: value,
          id: editPayload.id,
        });
      } else {
        await statusTypeMutation.mutateAsync({
          data: value as PostApiV1StatusCategoriesBody,
        });
      }
    },
  });

  return (
    <Form id="status-type-form">
      <form.Field name="name">
        {(field) => (
          <FormField field={field} required={requiredFields.includes('name')}>
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input
                  onBlur={field.handleBlur}
                  onChange={(e) => field.handleChange(e.target.value)}
                  placeholder="Status type name"
                  value={field.state.value}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>
        )}
      </form.Field>

      <form.Field name="description">
        {(field) => (
          <FormField field={field} required={requiredFields.includes('description')}>
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  onBlur={field.handleBlur}
                  onChange={(e) => field.handleChange(e.target.value)}
                  placeholder="Status type description"
                  value={field.state.value || ''}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>
        )}
      </form.Field>

      <form.Field name="color">
        {(field) => (
          <FormField field={field} required={requiredFields.includes('color')}>
            <FormItem>
              <FormLabel>Color</FormLabel>
              <FormControl>
                <Input
                  onBlur={field.handleBlur}
                  onChange={(e) => field.handleChange(e.target.value)}
                  type="color"
                  value={field.state.value || '#000000'}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>
        )}
      </form.Field>

      <form.Field name="isActive">
        {(field) => (
          <FormField field={field} required={requiredFields.includes('isActive')}>
            <FormItem className="flex items-center space-x-2">
              <FormLabel>Active</FormLabel>
              <FormControl>
                <Switch
                  checked={field.state.value}
                  onCheckedChange={(checked) => field.handleChange(checked)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>
        )}
      </form.Field>

      {/* <form.Field name="createdBy">
        {(field) => (
          <FormField
            field={field}
            required={requiredFields.includes("createdBy")}
          >
            <FormItem className="hidden">
              <FormControl>
                <Input
                  value={field.state.value || ""}
                  onChange={(e) => field.handleChange(e.target.value || null)}
                  onBlur={field.handleBlur}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>
        )}
      </form.Field> */}

      <DialogFooter>
        <Button onClick={onCancel} variant="outline">
          Cancel
        </Button>
        <Button form="status-type-form" type="submit">
          {editPayload ? 'Update' : 'Create'}
        </Button>
      </DialogFooter>
    </Form>
  );
};
