import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@repo/ui/components/dialog';
import { useState } from 'react';
import type { StatusCategory } from '@/web/services/hooks.schemas';
import { useGetApiV1StatusCategories } from '@/web/services/status-categories';
import { StatusTypeForm } from './status-type-form';
import StatusTypesList from './status-type-list';

export const StatusTypes = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editPayload, setEditPayload] = useState<StatusCategory | null>(null);

  // Fetch status types
  const { data: statusTypes, isLoading, refetch } = useGetApiV1StatusCategories({});

  const handleEdit = (statusType: StatusCategory) => {
    setEditPayload(statusType);
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditPayload(null);
  };

  const handleFormSubmit = () => {
    setIsDialogOpen(false);
    setEditPayload(null);
    refetch(); // Refresh status type list
  };

  return (
    <div className="h-screen">
      <StatusTypesList
        isLoading={isLoading}
        onEdit={handleEdit}
        refetch={refetch}
        statusTypes={statusTypes?.data || []}
      />
      <Dialog onOpenChange={setIsDialogOpen} open={isDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{editPayload ? 'Edit Status Type' : 'Add Status Type'}</DialogTitle>
          </DialogHeader>
          <StatusTypeForm
            editPayload={editPayload}
            onCancel={handleCloseDialog}
            onSubmit={handleFormSubmit}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default StatusTypes;
