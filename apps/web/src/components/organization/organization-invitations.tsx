import { Button } from '@repo/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/ui/components/card';
import { Toaster } from '@repo/ui/components/toaster';
import { useToast } from '@repo/ui/components/use-toast';
import { useNavigate } from '@tanstack/react-router';
import { motion } from 'framer-motion';
import { Building2, Check, Loader2, Plus, Users, X } from 'lucide-react';
import { useState } from 'react';
import { useGetApiV1Invitations, usePatchApiV1InvitationsId } from '@/web/services/invitations';

export function OrganizationInvitations() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [processingId, setProcessingId] = useState<string | null>(null);

  // Fetch pending invitations
  const {
    data: invitations,
    isLoading,
    refetch,
  } = useGetApiV1Invitations({
    filters: JSON.stringify({ status: 'pending' }),
    sort: '-createdAt',
    limit: 200,
  });

  const updateInvitation = usePatchApiV1InvitationsId();

  const pendingInvitations = invitations?.data?.filter((inv) => inv.status === 'pending') || [];
  const hasInvitations = pendingInvitations.length > 0;

  const handleAcceptInvitation = async (invitationId: string) => {
    try {
      setProcessingId(invitationId);
      // Navigate to the invited route with the invitation token
      const invitation = pendingInvitations.find((inv) => inv.id === invitationId);
      if (invitation?.token) {
        navigate({ to: `/invited/${invitation.token}` });
      }
    } catch (_error) {
      toast({
        title: 'Error',
        description: 'Failed to process invitation. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setProcessingId(null);
    }
  };

  const handleRejectInvitation = async (invitationId: string) => {
    try {
      setProcessingId(invitationId);
      await updateInvitation.mutateAsync({
        id: invitationId,
        data: { status: 'rejected' },
      });

      toast({
        title: 'Invitation Rejected',
        description: 'You have rejected the invitation.',
      });

      // Refresh the invitations list
      refetch();
    } catch (_error) {
      toast({
        title: 'Error',
        description: 'Failed to reject invitation. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setProcessingId(null);
    }
  };

  const handleCreateOrganization = () => {
    navigate({ to: '/organization-create' });
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Card className="w-full max-w-2xl">
          <CardContent className="flex items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">Loading invitations...</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen items-center justify-center p-4">
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className="w-full max-w-2xl"
        initial={{ opacity: 0, y: 20 }}
        transition={{ duration: 0.5 }}
      >
        <Card>
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2 text-2xl">
              <Users className="h-6 w-6" />
              Welcome! Let's get you started
            </CardTitle>
            <CardDescription>
              {hasInvitations
                ? 'You have pending invitations to join organizations'
                : 'Create your first organization to get started'}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {hasInvitations ? (
              <>
                <div className="space-y-3">
                  {pendingInvitations.map((invitation, index) => (
                    <motion.div
                      animate={{ opacity: 1, x: 0 }}
                      initial={{ opacity: 0, x: -20 }}
                      key={invitation.id}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                    >
                      <Card className="border">
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start gap-3">
                              <Building2 className="mt-1 h-10 w-10 text-muted-foreground" />
                              <div className="space-y-1">
                                <h4 className="font-semibold text-lg">
                                  {invitation.organization?.name ||
                                    invitation.workspace?.name ||
                                    invitation.project?.name ||
                                    'Organization Invitation'}
                                </h4>
                                <p className="text-muted-foreground text-sm">
                                  Invited by{' '}
                                  <span className="font-medium">
                                    {invitation.inviter?.displayName || invitation.invitedBy}
                                  </span>
                                </p>
                                <p className="text-muted-foreground text-sm">
                                  Role:{' '}
                                  <span className="font-medium">
                                    {invitation.role?.name || 'Member'}
                                  </span>
                                </p>
                                {invitation.scopeType && (
                                  <p className="text-muted-foreground text-xs">
                                    Type: {invitation.scopeType}
                                  </p>
                                )}
                              </div>
                            </div>
                            <div className="flex gap-2">
                              <Button
                                disabled={processingId === invitation.id}
                                onClick={() => handleRejectInvitation(invitation.id)}
                                size="sm"
                                variant="outline"
                              >
                                {processingId === invitation.id ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <X className="h-4 w-4" />
                                )}
                                <span className="ml-1">Reject</span>
                              </Button>
                              <Button
                                disabled={processingId === invitation.id}
                                onClick={() => handleAcceptInvitation(invitation.id)}
                                size="sm"
                              >
                                {processingId === invitation.id ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <Check className="h-4 w-4" />
                                )}
                                <span className="ml-1">Accept</span>
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </div>

                <div className="relative my-6">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-background px-2 text-muted-foreground">Or</span>
                  </div>
                </div>
              </>
            ) : null}

            <div className="flex justify-center">
              <Button
                className="flex items-center gap-2"
                onClick={handleCreateOrganization}
                size="lg"
              >
                <Plus className="h-5 w-5" />
                Create Your Own Organization
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
      <Toaster />
    </div>
  );
}
