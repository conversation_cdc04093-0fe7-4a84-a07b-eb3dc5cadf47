import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@repo/ui/components/dialog';
import { useState } from 'react';
import type { Status } from '@/web/services/hooks.schemas';
import { useGetApiV1Statuses } from '@/web/services/statuses';
import { StatusForm } from './status-form';
import StatusList from './status-list';

const Statuses: React.FC = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editStatus, setEditStatus] = useState<Status | null>(null);

  // Fetch statuses
  const { data: statuses = { data: [] }, isLoading, refetch } = useGetApiV1Statuses({});

  const handleEdit = (status: Status) => {
    setEditStatus(status);
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditStatus(null);
  };

  const handleFormSubmit = () => {
    handleCloseDialog();
    refetch(); // Refresh status list
  };

  return (
    <div className="h-screen">
      <StatusList
        isLoading={isLoading}
        onEdit={handleEdit}
        refetch={refetch}
        statuses={statuses.data}
      />
      <Dialog onOpenChange={setIsDialogOpen} open={isDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{editStatus ? 'Edit Status' : 'Add Status'}</DialogTitle>
          </DialogHeader>
          <StatusForm
            editPayload={editStatus}
            onCancel={handleCloseDialog}
            onSubmit={handleFormSubmit}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Statuses;
