import { Button } from '@repo/ui/components/button';
import { Confirmation } from '@repo/ui/components/custom/confirmDialog';
import { DataTable } from '@repo/ui/components/data-table';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@repo/ui/components/dialog';
import type { ColumnDef } from '@tanstack/react-table';
import { Edit, Plus, Trash2 } from 'lucide-react';
import { useState } from 'react';
import type { Status } from '@/web/services/hooks.schemas';
import { useDeleteApiV1StatusesId } from '@/web/services/statuses';
import { StatusForm } from './status-form';

interface StatusListProps {
  statuses: Status[];
  isLoading: boolean;
  onEdit: (status: Status) => void;
  refetch: () => void;
}

const StatusList: React.FC<StatusListProps> = ({ statuses, isLoading, onEdit, refetch }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editStatus, setEditStatus] = useState<Status | undefined>(undefined);

  const deleteMutation = useDeleteApiV1StatusesId({
    mutation: {
      onSuccess: () => {
        refetch();
      },
      onError: (_error) => {},
    },
  });

  const columns: ColumnDef<Status>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'color',
      header: 'Color',
      cell: ({ row }) => (
        <div
          className="h-6 w-6 rounded-full"
          style={{ backgroundColor: row.getValue('color') || '#FFFFFF' }}
        />
      ),
    },
    {
      accessorKey: 'isActive',
      header: 'Active',
      cell: ({ row }) => (row.getValue('isActive') ? 'Yes' : 'No'),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const status = row.original;
        return (
          <div className="flex space-x-2">
            <Button onClick={() => onEdit(status)} size="sm" variant="ghost">
              <Edit className="h-4 w-4" />
            </Button>
            <Confirmation
              cancelButton={{
                name: 'Cancel',
                onClick: () => {},
                variant: 'ghost',
              }}
              confirmButton={{
                name: 'Delete',
                onClick: () => deleteMutation.mutate({ id: status.id }),
                variant: 'destructive',
              }}
              description={`Are you sure you want to delete ${status.name}?`}
              title="Are You Sure?"
            >
              <Button disabled={deleteMutation.isPending} size="sm" variant="ghost">
                <Trash2 className="h-4 w-4" />
              </Button>
            </Confirmation>
          </div>
        );
      },
    },
  ];

  const handleOpenDialog = (status?: Status) => {
    setEditStatus(status);
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditStatus(undefined);
  };

  const handleFormSubmit = () => {
    handleCloseDialog();
    refetch(); // Refresh status list
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <div className="font-semibold text-xl">Statuses</div>
          <p className="text-muted-foreground">Manage workflow statuses.</p>
        </div>
        <Button disabled={isLoading} onClick={() => handleOpenDialog()}>
          <Plus className="mr-2 h-4 w-4" />
          Add Status
        </Button>
      </div>

      <DataTable
        columns={columns}
        data={statuses}
        enableColumnVisibility={true}
        enableFiltering={true}
        enablePagination={false}
        enableRowSelection={false}
        enableSorting={true}
        searchKey="name"
        searchPlaceholder="Search statuses..."
      />

      <Dialog onOpenChange={setIsDialogOpen} open={isDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{editStatus ? 'Edit Status' : 'Add Status'}</DialogTitle>
          </DialogHeader>
          <StatusForm
            editPayload={editStatus}
            onCancel={handleCloseDialog}
            onSubmit={handleFormSubmit}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default StatusList;
