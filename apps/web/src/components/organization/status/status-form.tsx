import { insertStatusSchema, patchStatusSchema } from '@repo/db/schema';
import { Button } from '@repo/ui/components/button';
import { DialogFooter } from '@repo/ui/components/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { Switch } from '@repo/ui/components/switch';
import { Textarea } from '@repo/ui/components/textarea';
import { useForm } from '@tanstack/react-form';
import type {
  PatchApiV1StatusesIdBody,
  PostApiV1StatusesBody,
  Status,
  StatusStatusType,
} from '@/web/services/hooks.schemas';
import { useGetApiV1StatusCategories } from '@/web/services/status-categories';
import { usePatchApiV1StatusesId, usePostApiV1Statuses } from '@/web/services/statuses';
import { useRootStore } from '@/web/store/store';

interface StatusFormProps {
  onSubmit?: () => void;
  onCancel?: () => void;
  editPayload?: Status | null;
}

export const StatusForm: React.FC<StatusFormProps> = ({ onSubmit, onCancel, editPayload }) => {
  const currentOrganizationId = useRootStore((state) => state.currentOrganizationId);
  const { data: statusTypes } = useGetApiV1StatusCategories();

  const defaultValues: PatchApiV1StatusesIdBody | PostApiV1StatusesBody = editPayload || {
    name: '',
    description: '',
    organizationId: currentOrganizationId,
    color: '#000000',
    order: 0,
    isActive: true as boolean,
    statusCategoryId: '',
    statusType: 'todo',
  };

  const requiredFields = ['name', 'statusTypeId', 'organizationId'];

  const statusMutation = usePostApiV1Statuses({
    mutation: {
      onSuccess: () => {
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (_error) => {},
    },
  });

  const statusUpdateMutation = usePatchApiV1StatusesId({
    mutation: {
      onSuccess: () => {
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (_error) => {},
    },
  });
  const schemaForForm = editPayload ? patchStatusSchema : insertStatusSchema;
  const form = useForm({
    defaultValues,
    validators: {
      onSubmit: schemaForForm,
    },
    onSubmit: async ({ value }) => {
      if (editPayload) {
        await statusUpdateMutation.mutateAsync({
          data: value,
          id: editPayload.id,
        });
      } else {
        await statusMutation.mutateAsync({
          data: value as PostApiV1StatusesBody,
        });
      }
    },
  });

  return (
    <>
      <div className="overflow-auto">
        <Form
          className="flex flex-col gap-3 p-6"
          id="status-form"
          onSubmit={(e) => {
            e.preventDefault();
            form.handleSubmit();
          }}
        >
          <div className="grid gap-3">
            <form.Field name="name">
              {(field) => (
                <FormField field={field} required={requiredFields.includes('name')}>
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="Status name"
                        value={field.state.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>

            <form.Field name="description">
              {(field) => (
                <FormField field={field} required={requiredFields.includes('description')}>
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="Status description"
                        value={field.state.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>

            <form.Field name="statusCategoryId">
              {(field) => (
                <FormField field={field} required={requiredFields.includes('statusTypeId')}>
                  <FormItem>
                    <FormLabel>Status Type</FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={(value) => field.handleChange(value)}
                        value={field.state.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select a status type" />
                        </SelectTrigger>
                        <SelectContent>
                          {statusTypes?.data?.map((statusType) => (
                            <SelectItem key={statusType.id} value={statusType.id}>
                              {statusType.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>

            <form.Field name="organizationId">
              {(field) => (
                <FormField field={field} required={requiredFields.includes('organizationId')}>
                  <FormItem className="hidden">
                    <FormControl>
                      <Input
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        value={field.state.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>

            <form.Field name="color">
              {(field) => (
                <FormField field={field} required={requiredFields.includes('color')}>
                  <FormItem>
                    <FormLabel>Color</FormLabel>
                    <FormControl>
                      <Input
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        type="color"
                        value={field.state.value || '#000000'}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>

            <form.Field name="statusType">
              {(field) => (
                <FormField field={field} required={requiredFields.includes('statusType')}>
                  <FormItem>
                    <FormLabel>Status Type</FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={(value) => field.handleChange(value as StatusStatusType)}
                        value={field.state.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select a status type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="todo">Todo</SelectItem>
                          <SelectItem value="in-progress">In Progress</SelectItem>
                          <SelectItem value="done">Done</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>

            <form.Field name="order">
              {(field) => (
                <FormField field={field} required={requiredFields.includes('order')}>
                  <FormItem>
                    <FormLabel>Order</FormLabel>
                    <FormControl>
                      <Input
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(Number(e.target.value))}
                        placeholder="Order number"
                        type="number"
                        value={field.state.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>

            <form.Field name="isActive">
              {(field) => (
                <FormField field={field} required={requiredFields.includes('isActive')}>
                  <FormItem className="flex items-center space-x-2">
                    <FormLabel>Active</FormLabel>
                    <FormControl>
                      <Switch
                        checked={field.state.value}
                        onCheckedChange={(checked) => field.handleChange(checked)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>
          </div>
        </Form>
      </div>
      <DialogFooter className="">
        <Button onClick={onCancel} variant="outline">
          Cancel
        </Button>
        <Button form="status-form" type="submit">
          {editPayload ? 'Update' : 'Create'}
        </Button>
      </DialogFooter>
    </>
  );
};
