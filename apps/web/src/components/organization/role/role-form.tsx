import { insertRoleSchema, patchRoleSchema } from '@repo/db/schema';
import { Button } from '@repo/ui/components/button';
import { DialogFooter } from '@repo/ui/components/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { Switch } from '@repo/ui/components/switch';
import { Textarea } from '@repo/ui/components/textarea';
import { useForm } from '@tanstack/react-form';
import type {
  GetApiV1RolesId200Data,
  PatchApiV1RolesIdBody,
  PostApiV1RolesBody,
} from '@/web/services/hooks.schemas';
import { usePatchApiV1RolesId, usePostApiV1Roles } from '@/web/services/roles';
import { useRootStore } from '@/web/store/store';

interface RoleFormProps {
  onSubmit?: () => void;
  onCancel?: () => void;
  editPayload?: GetApiV1RolesId200Data | null;
}

export const RoleForm: React.FC<RoleFormProps> = ({ onSubmit, onCancel, editPayload }) => {
  const currentUser = useRootStore((state) => state.currentUser);

  // Assuming possible values for level; adjust based on your schema
  const roleLevels = ['organization', 'workspace', 'project', 'app'];

  const defaultValues: PatchApiV1RolesIdBody | PostApiV1RolesBody = editPayload || {
    name: '',
    identifier: '',
    description: '',
    level: '',
    isSystem: false,
    isActive: true,
    createdBy: currentUser?.id,
  };

  const requiredFields = ['name', 'identifier', 'level'];

  const roleMutation = usePostApiV1Roles({
    mutation: {
      onSuccess: () => {
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (_error) => {},
    },
  });

  const roleUpdateMutation = usePatchApiV1RolesId({
    mutation: {
      onSuccess: () => {
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (_error) => {},
    },
  });

  const schemaForForm = editPayload ? patchRoleSchema : insertRoleSchema;

  const form = useForm({
    defaultValues,
    validators: {
      onSubmit: schemaForForm,
    },
    onSubmit: async ({ value }) => {
      if (editPayload) {
        await roleUpdateMutation.mutateAsync({
          data: value,
          id: editPayload.id,
        });
      } else {
        await roleMutation.mutateAsync({
          data: value as PostApiV1RolesBody,
        });
      }
    },
  });

  return (
    <>
      <div className="overflow-auto">
        <Form
          className="flex flex-col gap-y-3 p-6"
          id="role-form"
          onSubmit={(e) => {
            e.preventDefault();
            form.handleSubmit();
          }}
        >
          <div className="grid gap-3">
            <form.Field name="name">
              {(field) => (
                <FormField field={field} required={requiredFields.includes('name')}>
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="Role name"
                        value={field.state.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>

            <form.Field name="identifier">
              {(field) => (
                <FormField field={field} required={requiredFields.includes('identifier')}>
                  <FormItem>
                    <FormLabel>Identifier</FormLabel>
                    <FormControl>
                      <Input
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="Unique identifier"
                        value={field.state.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>

            <form.Field name="description">
              {(field) => (
                <FormField field={field} required={requiredFields.includes('description')}>
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="Role description"
                        value={field.state.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>

            <form.Field name="level">
              {(field) => (
                <FormField field={field} required={requiredFields.includes('level')}>
                  <FormItem>
                    <FormLabel>Level</FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={(value) => field.handleChange(value)}
                        value={field.state.value}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select a level" />
                        </SelectTrigger>
                        <SelectContent>
                          {roleLevels
                            .filter((l) => l !== 'app')
                            .map((level) => (
                              <SelectItem key={level} value={level}>
                                {level.charAt(0).toUpperCase() + level.slice(1)}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>

            <form.Field name="isActive">
              {(field) => (
                <FormField field={field} required={requiredFields.includes('isActive')}>
                  <FormItem className="flex items-center space-x-2">
                    <FormLabel>Active</FormLabel>
                    <FormControl>
                      <Switch
                        checked={field.state.value}
                        onCheckedChange={(checked) => field.handleChange(checked)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              )}
            </form.Field>
          </div>
        </Form>
      </div>
      <DialogFooter className="">
        <Button onClick={onCancel} variant="outline">
          Cancel
        </Button>
        <Button form="role-form" type="submit">
          {editPayload ? 'Update' : 'Create'}
        </Button>
      </DialogFooter>
    </>
  );
};
