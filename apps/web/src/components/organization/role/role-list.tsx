import { Button } from '@repo/ui/components/button';
import { Confirmation } from '@repo/ui/components/custom/confirmDialog';
import { DataTable } from '@repo/ui/components/data-table';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@repo/ui/components/dialog';
import type { ColumnDef } from '@tanstack/react-table';
import { Edit, Plus, Trash2 } from 'lucide-react';
import { useState } from 'react';
import type { Role } from '@/web/services/hooks.schemas';
import { deleteApiV1RolesId, useGetApiV1Roles } from '@/web/services/roles';
import { RoleForm } from './role-form';

export default function RoleList() {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editPayload, setEditPayload] = useState<null | Role>(null);

  const { data, isLoading, refetch } = useGetApiV1Roles({});

  const columns: ColumnDef<Role>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'description',
      header: 'Description',
    },
    {
      accessorKey: 'level',
      header: 'Level',
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const role = row.original;
        return (
          <div className="flex space-x-2">
            <Button
              onClick={() => {
                setIsAddDialogOpen(true);
                setEditPayload(role);
              }}
              size="sm"
              variant="ghost"
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Confirmation
              cancelButton={{
                name: 'Cancel',
                onClick: () => {},
                variant: 'secondary',
              }}
              confirmButton={{
                name: 'Delete',
                size: 'sm',
                onClick: async () => {
                  await deleteApiV1RolesId(role.id);
                  refetch();
                },
                variant: 'destructive',
              }}
              description="This action cannot be undone."
              title="Are you sure?"
            >
              <Button size="sm" variant="ghost">
                <Trash2 className="h-4 w-4" />
              </Button>
            </Confirmation>
          </div>
        );
      },
    },
  ];

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <div className="font-semibold text-xl">Roles</div>
          <p className="text-muted-foreground">Manage user roles and permissions.</p>
        </div>
        <Button disabled={isLoading} onClick={() => setIsAddDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Role
        </Button>
      </div>

      <DataTable
        columns={columns}
        data={data?.data || []}
        enableColumnVisibility={true}
        enableFiltering={true}
        enablePagination={false}
        enableRowSelection={false}
        enableSorting={true}
        searchKey="name"
        searchPlaceholder="Search roles..."
      />

      {isAddDialogOpen && (
        <Dialog
          onOpenChange={() => {
            setIsAddDialogOpen(false);
            setEditPayload(null);
          }}
          open={isAddDialogOpen}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create Role</DialogTitle>
            </DialogHeader>
            <RoleForm
              editPayload={editPayload}
              onCancel={() => setIsAddDialogOpen(false)}
              onSubmit={() => {
                setIsAddDialogOpen(false);
                refetch();
                setEditPayload(null);
              }}
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
