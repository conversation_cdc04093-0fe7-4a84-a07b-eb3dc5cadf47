'use client';

import { insertWorkItemTypeSchema, patchWorkItemTypeSchema } from '@repo/db/schema';
import { Button } from '@repo/ui/components/button';
import { DialogFooter } from '@repo/ui/components/dialog';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { Switch } from '@repo/ui/components/switch';
import { Textarea } from '@repo/ui/components/textarea';
import { cn } from '@repo/ui/lib/utils';
import { useForm } from '@tanstack/react-form';
import type {
  PatchApiV1WorkItemTypesIdBody,
  PostApiV1WorkItemTypesBody,
  WorkItemType,
} from '@/web/services/hooks.schemas';
import {
  usePatchApiV1WorkItemTypesId,
  usePostApiV1WorkItemTypes,
} from '@/web/services/work-item-types';
import { useRootStore } from '@/web/store/store';
import { renderIcon, workItemIcons } from '../work-item-icons';

interface WorkItemTypeFormProps {
  onSubmit?: () => void;
  onCancel?: () => void;
  editPayload?: WorkItemType | null;
}

export const WorkItemTypeForm: React.FC<WorkItemTypeFormProps> = ({
  onSubmit,
  onCancel,
  editPayload,
}) => {
  const currentOrganizationId = useRootStore((state) => state.currentOrganizationId);

  // Normalize defaultValues to ensure valid initialization
  const defaultValues: PostApiV1WorkItemTypesBody | PatchApiV1WorkItemTypesIdBody = editPayload || {
    name: '',
    description: null,
    icon: null,
    color: '#000000',
    organizationId: currentOrganizationId || '',
    isSystem: false,
    isActive: true,
  };

  const schemaForForm = editPayload ? patchWorkItemTypeSchema : insertWorkItemTypeSchema;
  const form = useForm({
    defaultValues,
    validators: {
      onSubmit: schemaForForm,
    },
    onSubmit: async ({ value }) => {
      if (editPayload?.id) {
        // Update existing work item type
        await updateMutation.mutateAsync({
          id: editPayload.id,
          data: value,
        });
      } else {
        // Create new work item type
        await createMutation.mutateAsync({
          data: value as PostApiV1WorkItemTypesBody,
        });
      }
      if (onSubmit) {
        onSubmit();
      }
    },
  });

  // Mutation for creating a new work item type
  const createMutation = usePostApiV1WorkItemTypes({
    mutation: {
      onSuccess: (_data) => {
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (_error) => {},
    },
  });

  // Mutation for updating an existing work item type
  const updateMutation = usePatchApiV1WorkItemTypesId({
    mutation: {
      onSuccess: (_data) => {
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (_error) => {},
    },
  });

  const requiredFields = ['name', 'organizationId', 'category'];

  return (
    <div className="overflow-auto">
      <form
        className="space-y-3 p-3"
        id="work-item-type-form"
        onSubmit={(e) => {
          e.preventDefault();
          form.handleSubmit();
        }}
      >
        <form.Field name="name">
          {(field) => (
            <FormField field={field} required={requiredFields.includes('name')}>
              <FormItem>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input
                    className={cn(field.state.meta.errors[0] && 'border-destructive')}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Work Item Type Name"
                    value={(field.state.value as string) ?? ''}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>

        <form.Field name="description">
          {(field) => (
            <FormField field={field} required={requiredFields.includes('description')}>
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea
                    className={cn(field.state.meta.errors[0] && 'border-destructive')}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value || null)}
                    placeholder="Work item type description"
                    value={(field.state.value as string | null) ?? ''}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>

        <form.Field name="icon">
          {(field) => (
            <FormField field={field} required={requiredFields.includes('icon')}>
              <FormItem>
                <FormLabel>Icon</FormLabel>
                <FormControl>
                  <div className="flex items-center space-x-2">
                    <Select
                      onValueChange={(value) => field.handleChange(value || null)}
                      value={field.state.value as string}
                    >
                      <SelectTrigger className="w-[200px]">
                        <SelectValue placeholder="Select an icon">
                          {field.state.value
                            ? workItemIcons.find((icon) => icon.name === field.state.value)?.label
                            : 'Select an icon'}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        {workItemIcons.map((icon) => (
                          <SelectItem key={icon.name} value={icon.name}>
                            <div className="flex items-center space-x-2">
                              <icon.component className="h-5 w-5" />
                              <span>{icon.label}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <form.Subscribe
                      children={(state) =>
                        renderIcon(field.state.value || null, state.values.color || null)
                      }
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>

        <form.Field name="color">
          {(field) => (
            <FormField field={field} required={requiredFields.includes('color')}>
              <FormItem>
                <FormLabel>Color</FormLabel>
                <FormControl>
                  <Input
                    className={cn(field.state.meta.errors[0] && 'border-destructive')}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    type="color"
                    value={(field.state.value as string) ?? '#000000'}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>

        <form.Field name="organizationId">
          {(field) => (
            <FormField field={field} required={requiredFields.includes('organizationId')}>
              <FormItem className="hidden">
                <FormControl>
                  <Input
                    className={cn(field.state.meta.errors[0] && 'border-destructive')}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    value={(field.state.value as string) ?? ''}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>

        <form.Field name="isSystem">
          {(field) => (
            <FormField field={field} required={requiredFields.includes('isSystem')}>
              <FormItem className="flex items-center space-x-2">
                <FormLabel>Is System</FormLabel>
                <FormControl>
                  <Switch
                    checked={field.state.value ?? false}
                    onCheckedChange={(checked) => field.handleChange(checked)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>

        <form.Field name="isActive">
          {(field) => (
            <FormField field={field} required={requiredFields.includes('isActive')}>
              <FormItem className="flex items-center space-x-2">
                <FormLabel>Is Active</FormLabel>
                <FormControl>
                  <Switch
                    checked={field.state.value ?? true}
                    onCheckedChange={(checked) => field.handleChange(checked)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          )}
        </form.Field>
      </form>

      <DialogFooter>
        <Button
          disabled={createMutation.isPending || updateMutation.isPending}
          onClick={onCancel}
          type="button"
          variant="outline"
        >
          Cancel
        </Button>
        <Button
          disabled={createMutation.isPending || updateMutation.isPending}
          form="work-item-type-form"
          type="submit"
        >
          {createMutation.isPending || updateMutation.isPending
            ? 'Saving...'
            : editPayload
              ? 'Update'
              : 'Create'}
        </Button>
      </DialogFooter>
    </div>
  );
};
