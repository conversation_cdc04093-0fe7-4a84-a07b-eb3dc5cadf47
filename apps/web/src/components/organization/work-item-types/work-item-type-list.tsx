import { Button } from '@repo/ui/components/button';
import { Confirmation } from '@repo/ui/components/custom/confirmDialog';
import { DataTable } from '@repo/ui/components/data-table';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@repo/ui/components/dialog';
import type { ColumnDef } from '@tanstack/react-table';
import { Edit, Plus, Trash2 } from 'lucide-react';
import { useState } from 'react';
import type { WorkItemType } from '@/web/services/hooks.schemas';
import {
  useDeleteApiV1WorkItemTypesId,
  useGetApiV1WorkItemTypes,
} from '@/web/services/work-item-types';
import { WorkItemTypeForm } from './work-item-type-form';

// Define the type for params in renderCell and valueFormatter

const WorkItemTypes = () => {
  const { data: workItemTypes, refetch: refetchWorkItemTypes } = useGetApiV1WorkItemTypes();
  const [openForm, setOpenForm] = useState(false);
  const [editPayload, setEditPayload] = useState<WorkItemType | null>(null);

  const deleteMutation = useDeleteApiV1WorkItemTypesId({
    mutation: {
      onSuccess: () => {
        refetchWorkItemTypes();
      },
      onError: (_error) => {},
    },
  });

  const handleAdd = () => {
    setEditPayload(null); // Clear edit payload for create mode
    setOpenForm(true);
  };

  const handleEdit = (workItemType: WorkItemType) => {
    setEditPayload(workItemType);
    setOpenForm(true);
  };

  const handleSubmit = () => {
    setOpenForm(false); // Close form on successful submit
    setEditPayload(null); // Clear edit payload
    refetchWorkItemTypes();
  };

  const handleCancel = () => {
    setOpenForm(false); // Close form on cancel
    setEditPayload(null); // Clear edit payload
  };

  const columns: ColumnDef<WorkItemType>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'description',
      header: 'Description',
    },
    {
      accessorKey: 'isSystem',
      header: 'Is System',
      cell: ({ row }) => (row.getValue('isSystem') ? 'Yes' : 'No'),
    },
    {
      accessorKey: 'isActive',
      header: 'Is Active',
      cell: ({ row }) => (row.getValue('isActive') ? 'Yes' : 'No'),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const workItemType = row.original;
        return (
          <div className="flex space-x-2">
            <Button onClick={() => handleEdit(workItemType)} size="sm" variant="ghost">
              <Edit className="h-4 w-4" />
            </Button>
            <Confirmation
              cancelButton={{
                name: 'Cancel',
                onClick: () => {},
                variant: 'secondary',
              }}
              confirmButton={{
                name: 'Delete',
                onClick: () => deleteMutation.mutate({ id: workItemType.id }),
                variant: 'destructive',
              }}
              description={`Are you sure you want to delete ${workItemType.name}?`}
              title="Are you sure?"
            >
              <Button disabled={deleteMutation.isPending} size="sm" variant="ghost">
                <Trash2 className="h-4 w-4" />
              </Button>
            </Confirmation>
          </div>
        );
      },
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <div className="font-semibold text-xl">Work Item Types</div>
          <p className="text-muted-foreground">Manage different types of work items.</p>
        </div>
        <Button onClick={handleAdd}>
          <Plus className="mr-2 h-4 w-4" />
          Add Work Item Type
        </Button>
      </div>

      <DataTable
        columns={columns}
        data={workItemTypes?.data || []}
        enableColumnVisibility={true}
        enableFiltering={true}
        enablePagination={false}
        enableRowSelection={false}
        enableSorting={true}
        searchKey="name"
        searchPlaceholder="Search work item types..."
      />

      <Dialog onOpenChange={setOpenForm} open={openForm}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{editPayload ? 'Edit Work Item Type' : 'Add Work Item Type'}</DialogTitle>
          </DialogHeader>
          <WorkItemTypeForm
            editPayload={editPayload}
            onCancel={handleCancel}
            onSubmit={handleSubmit}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default WorkItemTypes;
