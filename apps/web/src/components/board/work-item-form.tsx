import { insertWorkItemSchema, patchWorkItemSchema } from '@repo/db/schema';
import { Button } from '@repo/ui/components/button';
import { DialogFooter } from '@repo/ui/components/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { Switch } from '@repo/ui/components/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@repo/ui/components/tabs';
import { RichTextEditor } from '@repo/ui/components/tiptap/rich-text-editor-controlled';
import { useForm } from '@tanstack/react-form';
import { useQueryClient } from '@tanstack/react-query';
import { isEqual } from 'lodash';
import { useRef, useState } from 'react';
import { z } from 'zod';
import type {
  PatchApiV1WorkItemsIdBody,
  PostApiV1WorkItemsBody,
  Priority,
  Sprint,
  Status,
  User,
  WorkItem,
  WorkItemType,
} from '@/web/services/hooks.schemas';
import { useGetApiV1Users } from '@/web/services/users';
import { getGetApiV1WorkItemHistoryQueryKey } from '@/web/services/work-item-history';
import {
  getGetApiV1WorkItemsQueryKey,
  usePatchApiV1WorkItemsId,
  usePostApiV1WorkItems,
} from '@/web/services/work-items';
import { useRootStore } from '@/web/store/store';
import {
  type PendingFile,
  WorkItemAttachments,
  type WorkItemAttachmentsRef,
} from './work-item-attachments';
import { WorkItemComments } from './work-item-comments';
import { WorkItemHistory } from './work-item-history';
import { WorkItemLinks } from './work-item-links';

interface WorkItemFormProps {
  onSubmit?: () => void;
  onCancel?: () => void;
  editPayload?: null | WorkItem;
  workItemTypeId: string;
  priorities: Priority[];
  statuses: Status[];
  sprints: Sprint[];
  workItemTypes: WorkItemType[];
}

export const WorkItemForm: React.FC<WorkItemFormProps> = ({
  onSubmit,
  onCancel,
  editPayload,
  workItemTypeId,
  priorities = [],
  statuses = [],
  sprints = [],
  workItemTypes = [],
}) => {
  const queryClient = useQueryClient();
  const currentUser = useRootStore((state) => state.currentUser);
  const currentProjectId = useRootStore((state) => state.currentProjectId);
  const currentProjectWorkflowTransitions = useRootStore(
    (state) => state.currentProjectWorkflowTransitions,
  );

  const { data: usersData } = useGetApiV1Users();
  const [pendingFiles, setPendingFiles] = useState<PendingFile[]>([]);
  const [pendingDeletions, setPendingDeletions] = useState<string[]>([]);
  const attachmentsRef = useRef<WorkItemAttachmentsRef>(null);

  // Find the initial status from workflow transitions
  const initialStatus =
    currentProjectWorkflowTransitions.find((transition) => transition.isInitial)?.toStatus?.status
      ?.id || null;

  // Function to check if a status transition is allowed
  const isStatusAllowed = (fromStatusId: string | null, toStatusId: string) => {
    if (!fromStatusId) {
      return true; // Allow all statuses for new work items
    }
    const hasDirectTransition = currentProjectWorkflowTransitions.some(
      (transition) =>
        transition.fromStatus?.status?.id === fromStatusId &&
        transition.toStatus?.status?.id === toStatusId,
    );
    const hasGlobalTransition = currentProjectWorkflowTransitions.some(
      (transition) =>
        transition.fromStatus?.status?.id === null &&
        transition.toStatus?.status?.id === toStatusId,
    );
    return hasDirectTransition || hasGlobalTransition;
  };

  const defaultValues = editPayload || {
    projectId: currentProjectId,
    typeId: workItemTypeId,
    title: '',
    description: '',
    statusId: initialStatus, // Set default status for new work items
    priorityId: null,
    assigneeId: null,
    reporterId: null,
    createdBy: currentUser?.id || '',
    sprintId: null,
    initialSprintId: null,
    parentId: null,
    tags: {},
    estimate: {},
    dates: {},
    links: {},
    isActive: true,
  };

  const workItemMutation = usePostApiV1WorkItems();
  const workItemUpdateMutation = usePatchApiV1WorkItemsId();

  const requiredFields = ['title', 'projectId', 'typeId', 'statusId', 'priorityId'];

  const schema = editPayload ? patchWorkItemSchema : insertWorkItemSchema;
  const initialValuesRef = useRef(editPayload || defaultValues);
  const form = useForm({
    defaultValues: (editPayload as PatchApiV1WorkItemsIdBody) || defaultValues,
    validators: {
      onSubmit: schema,
    },
    onSubmit: async ({ value }) => {
      try {
        let workItemId: string;

        if (editPayload) {
          await workItemUpdateMutation.mutateAsync({
            id: editPayload.id,
            data: value as PatchApiV1WorkItemsIdBody,
          });
          workItemId = editPayload.id;
        } else {
          const result = await workItemMutation.mutateAsync({
            data: value as PostApiV1WorkItemsBody,
          });
          workItemId = result.data.id;
        }

        // Handle pending attachments and deletions after work item is saved
        if (attachmentsRef.current) {
          // Upload new files
          if (pendingFiles.length > 0) {
            await attachmentsRef.current.uploadFiles(workItemId);
          }

          // Delete marked attachments
          if (pendingDeletions.length > 0) {
            await attachmentsRef.current.deleteAttachments();
          }
        }

        // Invalidate work item history queries to fetch the latest history
        await queryClient.invalidateQueries({
          queryKey: getGetApiV1WorkItemHistoryQueryKey(),
        });

        // Also invalidate the work item query to ensure data consistency
        await queryClient.invalidateQueries({
          queryKey: getGetApiV1WorkItemsQueryKey(),
        });

        if (onSubmit) {
          onSubmit();
        }
      } catch (error) {
        if (error instanceof z.ZodError) {
          return;
        }
        throw error;
      }
    },
  });

  return (
    <>
      <div className="flex flex-1 flex-col overflow-hidden">
        <Form
          className="flex flex-1 flex-col overflow-hidden"
          id="work-item-form"
          onSubmit={(e) => {
            e.preventDefault();
            form.handleSubmit();
          }}
        >
          {/* Main content container */}
          <div className="flex flex-1 overflow-hidden">
            {/* Left side - Main content area */}
            <div className="flex w-full flex-1 flex-col overflow-auto">
              {/* Details section - Always visible */}
              <div className="border-b p-4">
                <h3 className="mb-4 font-semibold text-lg">
                  Work Item Details{' '}
                  <span className="rounded bg-muted px-2 py-0.5 font-mono text-muted-foreground text-xs">
                    {editPayload ? `#${editPayload.ticketId}` : ''}
                  </span>
                </h3>
                <div className="flex-col space-y-3">
                  <form.Field name="title">
                    {(field) => (
                      <FormField field={field} required={requiredFields.includes('title')}>
                        <FormItem>
                          <FormLabel>Title</FormLabel>
                          <FormControl>
                            <Input
                              onBlur={field.handleBlur}
                              onChange={(e) => field.handleChange(e.target.value)}
                              placeholder="Work Item Title"
                              value={field.state.value}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      </FormField>
                    )}
                  </form.Field>
                  <form.Field name="description">
                    {(field) => (
                      <FormField field={field} required={requiredFields.includes('description')}>
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <div className="h-100 max-w-full">
                              <RichTextEditor
                                className="rounded-md"
                                onChange={(value) => field.handleChange(value)}
                                value={field.state.value || ''}
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      </FormField>
                    )}
                  </form.Field>
                </div>
              </div>

              {/* Tabbed content area - Takes remaining space */}
              <div className="flex-1">
                <Tabs className="flex h-full flex-col" defaultValue="comments">
                  <div className="bg-muted px-4 pt-2">
                    <TabsList>
                      <TabsTrigger value="comments">Comments</TabsTrigger>
                      <TabsTrigger value="history">History</TabsTrigger>
                      <TabsTrigger value="link">Links</TabsTrigger>
                      <TabsTrigger value="attachments">Attachments</TabsTrigger>
                    </TabsList>
                  </div>

                  <div className="flex-1 overflow-hidden">
                    <TabsContent className="h-full overflow-auto p-4" value="comments">
                      {editPayload?.id ? (
                        <WorkItemComments workItemId={editPayload.id} />
                      ) : (
                        <div className="flex h-full items-center justify-center">
                          <p className="text-center text-muted-foreground text-sm">
                            Save the work item to add comments
                          </p>
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent className="h-full overflow-hidden" value="history">
                      {editPayload?.id ? (
                        <WorkItemHistory workItemId={editPayload.id} />
                      ) : (
                        <div className="flex h-full items-center justify-center">
                          <p className="text-center text-muted-foreground text-sm">
                            Save the work item to view history
                          </p>
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent className="h-full overflow-auto p-4" value="link">
                      {editPayload?.id ? (
                        <WorkItemLinks workItem={editPayload} workItemId={editPayload.id} />
                      ) : (
                        <div className="flex h-full items-center justify-center">
                          <p className="text-center text-muted-foreground text-sm">
                            Save the work item to add links
                          </p>
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent className="h-full overflow-auto p-4" value="attachments">
                      <WorkItemAttachments
                        initialPendingDeletions={pendingDeletions}
                        initialPendingFiles={pendingFiles}
                        isEditing={true}
                        onPendingDeletionsChange={setPendingDeletions}
                        onPendingFilesChange={setPendingFiles}
                        ref={attachmentsRef}
                        workItemId={editPayload?.id}
                      />
                    </TabsContent>
                  </div>
                </Tabs>
              </div>
            </div>

            {/* Right sidebar - Properties */}
            <div className="w-80 overflow-auto border-l bg-muted p-4">
              <div className="space-y-4">
                <h3 className="mb-4 font-semibold text-lg">Properties</h3>

                <form.Field name="typeId">
                  {(field) => (
                    <FormField field={field} required={requiredFields.includes('typeId')}>
                      <FormItem>
                        <FormLabel>Type</FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={(value) => field.handleChange(value)}
                            value={field.state.value}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select Type" />
                            </SelectTrigger>
                            <SelectContent>
                              {workItemTypes.map((type) => (
                                <SelectItem key={type.id} value={type.id}>
                                  {type.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>
                  )}
                </form.Field>

                <form.Field name="statusId">
                  {(field) => (
                    <FormField field={field} required={requiredFields.includes('statusId')}>
                      <FormItem>
                        <FormLabel>Status</FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={(value) => field.handleChange(value)}
                            value={field.state.value}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select Status" />
                            </SelectTrigger>
                            <SelectContent>
                              {statuses.map((status) => (
                                <SelectItem
                                  disabled={
                                    !isStatusAllowed(editPayload?.statusId || '', status.id) &&
                                    status.id !== editPayload?.statusId
                                  }
                                  key={status.id}
                                  value={status.id}
                                >
                                  {status.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>
                  )}
                </form.Field>

                <form.Field name="priorityId">
                  {(field) => (
                    <FormField field={field} required={requiredFields.includes('priorityId')}>
                      <FormItem>
                        <FormLabel>Priority</FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={(value) => field.handleChange(value)}
                            value={field.state.value || ''}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select priority" />
                            </SelectTrigger>
                            <SelectContent>
                              {priorities.map((priority) => (
                                <SelectItem key={priority.id} value={priority.id}>
                                  {priority.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>
                  )}
                </form.Field>

                <form.Field name="assigneeId">
                  {(field) => (
                    <FormField field={field} required={requiredFields.includes('assigneeId')}>
                      <FormItem>
                        <FormLabel>Assigned To</FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={(value) => field.handleChange(value)}
                            value={field.state.value || ''}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select assignee" />
                            </SelectTrigger>
                            <SelectContent>
                              {usersData?.data?.map((user: User) => (
                                <SelectItem key={user.id} value={user.id}>
                                  {user.firstName} {user.lastName}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>
                  )}
                </form.Field>

                <form.Field name="sprintId">
                  {(field) => (
                    <FormField field={field} required={requiredFields.includes('sprintId')}>
                      <FormItem>
                        <FormLabel>Sprint</FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={(value) => field.handleChange(value)}
                            value={field.state.value || ''}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select sprint" />
                            </SelectTrigger>
                            <SelectContent>
                              {sprints.map((sprint) => (
                                <SelectItem key={sprint.id} value={sprint.id}>
                                  {sprint.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>
                  )}
                </form.Field>

                <form.Field name="isActive">
                  {(field) => (
                    <FormField field={field}>
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <Switch
                            checked={!!field.state.value}
                            onCheckedChange={(checked) => field.handleChange(checked)}
                          />
                        </FormControl>
                        <FormLabel>Active</FormLabel>
                        <FormMessage />
                      </FormItem>
                    </FormField>
                  )}
                </form.Field>
              </div>
            </div>
          </div>
        </Form>
      </div>

      {/* Footer - Outside the main content, always visible */}
      <DialogFooter className="sticky bottom-0 border-t bg-background">
        <Button
          disabled={workItemMutation.isPending}
          onClick={onCancel}
          type="button"
          variant="outline"
        >
          Cancel
        </Button>
        <form.Subscribe
          children={(state) => {
            const values = state.values;
            const hasChanges = editPayload ? !isEqual(values, initialValuesRef.current) : true;
            const hasPendingFiles = pendingFiles.length > 0;
            const hasPendingDeletions = pendingDeletions.length > 0;
            const isDisabled =
              workItemUpdateMutation.isPending ||
              workItemMutation.isPending ||
              (!!editPayload && !hasChanges && !hasPendingFiles && !hasPendingDeletions);

            return (
              <Button disabled={isDisabled} form="work-item-form" type="submit">
                {workItemMutation.isPending || workItemUpdateMutation.isPending
                  ? 'Saving...'
                  : editPayload
                    ? hasPendingFiles && !hasChanges
                      ? 'Upload Attachments'
                      : 'Update'
                    : 'Create'}
              </Button>
            );
          }}
        />
      </DialogFooter>
    </>
  );
};
