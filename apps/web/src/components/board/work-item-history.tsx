'use client';

import { Badge } from '@repo/ui/components/badge';
import { ScrollArea } from '@repo/ui/components/scroll-area';
import { Skeleton } from '@repo/ui/components/skeleton';
import { cn } from '@repo/ui/lib/utils';
import { format, formatDistanceToNow } from 'date-fns';
import { ArrowRight, CheckCircle2, Circle, Edit3, History, Plus, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { useWorkItemLookups } from '@/web/hooks/use-work-item-lookups';
import type { WorkItemHistory as WorkItemHistoryType } from '@/web/services/hooks.schemas';
import { useGetApiV1WorkItemHistory } from '@/web/services/work-item-history';
import { sanitizeHtml } from '@/web/utils/sanitize-html';

// Type definition for metadata to avoid TypeScript warnings
interface WorkItemHistoryMetadata {
  comment?: {
    id: string;
    content: string;
  };
  attachment?: {
    id: string;
    filename: string;
    size?: number;
    mimeType?: string;
  };
  [key: string]: unknown;
}

interface WorkItemHistoryProps {
  workItemId: string;
}

export function WorkItemHistory({ workItemId }: WorkItemHistoryProps) {
  const {
    data: response,
    isLoading,
    error,
  } = useGetApiV1WorkItemHistory(
    {
      filters: JSON.stringify({ workItemId: { $eq: workItemId } }),
      sort: JSON.stringify([{ field: 'createdAt', order: 'desc' }]),
      limit: 100,
    },
    {
      query: {
        enabled: !!workItemId,
      },
    },
  );

  const historyData = response?.data || [];
  const [showRelativeTime, setShowRelativeTime] = useState(true);
  const { formatFieldValue, isLoading: lookupsLoading } = useWorkItemLookups();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    if (showRelativeTime) {
      return formatDistanceToNow(date, { addSuffix: true });
    }
    return format(date, "MMM d, yyyy 'at' h:mm a");
  };

  const getChangeIcon = (changeType: string) => {
    const iconClass = 'h-4 w-4';
    switch (changeType) {
      case 'CREATE':
        return <Plus className={cn(iconClass, 'text-success')} />;
      case 'UPDATE':
        return <Edit3 className={cn(iconClass, 'text-primary')} />;
      case 'DELETE':
        return <Trash2 className={cn(iconClass, 'text-destructive')} />;
      case 'RESTORE':
        return <CheckCircle2 className={cn(iconClass, 'text-success')} />;
      case 'COMMENT_ADDED':
        return <Plus className={cn(iconClass, 'text-primary')} />;
      case 'COMMENT_UPDATED':
        return <Edit3 className={cn(iconClass, 'text-warning')} />;
      case 'COMMENT_DELETED':
        return <Trash2 className={cn(iconClass, 'text-destructive')} />;
      case 'ATTACHMENT_ADDED':
        return (
          <Plus className={cn(iconClass, 'text-muted-foreground dark:text-muted-foreground')} />
        );
      case 'ATTACHMENT_DELETED':
        return <Trash2 className={cn(iconClass, 'text-accent')} />;
      default:
        return <Circle className={cn(iconClass, 'text-muted-foreground')} />;
    }
  };

  const formatHistoryChange = (history: WorkItemHistoryType) => {
    const { changeType, summary, changedFields, metadata } = history;

    switch (changeType) {
      case 'CREATE':
        return <span>created this work item</span>;

      case 'UPDATE':
        // Use the summary if available, otherwise show generic message
        if (summary) {
          return <span>{summary.toLowerCase()}</span>;
        }

        // Show detailed changes if changedFields is available
        if (changedFields && Object.keys(changedFields).length > 0) {
          const fieldCount = Object.keys(changedFields).length;
          if (fieldCount === 1) {
            const fieldName = Object.keys(changedFields)[0];
            const fieldLabel = fieldName
              .replace(/Id$/, '')
              .replace(/([A-Z])/g, ' $1')
              .toLowerCase();
            return <span>updated {fieldLabel}</span>;
          }
          return <span>updated {fieldCount} fields</span>;
        }

        return <span>made changes</span>;

      case 'COMMENT_ADDED':
        return <span>added a comment</span>;

      case 'COMMENT_UPDATED':
        return <span>updated a comment</span>;

      case 'COMMENT_DELETED':
        return <span>deleted a comment</span>;

      case 'ATTACHMENT_ADDED': {
        const typedMetadata = metadata as WorkItemHistoryMetadata | null;
        const attachmentName = typedMetadata?.attachment?.filename || 'a file';
        return <span>added attachment "{attachmentName}"</span>;
      }

      case 'ATTACHMENT_DELETED': {
        const typedMetadata = metadata as WorkItemHistoryMetadata | null;
        const deletedAttachmentName = typedMetadata?.attachment?.filename || 'a file';
        return <span>deleted attachment "{deletedAttachmentName}"</span>;
      }

      case 'DELETE':
        return <span>deleted this work item</span>;

      case 'RESTORE':
        return <span>restored this work item</span>;

      default:
        return <span>{summary || 'made changes'}</span>;
    }
  };

  const renderFieldDetails = (history: WorkItemHistoryType) => {
    const { changeType, changedFields, metadata } = history;

    // Show changed fields details for UPDATE changes
    if (changeType === 'UPDATE' && changedFields) {
      return (
        <div className="mt-2 space-y-2 text-sm">
          {Object.entries(changedFields).map(([fieldName, change]) => {
            // Special handling for title changes
            if (fieldName === 'title' && change.oldValue && change.newValue) {
              return (
                <div className="space-y-1" key={fieldName}>
                  <div className="font-medium text-muted-foreground">Title</div>
                  <div className="flex items-start gap-2">
                    <ArrowRight className="mt-0.5 h-3 w-3 flex-shrink-0 text-muted-foreground" />
                    <div className="flex-1 space-y-1">
                      <p className="break-words text-muted-foreground line-through">
                        {change.oldValue}
                      </p>
                      <p className="break-words text-foreground">{change.newValue}</p>
                    </div>
                  </div>
                </div>
              );
            }

            // discription
            if (fieldName === 'description' && change.oldValue && change.newValue) {
              return (
                <div className="space-y-1" key={fieldName}>
                  <div className="font-medium text-muted-foreground">Title</div>
                  <div className="flex items-start gap-2">
                    <ArrowRight className="mt-0.5 h-3 w-3 flex-shrink-0 text-muted-foreground" />
                    <div className="flex-1 space-y-1">
                      <div
                        className="break-words text-muted-foreground line-through"
                        dangerouslySetInnerHTML={{
                          __html: sanitizeHtml(change.oldValue),
                        }}
                      />

                      <div
                        className="break-words text-foreground"
                        dangerouslySetInnerHTML={{
                          __html: sanitizeHtml(change.newValue),
                        }}
                      />
                    </div>
                  </div>
                </div>
              );
            }

            // Special handling for tags
            if (
              fieldName === 'tags' &&
              Array.isArray(change.oldValue) &&
              Array.isArray(change.newValue)
            ) {
              const oldTags = (change.oldValue as string[]) || [];
              const newTags = (change.newValue as string[]) || [];
              const addedTags = newTags.filter((tag) => !oldTags.includes(tag));
              const removedTags = oldTags.filter((tag) => !newTags.includes(tag));

              if (addedTags.length > 0 || removedTags.length > 0) {
                return (
                  <div className="space-y-1" key={fieldName}>
                    <div className="font-medium text-muted-foreground">Tags</div>
                    {addedTags.length > 0 && (
                      <div className="flex items-center gap-2">
                        <Plus className="h-3 w-3 text-success" />
                        <div className="flex flex-wrap gap-1">
                          {addedTags.map((tag: string) => (
                            <Badge className="text-xs" key={tag} variant="outline">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                    {removedTags.length > 0 && (
                      <div className="flex items-center gap-2">
                        <Trash2 className="h-3 w-3 text-destructive" />
                        <div className="flex flex-wrap gap-1">
                          {removedTags.map((tag: string) => (
                            <Badge className="text-xs line-through" key={tag} variant="outline">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                );
              }
            }

            // Generic field change display
            return (
              <div className="space-y-1" key={fieldName}>
                <div className="font-medium text-muted-foreground capitalize">
                  {fieldName
                    .replace(/Id$/, '')
                    .replace(/([A-Z])/g, ' $1')
                    .toLowerCase()}
                </div>
                <div className="flex items-start gap-2">
                  <ArrowRight className="mt-0.5 h-3 w-3 flex-shrink-0 text-muted-foreground" />
                  <div className="flex-1 space-y-1">
                    <p className="break-words text-muted-foreground">
                      {formatFieldValue(fieldName, change.oldValue)}
                    </p>
                    <p className="break-words text-foreground">
                      {formatFieldValue(fieldName, change.newValue)}
                    </p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      );
    }

    // Show comment content for comment changes
    if ((changeType === 'COMMENT_ADDED' || changeType === 'COMMENT_UPDATED') && metadata) {
      const typedMetadata = metadata as WorkItemHistoryMetadata;
      if (typedMetadata.comment) {
        return (
          <div className="mt-2 rounded bg-muted p-2 text-sm">
            <div className="mb-1 font-medium text-muted-foreground">Comment</div>
            <p className="text-foreground">{typedMetadata.comment.content}</p>
          </div>
        );
      }
    }

    return null;
  };

  if (isLoading || lookupsLoading) {
    return (
      <div className="space-y-4 p-4">
        {[1, 2, 3].map((i) => (
          <div className="flex items-start gap-3" key={i}>
            <Skeleton className="h-8 w-8 rounded-full" />
            <div className="flex-1 space-y-2">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-3 w-1/4" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-muted-foreground">
        <History className="mb-3 h-8 w-8 text-muted-foreground" />
        <p className="text-sm">Failed to load history</p>
        <p className="mt-1 text-muted-foreground text-xs">Please try again later</p>
      </div>
    );
  }

  if (!historyData || historyData.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-muted-foreground">
        <History className="mb-3 h-8 w-8 text-muted-foreground" />
        <p className="text-sm">No history available</p>
        <p className="mt-1 text-muted-foreground text-xs">
          Changes to this work item will appear here
        </p>
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col overflow-hidden">
      {/* Header */}
      <div className="flex flex-shrink-0 items-center justify-between border-b px-4 py-2">
        <h3 className="font-medium text-foreground text-sm">
          Activity
          <span className="ml-2 text-muted-foreground text-xs">
            ({historyData.length} {historyData.length === 1 ? 'entry' : 'entries'})
          </span>
        </h3>
        <button
          className="text-muted-foreground text-xs transition-colors hover:text-muted-foreground"
          onClick={() => setShowRelativeTime(!showRelativeTime)}
          type="button"
        >
          {showRelativeTime ? 'Show absolute time' : 'Show relative time'}
        </button>
      </div>

      {/* History Timeline */}
      <ScrollArea className="flex-1 overflow-hidden">
        <div className="relative px-4 py-4">
          {/* Timeline line */}
          <div className="absolute top-6 bottom-6 left-4 w-px bg-muted" />

          {/* History entries */}
          <div className="space-y-4">
            {historyData.map((history, index) => (
              <div className="relative flex items-start gap-3" key={history.id}>
                {/* Timeline dot */}
                <div className="relative z-10 flex h-8 w-8 items-center justify-center rounded-full bg-background ring-4 ring-background">
                  <div
                    className={cn(
                      'flex h-8 w-8 items-center justify-center rounded-full',
                      index === 0 ? 'bg-primary/5' : 'bg-muted',
                    )}
                  >
                    {getChangeIcon(history.changeType)}
                  </div>
                </div>

                {/* Content */}
                <div className="flex-1 pt-0.5">
                  <div className="flex items-start justify-between gap-2">
                    <div className="flex-1">
                      <div className="flex flex-wrap items-center gap-2">
                        <span className="font-medium text-foreground text-sm">
                          {history.user?.displayName || history.user?.email || 'System'}
                        </span>
                        <span className="text-muted-foreground text-sm">
                          {formatHistoryChange(history)}
                        </span>
                      </div>
                      <p className="mt-0.5 text-muted-foreground text-xs">
                        {formatDate(history.createdAt)}
                      </p>
                      {renderFieldDetails(history)}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </ScrollArea>
    </div>
  );
}
