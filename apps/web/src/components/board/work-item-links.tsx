'use client';

import { Badge } from '@repo/ui/components/badge';
import { But<PERSON> } from '@repo/ui/components/button';
import { Card, CardContent } from '@repo/ui/components/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@repo/ui/components/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@repo/ui/components/dropdown-menu';
import { useQueryClient } from '@tanstack/react-query';
import { ArrowRight, Edit, Link2, Loader2, MoreHorizontal, Plus, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import type { WorkItem, WorkItemLink } from '@/web/services/hooks.schemas';
import {
  getGetApiV1WorkItemLinkQueryKey,
  useDeleteApiV1WorkItemLinkId,
  useGetApiV1WorkItemLink,
} from '@/web/services/work-item-links';
import { WorkItemLinkForm } from './work-item-link-form';

interface WorkItemLinksProps {
  workItemId: string;
  workItem?: WorkItem;
}

const linkTypeLabels: Record<string, { label: string; color: string }> = {
  is_caused_by: {
    label: 'Is caused by',
    color: 'bg-destructive/10 text-destructive',
  },
  blocked_by: {
    label: 'Blocked by',
    color: 'bg-destructive/10 text-destructive',
  },
  blocks: {
    label: 'Blocks',
    color: 'bg-secondary/10 text-secondary-foreground',
  },
  relates_to: { label: 'Relates to', color: 'bg-primary/10 text-primary' },
  is_child_of: {
    label: 'Is child of',
    color:
      'bg-muted dark:bg-muted/20 text-muted-foreground dark:text-muted-foreground dark:text-muted-foreground',
  },
  is_duplicated_by: {
    label: 'Is duplicated by',
    color: 'bg-muted text-muted-foreground',
  },
  duplicates: { label: 'Duplicates', color: 'bg-muted text-muted-foreground' },
};

export function WorkItemLinks({ workItemId }: WorkItemLinksProps) {
  const queryClient = useQueryClient();
  const [isAddLinkDialogOpen, setIsAddLinkDialogOpen] = useState(false);
  const [editingLink, setEditingLink] = useState<WorkItemLink | null>(null);

  // Fetch links where this work item is the source
  const {
    data: sourceLinks,
    isLoading: isLoadingSource,
    refetch: refetchSource,
  } = useGetApiV1WorkItemLink(
    {
      filters: JSON.stringify({ sourceWorkItemId: workItemId }),
      limit: 100,
    },
    {
      query: {
        enabled: !!workItemId,
      },
    },
  );

  // Fetch links where this work item is the target
  const {
    data: targetLinks,
    isLoading: isLoadingTarget,
    refetch: refetchTarget,
  } = useGetApiV1WorkItemLink(
    {
      filters: JSON.stringify({ targetWorkItemId: workItemId }),
      limit: 100,
    },
    {
      query: {
        enabled: !!workItemId,
      },
    },
  );

  const deleteLinkMutation = useDeleteApiV1WorkItemLinkId({
    mutation: {
      onSuccess: () => {
        toast.success('Link removed successfully');
        // Refetch both queries
        refetchSource();
        refetchTarget();
        queryClient.invalidateQueries({ queryKey: getGetApiV1WorkItemLinkQueryKey() });
      },
      onError: (error: Error) => {
        toast.error(error?.message || 'Failed to remove link');
      },
    },
  });

  const handleDeleteLink = (linkId: string) => {
    if (confirm('Are you sure you want to remove this link?')) {
      deleteLinkMutation.mutate({ id: linkId });
    }
  };

  const handleEditLink = (link: WorkItemLink) => {
    setEditingLink(link);
    setIsAddLinkDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsAddLinkDialogOpen(false);
    setEditingLink(null);
  };

  const handleFormSubmit = () => {
    handleCloseDialog();
    // Refetch both queries to get updated data
    refetchSource();
    refetchTarget();
  };

  // Loading state
  if (isLoadingSource || isLoadingTarget) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="font-medium text-lg">Links</h3>
          <Button disabled size="sm" variant="outline">
            <Loader2 className="mr-1 h-4 w-4 animate-spin" />
            Loading...
          </Button>
        </div>
        <Card>
          <CardContent className="py-8 text-center text-muted-foreground">
            <Loader2 className="mx-auto mb-3 h-8 w-8 animate-spin" />
            Loading links...
          </CardContent>
        </Card>
      </div>
    );
  }

  // Define the type for combined links
  type CombinedLink = WorkItemLink & {
    direction: 'incoming' | 'outgoing';
    relatedWorkItem?: WorkItem;
    relatedWorkItemId: string;
    displayLinkType?: string;
  };

  // Combine and organize links
  const allLinks: CombinedLink[] = [];

  // Add source links (this work item is the source)
  if (sourceLinks?.data) {
    allLinks.push(
      ...sourceLinks.data.map((link) => ({
        ...link,
        direction: 'outgoing' as const,
        relatedWorkItem: link.targetWorkItem,
        relatedWorkItemId: link.targetWorkItemId,
      })),
    );
  }

  // Add target links (this work item is the target)
  if (targetLinks?.data) {
    allLinks.push(
      ...targetLinks.data.map((link) => ({
        ...link,
        direction: 'incoming' as const,
        relatedWorkItem: link.sourceWorkItem,
        relatedWorkItemId: link.sourceWorkItemId,
        // Reverse the link type for incoming links
        displayLinkType: getReverseRelationship(link.linkType),
      })),
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-medium text-lg">Links</h3>
        <Button
          onClick={() => setIsAddLinkDialogOpen(true)}
          size="sm"
          type="button"
          variant="outline"
        >
          <Plus className="mr-1 h-4 w-4" />
          Add Link
        </Button>
      </div>

      {allLinks.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-8 text-center">
            <Link2 className="mb-3 h-8 w-8 text-muted-foreground" />
            <p className="text-muted-foreground text-sm">No links yet</p>
            <p className="mt-1 text-muted-foreground text-xs">Add links to related work items</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {allLinks.map((link) => {
            const linkType = link.displayLinkType || link.linkType;
            const linkConfig = linkTypeLabels[linkType] || {
              label: linkType,
              color: 'bg-muted text-muted-foreground',
            };

            return (
              <Card key={`${link.id}-${link.direction}`}>
                <CardContent className="py-0">
                  <div className="flex items-center justify-between">
                    <div className="flex min-w-0 flex-1 items-center gap-3">
                      <div className="flex items-center gap-2">
                        {link.direction === 'incoming' && (
                          <ArrowRight className="h-4 w-4 rotate-180 text-muted-foreground" />
                        )}
                        <Badge className={`${linkConfig.color} border-0`} variant="outline">
                          {linkConfig.label}
                        </Badge>
                        {link.direction === 'outgoing' && (
                          <ArrowRight className="h-4 w-4 text-muted-foreground" />
                        )}
                      </div>
                      <div className="min-w-0 flex-1">
                        {link.relatedWorkItem ? (
                          <div>
                            <div className="flex items-center gap-2">
                              <span className="font-medium text-muted-foreground text-sm">
                                {link.relatedWorkItem.project?.key || 'PRJ'}-
                                {link.relatedWorkItemId.slice(0, 8)}
                              </span>
                              <span className="truncate text-foreground text-sm">
                                {link.relatedWorkItem.title}
                              </span>
                            </div>
                            {link.title && (
                              <p className="mt-1 text-muted-foreground text-xs">{link.title}</p>
                            )}
                            {link.description && (
                              <p className="mt-1 line-clamp-1 text-muted-foreground text-xs">
                                {link.description}
                              </p>
                            )}
                          </div>
                        ) : (
                          <div>
                            <span className="text-muted-foreground text-sm">
                              Work Item ID: {link.relatedWorkItemId}
                            </span>
                            {link.title && (
                              <p className="mt-1 text-muted-foreground text-xs">{link.title}</p>
                            )}
                            {link.description && (
                              <p className="mt-1 line-clamp-1 text-muted-foreground text-xs">
                                {link.description}
                              </p>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      {/* {link.relatedWorkItem && (
                        <Button
                          type="button"
                          size="sm"
                          variant="ghost"
                          className="h-8 w-8 p-0"
                          onClick={() => {
                            window.location.href = `/projects/${link?.relatedWorkItem?.projectId}/work-items/${link?.relatedWorkItem?.id}`;
                          }}
                          title="View work item"
                        >
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      )} */}
                      {link.direction === 'outgoing' && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button className="h-8 w-8 p-0" size="sm" type="button" variant="ghost">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEditLink(link)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Link
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className="text-destructive"
                              onClick={() => handleDeleteLink(link.id)}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Remove Link
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      <Dialog onOpenChange={handleCloseDialog} open={isAddLinkDialogOpen}>
        <DialogContent
          className="max-w-2xl"
          onClick={(e) => e.stopPropagation()}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.stopPropagation();
            }
          }}
        >
          <DialogHeader>
            <DialogTitle>{editingLink ? 'Edit Link' : 'Add Link'}</DialogTitle>
          </DialogHeader>
          <WorkItemLinkForm
            editPayload={editingLink}
            onCancel={handleCloseDialog}
            onSubmit={handleFormSubmit}
            workItemId={workItemId}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Helper function to get reverse relationship
function getReverseRelationship(linkType: string): string {
  const reverseMap: Record<string, string> = {
    is_caused_by: 'causes',
    blocked_by: 'blocks',
    blocks: 'blocked_by',
    relates_to: 'relates_to',
    is_child_of: 'has_child',
    is_duplicated_by: 'duplicates',
    duplicates: 'is_duplicated_by',
  };

  return reverseMap[linkType] || linkType;
}
