import { Button } from '@repo/ui/components/button';
import { Confirmation } from '@repo/ui/components/custom/confirmDialog';
import CustomDataGrid, { type MuiColumn } from '@repo/ui/components/custom/customDataGrid';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@repo/ui/components/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@repo/ui/components/dropdown-menu';
import { Link } from '@tanstack/react-router';
import { Edit, PlusIcon, Trash2 } from 'lucide-react';
import { useState } from 'react';
import type { User, WorkItem } from '@/web/services/hooks.schemas';
import { useGetApiV1Priorities } from '@/web/services/priorities';
import { useGetApiV1Sprints } from '@/web/services/sprints';
import { useGetApiV1Statuses } from '@/web/services/statuses';
import { useGetApiV1Users } from '@/web/services/users';
import { useGetApiV1WorkItemTypes } from '@/web/services/work-item-types';
import { deleteApiV1WorkItemsId, useGetApiV1WorkItems } from '@/web/services/work-items';
import { useRootStore } from '@/web/store/store';
import { renderIcon } from '../organization/work-item-icons';
import { WorkItemForm } from './work-item-form';

// Define columns using MuiColumn<WorkItem>

const WorkItems = () => {
  const currentProjectId = useRootStore((state) => state.currentProjectId);
  const { data: workItems, refetch: refetchWorkItems } = useGetApiV1WorkItems({
    filters: JSON.stringify({
      projectId: {
        $eq: currentProjectId,
      },
    }),
  });
  const { data: workItemTypes } = useGetApiV1WorkItemTypes();
  const { data: status } = useGetApiV1Statuses({});
  const { data: priorities } = useGetApiV1Priorities();
  const { data: sprint } = useGetApiV1Sprints();
  const { data: users } = useGetApiV1Users({}); // Fetch user data

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedWorkItemType, setSelectedWorkItemType] = useState<string>('');
  const [workitemToEdit, setWorkItemToEdit] = useState<WorkItem | null>(null);

  const handleWorkItemTypeSelect = (type: string) => {
    setSelectedWorkItemType(type);
    setIsCreateDialogOpen(true);
  };

  const columns: MuiColumn<WorkItem>[] = [
    {
      field: 'title',
      headerName: 'Title',
      width: 200,
      sortable: true,
      renderCell: (params) => (
        <Link
          className="text-primary hover:underline"
          params={{ workItemId: params.row.original.ticketId }}
          to="/project/boards/workItem/$workItemId"
        >
          {params.row.original.title || 'Untitled'}
        </Link>
      ),
    },
    {
      field: 'ticketId',
      headerName: 'Ticket ID',
      width: 100,
      sortable: true,
    },
    {
      field: 'statusId',
      headerName: 'Status',
      width: 120,
      sortable: true,
      valueFormatter: (params) => status?.data.find((st) => st.id === params.value)?.name,
    },
    {
      field: 'priorityId',
      headerName: 'Priority',
      width: 120,
      sortable: true,
      valueFormatter: (params) => priorities?.data.find((st) => st.id === params.value)?.name,
    },
    {
      field: 'sprintId',
      headerName: 'Sprint',
      width: 120,
      sortable: true,
      valueFormatter: (params) => sprint?.data.find((st) => st.id === params.value)?.name,
    },
    {
      field: 'assigneeId',
      headerName: 'Assigned To',
      width: 150,
      sortable: true,
      valueFormatter: (params) =>
        users?.data.find((user: User) => user.id === params.value)?.displayName || 'Unassigned',
    },
    {
      field: 'id', // Reusing 'id' as a key for the actions column
      headerName: 'Actions',
      width: 200,
      sortable: false,
      renderCell: (params) => (
        <div className="flex space-x-2">
          <Button
            onClick={() => {
              setWorkItemToEdit(params.row.original);
              setIsCreateDialogOpen(true);
            }}
            size="sm"
            variant="ghost"
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Confirmation
            cancelButton={{
              name: 'Cancel',
              onClick: () => {},
              variant: 'secondary',
            }}
            confirmButton={{
              name: 'Delete',
              onClick: async () => {
                await deleteApiV1WorkItemsId(params.row.original.id);
                refetchWorkItems();
              },
              variant: 'destructive',
            }}
            description="This action cannot be undone. This will permanently delete your work item."
            title="Are You Sure?"
          >
            <Button size="sm" variant="ghost">
              <Trash2 className="h-4 w-4" />
            </Button>
          </Confirmation>
        </div>
      ),
    },
  ];

  const handleClose = () => {
    setIsCreateDialogOpen(false);
    setSelectedWorkItemType('');
    setWorkItemToEdit(null);
    refetchWorkItems();
  };

  return (
    <div className="h-screen p-4">
      <CustomDataGrid<WorkItem>
        addButton={
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size="sm" variant="default">
                <PlusIcon className="mr-2" size={16} />
                Add Work Item
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              {workItemTypes?.data.map((type) => (
                <DropdownMenuItem key={type.id} onSelect={() => handleWorkItemTypeSelect(type.id)}>
                  {renderIcon(type.icon, type.color)}
                  <span>{type.name}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        }
        autoHeight={false}
        checkboxSelection
        columns={columns}
        onRowSelectionChange={(_selection) => {}}
        pageSize={10}
        pageSizeOptions={[5, 10, 20]}
        rows={workItems?.data || []}
      />
      {isCreateDialogOpen && (
        <Dialog onOpenChange={setIsCreateDialogOpen} open={isCreateDialogOpen}>
          <DialogContent className="min-h-[80vh] max-w-4xl">
            <DialogHeader>
              <DialogTitle>Create Work Item</DialogTitle>
            </DialogHeader>
            <WorkItemForm
              editPayload={workitemToEdit}
              onCancel={handleClose}
              onSubmit={handleClose}
              priorities={priorities?.data || []}
              sprints={sprint?.data || []}
              statuses={status?.data || []}
              workItemTypeId={selectedWorkItemType}
              workItemTypes={workItemTypes?.data || []}
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default WorkItems;
