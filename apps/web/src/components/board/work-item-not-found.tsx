'use client';

interface WorkItemNotFoundProps {
  workItemId: string;
  onBack: () => void;
}

export function WorkItemNotFound({ workItemId, onBack }: WorkItemNotFoundProps) {
  return (
    <div className="flex h-full min-h-[400px] flex-col items-center justify-center p-6">
      <div className="space-y-4 text-center">
        <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted">
          <svg
            className="h-10 w-10 text-muted-foreground"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>Warning icon</title>
            <path
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
            />
          </svg>
        </div>
        <div>
          <h3 className="font-semibold text-foreground text-lg">Work item not found</h3>
          <p className="mx-auto mt-2 max-w-sm text-muted-foreground text-sm">
            The work item <span className="font-medium font-mono">{workItemId}</span> doesn't exist
            in this project or you don't have permission to view it.
          </p>
        </div>
        <div className="pt-4">
          <button
            className="inline-flex items-center rounded-md bg-secondary px-4 py-2 font-medium text-foreground text-sm transition-colors hover:bg-secondary/80"
            onClick={onBack}
            type="button"
          >
            Go back
          </button>
        </div>
      </div>
    </div>
  );
}
