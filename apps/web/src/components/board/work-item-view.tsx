'use client';

// import { Button } from "@repo/ui/components/button";
import { useParams, useRouter } from '@tanstack/react-router';
import { useGetApiV1Priorities } from '@/web/services/priorities';
import { useGetApiV1Sprints } from '@/web/services/sprints';
import { useGetApiV1Statuses } from '@/web/services/statuses';
import { useGetApiV1WorkItemTypes } from '@/web/services/work-item-types';
import { useGetApiV1WorkItems } from '@/web/services/work-items';
import { useRootStore } from '@/web/store/store';
// import { ArrowLeft } from "lucide-react";
import { WorkItemForm } from './work-item-form';
import { WorkItemNotFound } from './work-item-not-found';

export default function WorkItemView({
  workItemId: quickViewId,
  isInDialog = false,
  onCancel,
}: {
  workItemId?: string;
  isInDialog?: boolean;
  onCancel?: () => void;
}) {
  const { workItemId = quickViewId } = useParams({
    strict: false,
  });
  const router = useRouter();
  const currentProjectId = useRootStore((state) => state.currentProjectId);
  const currentOrganizationId = useRootStore((state) => state.currentOrganizationId);

  // Fetch work item data
  const { data: workItemData } = useGetApiV1WorkItems({
    filters: JSON.stringify({
      ticketId: { $eq: workItemId },
      projectId: { $eq: currentProjectId },
    }),
  });

  // Fetch all required data for the form
  const { data: workItemTypesData } = useGetApiV1WorkItemTypes({
    filters: JSON.stringify({
      organizationId: { $or: { $eq: currentOrganizationId, $null: true } },
    }),
  });
  const { data: statusesData } = useGetApiV1Statuses({
    filters: JSON.stringify({
      organizationId: { $or: { $eq: currentOrganizationId, $null: true } },
    }),
  });
  const { data: prioritiesData } = useGetApiV1Priorities({
    filters: JSON.stringify({
      organizationId: { $or: { $eq: currentOrganizationId, $null: true } },
    }),
  });
  const { data: sprintsData } = useGetApiV1Sprints({
    filters: JSON.stringify({
      projectId: { $or: { $eq: currentProjectId, $null: true } },
    }),
  });

  const handleBack = () => {
    if (onCancel) {
      onCancel();
    } else {
      router.history.back();
    }
  };

  const handleSubmit = () => {
    // Refresh the work item data after update
    router.invalidate();
  };

  if (!(workItemData && workItemTypesData && statusesData && prioritiesData && sprintsData)) {
    return <div className="p-6 text-muted-foreground">Loading...</div>;
  }

  // Check if work item exists
  if (!workItemData.data || workItemData.data.length === 0) {
    return <WorkItemNotFound onBack={handleBack} workItemId={workItemId || ''} />;
  }

  const workItem = workItemData.data[0];

  return (
    <div
      className={`flex w-full flex-col bg-background ${isInDialog || quickViewId ? 'h-full' : 'h-screen'}`}
    >
      {/* Header */}
      {/* {!quickViewId && !isInDialog && (
        <div className="sticky top-0 bg-background/80 backdrop-blur-sm border-b px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                className="text-muted-foreground"
                onClick={handleBack}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            </div>
          </div>
        </div>
      )} */}

      <WorkItemForm
        editPayload={workItem}
        onCancel={handleBack}
        onSubmit={handleSubmit}
        priorities={prioritiesData.data || []}
        sprints={sprintsData.data || []}
        statuses={statusesData.data || []}
        workItemTypeId={workItem.typeId}
        workItemTypes={workItemTypesData.data || []}
      />
    </div>
  );
}
