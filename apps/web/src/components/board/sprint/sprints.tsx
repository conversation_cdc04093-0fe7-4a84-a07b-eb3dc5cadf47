// src/components/Sprints.tsx

import { Button } from '@repo/ui/components/button';
import { Confirmation } from '@repo/ui/components/custom/confirmDialog';
import CustomDataGrid, { type MuiColumn } from '@repo/ui/components/custom/customDataGrid';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@repo/ui/components/dialog';
import { Edit, Trash2 } from 'lucide-react';
import { useState } from 'react';
// import WorkItems from "../work-items";
// import { useGetV1WorkItem } from "@/web/services/work-item/work-item";
// import { useGetV1WorkItemType } from "@/web/services/work-item-type/work-item-type";
// import { useGetV1Status } from "@/web/services/status/status";
// import { useGetV1Priority } from "@/web/services/priority/priority";
import type { Sprint } from '@/web/services/hooks.schemas';
import { deleteApiV1SprintsId, useGetApiV1Sprints } from '@/web/services/sprints';
import SprintWorkItems from '../sprint-work-items';
import { SprintForm } from './sprint-form'; // Assuming this is in the same directory

interface SprintsProps {
  sprints: Sprint[];
  onEdit: (sprint: Sprint) => void;
  projectId: string;
}

const Sprints: React.FC<SprintsProps> = ({ sprints, onEdit, projectId }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editSprint, setEditSprint] = useState<Sprint | undefined>(undefined);

  // Fetch sprints for refetching after form submission
  const { refetch } = useGetApiV1Sprints({});
  // const { data: workItemTypes } = useGetV1WorkItemType();
  // const { data: status } = useGetV1Status({});
  // const { data: priorities } = useGetV1Priority();

  const columns: MuiColumn<Sprint>[] = [
    {
      field: 'name',
      headerName: 'Name',
      width: 150,
      sortable: true,
    },
    {
      field: 'startDate',
      headerName: 'Start Date',
      width: 120,
      sortable: true,
      valueFormatter: ({ value }) =>
        typeof value === 'string'
          ? new Date(value).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric',
            })
          : 'N/A',
    },
    {
      field: 'endDate',
      headerName: 'End Date',
      width: 120,
      sortable: true,
      valueFormatter: ({ value }) =>
        typeof value === 'string'
          ? new Date(value).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric',
            })
          : 'N/A',
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      sortable: true,
    },

    {
      field: 'isActive',
      headerName: 'Active',
      width: 100,
      sortable: true,
      valueFormatter: ({ value }) => (value ? 'Yes' : 'No'),
    },
    {
      field: 'id',
      headerName: 'Actions',
      width: 200,
      sortable: false,
      renderCell: ({ row }) => {
        return (
          <div className="flex space-x-2">
            <Button onClick={() => onEdit(row.original)} size="sm" variant="ghost">
              <Edit className="h-4 w-4" />
            </Button>
            <Confirmation
              cancelButton={{
                name: 'Cancel',
                onClick: () => {},
                variant: 'ghost',
              }}
              confirmButton={{
                name: 'Delete',
                onClick: async () => {
                  await deleteApiV1SprintsId(row.original.id);
                  refetch();
                },
                variant: 'destructive',
              }}
              description="This action cannot be undone. This will permanently delete your sprint."
              title="Are You Sure?"
            >
              <Button size="sm" variant="ghost">
                <Trash2 className="h-4 w-4" />
              </Button>
            </Confirmation>
          </div>
        );
      },
    },
  ];
  const handleOpenDialog = (sprint?: Sprint) => {
    setEditSprint(sprint);
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditSprint(undefined);
  };

  const handleFormSubmit = () => {
    handleCloseDialog();
    refetch(); // Refresh sprint list
  };

  return (
    <div className="h-full">
      <CustomDataGrid<Sprint>
        addButton={{
          variant: 'default',
          size: 'sm',
          onClick: () => handleOpenDialog(),
          title: 'Add Sprint',
        }}
        autoHeight={false}
        checkboxSelection
        columns={columns}
        nestedTable={{
          renderNestedTable: (row) => {
            return <SprintWorkItems sprintId={row.id} />;
          },
          nestedMaxHeight: '400px',
        }}
        onRowSelectionChange={(_selection) => {}}
        pageSize={10}
        pageSizeOptions={[5, 10, 20]}
        rows={sprints}
      />

      {/* Sprint Form Dialog */}
      <Dialog onOpenChange={setIsDialogOpen} open={isDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{editSprint ? 'Edit Sprint' : 'Create Sprint'}</DialogTitle>
          </DialogHeader>
          <SprintForm
            editPayload={editSprint}
            onCancel={handleCloseDialog}
            onSubmit={handleFormSubmit}
            projectId={projectId}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Sprints;
