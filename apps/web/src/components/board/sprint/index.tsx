// src/components/index.tsx

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from '@repo/ui/components/dialog';
import { useParams } from '@tanstack/react-router';
import { useState } from 'react';
import type { Sprint } from '@/web/services/hooks.schemas';
import { useGetApiV1Sprints } from '@/web/services/sprints';
import { SprintForm } from './sprint-form';
import Sprints from './sprints';

interface SprintModuleProps {
  projectId: string; // To filter sprints by project
}

const SprintModule: React.FC<SprintModuleProps> = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editSprint, setEditSprint] = useState<Sprint | null>(null);
  const { projId } = useParams({
    from: '/__mainLayout/project/boards/sprints/$projId',
  });

  // Fetch sprints
  const { data: sprints = { data: [] }, refetch } = useGetApiV1Sprints({});

  const handleEdit = (sprint: Sprint) => {
    setEditSprint(sprint);
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditSprint(null);
  };

  const handleFormSubmit = () => {
    handleCloseDialog();
    setEditSprint(null);
    refetch(); // Refresh sprint list
  };

  return (
    <div className="h-screen p-4">
      {/* Add Sprint Button */}

      {/* Sprint Form Dialog */}
      <Dialog onOpenChange={setIsDialogOpen} open={isDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{editSprint ? 'Edit Sprint' : 'Create Sprint'}</DialogTitle>
          </DialogHeader>
          <SprintForm
            editPayload={editSprint}
            onCancel={handleCloseDialog}
            onSubmit={handleFormSubmit}
            projectId={projId}
          />
        </DialogContent>
      </Dialog>

      {/* Sprint List */}

      <Sprints
        onEdit={(sprint) => handleEdit(sprint)}
        projectId={projId}
        sprints={sprints.data || []}
      />
    </div>
  );
};

export default SprintModule;
