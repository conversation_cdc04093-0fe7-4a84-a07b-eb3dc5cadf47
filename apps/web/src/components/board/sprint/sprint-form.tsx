import { insertSprintSchema, patchSprintSchema } from '@repo/db/schema';
import { Button } from '@repo/ui/components/button';
import { DialogFooter } from '@repo/ui/components/dialog';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@repo/ui/components/form';
import { Input } from '@repo/ui/components/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { Switch } from '@repo/ui/components/switch';
import { Textarea } from '@repo/ui/components/textarea';
import { useForm } from '@tanstack/react-form';
import type {
  PatchApiV1SprintsIdBody,
  PostApiV1SprintsBody,
  Sprint,
  SprintStatus,
} from '@/web/services/hooks.schemas';
import { usePatchApiV1SprintsId, usePostApiV1Sprints } from '@/web/services/sprints';
import { useRootStore } from '@/web/store/store';

interface SprintFormProps {
  onSubmit?: () => void;
  onCancel?: () => void;
  editPayload?: Sprint | null;
  projectId: string;
}

export const SprintForm: React.FC<SprintFormProps> = ({
  onSubmit,
  onCancel,
  editPayload,
  projectId,
}) => {
  const currentUser = useRootStore((state) => state.currentUser);

  const defaultValues: PatchApiV1SprintsIdBody | PostApiV1SprintsBody = editPayload || {
    name: '',
    projectId,
    startDate: null,
    endDate: null,
    goal: '',
    createdBy: currentUser?.id || '',
    isActive: true,
    status: 'draft' as SprintStatus,
  };

  const sprintMutation = usePostApiV1Sprints({
    mutation: {
      onSuccess: (_data) => {
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (_error) => {},
    },
  });

  const sprintUpdateMutation = usePatchApiV1SprintsId({
    mutation: {
      onSuccess: (_data) => {
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (_error) => {},
    },
  });

  const schema = editPayload ? patchSprintSchema : insertSprintSchema;
  const form = useForm({
    defaultValues,
    validators: {
      onSubmit: schema,
    },

    onSubmit: async ({ value }) => {
      if (editPayload) {
        await sprintUpdateMutation.mutateAsync({
          id: editPayload.id,
          data: value,
        });
      } else {
        await sprintMutation.mutateAsync({
          data: value as PostApiV1SprintsBody,
        });
      }
    },
  });

  const requiredFields = ['name', 'projectId', 'createdById'];

  return (
    <>
      <div className="overflow-auto">
        <form
          className="space-y-3 p-3"
          id="sprint-form"
          onSubmit={(e) => {
            e.preventDefault();
            form.handleSubmit();
          }}
        >
          <form.Field name="name">
            {(field) => (
              <FormField field={field} required={requiredFields.includes('name')}>
                <FormItem>
                  <FormLabel>Sprint Name</FormLabel>
                  <FormControl>
                    <Input
                      className={field.state.meta.errors[0] && 'border-destructive'}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                      placeholder="Sprint Name"
                      value={field.state.value}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            )}
          </form.Field>

          <form.Field name="projectId">
            {(field) => (
              <FormField field={field} required={requiredFields.includes('projectId')}>
                <FormItem className="hidden">
                  <FormControl>
                    <Input
                      className={field.state.meta.errors[0] && 'border-destructive'}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                      value={field.state.value}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            )}
          </form.Field>

          <form.Field name="startDate">
            {(field) => (
              <FormField field={field} required={requiredFields.includes('startDate')}>
                <FormItem>
                  <FormLabel>Start Date</FormLabel>
                  <FormControl>
                    <Input
                      className={field.state.meta.errors[0] && 'border-destructive'}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value || null)}
                      type="date"
                      value={
                        field.state.value
                          ? typeof field.state.value === 'string'
                            ? field.state.value
                            : new Date(field.state.value as string | Date)
                                .toISOString()
                                .slice(0, 10)
                          : ''
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            )}
          </form.Field>

          <form.Field name="endDate">
            {(field) => (
              <FormField field={field} required={requiredFields.includes('endDate')}>
                <FormItem>
                  <FormLabel>End Date</FormLabel>
                  <FormControl>
                    <Input
                      className={field.state.meta.errors[0] && 'border-destructive'}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value || null)}
                      type="date"
                      value={
                        field.state.value
                          ? typeof field.state.value === 'string'
                            ? field.state.value
                            : new Date(field.state.value as string | Date)
                                .toISOString()
                                .slice(0, 10)
                          : ''
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            )}
          </form.Field>

          <form.Field name="status">
            {(field) => (
              <FormField field={field} required={requiredFields.includes('status')}>
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <FormControl>
                    <Select
                      onValueChange={(value) => field.handleChange(value as SprintStatus)}
                      value={field.state.value || ''}
                    >
                      <SelectTrigger className={field.state.meta.errors[0] && 'border-destructive'}>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="archived">Archived</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            )}
          </form.Field>

          <form.Field name="goal">
            {(field) => (
              <FormField field={field} required={requiredFields.includes('goal')}>
                <FormItem>
                  <FormLabel>Goal</FormLabel>
                  <FormControl>
                    <Textarea
                      className={field.state.meta.errors[0] && 'border-destructive'}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                      placeholder="Sprint goal"
                      value={field.state.value || ''}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            )}
          </form.Field>

          <form.Field name="isActive">
            {(field) => (
              <FormField field={field} required={requiredFields.includes('isActive')}>
                <FormItem className="flex items-center space-x-2">
                  <FormLabel>Is Active</FormLabel>
                  <FormControl>
                    <Switch
                      checked={field.state.value}
                      onCheckedChange={(checked) => field.handleChange(checked)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            )}
          </form.Field>
        </form>
      </div>
      <DialogFooter>
        <Button
          disabled={sprintMutation.isPending || sprintUpdateMutation.isPending}
          onClick={onCancel}
          type="button"
          variant="outline"
        >
          Cancel
        </Button>
        <Button
          disabled={sprintMutation.isPending || sprintUpdateMutation.isPending}
          form="sprint-form"
          type="submit"
        >
          {sprintMutation.isPending || sprintUpdateMutation.isPending
            ? 'Saving...'
            : editPayload
              ? 'Update'
              : 'Create'}
        </Button>
      </DialogFooter>
    </>
  );
};
