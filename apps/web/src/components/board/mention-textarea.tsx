'use client';

import { Textarea } from '@repo/ui/components/textarea';
import { cn } from '@repo/ui/lib/utils';
import type React from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';
import Man from '@/web/assets/man.png';
import type { User } from '@/web/services/hooks.schemas';

interface MentionTextareaProps {
  value: string;
  onChange: (value: string) => void;
  users: User[];
  placeholder?: string;
  className?: string;
  autoFocus?: boolean;
}

export function MentionTextarea({
  value,
  onChange,
  users,
  placeholder,
  className,
  autoFocus = false,
}: MentionTextareaProps) {
  const [showMentions, setShowMentions] = useState(false);
  const [mentionSearch, setMentionSearch] = useState('');
  const [mentionIndex, setMentionIndex] = useState(0);
  const [cursorPosition, setCursorPosition] = useState(0);
  const [mentionStartPosition, setMentionStartPosition] = useState(-1);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const mentionListRef = useRef<HTMLDivElement>(null);

  // Filter users based on search
  const filteredUsers = users.filter((user) => {
    const searchLower = mentionSearch.toLowerCase();
    const displayName = user.displayName || user.email || '';
    return (
      displayName.toLowerCase().includes(searchLower) ||
      user.email?.toLowerCase().includes(searchLower)
    );
  });

  // Handle textarea input
  const handleInput = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    const newCursorPos = e.target.selectionStart;
    setCursorPosition(newCursorPos);

    // Check if user typed @
    const lastChar = newValue[newCursorPos - 1];
    if (lastChar === '@') {
      setShowMentions(true);
      setMentionSearch('');
      setMentionStartPosition(newCursorPos - 1);
      setMentionIndex(0);
    } else if (showMentions && mentionStartPosition >= 0) {
      // Update mention search
      const searchText = newValue.substring(mentionStartPosition + 1, newCursorPos);

      // Close mentions if space or newline after @
      if (searchText.includes(' ') || searchText.includes('\n')) {
        setShowMentions(false);
        setMentionStartPosition(-1);
      } else {
        setMentionSearch(searchText);
        setMentionIndex(0);
      }
    }

    onChange(newValue);
  };

  // Handle mention selection
  const selectMention = useCallback(
    (user: User) => {
      if (mentionStartPosition < 0) {
        return;
      }

      const displayName = user.displayName || user.email || 'Unknown User';
      const beforeMention = value.substring(0, mentionStartPosition);
      const afterMention = value.substring(cursorPosition);

      const newValue = `${beforeMention}@${displayName}${afterMention}`;
      onChange(newValue);

      // Move cursor after the mention
      setTimeout(() => {
        if (textareaRef.current) {
          const newCursorPos = mentionStartPosition + displayName.length + 1;
          textareaRef.current.selectionStart = newCursorPos;
          textareaRef.current.selectionEnd = newCursorPos;
          textareaRef.current.focus();
        }
      }, 0);

      setShowMentions(false);
      setMentionStartPosition(-1);
      setMentionSearch('');
    },
    [mentionStartPosition, value, cursorPosition, onChange],
  );

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (!showMentions || filteredUsers.length === 0) {
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setMentionIndex((prev) => (prev < filteredUsers.length - 1 ? prev + 1 : 0));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setMentionIndex((prev) => (prev > 0 ? prev - 1 : filteredUsers.length - 1));
        break;
      case 'Enter':
        if (showMentions && filteredUsers[mentionIndex]) {
          e.preventDefault();
          selectMention(filteredUsers[mentionIndex]);
        }
        break;
      case 'Escape':
        setShowMentions(false);
        setMentionStartPosition(-1);
        break;
    }
  };

  // Close mentions when clicking outside
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (
        mentionListRef.current &&
        !mentionListRef.current.contains(e.target as Node) &&
        textareaRef.current &&
        !textareaRef.current.contains(e.target as Node)
      ) {
        setShowMentions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Auto-focus on mount if requested
  useEffect(() => {
    if (autoFocus && textareaRef.current) {
      textareaRef.current.focus();
      textareaRef.current.selectionStart = value.length;
      textareaRef.current.selectionEnd = value.length;
    }
  }, [autoFocus, value.length]);

  return (
    <div className="relative">
      <Textarea
        className={className}
        onChange={handleInput}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        ref={textareaRef}
        value={value}
      />

      {showMentions && filteredUsers.length > 0 && (
        <div
          className="absolute z-50 mt-1 max-h-48 w-64 overflow-auto rounded-md border bg-background shadow-lg"
          ref={mentionListRef}
        >
          {filteredUsers.map((user, index) => (
            <button
              className={cn(
                'flex w-full cursor-pointer items-center gap-2 px-3 py-2 text-left hover:bg-muted',
                index === mentionIndex && 'bg-muted',
              )}
              key={user.id}
              onClick={() => selectMention(user)}
              onMouseEnter={() => setMentionIndex(index)}
              type="button"
            >
              <img
                alt={user.displayName || user.email || ''}
                className="h-6 w-6 rounded-full"
                src={user.avatarUrl || Man}
              />
              <div className="min-w-0 flex-1">
                <div className="truncate font-medium text-sm">{user.displayName || user.email}</div>
                {user.displayName && user.email && (
                  <div className="truncate text-muted-foreground text-xs">{user.email}</div>
                )}
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
