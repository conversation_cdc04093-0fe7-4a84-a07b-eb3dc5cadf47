import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@repo/ui/components/alert-dialog';
import { Button } from '@repo/ui/components/button';
import { cn } from '@repo/ui/lib/utils';
import { format } from 'date-fns';
import { Download, Eye, File, FileText, Image, Paperclip, Trash2, Upload, X } from 'lucide-react';
import { forwardRef, useCallback, useEffect, useImperativeHandle, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { toast } from 'sonner';
import { ImagePreview, ImageThumbnail } from '@/web/components/image-previewer/image-preview';
import axiosInstance from '@/web/services/axiosClient';
import {
  useDeleteApiV1AttachmentsId,
  useGetApiV1AttachmentsByEntity,
} from '@/web/services/default';
import type { Attachment } from '@/web/services/hooks.schemas';

export interface PendingFile {
  id: string;
  file: File;
  preview?: string;
}

export interface WorkItemAttachmentsRef {
  uploadFiles: (workItemId: string) => Promise<void>;
  getPendingFiles: () => PendingFile[];
  clearPendingFiles: () => void;
  deleteAttachments: () => Promise<void>;
  getPendingDeletions: () => string[];
}

interface WorkItemAttachmentsProps {
  workItemId?: string;
  isEditing: boolean;
  onPendingFilesChange?: (files: PendingFile[]) => void;
  onPendingDeletionsChange?: (deletions: string[]) => void;
  initialPendingFiles?: PendingFile[];
  initialPendingDeletions?: string[];
}

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
// const ALLOWED_FILE_TYPES = [
//   "image/jpeg",
//   "image/png",
//   "image/gif",
//   "image/webp",
//   "application/pdf",
//   "text/plain",
//   "application/msword",
//   "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
//   "application/vnd.ms-excel",
//   "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
// ];

export const WorkItemAttachments = forwardRef<WorkItemAttachmentsRef, WorkItemAttachmentsProps>(
  (
    {
      workItemId,
      isEditing,
      onPendingFilesChange,
      onPendingDeletionsChange,
      initialPendingFiles = [],
      initialPendingDeletions = [],
    },
    ref,
  ) => {
    const [pendingFiles, setPendingFiles] = useState<PendingFile[]>(initialPendingFiles);
    const [pendingDeletions, setPendingDeletions] = useState<string[]>(initialPendingDeletions);
    const [deleteConfirmId, setDeleteConfirmId] = useState<string | null>(null);
    const [deletePendingId, setDeletePendingId] = useState<string | null>(null);
    const [isUploading, setIsUploading] = useState(false);

    // API hooks
    const { data: attachmentsData, refetch: refetchAttachments } = useGetApiV1AttachmentsByEntity(
      {
        entityType: 'work_item',
        entityId: workItemId || '',
      },
      {
        query: {
          enabled: !!workItemId,
        },
      },
    );

    const deleteMutation = useDeleteApiV1AttachmentsId();

    // Notify parent of pending files changes
    useEffect(() => {
      onPendingFilesChange?.(pendingFiles);
    }, [pendingFiles, onPendingFilesChange]);

    // Notify parent of pending deletions changes
    useEffect(() => {
      onPendingDeletionsChange?.(pendingDeletions);
    }, [pendingDeletions, onPendingDeletionsChange]);

    // Expose methods to parent via ref
    useImperativeHandle(ref, () => ({
      uploadFiles: async (workItemId: string) => {
        if (pendingFiles.length === 0) {
          return;
        }

        setIsUploading(true);
        const errors: string[] = [];

        try {
          for (const pendingFile of pendingFiles) {
            try {
              const formData = new FormData();
              formData.append('file', pendingFile.file);

              await axiosInstance.post(
                `/api/v1/attachments/upload?entityType=work_item&entityId=${workItemId}`,
                formData,
              );
            } catch (_error) {
              errors.push(`Failed to upload ${pendingFile.file.name}`);
            }
          }

          if (errors.length > 0) {
            toast.error(`Some files failed to upload: ${errors.join(', ')}`);
          } else {
            toast.success('All attachments uploaded successfully');
          }

          // Clear pending files after upload
          setPendingFiles([]);
          refetchAttachments();
        } finally {
          setIsUploading(false);
        }
      },
      getPendingFiles: () => pendingFiles,
      clearPendingFiles: () => setPendingFiles([]),
      deleteAttachments: async () => {
        if (pendingDeletions.length === 0) {
          return;
        }

        const errors: string[] = [];

        for (const attachmentId of pendingDeletions) {
          try {
            await deleteMutation.mutateAsync({ id: attachmentId });
          } catch (_error) {
            errors.push(`Failed to delete attachment ${attachmentId}`);
          }
        }

        if (errors.length > 0) {
          toast.error(`Some attachments failed to delete: ${errors.join(', ')}`);
        } else {
          toast.success('Attachments deleted successfully');
        }

        // Clear pending deletions after processing
        setPendingDeletions([]);
        refetchAttachments();
      },
      getPendingDeletions: () => pendingDeletions,
    }));

    // File drop handler
    const onDrop = useCallback((acceptedFiles: File[]) => {
      const newPendingFiles = acceptedFiles.map((file) => ({
        id: `${file.name}-${Date.now()}-${Math.random()}`,
        file,
        preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined,
      }));

      setPendingFiles((prev) => [...prev, ...newPendingFiles]);
    }, []);

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
      onDrop,
      maxSize: MAX_FILE_SIZE,
      accept: {
        'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp'],
        'application/pdf': ['.pdf'],
        'text/plain': ['.txt'],
        'application/msword': ['.doc'],
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
        'application/vnd.ms-excel': ['.xls'],
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      },
      disabled: !isEditing || isUploading,
    });

    const getFileIcon = (fileType: string | null) => {
      if (!fileType) {
        return <File className="h-4 w-4" />;
      }
      if (fileType.startsWith('image/')) {
        return <Image className="h-4 w-4" />;
      }
      if (fileType === 'application/pdf') {
        return <FileText className="h-4 w-4" />;
      }
      return <File className="h-4 w-4" />;
    };

    const formatFileSize = (bytes: number | null) => {
      if (!bytes) {
        return 'Unknown size';
      }
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(1024));
      return `${(bytes / 1024 ** i).toFixed(2)} ${sizes[i]}`;
    };

    const handleDownload = async (attachment: Attachment) => {
      try {
        const response = await axiosInstance.get(`/api/v1/attachments/${attachment.id}/signed-url`);
        if (response.data?.url) {
          window.open(response.data.url, '_blank');
        }
      } catch (_error) {
        toast.error('Failed to get download URL');
      }
    };

    const removePendingFile = (fileId: string) => {
      setPendingFiles((prev) => {
        const file = prev.find((f) => f.id === fileId);
        if (file?.preview) {
          URL.revokeObjectURL(file.preview);
        }
        return prev.filter((f) => f.id !== fileId);
      });
      setDeletePendingId(null);
    };

    const attachments = (attachmentsData || []).filter(
      (attachment) => !pendingDeletions.includes(attachment.id),
    );
    const totalFiles = attachments.length + pendingFiles.length;

    if (!isEditing && attachments.length === 0 && pendingFiles.length === 0) {
      return (
        <div className="py-12 text-center text-muted-foreground">
          <Paperclip className="mx-auto mb-3 h-8 w-8 text-muted-foreground" />
          <p className="text-sm">No attachments</p>
        </div>
      );
    }

    return (
      <div className="flex h-full flex-col gap-4">
        {isEditing && (
          <div
            {...getRootProps()}
            className={cn(
              'flex-shrink-0 cursor-pointer rounded-lg border-2 border-dashed p-6 text-center transition-colors',
              isDragActive ? 'border-primary bg-primary/5' : 'border-border hover:border-border',
              isUploading && 'cursor-not-allowed opacity-50',
            )}
          >
            <input {...getInputProps()} />
            <Upload className="mx-auto mb-2 h-8 w-8 text-muted-foreground" />
            {isDragActive ? (
              <p className="text-muted-foreground text-sm">Drop the files here...</p>
            ) : (
              <>
                <p className="text-muted-foreground text-sm">
                  Drag and drop files here, or click to select
                </p>
                <p className="mt-1 text-muted-foreground text-xs">
                  Max file size: 10MB. Supported: Images, PDF, Word, Excel, Text
                </p>
                {!workItemId && (
                  <p className="mt-2 text-secondary text-xs">
                    Files will be uploaded when you save the work item
                  </p>
                )}
              </>
            )}
          </div>
        )}

        {/* Combined list header */}
        {totalFiles > 0 && (
          <div className="flex flex-shrink-0 items-center justify-between">
            <h4 className="font-medium text-muted-foreground text-sm">
              Attachments ({totalFiles})
            </h4>
            <div className="flex gap-3">
              {pendingFiles.length > 0 && (
                <span className="text-secondary text-xs">{pendingFiles.length} pending upload</span>
              )}
              {pendingDeletions.length > 0 && (
                <span className="text-destructive text-xs">
                  {pendingDeletions.length} pending deletion
                </span>
              )}
            </div>
          </div>
        )}

        {/* Scrollable content area */}
        <div className="flex-1 overflow-y-auto overflow-x-hidden pr-2">
          <div className="space-y-4">
            {/* Pending Files (to be uploaded) */}
            {pendingFiles.length > 0 && (
              <div className="space-y-2">
                {pendingFiles.map((pendingFile) => {
                  const isImage = pendingFile.file.type.startsWith('image/');
                  const previewUrl =
                    pendingFile.preview || (isImage ? URL.createObjectURL(pendingFile.file) : null);

                  return (
                    <div
                      className="flex max-w-full items-center gap-3 rounded-lg border border-secondary/20 bg-secondary/5 p-3"
                      key={pendingFile.id}
                    >
                      <div className="flex min-w-0 flex-1 items-center gap-3">
                        <div className="flex-shrink-0">
                          {isImage && previewUrl ? (
                            <ImageThumbnail
                              alt={pendingFile.file.name}
                              fileName={pendingFile.file.name}
                              size="sm"
                              src={previewUrl}
                            />
                          ) : (
                            getFileIcon(pendingFile.file.type)
                          )}
                        </div>
                        <div className="min-w-0 max-w-[300px] flex-1">
                          <p
                            className="block truncate font-medium text-sm"
                            title={pendingFile.file.name}
                          >
                            {pendingFile.file.name}
                          </p>
                          <p className="block truncate text-muted-foreground text-xs">
                            {formatFileSize(pendingFile.file.size)} • Pending upload
                          </p>
                        </div>
                      </div>
                      {isEditing && (
                        <Button
                          className="h-8 w-8 flex-shrink-0 p-0 hover:border-destructive/30 hover:bg-destructive/5 hover:text-destructive"
                          disabled={isUploading}
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            setDeletePendingId(pendingFile.id);
                          }}
                          size="sm"
                          type="button"
                          variant="outline"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  );
                })}
              </div>
            )}

            {/* Existing Attachments */}
            {attachmentsData && attachmentsData.length > 0 && (
              <div className="space-y-2">
                {attachmentsData.map((attachment) => {
                  const isImage = attachment.fileType?.startsWith('image/');
                  const isMarkedForDeletion = pendingDeletions.includes(attachment.id);

                  if (isMarkedForDeletion && !isEditing) {
                    return null; // Hide in view mode if marked for deletion
                  }

                  return (
                    <div
                      className={cn(
                        'flex max-w-full items-center gap-3 rounded-lg p-3 transition-colors',
                        isMarkedForDeletion
                          ? 'border border-destructive/20 bg-destructive/5 opacity-60'
                          : 'bg-muted hover:bg-muted',
                      )}
                      key={attachment.id}
                    >
                      <div className="flex min-w-0 flex-1 items-center gap-3">
                        <div className="flex-shrink-0">
                          {isImage ? (
                            <ImageThumbnail
                              alt={attachment.fileName}
                              fileName={attachment.fileName}
                              onDownload={() => handleDownload(attachment)}
                              size="sm"
                              src={attachment.url}
                            />
                          ) : (
                            getFileIcon(attachment.fileType)
                          )}
                        </div>
                        <div className="min-w-0 max-w-[300px] flex-1">
                          <p
                            className="block truncate font-medium text-sm"
                            title={attachment.fileName}
                          >
                            {attachment.fileName}
                          </p>
                          <p
                            className="block truncate text-muted-foreground text-xs"
                            title={`${formatFileSize(attachment.size)} • ${format(new Date(attachment.uploadedAt), "MMM d, yyyy 'at' h:mm a")}${attachment.uploader ? ` • ${attachment.uploader.displayName}` : ''}${isMarkedForDeletion ? ' • Marked for deletion' : ''}`}
                          >
                            {formatFileSize(attachment.size)} •{' '}
                            {format(new Date(attachment.uploadedAt), "MMM d, yyyy 'at' h:mm a")}
                            {attachment.uploader && <> • {attachment.uploader.displayName}</>}
                            {isMarkedForDeletion && (
                              <span className="font-medium text-destructive">
                                {' '}
                                • Marked for deletion
                              </span>
                            )}
                          </p>
                        </div>
                      </div>
                      <div className="flex flex-shrink-0 items-center gap-1">
                        {isImage && (
                          <ImagePreview
                            alt={attachment.fileName}
                            fileName={attachment.fileName}
                            onDownload={() => handleDownload(attachment)}
                            src={attachment.url}
                            trigger={
                              <Button
                                className="h-8 w-8 p-0"
                                size="sm"
                                title="Preview"
                                type="button"
                                variant="outline"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            }
                          />
                        )}
                        <Button
                          className="h-8 w-8 p-0"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleDownload(attachment);
                          }}
                          size="sm"
                          title="Download"
                          type="button"
                          variant="outline"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                        {isEditing &&
                          (isMarkedForDeletion ? (
                            <Button
                              className="h-8 w-8 p-0 hover:border-primary/30 hover:bg-primary/5 hover:text-primary dark:hover:border-primary/70 dark:hover:bg-primary/10 dark:hover:text-primary"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                setPendingDeletions((prev) =>
                                  prev.filter((id) => id !== attachment.id),
                                );
                                toast.info('Deletion cancelled');
                              }}
                              size="sm"
                              title="Undo Delete"
                              type="button"
                              variant="outline"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          ) : (
                            <Button
                              className="h-8 w-8 p-0 hover:border-destructive/30 hover:bg-destructive/5 hover:text-destructive"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                setDeleteConfirmId(attachment.id);
                              }}
                              size="sm"
                              title="Delete"
                              type="button"
                              variant="outline"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>

        {/* Delete Pending File Confirmation */}
        <AlertDialog onOpenChange={() => setDeletePendingId(null)} open={!!deletePendingId}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Remove File</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to remove this file? It has not been uploaded yet.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => {
                  if (deletePendingId) {
                    removePendingFile(deletePendingId);
                  }
                }}
              >
                Remove
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Delete Existing Attachment Confirmation */}
        <AlertDialog onOpenChange={() => setDeleteConfirmId(null)} open={!!deleteConfirmId}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Attachment</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete this attachment? This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => {
                  if (deleteConfirmId) {
                    setPendingDeletions((prev) => [...prev, deleteConfirmId]);
                    setDeleteConfirmId(null);
                    toast.info('Attachment marked for deletion. Save changes to confirm.');
                  }
                }}
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    );
  },
);

WorkItemAttachments.displayName = 'WorkItemAttachments';
