import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/avatar';
import { Badge } from '@repo/ui/components/badge';
import { Button } from '@repo/ui/components/button';
import { Card, CardContent, CardHeader } from '@repo/ui/components/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@repo/ui/components/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@repo/ui/components/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@repo/ui/components/tooltip';
import { useToast } from '@repo/ui/components/use-toast';
import { cn } from '@repo/ui/lib/utils';
import { Link } from '@tanstack/react-router';
import { Pencil, PlusIcon } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import {
  DragDropContext,
  Draggable,
  type DraggableProvided,
  type DragStart,
  Droppable,
  type DroppableProvided,
  type DroppableStateSnapshot,
  type DropResult,
} from 'react-beautiful-dnd';
import type { WorkItem } from '@/web/services/hooks.schemas';
import { useGetApiV1Priorities } from '@/web/services/priorities';
import { useGetApiV1Sprints } from '@/web/services/sprints';
import { useGetApiV1Statuses } from '@/web/services/statuses';
// import { useGetApiV1Users } from "@/web/services/users";
import { useGetApiV1WorkItemTypes } from '@/web/services/work-item-types';
import { useGetApiV1WorkItems, usePatchApiV1WorkItemsId } from '@/web/services/work-items';
import { useRootStore } from '@/web/store/store';
import { stripHtml } from '@/web/utils/sanitize-html';
import { renderIcon, renderPriorityIcon } from '../organization/work-item-icons';
import { WorkItemForm } from './work-item-form';
import WorkItemView from './work-item-view';

// Empty arrays for other data (to be populated by API if needed)

function hexToRgba(hex: string, opacity: number): string {
  const cleanHex = hex.replace('#', '');
  if (cleanHex.length !== 6) {
    return 'rgba(0,0,0,0)';
  }

  const r = Number.parseInt(cleanHex.substring(0, 2), 16);
  const g = Number.parseInt(cleanHex.substring(2, 4), 16);
  const b = Number.parseInt(cleanHex.substring(4, 6), 16);

  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
}

const StrictModeDroppable = ({
  children,
  droppableId,
  direction,
  isDropDisabled,
}: {
  children: (
    provided: DroppableProvided,
    snapshot: DroppableStateSnapshot,
  ) => React.ReactElement<HTMLElement>; // Force correct return type
  droppableId: string;
  direction: 'horizontal' | 'vertical';
  isDropDisabled?: boolean;
}) => {
  const [enabled, setEnabled] = useState(false);
  useEffect(() => {
    const animation = requestAnimationFrame(() => setEnabled(true));
    return () => {
      cancelAnimationFrame(animation);
      setEnabled(false);
    };
  }, []);
  if (!enabled) {
    return null;
  }
  return (
    <Droppable direction={direction} droppableId={droppableId} isDropDisabled={isDropDisabled}>
      {children}
    </Droppable>
  );
};

interface TaskCardProps {
  taskData: WorkItem;
  onEdit?: (taskId: string) => void;
  onView?: (taskId: string) => void;
}

function AgileBoard() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedWorkItemType, setSelectedWorkItemType] = useState<string>('');
  const [selectedWorkItem, setSelectedWorkItem] = useState<WorkItem | null>(null);
  const [draggingWorkItemId, setDraggingWorkItemId] = useState<string | null>(null);
  const [openQuickView, setOpenQuickView] = useState<string | null>(null);
  const { toast } = useToast();
  // Refs for manual scrolling and position tracking
  const scrollContainerRef = React.useRef<HTMLDivElement>(null);
  const scrollIntervalRef = React.useRef<NodeJS.Timeout | null>(null);
  const hasScrolledRef = React.useRef<boolean>(false);
  const mousePositionRef = React.useRef<{ x: number; y: number }>({
    x: 0,
    y: 0,
  });

  const currentProjectId = useRootStore((state) => state.currentProjectId);
  // Get workflow transitions from store
  const currentProjectWorkflowTransitions = useRootStore(
    (state) => state.currentProjectWorkflowTransitions,
  );

  // Fetch status and work items from API
  const { data: status } = useGetApiV1Statuses({
    page: 1,
    limit: 1000,
  });
  const { data: workItems, refetch: refetchWorkItems } = useGetApiV1WorkItems({
    filters: JSON.stringify({
      projectId: {
        $eq: currentProjectId,
      },
    }),
  });
  const { data: workItemTypes } = useGetApiV1WorkItemTypes({
    page: 1,
    limit: 1000,
  });
  const { data: priorities } = useGetApiV1Priorities({
    page: 1,
    limit: 1000,
  });
  // const { data: userData } = useGetApiV1Users();
  const { data: sprint } = useGetApiV1Sprints();
  const workItemPatchMutaion = usePatchApiV1WorkItemsId({
    mutation: {
      onSuccess: () => {
        toast({
          title: 'Success',
          description: 'Work item updated successfully',
        });
        refetchWorkItems();
      },
      onError: (_error) => {},
    },
  });

  // Remove the order mutation - we'll use the standard patch mutation instead

  // Helper function to check if a status transition is allowed
  const isStatusAllowedForDrag = (fromStatusId: string, toStatusId: string) => {
    // Check direct transitions
    const hasDirectTransition = currentProjectWorkflowTransitions.some(
      (transition) =>
        transition.fromStatus?.status?.id === fromStatusId &&
        transition.toStatus?.status?.id === toStatusId,
    );

    // Check global transitions (null fromStatusId means any status can transition)
    const hasGlobalTransition = currentProjectWorkflowTransitions.some(
      (transition) =>
        transition.fromStatus?.status?.id === null &&
        transition.toStatus?.status?.id === toStatusId,
    );

    return hasDirectTransition || hasGlobalTransition;
  };

  // Get drop target from mouse position
  const getDropTargetFromPosition = (x: number, y: number): string | null => {
    const elements = document.elementsFromPoint(x, y);

    for (const element of elements) {
      // Look for droppable elements
      const droppableId = element.getAttribute('data-rbd-droppable-id');
      if (droppableId) {
        return droppableId;
      }

      // Also check parent elements
      const parent = element.closest('[data-rbd-droppable-id]');
      if (parent) {
        return parent.getAttribute('data-rbd-droppable-id');
      }
    }

    return null;
  };

  // Manual scroll handling
  React.useEffect(() => {
    if (!(draggingWorkItemId && scrollContainerRef.current)) {
      return;
    }

    const handleMouseMove = (e: MouseEvent) => {
      mousePositionRef.current = { x: e.clientX, y: e.clientY };

      const container = scrollContainerRef.current;
      if (!container) {
        return;
      }

      const rect = container.getBoundingClientRect();
      const scrollThreshold = 100; // pixels from edge to start scrolling
      const scrollSpeed = 10;

      // Clear any existing scroll interval
      if (scrollIntervalRef.current) {
        clearInterval(scrollIntervalRef.current);
        scrollIntervalRef.current = null;
      }

      // Check if near left edge
      if (e.clientX < rect.left + scrollThreshold && container.scrollLeft > 0) {
        hasScrolledRef.current = true;
        scrollIntervalRef.current = setInterval(() => {
          container.scrollLeft -= scrollSpeed;
        }, 16);
      }
      // Check if near right edge
      else if (
        e.clientX > rect.right - scrollThreshold &&
        container.scrollLeft < container.scrollWidth - container.clientWidth
      ) {
        hasScrolledRef.current = true;
        scrollIntervalRef.current = setInterval(() => {
          container.scrollLeft += scrollSpeed;
        }, 16);
      }
    };

    document.addEventListener('mousemove', handleMouseMove);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      if (scrollIntervalRef.current) {
        clearInterval(scrollIntervalRef.current);
      }
    };
  }, [draggingWorkItemId]);

  // Handle drag start
  const onDragStart = (result: DragStart) => {
    setDraggingWorkItemId(result.draggableId);
    hasScrolledRef.current = false;
  };

  // Handle drag update
  const onDragUpdate = (_update: import('react-beautiful-dnd').DragUpdate) => {
    // Let React Beautiful DnD handle drop detection
  };

  // Handle drag end
  const onDragEnd = (result: DropResult) => {
    setDraggingWorkItemId(null);

    // Clear scroll interval
    if (scrollIntervalRef.current) {
      clearInterval(scrollIntervalRef.current);
      scrollIntervalRef.current = null;
    }

    let { destination } = result;
    const { source, draggableId } = result;

    // If we scrolled during drag, manually detect the drop target
    if (hasScrolledRef.current && mousePositionRef.current) {
      const manualDropTarget = getDropTargetFromPosition(
        mousePositionRef.current.x,
        mousePositionRef.current.y,
      );

      if (manualDropTarget && manualDropTarget !== source.droppableId) {
        destination = {
          droppableId: manualDropTarget,
          index: 0, // Add to end of column
        };
      }
    }

    hasScrolledRef.current = false;

    if (!destination) {
      return;
    }

    if (destination.droppableId === source.droppableId && destination.index === source.index) {
      return;
    }

    // Find the work item being moved
    const workItem = workItems?.data.find((item) => item.id === draggableId);
    if (!workItem) {
      return;
    }

    // Get work items in the destination column (excluding the item being dragged)
    const destinationWorkItems =
      workItems?.data
        .filter((item) => item.statusId === destination.droppableId && item.id !== draggableId)
        .sort((a, b) => (a.order || 0) - (b.order || 0)) || [];

    // Calculate new order value
    let newOrder: number;

    if (destination.index === 0) {
      // Moving to the beginning
      newOrder = destinationWorkItems.length > 0 ? (destinationWorkItems[0]?.order || 0) - 1000 : 0;
    } else if (destination.index >= destinationWorkItems.length) {
      // Moving to the end
      newOrder =
        destinationWorkItems.length > 0 ? (destinationWorkItems.at(-1)?.order || 0) + 1000 : 0;
    } else {
      // Moving between items
      const prevItem = destinationWorkItems[destination.index - 1];
      const nextItem = destinationWorkItems[destination.index];
      const prevOrder = prevItem?.order || 0;
      const nextOrder = nextItem?.order || 0;
      newOrder = (prevOrder + nextOrder) / 2;
    }

    // Check if we're changing status
    const isStatusChange = source.droppableId !== destination.droppableId;

    if (isStatusChange) {
      // Check if transition is allowed based on workflow transitions
      const isTransitionAllowed = currentProjectWorkflowTransitions.some(
        (transition) =>
          transition.fromStatus?.status?.id === workItem.statusId &&
          transition.toStatus?.status?.id === destination.droppableId,
      );

      // Also check for global transitions (null fromStatusId means any status can transition)
      const hasGlobalTransition = currentProjectWorkflowTransitions.some(
        (transition) =>
          transition.fromStatus?.status?.id === null &&
          transition.toStatus?.status?.id === destination.droppableId,
      );

      if (!(isTransitionAllowed || hasGlobalTransition)) {
        toast({
          title: 'Not Allowed',
          description: `Transition from ${workItem.status?.name} to this destination not allowed`,
          variant: 'destructive',
          duration: 5000,
        });

        return;
      }
      workItemPatchMutaion.mutateAsync(
        {
          id: draggableId,
          data: { order: newOrder, statusId: destination.droppableId },
        },
        {},
      );
    } else {
      workItemPatchMutaion.mutateAsync(
        {
          id: draggableId,
          data: { order: newOrder },
        },
        {},
      );
    }
  };

  const handleWorkItemTypeSelect = (type: string) => {
    setSelectedWorkItemType(type);
    setIsCreateDialogOpen(true);
  };

  const handleCreate = () => {
    setIsCreateDialogOpen(false);
    setSelectedWorkItemType('');
    setSelectedWorkItem(null);
    refetchWorkItems();
  };

  const handleEdit = (data: WorkItem) => {
    setSelectedWorkItem(data);
    setSelectedWorkItemType(data.typeId);
    setIsCreateDialogOpen(true);
  };

  const handleView = (taskId: string) => {
    setOpenQuickView(taskId);
    // Your view logic here
  };

  const TaskCard: React.FC<TaskCardProps> = ({ taskData, onEdit }) => {
    const getInitials = (name: string) => {
      return name
        ?.split(' ')
        .map((word) => word[0])
        .join('')
        .toUpperCase()
        .slice(0, 2);
    };

    return (
      <TooltipProvider>
        <div className="group relative rounded-lg border border-border bg-card shadow-sm transition-all duration-200 hover:border-primary/20 hover:shadow-md">
          {/* Header with Edit Button */}
          <div className="absolute top-3 right-3 z-10">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  className="h-7 w-7 p-0 opacity-0 transition-opacity hover:bg-primary/10 group-hover:opacity-100"
                  onClick={(e) => {
                    e.stopPropagation();
                    onEdit?.(taskData.id);
                  }}
                  size="sm"
                  variant="ghost"
                >
                  <Pencil size={14} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Edit task</p>
              </TooltipContent>
            </Tooltip>
            {/* Eye icon for quick view
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 flex-shrink-0 hover:bg-primary/10 opacity-70 hover:opacity-100"
                  onClick={(e) => {
                    e.stopPropagation();
                    onView?.(taskData.id);
                  }}
                >
                  <Eye size={12} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Quick view</p>
              </TooltipContent>
            </Tooltip> */}
          </div>

          {/* Main Content */}
          <div className="p-4 ">
            {/* Title - Single line ellipsis */}
            <div className="mb-3 flex items-start justify-between">
              <h3 className="truncate pr-2 font-semibold text-sm leading-5">
                <Link
                  className="text-primary hover:underline"
                  params={{ workItemId: taskData.ticketId }}
                  to="/project/boards/workItem/$workItemId"
                >
                  {taskData.title || 'Untitled Task'}{' '}
                </Link>
              </h3>
            </div>

            {/* Description - Two line ellipsis */}
            {taskData.description && (
              <div className="mb-4 line-clamp-2 min-h-[2rem] text-muted-foreground text-xs leading-4">
                {stripHtml(taskData.description)}
              </div>
            )}

            {/* Bottom Flex Layout */}
            <div className="flex items-center justify-between">
              {/* Left: Ticket Type and ID */}
              <div className="flex items-center gap-2">
                {/* Type Icon */}
                <div className="flex items-center">
                  {renderIcon(taskData.type.icon, taskData.type.color)}
                  <span className="font-medium text-muted-foreground text-xs">
                    {/* {taskData.type.name} */}
                  </span>
                </div>

                {/* Separator */}
                {/* <div className="w-1 h-1 rounded-full bg-muted-foreground/30" /> */}

                {/* Ticket ID */}
                <span className="rounded bg-muted px-2 py-0.5 font-mono text-muted-foreground text-xs">
                  {taskData.ticketId}
                </span>
              </div>

              {/* Right: Priority Icon and User Icon */}
              <div className="flex items-center gap-2">
                {/* Priority Icon */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center">
                      {renderPriorityIcon(taskData.priority?.icon, taskData.priority?.color)}
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{taskData.priority?.name} Priority</p>
                  </TooltipContent>
                </Tooltip>

                {/* User Avatar */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Avatar className="h-6 w-6 border">
                      <AvatarImage
                        alt={taskData.assignee?.displayName}
                        src={taskData.assignee?.avatarUrl || ''}
                      />
                      <AvatarFallback className="bg-primary/10 text-primary text-xs">
                        {getInitials(taskData.assignee?.displayName)}
                      </AvatarFallback>
                    </Avatar>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Assigned to {taskData.assignee?.displayName}</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            </div>
          </div>

          {/* Status Indicator Bar */}
          {/* <div
            className="h-1 w-full rounded-b-lg"
            style={{ backgroundColor: taskData.status.color }}
          /> */}
        </div>
      </TooltipProvider>
    );
  };

  return (
    <>
      <div className="h-full w-full border p-4">
        <div className=" border-b p-2">
          <div className="flex items-center justify-between">
            <div className="font-semibold text-2xl">Agile Board</div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button size="sm" variant="default">
                  <PlusIcon className="mr-2" size={16} />
                  Add Work Item
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                {workItemTypes?.data.map((type) => (
                  <DropdownMenuItem
                    key={type.id}
                    onSelect={() => handleWorkItemTypeSelect(type.id)}
                  >
                    {renderIcon(type.icon, type.color)}
                    <span>{type.name}</span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <DragDropContext
          onDragEnd={onDragEnd}
          onDragStart={onDragStart}
          onDragUpdate={onDragUpdate}
        >
          <div
            className="relative overflow-x-auto p-4"
            ref={scrollContainerRef}
            style={{ minHeight: 'calc(100vh - 200px)' }}
          >
            {/* Scroll indicators when dragging */}
            {draggingWorkItemId && (
              <>
                <div
                  className="pointer-events-none fixed top-0 bottom-0 left-0 z-50 w-24 bg-gradient-to-r from-accent/20 to-transparent"
                  style={{
                    top: scrollContainerRef.current?.getBoundingClientRect().top,
                    height: scrollContainerRef.current?.getBoundingClientRect().height,
                  }}
                />
                <div
                  className="pointer-events-none fixed top-0 right-0 bottom-0 z-50 w-24 bg-gradient-to-l from-accent/20 to-transparent"
                  style={{
                    top: scrollContainerRef.current?.getBoundingClientRect().top,
                    height: scrollContainerRef.current?.getBoundingClientRect().height,
                  }}
                />
              </>
            )}
            <div className="flex min-w-max gap-4">
              {/* Horizontal scrolling for columns, vertical constrained */}
              {status?.data?.map((workflowStatus) => {
                const statusTasks = workItems?.data
                  .filter((task) => task.statusId === workflowStatus.id)
                  .sort((a, b) => (a.order || 0) - (b.order || 0));

                // Debug: Log order values for first status
                if (workflowStatus === status?.data?.[0]) {
                }

                // Check if this status is an allowed destination when dragging
                const draggingWorkItem = draggingWorkItemId
                  ? workItems?.data.find((item) => item.id === draggingWorkItemId)
                  : null;

                const isSourceStatus = draggingWorkItem?.statusId === workflowStatus.id;
                const isAllowedDestination = draggingWorkItem
                  ? isStatusAllowedForDrag(draggingWorkItem.statusId, workflowStatus.id) ||
                    isSourceStatus
                  : true;

                return (
                  <div className="h-full w-80 flex-shrink-0" key={workflowStatus.id}>
                    <Card
                      className={cn(
                        'flex h-full flex-col transition-all duration-200',
                        draggingWorkItemId &&
                          !isAllowedDestination &&
                          'cursor-not-allowed opacity-40',
                        draggingWorkItemId &&
                          isAllowedDestination &&
                          !isSourceStatus &&
                          'shadow-lg ring-2 ring-primary',
                        draggingWorkItemId && isSourceStatus && 'ring-2 ring-primary',
                      )}
                      style={{ minHeight: '54rem' }}
                    >
                      <CardHeader
                        className={cn(
                          'flex-shrink-0 p-2',
                          draggingWorkItemId && !isAllowedDestination && 'bg-muted',
                        )}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <span className="font-semibold text-sm">{workflowStatus.name}</span>
                            <Badge className="rounded-full">{statusTasks?.length}</Badge>
                          </div>
                          {draggingWorkItemId && (
                            <div className="text-xs">
                              {isSourceStatus && (
                                <span className="font-medium text-primary">Current</span>
                              )}
                              {!isSourceStatus && isAllowedDestination && (
                                <span className="font-medium text-primary dark:text-primary">
                                  ✓ Allowed
                                </span>
                              )}
                              {!isAllowedDestination && (
                                <span className="font-medium text-destructive">✗ Not Allowed</span>
                              )}
                            </div>
                          )}
                        </div>
                      </CardHeader>
                      <CardContent
                        className={cn(
                          'flex-grow overflow-hidden border-t-2 p-2 transition-all duration-200',
                          draggingWorkItemId && isAllowedDestination && !isSourceStatus
                            ? 'border-t-4 border-t-green-500'
                            : 'border-t-muted',
                          draggingWorkItemId &&
                            !isAllowedDestination &&
                            'border-t-destructive/30 opacity-50',
                          draggingWorkItemId && isSourceStatus && 'border-t-4 border-t-accent',
                        )}
                        style={{
                          backgroundColor: hexToRgba(
                            workflowStatus.color ?? '#000000',
                            draggingWorkItemId && !isAllowedDestination ? 0.1 : 0.5,
                          ),
                        }}
                      >
                        <StrictModeDroppable
                          direction="vertical"
                          droppableId={workflowStatus.id}
                          isDropDisabled={!!(draggingWorkItemId && !isAllowedDestination)}
                        >
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.droppableProps}
                              className={cn(
                                'h-full overflow-y-auto overflow-x-hidden p-2',
                                snapshot.isDraggingOver &&
                                  isAllowedDestination &&
                                  'bg-primary/5 dark:bg-primary/10',
                                snapshot.isDraggingOver &&
                                  !isAllowedDestination &&
                                  'bg-destructive/5',
                              )}
                              style={{
                                minHeight: '46rem',
                                maxHeight: 'calc(100vh - 300px)',
                                scrollbarWidth: 'thin',
                                scrollbarColor: '#9ca3af #f3f4f6',
                              }}
                            >
                              {statusTasks?.map((task, index) => (
                                <Draggable draggableId={task.id} index={index} key={task.id}>
                                  {(provided: DraggableProvided) => (
                                    <div
                                      ref={provided.innerRef}
                                      {...provided.draggableProps}
                                      {...provided.dragHandleProps}
                                      className="mb-2"
                                    >
                                      <TaskCard
                                        onEdit={() => handleEdit(task)}
                                        onView={() => handleView(task.id)}
                                        taskData={task}
                                      />
                                    </div>
                                  )}
                                </Draggable>
                              ))}
                              {provided.placeholder}
                            </div>
                          )}
                        </StrictModeDroppable>
                      </CardContent>
                    </Card>
                  </div>
                );
              })}
            </div>
          </div>
        </DragDropContext>
      </div>
      {isCreateDialogOpen && (
        <Dialog onOpenChange={setIsCreateDialogOpen} open={isCreateDialogOpen}>
          <DialogContent className="flex h-[80vh] max-w-4xl flex-col">
            <DialogHeader>
              <DialogTitle>Create Work Item</DialogTitle>
            </DialogHeader>
            <WorkItemForm
              editPayload={selectedWorkItem}
              onCancel={() => setIsCreateDialogOpen(false)}
              onSubmit={handleCreate}
              priorities={priorities?.data || []}
              sprints={sprint?.data || []}
              statuses={status?.data || []}
              workItemTypeId={selectedWorkItemType}
              workItemTypes={workItemTypes?.data || []}
            />
          </DialogContent>
        </Dialog>
      )}
      {openQuickView && (
        <Dialog
          onOpenChange={(open) => {
            if (!open) {
              setOpenQuickView(null);
            }
          }}
          open={!!openQuickView}
        >
          <DialogContent className="flex h-[80vh] max-w-4xl flex-col">
            <DialogHeader>
              <DialogTitle>Quick View</DialogTitle>
            </DialogHeader>
            <div className="min-h-0 flex-1 overflow-hidden">
              <WorkItemView isInDialog={true} workItemId={openQuickView} />
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}

export default AgileBoard;
