import { insertWorkItemLinkSchema, patchWorkItemLinkSchema } from '@repo/db/schema';
import { Button } from '@repo/ui/components/button';
import { DialogFooter } from '@repo/ui/components/dialog';
import { Input } from '@repo/ui/components/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { Switch } from '@repo/ui/components/switch';
import { Textarea } from '@repo/ui/components/textarea';
import { useForm } from '@tanstack/react-form';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import type {
  PatchApiV1WorkItemLinkIdBody,
  PostApiV1WorkItemLinkBody,
  WorkItemLink,
  WorkItemLinkLinkType,
} from '@/web/services/hooks.schemas';
import {
  getGetApiV1WorkItemLinkQueryKey,
  useGetApiV1WorkItemLink,
  usePatchApiV1WorkItemLinkId,
  usePostApiV1WorkItemLink,
} from '@/web/services/work-item-links';
import { useGetApiV1WorkItems } from '@/web/services/work-items';
import { useRootStore } from '@/web/store/store';

interface WorkItemLinkFormProps {
  onSubmit?: () => void;
  onCancel?: () => void;
  editPayload?: WorkItemLink | null; // If editing an existing link, pass the payload
  workItemId: string;
}

const linkTypes: { value: WorkItemLinkLinkType; label: string }[] = [
  { value: 'blocked_by', label: 'Blocked by' },
  { value: 'blocks', label: 'Blocks' },
  { value: 'duplicates', label: 'Duplicates' },
  { value: 'is_caused_by', label: 'Is caused by' },
  { value: 'is_child_of', label: 'Is child of' },
  { value: 'is_duplicated_by', label: 'Is duplicated by' },
  { value: 'relates_to', label: 'Relates to' },
];

export const WorkItemLinkForm: React.FC<WorkItemLinkFormProps> = ({
  onSubmit,
  onCancel,
  editPayload,
  workItemId,
}) => {
  const queryClient = useQueryClient();
  const currentUser = useRootStore((state) => state.currentUser);
  const currentProjectId = useRootStore((state) => state.currentProjectId);

  // Fetch existing links to prevent duplicates
  const { data: existingLinks } = useGetApiV1WorkItemLink(
    {
      filters: JSON.stringify({ sourceWorkItemId: workItemId }),
      limit: 100,
    },
    {
      query: {
        enabled: !!workItemId && !editPayload, // Only check when creating new links
      },
    },
  );

  // Fetch work items for the current project
  const { data: workItemsData, isLoading: isLoadingWorkItems } = useGetApiV1WorkItems(
    {
      filters: currentProjectId ? JSON.stringify({ projectId: currentProjectId }) : undefined,
      limit: 100,
    },
    {
      query: {
        enabled: !!currentProjectId,
      },
    },
  );

  const createMutation = usePostApiV1WorkItemLink({
    mutation: {
      onSuccess: () => {
        toast.success('Link created successfully');
        queryClient.invalidateQueries({ queryKey: getGetApiV1WorkItemLinkQueryKey() });
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (error) => {
        // Check for unique constraint violation
        if (error?.message?.includes('unique') || error?.message?.includes('duplicate')) {
          toast.error(
            'This link already exists. A work item can only be linked once with the same relationship type.',
          );
        } else {
          toast.error(error?.message || 'Failed to create link');
        }
      },
    },
  });

  const updateMutation = usePatchApiV1WorkItemLinkId({
    mutation: {
      onSuccess: () => {
        toast.success('Link updated successfully');
        queryClient.invalidateQueries({ queryKey: getGetApiV1WorkItemLinkQueryKey() });
        if (onSubmit) {
          onSubmit();
        }
      },
      onError: (error) => {
        toast.error(error?.message || 'Failed to update link');
      },
    },
  });

  const defaultValues: PostApiV1WorkItemLinkBody | PatchApiV1WorkItemLinkIdBody =
    (editPayload as PatchApiV1WorkItemLinkIdBody) || {
      targetWorkItemId: '',
      linkType: linkTypes[0].value,
      title: '',
      description: '',
      createdById: currentUser?.id || '',
      isActive: true as boolean,
      sourceWorkItemId: workItemId,
    };
  const formSchema = editPayload ? patchWorkItemLinkSchema : insertWorkItemLinkSchema;
  const form = useForm({
    defaultValues,
    validators: {
      onSubmit: formSchema,
    },
    onSubmit: async ({ value }) => {
      if (editPayload) {
        const updateData: PatchApiV1WorkItemLinkIdBody = {
          linkType: value.linkType,
          title: value.title,
          description: value.description || null,
          isActive: value.isActive,
        };
        await updateMutation.mutateAsync({
          id: editPayload?.id,
          data: updateData as PatchApiV1WorkItemLinkIdBody,
        });
      } else {
        // Check if this link already exists
        const isDuplicate = existingLinks?.data?.some(
          (link) =>
            link.targetWorkItemId === value.targetWorkItemId && link.linkType === value.linkType,
        );

        if (isDuplicate) {
          toast.error(
            'This link already exists. A work item can only be linked once with the same relationship type.',
          );
          return;
        }

        await createMutation.mutateAsync({
          data: value as PostApiV1WorkItemLinkBody,
        });
      }
    },
  });

  const isLoading = createMutation.isPending || updateMutation.isPending;

  // Filter out the current work item from the target selection
  const availableWorkItems = workItemsData?.data?.filter((item) => item.id !== workItemId) || [];

  return (
    <form
      id="work-item-link-form"
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <div className="overflow-auto">
        <div className="flex flex-col gap-y-3 p-6">
          <div className="grid gap-3">
            <form.Field name="linkType">
              {(field) => (
                <div className="space-y-2">
                  <label className="font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Link Type
                  </label>
                  <Select
                    onValueChange={(value) => field.handleChange(value as WorkItemLinkLinkType)}
                    value={field.state.value}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select a link type" />
                    </SelectTrigger>
                    <SelectContent>
                      {linkTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {field.state.meta.errors && field.state.meta.errors.length > 0 && (
                    <p className="font-medium text-destructive text-sm">
                      {typeof field.state.meta.errors[0] === 'string'
                        ? field.state.meta.errors[0]
                        : (field.state.meta.errors[0]?.message ??
                          String(field.state.meta.errors[0]))}
                    </p>
                  )}
                </div>
              )}
            </form.Field>

            {!editPayload && (
              <form.Field name="targetWorkItemId">
                {(field) => (
                  <div className="space-y-2">
                    <label className="font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                      Target Work Item
                    </label>
                    <Select
                      disabled={isLoadingWorkItems || availableWorkItems.length === 0}
                      onValueChange={(value) => field.handleChange(value)}
                      value={field.state.value}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue
                          placeholder={
                            isLoadingWorkItems
                              ? 'Loading work items...'
                              : availableWorkItems.length === 0
                                ? 'No work items available'
                                : 'Select a work item to link'
                          }
                        />
                      </SelectTrigger>
                      <SelectContent>
                        {availableWorkItems.map((workItem) => {
                          // Check if this work item is already linked with the current link type
                          const isAlreadyLinked = existingLinks?.data?.some(
                            (link) =>
                              link.targetWorkItemId === workItem.id &&
                              link.linkType === field.form.state.values.linkType,
                          );

                          return (
                            <SelectItem
                              disabled={isAlreadyLinked}
                              key={workItem.id}
                              value={workItem.id}
                            >
                              <div className="flex items-center gap-2">
                                <span className="line-clamp-1 font-medium text-muted-foreground">
                                  {workItem.ticketId}
                                </span>
                                <span className="text-foreground">{workItem.title}</span>
                                {isAlreadyLinked && (
                                  <span className="ml-2 text-muted-foreground text-xs">
                                    (Already linked)
                                  </span>
                                )}
                              </div>
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                    {field.state.meta.errors && field.state.meta.errors.length > 0 && (
                      <p className="font-medium text-destructive text-sm">
                        {typeof field.state.meta.errors[0] === 'string'
                          ? field.state.meta.errors[0]
                          : (field.state.meta.errors[0]?.message ??
                            String(field.state.meta.errors[0]))}
                      </p>
                    )}
                  </div>
                )}
              </form.Field>
            )}

            <form.Field name="title">
              {(field) => (
                <div className="space-y-2">
                  <label className="font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Title (Optional)
                  </label>
                  <Input
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Brief description of the link"
                    value={field.state.value || ''}
                  />
                  {field.state.meta.errors && field.state.meta.errors.length > 0 && (
                    <p className="font-medium text-destructive text-sm">
                      {typeof field.state.meta.errors[0] === 'string'
                        ? field.state.meta.errors[0]
                        : (field.state.meta.errors[0]?.message ??
                          String(field.state.meta.errors[0]))}
                    </p>
                  )}
                </div>
              )}
            </form.Field>

            <form.Field name="description">
              {(field) => (
                <div className="space-y-2">
                  <label className="font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Description (Optional)
                  </label>
                  <Textarea
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Additional details about this link"
                    rows={3}
                    value={field.state.value || ''}
                  />
                  {field.state.meta.errors && field.state.meta.errors.length > 0 && (
                    <p className="font-medium text-destructive text-sm">
                      {typeof field.state.meta.errors[0] === 'string'
                        ? field.state.meta.errors[0]
                        : (field.state.meta.errors[0]?.message ??
                          String(field.state.meta.errors[0]))}
                    </p>
                  )}
                </div>
              )}
            </form.Field>

            <form.Field name="isActive">
              {(field) => (
                <div className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                  <div className="space-y-0.5">
                    <label className="font-medium text-sm">Active</label>
                    <div className="text-muted-foreground text-sm">Enable or disable this link</div>
                  </div>
                  <Switch
                    checked={field.state.value}
                    onCheckedChange={(checked) => field.handleChange(checked)}
                  />
                </div>
              )}
            </form.Field>
          </div>
        </div>
      </div>
      <DialogFooter className="">
        <Button disabled={isLoading} onClick={onCancel} type="button" variant="outline">
          Cancel
        </Button>
        <Button disabled={isLoading} type="submit">
          {isLoading
            ? editPayload
              ? 'Updating...'
              : 'Creating...'
            : editPayload
              ? 'Update'
              : 'Create'}
        </Button>
      </DialogFooter>
    </form>
  );
};
