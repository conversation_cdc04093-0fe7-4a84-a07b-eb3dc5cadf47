import { Button } from '@repo/ui/components/button';
import { Confirmation } from '@repo/ui/components/custom/confirmDialog';
import CustomDataGrid, { type MuiColumn } from '@repo/ui/components/custom/customDataGrid';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@repo/ui/components/dialog';
import { Edit, Trash2 } from 'lucide-react';
import type React from 'react';
import { useState } from 'react';
import type { WorkItem } from '@/web/services/hooks.schemas';
import { useGetApiV1Priorities } from '@/web/services/priorities';
import { useGetApiV1Sprints } from '@/web/services/sprints';
import { useGetApiV1Statuses } from '@/web/services/statuses';
import { useGetApiV1WorkItemTypes } from '@/web/services/work-item-types';
import { deleteApiV1WorkItemsId, useGetApiV1WorkItems } from '@/web/services/work-items';
import { WorkItemForm } from './work-item-form'; // Adjust the import path as needed

// Define columns using MuiColumn<WorkItem>

//prop type
interface SprintWorkItemsProps {
  sprintId: string;
}

const SprintWorkItems: React.FC<SprintWorkItemsProps> = ({ sprintId }) => {
  const { data: workItems, refetch: refetchWorkItems } = useGetApiV1WorkItems({
    filters: JSON.stringify({ sprintId: { $eq: sprintId } }),
  });
  const { data: workItemTypes } = useGetApiV1WorkItemTypes();
  const { data: status } = useGetApiV1Statuses({});
  const { data: priorities } = useGetApiV1Priorities();
  const { data: sprint } = useGetApiV1Sprints();

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedWorkItemType, setSelectedWorkItemType] = useState<string>('');
  const [workitemToEdit, setWorkItemToEdit] = useState<WorkItem | null>(null);

  const columns: MuiColumn<WorkItem>[] = [
    // {
    //   field: "id",
    //   headerName: "ID",
    //   width: 100,
    //   sortable: true,
    // },
    {
      field: 'title',
      headerName: 'Title',
      width: 200,
      sortable: true,
    },
    {
      field: 'statusId',
      headerName: 'Status',
      width: 120,
      sortable: true,
      valueFormatter: (params) => status?.data.find((st) => st.id === params.value)?.name,
    },
    {
      field: 'priorityId',
      headerName: 'Priority',
      width: 120,
      sortable: true,
      valueFormatter: (params) => priorities?.data.find((st) => st.id === params.value)?.name,
    },
    {
      field: 'sprintId',
      headerName: 'Sprint',
      width: 120,
      sortable: true,
      valueFormatter: (params) => sprint?.data.find((st) => st.id === params.value)?.name,
    },
    {
      field: 'assigneeId',
      headerName: 'Assigned To',
      width: 150,
      sortable: true,
    },
    {
      field: 'id', // Reusing 'id' as a key for the actions column
      headerName: 'Actions',
      width: 200,
      sortable: false,
      renderCell: (params) => (
        <div className="flex space-x-2">
          <Button
            onClick={() => {
              setWorkItemToEdit(params.row.original);
              setIsCreateDialogOpen(true);
            }}
            size="sm"
            variant="ghost"
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Confirmation
            cancelButton={{
              name: 'Cancel',
              onClick: () => {},
              variant: 'secondary',
            }}
            confirmButton={{
              name: 'Delete',
              onClick: async () => {
                await deleteApiV1WorkItemsId(params.row.original.id);
                refetchWorkItems();
              },
              variant: 'destructive',
            }}
            description="This action cannot be undone. This will permanently delete your work item."
            title="Are You Sure?"
          >
            <Button size="sm" variant="ghost">
              <Trash2 className="h-4 w-4" />
            </Button>
          </Confirmation>
        </div>
      ),
    },
  ];

  const handleClose = () => {
    setIsCreateDialogOpen(false);
    setSelectedWorkItemType('');
    setWorkItemToEdit(null);
    refetchWorkItems();
  };

  return (
    <div className="h-full p-4">
      <CustomDataGrid<WorkItem>
        autoHeight={false}
        checkboxSelection
        columns={columns}
        isNested={true}
        maxHeight="400px"
        pageSize={10}
        pageSizeOptions={[5, 10, 20]}
        rows={workItems?.data || []}
      />
      {isCreateDialogOpen && (
        <Dialog onOpenChange={setIsCreateDialogOpen} open={isCreateDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create Work Item</DialogTitle>
            </DialogHeader>
            <WorkItemForm
              editPayload={workitemToEdit}
              onCancel={handleClose}
              onSubmit={handleClose}
              priorities={priorities?.data || []}
              sprints={sprint?.data || []}
              statuses={status?.data || []}
              workItemTypeId={selectedWorkItemType}
              workItemTypes={workItemTypes?.data || []} // Pass work item types
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default SprintWorkItems;
