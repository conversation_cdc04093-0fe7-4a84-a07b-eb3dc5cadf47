import type { Invitation } from '@/web/components/onboarding/onboarding-context';

// Mock implementation - replace with your actual database queries
export async function getPendingInvitations(_email: string): Promise<Invitation[]> {
  // This would typically fetch invitations from your database
  return [
    {
      id: 'inv_1',
      scopeType: 'organization',
      scopeId: 'org_1',
      scopeName: 'Acme Corporation',
      inviterName: '<PERSON>',
      inviterEmail: '<EMAIL>',
      role: 'member',
      createdAt: new Date().toISOString(),
    },
    {
      id: 'inv_2',
      scopeType: 'workspace',
      scopeId: 'ws_1',
      scopeName: 'Engineering Team',
      inviterName: '<PERSON>',
      inviterEmail: '<EMAIL>',
      role: 'developer',
      createdAt: new Date().toISOString(),
    },
    {
      id: 'inv_3',
      scopeType: 'project',
      scopeId: 'proj_1',
      scopeName: 'Website Redesign',
      inviterName: '<PERSON>',
      inviterEmail: '<EMAIL>',
      role: 'contributor',
      createdAt: new Date().toISOString(),
    },
  ];
}
