/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as authLayoutImport } from './routes/__authLayout';
import { Route as authLayoutCheckEmailImport } from './routes/__authLayout/check-email';
import { Route as authLayoutInvitedTokenImport } from './routes/__authLayout/invited.$token';
import { Route as authLayoutLoginImport } from './routes/__authLayout/login';
import { Route as authLayoutResetTokenImport } from './routes/__authLayout/reset.$token';
import { Route as authLayoutResetlinkImport } from './routes/__authLayout/resetlink';
import { Route as authLayoutSignupImport } from './routes/__authLayout/signup';
import { Route as authLayoutVerifyEmailTokenImport } from './routes/__authLayout/verifyEmail.$token';
import { Route as authLayoutWorkflowImport } from './routes/__authLayout/workflow';
import { Route as mainLayoutImport } from './routes/__mainLayout';
import { Route as mainLayoutDashboardImport } from './routes/__mainLayout/dashboard';
import { Route as mainLayoutIndexImport } from './routes/__mainLayout/index';
import { Route as mainLayoutOrganizationCreateImport } from './routes/__mainLayout/organization-create';
import { Route as mainLayoutOrganizationInvitationsImport } from './routes/__mainLayout/organization-invitations';
import { Route as mainLayoutProjectProjIdImport } from './routes/__mainLayout/project/$projId';
import { Route as mainLayoutProjectBoardsAgileBoardProjIdImport } from './routes/__mainLayout/project/boards/agileBoard.$projId';
import { Route as mainLayoutProjectBoardsSprintsProjIdImport } from './routes/__mainLayout/project/boards/sprints.$projId';
import { Route as mainLayoutProjectBoardsWorkItemWorkItemIdImport } from './routes/__mainLayout/project/boards/workItem.$workItemId';
import { Route as mainLayoutProjectBoardsWorkItemsProjIdImport } from './routes/__mainLayout/project/boards/workItems.$projId';
import { Route as mainLayoutProjectGroupsProjIdImport } from './routes/__mainLayout/project/groups.$projId';
import { Route as mainLayoutProjectTestPlansProjIdImport } from './routes/__mainLayout/project/testPlans/$projId';
import { Route as mainLayoutProjectTestPlansDetailTpIdImport } from './routes/__mainLayout/project/testPlans/detail.$tpId';
import { Route as mainLayoutProjectUsersProjIdImport } from './routes/__mainLayout/project/users.$projId';
import { Route as mainLayoutProjectCreateFormImport } from './routes/__mainLayout/project-create-form';
import { Route as mainLayoutSettingsImport } from './routes/__mainLayout/settings';
import { Route as mainLayoutSettingsIndexImport } from './routes/__mainLayout/settings/index';
import { Route as mainLayoutSettingsPriorityImport } from './routes/__mainLayout/settings/priority';
import { Route as mainLayoutSettingsRoleImport } from './routes/__mainLayout/settings/role';
import { Route as mainLayoutSettingsStatusImport } from './routes/__mainLayout/settings/status';
import { Route as mainLayoutSettingsStatusCategoriesImport } from './routes/__mainLayout/settings/status-categories';
import { Route as mainLayoutSettingsStatusTypeImport } from './routes/__mainLayout/settings/status-type';
import { Route as mainLayoutSettingsWorkFlowImport } from './routes/__mainLayout/settings/work-flow';
import { Route as mainLayoutSettingsWorkFlowWorkflowIdDesignImport } from './routes/__mainLayout/settings/work-flow/$workflowId.design';
import { Route as mainLayoutSettingsWorkFlowWorkflowIdEditImport } from './routes/__mainLayout/settings/work-flow/$workflowId.edit';
import { Route as mainLayoutSettingsWorkFlowCreateImport } from './routes/__mainLayout/settings/work-flow/create';
import { Route as mainLayoutSettingsWorkItemTypeImport } from './routes/__mainLayout/settings/work-item-type';
import { Route as mainLayoutTiptapImport } from './routes/__mainLayout/tiptap';
import { Route as mainLayoutUserAccountImport } from './routes/__mainLayout/user/account';
import { Route as mainLayoutUserNotificationImport } from './routes/__mainLayout/user/notification';
import { Route as mainLayoutWorkspaceWsIdImport } from './routes/__mainLayout/workspace/$wsId';
import { Route as mainLayoutWorkspaceOverviewWsIdImport } from './routes/__mainLayout/workspace/overview.$wsId';
import { Route as mainLayoutWorkspaceProjectsWsIdImport } from './routes/__mainLayout/workspace/projects.$wsId';
import { Route as mainLayoutWorkspaceUsersWsIdImport } from './routes/__mainLayout/workspace/users.$wsId';
import { Route as mainLayoutWorkspaceCreateFormImport } from './routes/__mainLayout/workspace-create-form';
import { Route as rootRoute } from './routes/__root';

// Create/Update Routes

const mainLayoutRoute = mainLayoutImport.update({
  id: '/__mainLayout',
  getParentRoute: () => rootRoute,
} as any);

const authLayoutRoute = authLayoutImport.update({
  id: '/__authLayout',
  getParentRoute: () => rootRoute,
} as any);

const mainLayoutIndexRoute = mainLayoutIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => mainLayoutRoute,
} as any);

const mainLayoutWorkspaceCreateFormRoute = mainLayoutWorkspaceCreateFormImport.update({
  id: '/workspace-create-form',
  path: '/workspace-create-form',
  getParentRoute: () => mainLayoutRoute,
} as any);

const mainLayoutTiptapRoute = mainLayoutTiptapImport.update({
  id: '/tiptap',
  path: '/tiptap',
  getParentRoute: () => mainLayoutRoute,
} as any);

const mainLayoutSettingsRoute = mainLayoutSettingsImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => mainLayoutRoute,
} as any);

const mainLayoutProjectCreateFormRoute = mainLayoutProjectCreateFormImport.update({
  id: '/project-create-form',
  path: '/project-create-form',
  getParentRoute: () => mainLayoutRoute,
} as any);

const mainLayoutOrganizationInvitationsRoute = mainLayoutOrganizationInvitationsImport.update({
  id: '/organization-invitations',
  path: '/organization-invitations',
  getParentRoute: () => mainLayoutRoute,
} as any);

const mainLayoutOrganizationCreateRoute = mainLayoutOrganizationCreateImport.update({
  id: '/organization-create',
  path: '/organization-create',
  getParentRoute: () => mainLayoutRoute,
} as any);

const mainLayoutDashboardRoute = mainLayoutDashboardImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => mainLayoutRoute,
} as any);

const authLayoutWorkflowRoute = authLayoutWorkflowImport.update({
  id: '/workflow',
  path: '/workflow',
  getParentRoute: () => authLayoutRoute,
} as any);

const authLayoutSignupRoute = authLayoutSignupImport.update({
  id: '/signup',
  path: '/signup',
  getParentRoute: () => authLayoutRoute,
} as any);

const authLayoutResetlinkRoute = authLayoutResetlinkImport.update({
  id: '/resetlink',
  path: '/resetlink',
  getParentRoute: () => authLayoutRoute,
} as any);

const authLayoutLoginRoute = authLayoutLoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => authLayoutRoute,
} as any);

const authLayoutCheckEmailRoute = authLayoutCheckEmailImport.update({
  id: '/check-email',
  path: '/check-email',
  getParentRoute: () => authLayoutRoute,
} as any);

const mainLayoutSettingsIndexRoute = mainLayoutSettingsIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => mainLayoutSettingsRoute,
} as any);

const mainLayoutWorkspaceWsIdRoute = mainLayoutWorkspaceWsIdImport.update({
  id: '/workspace/$wsId',
  path: '/workspace/$wsId',
  getParentRoute: () => mainLayoutRoute,
} as any);

const mainLayoutUserNotificationRoute = mainLayoutUserNotificationImport.update({
  id: '/user/notification',
  path: '/user/notification',
  getParentRoute: () => mainLayoutRoute,
} as any);

const mainLayoutUserAccountRoute = mainLayoutUserAccountImport.update({
  id: '/user/account',
  path: '/user/account',
  getParentRoute: () => mainLayoutRoute,
} as any);

const mainLayoutSettingsWorkItemTypeRoute = mainLayoutSettingsWorkItemTypeImport.update({
  id: '/work-item-type',
  path: '/work-item-type',
  getParentRoute: () => mainLayoutSettingsRoute,
} as any);

const mainLayoutSettingsWorkFlowRoute = mainLayoutSettingsWorkFlowImport.update({
  id: '/work-flow',
  path: '/work-flow',
  getParentRoute: () => mainLayoutSettingsRoute,
} as any);

const mainLayoutSettingsStatusTypeRoute = mainLayoutSettingsStatusTypeImport.update({
  id: '/status-type',
  path: '/status-type',
  getParentRoute: () => mainLayoutSettingsRoute,
} as any);

const mainLayoutSettingsStatusCategoriesRoute = mainLayoutSettingsStatusCategoriesImport.update({
  id: '/status-categories',
  path: '/status-categories',
  getParentRoute: () => mainLayoutSettingsRoute,
} as any);

const mainLayoutSettingsStatusRoute = mainLayoutSettingsStatusImport.update({
  id: '/status',
  path: '/status',
  getParentRoute: () => mainLayoutSettingsRoute,
} as any);

const mainLayoutSettingsRoleRoute = mainLayoutSettingsRoleImport.update({
  id: '/role',
  path: '/role',
  getParentRoute: () => mainLayoutSettingsRoute,
} as any);

const mainLayoutSettingsPriorityRoute = mainLayoutSettingsPriorityImport.update({
  id: '/priority',
  path: '/priority',
  getParentRoute: () => mainLayoutSettingsRoute,
} as any);

const mainLayoutProjectProjIdRoute = mainLayoutProjectProjIdImport.update({
  id: '/project/$projId',
  path: '/project/$projId',
  getParentRoute: () => mainLayoutRoute,
} as any);

const authLayoutVerifyEmailTokenRoute = authLayoutVerifyEmailTokenImport.update({
  id: '/verifyEmail/$token',
  path: '/verifyEmail/$token',
  getParentRoute: () => authLayoutRoute,
} as any);

const authLayoutResetTokenRoute = authLayoutResetTokenImport.update({
  id: '/reset/$token',
  path: '/reset/$token',
  getParentRoute: () => authLayoutRoute,
} as any);

const authLayoutInvitedTokenRoute = authLayoutInvitedTokenImport.update({
  id: '/invited/$token',
  path: '/invited/$token',
  getParentRoute: () => authLayoutRoute,
} as any);

const mainLayoutWorkspaceUsersWsIdRoute = mainLayoutWorkspaceUsersWsIdImport.update({
  id: '/workspace/users/$wsId',
  path: '/workspace/users/$wsId',
  getParentRoute: () => mainLayoutRoute,
} as any);

const mainLayoutWorkspaceProjectsWsIdRoute = mainLayoutWorkspaceProjectsWsIdImport.update({
  id: '/workspace/projects/$wsId',
  path: '/workspace/projects/$wsId',
  getParentRoute: () => mainLayoutRoute,
} as any);

const mainLayoutWorkspaceOverviewWsIdRoute = mainLayoutWorkspaceOverviewWsIdImport.update({
  id: '/workspace/overview/$wsId',
  path: '/workspace/overview/$wsId',
  getParentRoute: () => mainLayoutRoute,
} as any);

const mainLayoutSettingsWorkFlowCreateRoute = mainLayoutSettingsWorkFlowCreateImport.update({
  id: '/create',
  path: '/create',
  getParentRoute: () => mainLayoutSettingsWorkFlowRoute,
} as any);

const mainLayoutProjectUsersProjIdRoute = mainLayoutProjectUsersProjIdImport.update({
  id: '/project/users/$projId',
  path: '/project/users/$projId',
  getParentRoute: () => mainLayoutRoute,
} as any);

const mainLayoutProjectTestPlansProjIdRoute = mainLayoutProjectTestPlansProjIdImport.update({
  id: '/project/testPlans/$projId',
  path: '/project/testPlans/$projId',
  getParentRoute: () => mainLayoutRoute,
} as any);

const mainLayoutProjectGroupsProjIdRoute = mainLayoutProjectGroupsProjIdImport.update({
  id: '/project/groups/$projId',
  path: '/project/groups/$projId',
  getParentRoute: () => mainLayoutRoute,
} as any);

const mainLayoutSettingsWorkFlowWorkflowIdEditRoute =
  mainLayoutSettingsWorkFlowWorkflowIdEditImport.update({
    id: '/$workflowId/edit',
    path: '/$workflowId/edit',
    getParentRoute: () => mainLayoutSettingsWorkFlowRoute,
  } as any);

const mainLayoutSettingsWorkFlowWorkflowIdDesignRoute =
  mainLayoutSettingsWorkFlowWorkflowIdDesignImport.update({
    id: '/$workflowId/design',
    path: '/$workflowId/design',
    getParentRoute: () => mainLayoutSettingsWorkFlowRoute,
  } as any);

const mainLayoutProjectTestPlansDetailTpIdRoute = mainLayoutProjectTestPlansDetailTpIdImport.update(
  {
    id: '/project/testPlans/detail/$tpId',
    path: '/project/testPlans/detail/$tpId',
    getParentRoute: () => mainLayoutRoute,
  } as any,
);

const mainLayoutProjectBoardsWorkItemsProjIdRoute =
  mainLayoutProjectBoardsWorkItemsProjIdImport.update({
    id: '/project/boards/workItems/$projId',
    path: '/project/boards/workItems/$projId',
    getParentRoute: () => mainLayoutRoute,
  } as any);

const mainLayoutProjectBoardsWorkItemWorkItemIdRoute =
  mainLayoutProjectBoardsWorkItemWorkItemIdImport.update({
    id: '/project/boards/workItem/$workItemId',
    path: '/project/boards/workItem/$workItemId',
    getParentRoute: () => mainLayoutRoute,
  } as any);

const mainLayoutProjectBoardsSprintsProjIdRoute = mainLayoutProjectBoardsSprintsProjIdImport.update(
  {
    id: '/project/boards/sprints/$projId',
    path: '/project/boards/sprints/$projId',
    getParentRoute: () => mainLayoutRoute,
  } as any,
);

const mainLayoutProjectBoardsAgileBoardProjIdRoute =
  mainLayoutProjectBoardsAgileBoardProjIdImport.update({
    id: '/project/boards/agileBoard/$projId',
    path: '/project/boards/agileBoard/$projId',
    getParentRoute: () => mainLayoutRoute,
  } as any);

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/__authLayout': {
      id: '/__authLayout';
      path: '';
      fullPath: '';
      preLoaderRoute: typeof authLayoutImport;
      parentRoute: typeof rootRoute;
    };
    '/__mainLayout': {
      id: '/__mainLayout';
      path: '';
      fullPath: '';
      preLoaderRoute: typeof mainLayoutImport;
      parentRoute: typeof rootRoute;
    };
    '/__authLayout/check-email': {
      id: '/__authLayout/check-email';
      path: '/check-email';
      fullPath: '/check-email';
      preLoaderRoute: typeof authLayoutCheckEmailImport;
      parentRoute: typeof authLayoutImport;
    };
    '/__authLayout/login': {
      id: '/__authLayout/login';
      path: '/login';
      fullPath: '/login';
      preLoaderRoute: typeof authLayoutLoginImport;
      parentRoute: typeof authLayoutImport;
    };
    '/__authLayout/resetlink': {
      id: '/__authLayout/resetlink';
      path: '/resetlink';
      fullPath: '/resetlink';
      preLoaderRoute: typeof authLayoutResetlinkImport;
      parentRoute: typeof authLayoutImport;
    };
    '/__authLayout/signup': {
      id: '/__authLayout/signup';
      path: '/signup';
      fullPath: '/signup';
      preLoaderRoute: typeof authLayoutSignupImport;
      parentRoute: typeof authLayoutImport;
    };
    '/__authLayout/workflow': {
      id: '/__authLayout/workflow';
      path: '/workflow';
      fullPath: '/workflow';
      preLoaderRoute: typeof authLayoutWorkflowImport;
      parentRoute: typeof authLayoutImport;
    };
    '/__mainLayout/dashboard': {
      id: '/__mainLayout/dashboard';
      path: '/dashboard';
      fullPath: '/dashboard';
      preLoaderRoute: typeof mainLayoutDashboardImport;
      parentRoute: typeof mainLayoutImport;
    };
    '/__mainLayout/organization-create': {
      id: '/__mainLayout/organization-create';
      path: '/organization-create';
      fullPath: '/organization-create';
      preLoaderRoute: typeof mainLayoutOrganizationCreateImport;
      parentRoute: typeof mainLayoutImport;
    };
    '/__mainLayout/organization-invitations': {
      id: '/__mainLayout/organization-invitations';
      path: '/organization-invitations';
      fullPath: '/organization-invitations';
      preLoaderRoute: typeof mainLayoutOrganizationInvitationsImport;
      parentRoute: typeof mainLayoutImport;
    };
    '/__mainLayout/project-create-form': {
      id: '/__mainLayout/project-create-form';
      path: '/project-create-form';
      fullPath: '/project-create-form';
      preLoaderRoute: typeof mainLayoutProjectCreateFormImport;
      parentRoute: typeof mainLayoutImport;
    };
    '/__mainLayout/settings': {
      id: '/__mainLayout/settings';
      path: '/settings';
      fullPath: '/settings';
      preLoaderRoute: typeof mainLayoutSettingsImport;
      parentRoute: typeof mainLayoutImport;
    };
    '/__mainLayout/tiptap': {
      id: '/__mainLayout/tiptap';
      path: '/tiptap';
      fullPath: '/tiptap';
      preLoaderRoute: typeof mainLayoutTiptapImport;
      parentRoute: typeof mainLayoutImport;
    };
    '/__mainLayout/workspace-create-form': {
      id: '/__mainLayout/workspace-create-form';
      path: '/workspace-create-form';
      fullPath: '/workspace-create-form';
      preLoaderRoute: typeof mainLayoutWorkspaceCreateFormImport;
      parentRoute: typeof mainLayoutImport;
    };
    '/__mainLayout/': {
      id: '/__mainLayout/';
      path: '/';
      fullPath: '/';
      preLoaderRoute: typeof mainLayoutIndexImport;
      parentRoute: typeof mainLayoutImport;
    };
    '/__authLayout/invited/$token': {
      id: '/__authLayout/invited/$token';
      path: '/invited/$token';
      fullPath: '/invited/$token';
      preLoaderRoute: typeof authLayoutInvitedTokenImport;
      parentRoute: typeof authLayoutImport;
    };
    '/__authLayout/reset/$token': {
      id: '/__authLayout/reset/$token';
      path: '/reset/$token';
      fullPath: '/reset/$token';
      preLoaderRoute: typeof authLayoutResetTokenImport;
      parentRoute: typeof authLayoutImport;
    };
    '/__authLayout/verifyEmail/$token': {
      id: '/__authLayout/verifyEmail/$token';
      path: '/verifyEmail/$token';
      fullPath: '/verifyEmail/$token';
      preLoaderRoute: typeof authLayoutVerifyEmailTokenImport;
      parentRoute: typeof authLayoutImport;
    };
    '/__mainLayout/project/$projId': {
      id: '/__mainLayout/project/$projId';
      path: '/project/$projId';
      fullPath: '/project/$projId';
      preLoaderRoute: typeof mainLayoutProjectProjIdImport;
      parentRoute: typeof mainLayoutImport;
    };
    '/__mainLayout/settings/priority': {
      id: '/__mainLayout/settings/priority';
      path: '/priority';
      fullPath: '/settings/priority';
      preLoaderRoute: typeof mainLayoutSettingsPriorityImport;
      parentRoute: typeof mainLayoutSettingsImport;
    };
    '/__mainLayout/settings/role': {
      id: '/__mainLayout/settings/role';
      path: '/role';
      fullPath: '/settings/role';
      preLoaderRoute: typeof mainLayoutSettingsRoleImport;
      parentRoute: typeof mainLayoutSettingsImport;
    };
    '/__mainLayout/settings/status': {
      id: '/__mainLayout/settings/status';
      path: '/status';
      fullPath: '/settings/status';
      preLoaderRoute: typeof mainLayoutSettingsStatusImport;
      parentRoute: typeof mainLayoutSettingsImport;
    };
    '/__mainLayout/settings/status-categories': {
      id: '/__mainLayout/settings/status-categories';
      path: '/status-categories';
      fullPath: '/settings/status-categories';
      preLoaderRoute: typeof mainLayoutSettingsStatusCategoriesImport;
      parentRoute: typeof mainLayoutSettingsImport;
    };
    '/__mainLayout/settings/status-type': {
      id: '/__mainLayout/settings/status-type';
      path: '/status-type';
      fullPath: '/settings/status-type';
      preLoaderRoute: typeof mainLayoutSettingsStatusTypeImport;
      parentRoute: typeof mainLayoutSettingsImport;
    };
    '/__mainLayout/settings/work-flow': {
      id: '/__mainLayout/settings/work-flow';
      path: '/work-flow';
      fullPath: '/settings/work-flow';
      preLoaderRoute: typeof mainLayoutSettingsWorkFlowImport;
      parentRoute: typeof mainLayoutSettingsImport;
    };
    '/__mainLayout/settings/work-item-type': {
      id: '/__mainLayout/settings/work-item-type';
      path: '/work-item-type';
      fullPath: '/settings/work-item-type';
      preLoaderRoute: typeof mainLayoutSettingsWorkItemTypeImport;
      parentRoute: typeof mainLayoutSettingsImport;
    };
    '/__mainLayout/user/account': {
      id: '/__mainLayout/user/account';
      path: '/user/account';
      fullPath: '/user/account';
      preLoaderRoute: typeof mainLayoutUserAccountImport;
      parentRoute: typeof mainLayoutImport;
    };
    '/__mainLayout/user/notification': {
      id: '/__mainLayout/user/notification';
      path: '/user/notification';
      fullPath: '/user/notification';
      preLoaderRoute: typeof mainLayoutUserNotificationImport;
      parentRoute: typeof mainLayoutImport;
    };
    '/__mainLayout/workspace/$wsId': {
      id: '/__mainLayout/workspace/$wsId';
      path: '/workspace/$wsId';
      fullPath: '/workspace/$wsId';
      preLoaderRoute: typeof mainLayoutWorkspaceWsIdImport;
      parentRoute: typeof mainLayoutImport;
    };
    '/__mainLayout/settings/': {
      id: '/__mainLayout/settings/';
      path: '/';
      fullPath: '/settings/';
      preLoaderRoute: typeof mainLayoutSettingsIndexImport;
      parentRoute: typeof mainLayoutSettingsImport;
    };
    '/__mainLayout/project/groups/$projId': {
      id: '/__mainLayout/project/groups/$projId';
      path: '/project/groups/$projId';
      fullPath: '/project/groups/$projId';
      preLoaderRoute: typeof mainLayoutProjectGroupsProjIdImport;
      parentRoute: typeof mainLayoutImport;
    };
    '/__mainLayout/project/testPlans/$projId': {
      id: '/__mainLayout/project/testPlans/$projId';
      path: '/project/testPlans/$projId';
      fullPath: '/project/testPlans/$projId';
      preLoaderRoute: typeof mainLayoutProjectTestPlansProjIdImport;
      parentRoute: typeof mainLayoutImport;
    };
    '/__mainLayout/project/users/$projId': {
      id: '/__mainLayout/project/users/$projId';
      path: '/project/users/$projId';
      fullPath: '/project/users/$projId';
      preLoaderRoute: typeof mainLayoutProjectUsersProjIdImport;
      parentRoute: typeof mainLayoutImport;
    };
    '/__mainLayout/settings/work-flow/create': {
      id: '/__mainLayout/settings/work-flow/create';
      path: '/create';
      fullPath: '/settings/work-flow/create';
      preLoaderRoute: typeof mainLayoutSettingsWorkFlowCreateImport;
      parentRoute: typeof mainLayoutSettingsWorkFlowImport;
    };
    '/__mainLayout/workspace/overview/$wsId': {
      id: '/__mainLayout/workspace/overview/$wsId';
      path: '/workspace/overview/$wsId';
      fullPath: '/workspace/overview/$wsId';
      preLoaderRoute: typeof mainLayoutWorkspaceOverviewWsIdImport;
      parentRoute: typeof mainLayoutImport;
    };
    '/__mainLayout/workspace/projects/$wsId': {
      id: '/__mainLayout/workspace/projects/$wsId';
      path: '/workspace/projects/$wsId';
      fullPath: '/workspace/projects/$wsId';
      preLoaderRoute: typeof mainLayoutWorkspaceProjectsWsIdImport;
      parentRoute: typeof mainLayoutImport;
    };
    '/__mainLayout/workspace/users/$wsId': {
      id: '/__mainLayout/workspace/users/$wsId';
      path: '/workspace/users/$wsId';
      fullPath: '/workspace/users/$wsId';
      preLoaderRoute: typeof mainLayoutWorkspaceUsersWsIdImport;
      parentRoute: typeof mainLayoutImport;
    };
    '/__mainLayout/project/boards/agileBoard/$projId': {
      id: '/__mainLayout/project/boards/agileBoard/$projId';
      path: '/project/boards/agileBoard/$projId';
      fullPath: '/project/boards/agileBoard/$projId';
      preLoaderRoute: typeof mainLayoutProjectBoardsAgileBoardProjIdImport;
      parentRoute: typeof mainLayoutImport;
    };
    '/__mainLayout/project/boards/sprints/$projId': {
      id: '/__mainLayout/project/boards/sprints/$projId';
      path: '/project/boards/sprints/$projId';
      fullPath: '/project/boards/sprints/$projId';
      preLoaderRoute: typeof mainLayoutProjectBoardsSprintsProjIdImport;
      parentRoute: typeof mainLayoutImport;
    };
    '/__mainLayout/project/boards/workItem/$workItemId': {
      id: '/__mainLayout/project/boards/workItem/$workItemId';
      path: '/project/boards/workItem/$workItemId';
      fullPath: '/project/boards/workItem/$workItemId';
      preLoaderRoute: typeof mainLayoutProjectBoardsWorkItemWorkItemIdImport;
      parentRoute: typeof mainLayoutImport;
    };
    '/__mainLayout/project/boards/workItems/$projId': {
      id: '/__mainLayout/project/boards/workItems/$projId';
      path: '/project/boards/workItems/$projId';
      fullPath: '/project/boards/workItems/$projId';
      preLoaderRoute: typeof mainLayoutProjectBoardsWorkItemsProjIdImport;
      parentRoute: typeof mainLayoutImport;
    };
    '/__mainLayout/project/testPlans/detail/$tpId': {
      id: '/__mainLayout/project/testPlans/detail/$tpId';
      path: '/project/testPlans/detail/$tpId';
      fullPath: '/project/testPlans/detail/$tpId';
      preLoaderRoute: typeof mainLayoutProjectTestPlansDetailTpIdImport;
      parentRoute: typeof mainLayoutImport;
    };
    '/__mainLayout/settings/work-flow/$workflowId/design': {
      id: '/__mainLayout/settings/work-flow/$workflowId/design';
      path: '/$workflowId/design';
      fullPath: '/settings/work-flow/$workflowId/design';
      preLoaderRoute: typeof mainLayoutSettingsWorkFlowWorkflowIdDesignImport;
      parentRoute: typeof mainLayoutSettingsWorkFlowImport;
    };
    '/__mainLayout/settings/work-flow/$workflowId/edit': {
      id: '/__mainLayout/settings/work-flow/$workflowId/edit';
      path: '/$workflowId/edit';
      fullPath: '/settings/work-flow/$workflowId/edit';
      preLoaderRoute: typeof mainLayoutSettingsWorkFlowWorkflowIdEditImport;
      parentRoute: typeof mainLayoutSettingsWorkFlowImport;
    };
  }
}

// Create and export the route tree

interface authLayoutRouteChildren {
  authLayoutCheckEmailRoute: typeof authLayoutCheckEmailRoute;
  authLayoutLoginRoute: typeof authLayoutLoginRoute;
  authLayoutResetlinkRoute: typeof authLayoutResetlinkRoute;
  authLayoutSignupRoute: typeof authLayoutSignupRoute;
  authLayoutWorkflowRoute: typeof authLayoutWorkflowRoute;
  authLayoutInvitedTokenRoute: typeof authLayoutInvitedTokenRoute;
  authLayoutResetTokenRoute: typeof authLayoutResetTokenRoute;
  authLayoutVerifyEmailTokenRoute: typeof authLayoutVerifyEmailTokenRoute;
}

const authLayoutRouteChildren: authLayoutRouteChildren = {
  authLayoutCheckEmailRoute,
  authLayoutLoginRoute,
  authLayoutResetlinkRoute,
  authLayoutSignupRoute,
  authLayoutWorkflowRoute,
  authLayoutInvitedTokenRoute,
  authLayoutResetTokenRoute,
  authLayoutVerifyEmailTokenRoute,
};

const authLayoutRouteWithChildren = authLayoutRoute._addFileChildren(authLayoutRouteChildren);

interface mainLayoutSettingsWorkFlowRouteChildren {
  mainLayoutSettingsWorkFlowCreateRoute: typeof mainLayoutSettingsWorkFlowCreateRoute;
  mainLayoutSettingsWorkFlowWorkflowIdDesignRoute: typeof mainLayoutSettingsWorkFlowWorkflowIdDesignRoute;
  mainLayoutSettingsWorkFlowWorkflowIdEditRoute: typeof mainLayoutSettingsWorkFlowWorkflowIdEditRoute;
}

const mainLayoutSettingsWorkFlowRouteChildren: mainLayoutSettingsWorkFlowRouteChildren = {
  mainLayoutSettingsWorkFlowCreateRoute,
  mainLayoutSettingsWorkFlowWorkflowIdDesignRoute,
  mainLayoutSettingsWorkFlowWorkflowIdEditRoute,
};

const mainLayoutSettingsWorkFlowRouteWithChildren =
  mainLayoutSettingsWorkFlowRoute._addFileChildren(mainLayoutSettingsWorkFlowRouteChildren);

interface mainLayoutSettingsRouteChildren {
  mainLayoutSettingsPriorityRoute: typeof mainLayoutSettingsPriorityRoute;
  mainLayoutSettingsRoleRoute: typeof mainLayoutSettingsRoleRoute;
  mainLayoutSettingsStatusRoute: typeof mainLayoutSettingsStatusRoute;
  mainLayoutSettingsStatusCategoriesRoute: typeof mainLayoutSettingsStatusCategoriesRoute;
  mainLayoutSettingsStatusTypeRoute: typeof mainLayoutSettingsStatusTypeRoute;
  mainLayoutSettingsWorkFlowRoute: typeof mainLayoutSettingsWorkFlowRouteWithChildren;
  mainLayoutSettingsWorkItemTypeRoute: typeof mainLayoutSettingsWorkItemTypeRoute;
  mainLayoutSettingsIndexRoute: typeof mainLayoutSettingsIndexRoute;
}

const mainLayoutSettingsRouteChildren: mainLayoutSettingsRouteChildren = {
  mainLayoutSettingsPriorityRoute,
  mainLayoutSettingsRoleRoute,
  mainLayoutSettingsStatusRoute,
  mainLayoutSettingsStatusCategoriesRoute,
  mainLayoutSettingsStatusTypeRoute,
  mainLayoutSettingsWorkFlowRoute: mainLayoutSettingsWorkFlowRouteWithChildren,
  mainLayoutSettingsWorkItemTypeRoute,
  mainLayoutSettingsIndexRoute,
};

const mainLayoutSettingsRouteWithChildren = mainLayoutSettingsRoute._addFileChildren(
  mainLayoutSettingsRouteChildren,
);

interface mainLayoutRouteChildren {
  mainLayoutDashboardRoute: typeof mainLayoutDashboardRoute;
  mainLayoutOrganizationCreateRoute: typeof mainLayoutOrganizationCreateRoute;
  mainLayoutOrganizationInvitationsRoute: typeof mainLayoutOrganizationInvitationsRoute;
  mainLayoutProjectCreateFormRoute: typeof mainLayoutProjectCreateFormRoute;
  mainLayoutSettingsRoute: typeof mainLayoutSettingsRouteWithChildren;
  mainLayoutTiptapRoute: typeof mainLayoutTiptapRoute;
  mainLayoutWorkspaceCreateFormRoute: typeof mainLayoutWorkspaceCreateFormRoute;
  mainLayoutIndexRoute: typeof mainLayoutIndexRoute;
  mainLayoutProjectProjIdRoute: typeof mainLayoutProjectProjIdRoute;
  mainLayoutUserAccountRoute: typeof mainLayoutUserAccountRoute;
  mainLayoutUserNotificationRoute: typeof mainLayoutUserNotificationRoute;
  mainLayoutWorkspaceWsIdRoute: typeof mainLayoutWorkspaceWsIdRoute;
  mainLayoutProjectGroupsProjIdRoute: typeof mainLayoutProjectGroupsProjIdRoute;
  mainLayoutProjectTestPlansProjIdRoute: typeof mainLayoutProjectTestPlansProjIdRoute;
  mainLayoutProjectUsersProjIdRoute: typeof mainLayoutProjectUsersProjIdRoute;
  mainLayoutWorkspaceOverviewWsIdRoute: typeof mainLayoutWorkspaceOverviewWsIdRoute;
  mainLayoutWorkspaceProjectsWsIdRoute: typeof mainLayoutWorkspaceProjectsWsIdRoute;
  mainLayoutWorkspaceUsersWsIdRoute: typeof mainLayoutWorkspaceUsersWsIdRoute;
  mainLayoutProjectBoardsAgileBoardProjIdRoute: typeof mainLayoutProjectBoardsAgileBoardProjIdRoute;
  mainLayoutProjectBoardsSprintsProjIdRoute: typeof mainLayoutProjectBoardsSprintsProjIdRoute;
  mainLayoutProjectBoardsWorkItemWorkItemIdRoute: typeof mainLayoutProjectBoardsWorkItemWorkItemIdRoute;
  mainLayoutProjectBoardsWorkItemsProjIdRoute: typeof mainLayoutProjectBoardsWorkItemsProjIdRoute;
  mainLayoutProjectTestPlansDetailTpIdRoute: typeof mainLayoutProjectTestPlansDetailTpIdRoute;
}

const mainLayoutRouteChildren: mainLayoutRouteChildren = {
  mainLayoutDashboardRoute,
  mainLayoutOrganizationCreateRoute,
  mainLayoutOrganizationInvitationsRoute,
  mainLayoutProjectCreateFormRoute,
  mainLayoutSettingsRoute: mainLayoutSettingsRouteWithChildren,
  mainLayoutTiptapRoute,
  mainLayoutWorkspaceCreateFormRoute,
  mainLayoutIndexRoute,
  mainLayoutProjectProjIdRoute,
  mainLayoutUserAccountRoute,
  mainLayoutUserNotificationRoute,
  mainLayoutWorkspaceWsIdRoute,
  mainLayoutProjectGroupsProjIdRoute,
  mainLayoutProjectTestPlansProjIdRoute,
  mainLayoutProjectUsersProjIdRoute,
  mainLayoutWorkspaceOverviewWsIdRoute,
  mainLayoutWorkspaceProjectsWsIdRoute,
  mainLayoutWorkspaceUsersWsIdRoute,
  mainLayoutProjectBoardsAgileBoardProjIdRoute,
  mainLayoutProjectBoardsSprintsProjIdRoute,
  mainLayoutProjectBoardsWorkItemWorkItemIdRoute,
  mainLayoutProjectBoardsWorkItemsProjIdRoute,
  mainLayoutProjectTestPlansDetailTpIdRoute,
};

const mainLayoutRouteWithChildren = mainLayoutRoute._addFileChildren(mainLayoutRouteChildren);

export interface FileRoutesByFullPath {
  '': typeof mainLayoutRouteWithChildren;
  '/check-email': typeof authLayoutCheckEmailRoute;
  '/login': typeof authLayoutLoginRoute;
  '/resetlink': typeof authLayoutResetlinkRoute;
  '/signup': typeof authLayoutSignupRoute;
  '/workflow': typeof authLayoutWorkflowRoute;
  '/dashboard': typeof mainLayoutDashboardRoute;
  '/organization-create': typeof mainLayoutOrganizationCreateRoute;
  '/organization-invitations': typeof mainLayoutOrganizationInvitationsRoute;
  '/project-create-form': typeof mainLayoutProjectCreateFormRoute;
  '/settings': typeof mainLayoutSettingsRouteWithChildren;
  '/tiptap': typeof mainLayoutTiptapRoute;
  '/workspace-create-form': typeof mainLayoutWorkspaceCreateFormRoute;
  '/': typeof mainLayoutIndexRoute;
  '/invited/$token': typeof authLayoutInvitedTokenRoute;
  '/reset/$token': typeof authLayoutResetTokenRoute;
  '/verifyEmail/$token': typeof authLayoutVerifyEmailTokenRoute;
  '/project/$projId': typeof mainLayoutProjectProjIdRoute;
  '/settings/priority': typeof mainLayoutSettingsPriorityRoute;
  '/settings/role': typeof mainLayoutSettingsRoleRoute;
  '/settings/status': typeof mainLayoutSettingsStatusRoute;
  '/settings/status-categories': typeof mainLayoutSettingsStatusCategoriesRoute;
  '/settings/status-type': typeof mainLayoutSettingsStatusTypeRoute;
  '/settings/work-flow': typeof mainLayoutSettingsWorkFlowRouteWithChildren;
  '/settings/work-item-type': typeof mainLayoutSettingsWorkItemTypeRoute;
  '/user/account': typeof mainLayoutUserAccountRoute;
  '/user/notification': typeof mainLayoutUserNotificationRoute;
  '/workspace/$wsId': typeof mainLayoutWorkspaceWsIdRoute;
  '/settings/': typeof mainLayoutSettingsIndexRoute;
  '/project/groups/$projId': typeof mainLayoutProjectGroupsProjIdRoute;
  '/project/testPlans/$projId': typeof mainLayoutProjectTestPlansProjIdRoute;
  '/project/users/$projId': typeof mainLayoutProjectUsersProjIdRoute;
  '/settings/work-flow/create': typeof mainLayoutSettingsWorkFlowCreateRoute;
  '/workspace/overview/$wsId': typeof mainLayoutWorkspaceOverviewWsIdRoute;
  '/workspace/projects/$wsId': typeof mainLayoutWorkspaceProjectsWsIdRoute;
  '/workspace/users/$wsId': typeof mainLayoutWorkspaceUsersWsIdRoute;
  '/project/boards/agileBoard/$projId': typeof mainLayoutProjectBoardsAgileBoardProjIdRoute;
  '/project/boards/sprints/$projId': typeof mainLayoutProjectBoardsSprintsProjIdRoute;
  '/project/boards/workItem/$workItemId': typeof mainLayoutProjectBoardsWorkItemWorkItemIdRoute;
  '/project/boards/workItems/$projId': typeof mainLayoutProjectBoardsWorkItemsProjIdRoute;
  '/project/testPlans/detail/$tpId': typeof mainLayoutProjectTestPlansDetailTpIdRoute;
  '/settings/work-flow/$workflowId/design': typeof mainLayoutSettingsWorkFlowWorkflowIdDesignRoute;
  '/settings/work-flow/$workflowId/edit': typeof mainLayoutSettingsWorkFlowWorkflowIdEditRoute;
}

export interface FileRoutesByTo {
  '': typeof authLayoutRouteWithChildren;
  '/check-email': typeof authLayoutCheckEmailRoute;
  '/login': typeof authLayoutLoginRoute;
  '/resetlink': typeof authLayoutResetlinkRoute;
  '/signup': typeof authLayoutSignupRoute;
  '/workflow': typeof authLayoutWorkflowRoute;
  '/dashboard': typeof mainLayoutDashboardRoute;
  '/organization-create': typeof mainLayoutOrganizationCreateRoute;
  '/organization-invitations': typeof mainLayoutOrganizationInvitationsRoute;
  '/project-create-form': typeof mainLayoutProjectCreateFormRoute;
  '/tiptap': typeof mainLayoutTiptapRoute;
  '/workspace-create-form': typeof mainLayoutWorkspaceCreateFormRoute;
  '/': typeof mainLayoutIndexRoute;
  '/invited/$token': typeof authLayoutInvitedTokenRoute;
  '/reset/$token': typeof authLayoutResetTokenRoute;
  '/verifyEmail/$token': typeof authLayoutVerifyEmailTokenRoute;
  '/project/$projId': typeof mainLayoutProjectProjIdRoute;
  '/settings/priority': typeof mainLayoutSettingsPriorityRoute;
  '/settings/role': typeof mainLayoutSettingsRoleRoute;
  '/settings/status': typeof mainLayoutSettingsStatusRoute;
  '/settings/status-categories': typeof mainLayoutSettingsStatusCategoriesRoute;
  '/settings/status-type': typeof mainLayoutSettingsStatusTypeRoute;
  '/settings/work-flow': typeof mainLayoutSettingsWorkFlowRouteWithChildren;
  '/settings/work-item-type': typeof mainLayoutSettingsWorkItemTypeRoute;
  '/user/account': typeof mainLayoutUserAccountRoute;
  '/user/notification': typeof mainLayoutUserNotificationRoute;
  '/workspace/$wsId': typeof mainLayoutWorkspaceWsIdRoute;
  '/settings': typeof mainLayoutSettingsIndexRoute;
  '/project/groups/$projId': typeof mainLayoutProjectGroupsProjIdRoute;
  '/project/testPlans/$projId': typeof mainLayoutProjectTestPlansProjIdRoute;
  '/project/users/$projId': typeof mainLayoutProjectUsersProjIdRoute;
  '/settings/work-flow/create': typeof mainLayoutSettingsWorkFlowCreateRoute;
  '/workspace/overview/$wsId': typeof mainLayoutWorkspaceOverviewWsIdRoute;
  '/workspace/projects/$wsId': typeof mainLayoutWorkspaceProjectsWsIdRoute;
  '/workspace/users/$wsId': typeof mainLayoutWorkspaceUsersWsIdRoute;
  '/project/boards/agileBoard/$projId': typeof mainLayoutProjectBoardsAgileBoardProjIdRoute;
  '/project/boards/sprints/$projId': typeof mainLayoutProjectBoardsSprintsProjIdRoute;
  '/project/boards/workItem/$workItemId': typeof mainLayoutProjectBoardsWorkItemWorkItemIdRoute;
  '/project/boards/workItems/$projId': typeof mainLayoutProjectBoardsWorkItemsProjIdRoute;
  '/project/testPlans/detail/$tpId': typeof mainLayoutProjectTestPlansDetailTpIdRoute;
  '/settings/work-flow/$workflowId/design': typeof mainLayoutSettingsWorkFlowWorkflowIdDesignRoute;
  '/settings/work-flow/$workflowId/edit': typeof mainLayoutSettingsWorkFlowWorkflowIdEditRoute;
}

export interface FileRoutesById {
  __root__: typeof rootRoute;
  '/__authLayout': typeof authLayoutRouteWithChildren;
  '/__mainLayout': typeof mainLayoutRouteWithChildren;
  '/__authLayout/check-email': typeof authLayoutCheckEmailRoute;
  '/__authLayout/login': typeof authLayoutLoginRoute;
  '/__authLayout/resetlink': typeof authLayoutResetlinkRoute;
  '/__authLayout/signup': typeof authLayoutSignupRoute;
  '/__authLayout/workflow': typeof authLayoutWorkflowRoute;
  '/__mainLayout/dashboard': typeof mainLayoutDashboardRoute;
  '/__mainLayout/organization-create': typeof mainLayoutOrganizationCreateRoute;
  '/__mainLayout/organization-invitations': typeof mainLayoutOrganizationInvitationsRoute;
  '/__mainLayout/project-create-form': typeof mainLayoutProjectCreateFormRoute;
  '/__mainLayout/settings': typeof mainLayoutSettingsRouteWithChildren;
  '/__mainLayout/tiptap': typeof mainLayoutTiptapRoute;
  '/__mainLayout/workspace-create-form': typeof mainLayoutWorkspaceCreateFormRoute;
  '/__mainLayout/': typeof mainLayoutIndexRoute;
  '/__authLayout/invited/$token': typeof authLayoutInvitedTokenRoute;
  '/__authLayout/reset/$token': typeof authLayoutResetTokenRoute;
  '/__authLayout/verifyEmail/$token': typeof authLayoutVerifyEmailTokenRoute;
  '/__mainLayout/project/$projId': typeof mainLayoutProjectProjIdRoute;
  '/__mainLayout/settings/priority': typeof mainLayoutSettingsPriorityRoute;
  '/__mainLayout/settings/role': typeof mainLayoutSettingsRoleRoute;
  '/__mainLayout/settings/status': typeof mainLayoutSettingsStatusRoute;
  '/__mainLayout/settings/status-categories': typeof mainLayoutSettingsStatusCategoriesRoute;
  '/__mainLayout/settings/status-type': typeof mainLayoutSettingsStatusTypeRoute;
  '/__mainLayout/settings/work-flow': typeof mainLayoutSettingsWorkFlowRouteWithChildren;
  '/__mainLayout/settings/work-item-type': typeof mainLayoutSettingsWorkItemTypeRoute;
  '/__mainLayout/user/account': typeof mainLayoutUserAccountRoute;
  '/__mainLayout/user/notification': typeof mainLayoutUserNotificationRoute;
  '/__mainLayout/workspace/$wsId': typeof mainLayoutWorkspaceWsIdRoute;
  '/__mainLayout/settings/': typeof mainLayoutSettingsIndexRoute;
  '/__mainLayout/project/groups/$projId': typeof mainLayoutProjectGroupsProjIdRoute;
  '/__mainLayout/project/testPlans/$projId': typeof mainLayoutProjectTestPlansProjIdRoute;
  '/__mainLayout/project/users/$projId': typeof mainLayoutProjectUsersProjIdRoute;
  '/__mainLayout/settings/work-flow/create': typeof mainLayoutSettingsWorkFlowCreateRoute;
  '/__mainLayout/workspace/overview/$wsId': typeof mainLayoutWorkspaceOverviewWsIdRoute;
  '/__mainLayout/workspace/projects/$wsId': typeof mainLayoutWorkspaceProjectsWsIdRoute;
  '/__mainLayout/workspace/users/$wsId': typeof mainLayoutWorkspaceUsersWsIdRoute;
  '/__mainLayout/project/boards/agileBoard/$projId': typeof mainLayoutProjectBoardsAgileBoardProjIdRoute;
  '/__mainLayout/project/boards/sprints/$projId': typeof mainLayoutProjectBoardsSprintsProjIdRoute;
  '/__mainLayout/project/boards/workItem/$workItemId': typeof mainLayoutProjectBoardsWorkItemWorkItemIdRoute;
  '/__mainLayout/project/boards/workItems/$projId': typeof mainLayoutProjectBoardsWorkItemsProjIdRoute;
  '/__mainLayout/project/testPlans/detail/$tpId': typeof mainLayoutProjectTestPlansDetailTpIdRoute;
  '/__mainLayout/settings/work-flow/$workflowId/design': typeof mainLayoutSettingsWorkFlowWorkflowIdDesignRoute;
  '/__mainLayout/settings/work-flow/$workflowId/edit': typeof mainLayoutSettingsWorkFlowWorkflowIdEditRoute;
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath;
  fullPaths:
    | ''
    | '/check-email'
    | '/login'
    | '/resetlink'
    | '/signup'
    | '/workflow'
    | '/dashboard'
    | '/organization-create'
    | '/organization-invitations'
    | '/project-create-form'
    | '/settings'
    | '/tiptap'
    | '/workspace-create-form'
    | '/'
    | '/invited/$token'
    | '/reset/$token'
    | '/verifyEmail/$token'
    | '/project/$projId'
    | '/settings/priority'
    | '/settings/role'
    | '/settings/status'
    | '/settings/status-categories'
    | '/settings/status-type'
    | '/settings/work-flow'
    | '/settings/work-item-type'
    | '/user/account'
    | '/user/notification'
    | '/workspace/$wsId'
    | '/settings/'
    | '/project/groups/$projId'
    | '/project/testPlans/$projId'
    | '/project/users/$projId'
    | '/settings/work-flow/create'
    | '/workspace/overview/$wsId'
    | '/workspace/projects/$wsId'
    | '/workspace/users/$wsId'
    | '/project/boards/agileBoard/$projId'
    | '/project/boards/sprints/$projId'
    | '/project/boards/workItem/$workItemId'
    | '/project/boards/workItems/$projId'
    | '/project/testPlans/detail/$tpId'
    | '/settings/work-flow/$workflowId/design'
    | '/settings/work-flow/$workflowId/edit';
  fileRoutesByTo: FileRoutesByTo;
  to:
    | ''
    | '/check-email'
    | '/login'
    | '/resetlink'
    | '/signup'
    | '/workflow'
    | '/dashboard'
    | '/organization-create'
    | '/organization-invitations'
    | '/project-create-form'
    | '/tiptap'
    | '/workspace-create-form'
    | '/'
    | '/invited/$token'
    | '/reset/$token'
    | '/verifyEmail/$token'
    | '/project/$projId'
    | '/settings/priority'
    | '/settings/role'
    | '/settings/status'
    | '/settings/status-categories'
    | '/settings/status-type'
    | '/settings/work-flow'
    | '/settings/work-item-type'
    | '/user/account'
    | '/user/notification'
    | '/workspace/$wsId'
    | '/settings'
    | '/project/groups/$projId'
    | '/project/testPlans/$projId'
    | '/project/users/$projId'
    | '/settings/work-flow/create'
    | '/workspace/overview/$wsId'
    | '/workspace/projects/$wsId'
    | '/workspace/users/$wsId'
    | '/project/boards/agileBoard/$projId'
    | '/project/boards/sprints/$projId'
    | '/project/boards/workItem/$workItemId'
    | '/project/boards/workItems/$projId'
    | '/project/testPlans/detail/$tpId'
    | '/settings/work-flow/$workflowId/design'
    | '/settings/work-flow/$workflowId/edit';
  id:
    | '__root__'
    | '/__authLayout'
    | '/__mainLayout'
    | '/__authLayout/check-email'
    | '/__authLayout/login'
    | '/__authLayout/resetlink'
    | '/__authLayout/signup'
    | '/__authLayout/workflow'
    | '/__mainLayout/dashboard'
    | '/__mainLayout/organization-create'
    | '/__mainLayout/organization-invitations'
    | '/__mainLayout/project-create-form'
    | '/__mainLayout/settings'
    | '/__mainLayout/tiptap'
    | '/__mainLayout/workspace-create-form'
    | '/__mainLayout/'
    | '/__authLayout/invited/$token'
    | '/__authLayout/reset/$token'
    | '/__authLayout/verifyEmail/$token'
    | '/__mainLayout/project/$projId'
    | '/__mainLayout/settings/priority'
    | '/__mainLayout/settings/role'
    | '/__mainLayout/settings/status'
    | '/__mainLayout/settings/status-categories'
    | '/__mainLayout/settings/status-type'
    | '/__mainLayout/settings/work-flow'
    | '/__mainLayout/settings/work-item-type'
    | '/__mainLayout/user/account'
    | '/__mainLayout/user/notification'
    | '/__mainLayout/workspace/$wsId'
    | '/__mainLayout/settings/'
    | '/__mainLayout/project/groups/$projId'
    | '/__mainLayout/project/testPlans/$projId'
    | '/__mainLayout/project/users/$projId'
    | '/__mainLayout/settings/work-flow/create'
    | '/__mainLayout/workspace/overview/$wsId'
    | '/__mainLayout/workspace/projects/$wsId'
    | '/__mainLayout/workspace/users/$wsId'
    | '/__mainLayout/project/boards/agileBoard/$projId'
    | '/__mainLayout/project/boards/sprints/$projId'
    | '/__mainLayout/project/boards/workItem/$workItemId'
    | '/__mainLayout/project/boards/workItems/$projId'
    | '/__mainLayout/project/testPlans/detail/$tpId'
    | '/__mainLayout/settings/work-flow/$workflowId/design'
    | '/__mainLayout/settings/work-flow/$workflowId/edit';
  fileRoutesById: FileRoutesById;
}

export interface RootRouteChildren {
  authLayoutRoute: typeof authLayoutRouteWithChildren;
  mainLayoutRoute: typeof mainLayoutRouteWithChildren;
}

const rootRouteChildren: RootRouteChildren = {
  authLayoutRoute: authLayoutRouteWithChildren,
  mainLayoutRoute: mainLayoutRouteWithChildren,
};

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>();

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/__authLayout",
        "/__mainLayout"
      ]
    },
    "/__authLayout": {
      "filePath": "__authLayout.tsx",
      "children": [
        "/__authLayout/check-email",
        "/__authLayout/login",
        "/__authLayout/resetlink",
        "/__authLayout/signup",
        "/__authLayout/workflow",
        "/__authLayout/invited/$token",
        "/__authLayout/reset/$token",
        "/__authLayout/verifyEmail/$token"
      ]
    },
    "/__mainLayout": {
      "filePath": "__mainLayout.tsx",
      "children": [
        "/__mainLayout/dashboard",
        "/__mainLayout/organization-create",
        "/__mainLayout/organization-invitations",
        "/__mainLayout/project-create-form",
        "/__mainLayout/settings",
        "/__mainLayout/tiptap",
        "/__mainLayout/workspace-create-form",
        "/__mainLayout/",
        "/__mainLayout/project/$projId",
        "/__mainLayout/user/account",
        "/__mainLayout/user/notification",
        "/__mainLayout/workspace/$wsId",
        "/__mainLayout/project/groups/$projId",
        "/__mainLayout/project/testPlans/$projId",
        "/__mainLayout/project/users/$projId",
        "/__mainLayout/workspace/overview/$wsId",
        "/__mainLayout/workspace/projects/$wsId",
        "/__mainLayout/workspace/users/$wsId",
        "/__mainLayout/project/boards/agileBoard/$projId",
        "/__mainLayout/project/boards/sprints/$projId",
        "/__mainLayout/project/boards/workItem/$workItemId",
        "/__mainLayout/project/boards/workItems/$projId",
        "/__mainLayout/project/testPlans/detail/$tpId"
      ]
    },
    "/__authLayout/check-email": {
      "filePath": "__authLayout/check-email.tsx",
      "parent": "/__authLayout"
    },
    "/__authLayout/login": {
      "filePath": "__authLayout/login.tsx",
      "parent": "/__authLayout"
    },
    "/__authLayout/resetlink": {
      "filePath": "__authLayout/resetlink.tsx",
      "parent": "/__authLayout"
    },
    "/__authLayout/signup": {
      "filePath": "__authLayout/signup.tsx",
      "parent": "/__authLayout"
    },
    "/__authLayout/workflow": {
      "filePath": "__authLayout/workflow.tsx",
      "parent": "/__authLayout"
    },
    "/__mainLayout/dashboard": {
      "filePath": "__mainLayout/dashboard.tsx",
      "parent": "/__mainLayout"
    },
    "/__mainLayout/organization-create": {
      "filePath": "__mainLayout/organization-create.tsx",
      "parent": "/__mainLayout"
    },
    "/__mainLayout/organization-invitations": {
      "filePath": "__mainLayout/organization-invitations.tsx",
      "parent": "/__mainLayout"
    },
    "/__mainLayout/project-create-form": {
      "filePath": "__mainLayout/project-create-form.tsx",
      "parent": "/__mainLayout"
    },
    "/__mainLayout/settings": {
      "filePath": "__mainLayout/settings.tsx",
      "parent": "/__mainLayout",
      "children": [
        "/__mainLayout/settings/priority",
        "/__mainLayout/settings/role",
        "/__mainLayout/settings/status",
        "/__mainLayout/settings/status-categories",
        "/__mainLayout/settings/status-type",
        "/__mainLayout/settings/work-flow",
        "/__mainLayout/settings/work-item-type",
        "/__mainLayout/settings/"
      ]
    },
    "/__mainLayout/tiptap": {
      "filePath": "__mainLayout/tiptap.tsx",
      "parent": "/__mainLayout"
    },
    "/__mainLayout/workspace-create-form": {
      "filePath": "__mainLayout/workspace-create-form.tsx",
      "parent": "/__mainLayout"
    },
    "/__mainLayout/": {
      "filePath": "__mainLayout/index.tsx",
      "parent": "/__mainLayout"
    },
    "/__authLayout/invited/$token": {
      "filePath": "__authLayout/invited.$token.tsx",
      "parent": "/__authLayout"
    },
    "/__authLayout/reset/$token": {
      "filePath": "__authLayout/reset.$token.tsx",
      "parent": "/__authLayout"
    },
    "/__authLayout/verifyEmail/$token": {
      "filePath": "__authLayout/verifyEmail.$token.tsx",
      "parent": "/__authLayout"
    },
    "/__mainLayout/project/$projId": {
      "filePath": "__mainLayout/project/$projId.tsx",
      "parent": "/__mainLayout"
    },
    "/__mainLayout/settings/priority": {
      "filePath": "__mainLayout/settings/priority.tsx",
      "parent": "/__mainLayout/settings"
    },
    "/__mainLayout/settings/role": {
      "filePath": "__mainLayout/settings/role.tsx",
      "parent": "/__mainLayout/settings"
    },
    "/__mainLayout/settings/status": {
      "filePath": "__mainLayout/settings/status.tsx",
      "parent": "/__mainLayout/settings"
    },
    "/__mainLayout/settings/status-categories": {
      "filePath": "__mainLayout/settings/status-categories.tsx",
      "parent": "/__mainLayout/settings"
    },
    "/__mainLayout/settings/status-type": {
      "filePath": "__mainLayout/settings/status-type.tsx",
      "parent": "/__mainLayout/settings"
    },
    "/__mainLayout/settings/work-flow": {
      "filePath": "__mainLayout/settings/work-flow.tsx",
      "parent": "/__mainLayout/settings",
      "children": [
        "/__mainLayout/settings/work-flow/create",
        "/__mainLayout/settings/work-flow/$workflowId/design",
        "/__mainLayout/settings/work-flow/$workflowId/edit"
      ]
    },
    "/__mainLayout/settings/work-item-type": {
      "filePath": "__mainLayout/settings/work-item-type.tsx",
      "parent": "/__mainLayout/settings"
    },
    "/__mainLayout/user/account": {
      "filePath": "__mainLayout/user/account.tsx",
      "parent": "/__mainLayout"
    },
    "/__mainLayout/user/notification": {
      "filePath": "__mainLayout/user/notification.tsx",
      "parent": "/__mainLayout"
    },
    "/__mainLayout/workspace/$wsId": {
      "filePath": "__mainLayout/workspace/$wsId.tsx",
      "parent": "/__mainLayout"
    },
    "/__mainLayout/settings/": {
      "filePath": "__mainLayout/settings/index.tsx",
      "parent": "/__mainLayout/settings"
    },
    "/__mainLayout/project/groups/$projId": {
      "filePath": "__mainLayout/project/groups.$projId.tsx",
      "parent": "/__mainLayout"
    },
    "/__mainLayout/project/testPlans/$projId": {
      "filePath": "__mainLayout/project/testPlans/$projId.tsx",
      "parent": "/__mainLayout"
    },
    "/__mainLayout/project/users/$projId": {
      "filePath": "__mainLayout/project/users.$projId.tsx",
      "parent": "/__mainLayout"
    },
    "/__mainLayout/settings/work-flow/create": {
      "filePath": "__mainLayout/settings/work-flow/create.tsx",
      "parent": "/__mainLayout/settings/work-flow"
    },
    "/__mainLayout/workspace/overview/$wsId": {
      "filePath": "__mainLayout/workspace/overview.$wsId.tsx",
      "parent": "/__mainLayout"
    },
    "/__mainLayout/workspace/projects/$wsId": {
      "filePath": "__mainLayout/workspace/projects.$wsId.tsx",
      "parent": "/__mainLayout"
    },
    "/__mainLayout/workspace/users/$wsId": {
      "filePath": "__mainLayout/workspace/users.$wsId.tsx",
      "parent": "/__mainLayout"
    },
    "/__mainLayout/project/boards/agileBoard/$projId": {
      "filePath": "__mainLayout/project/boards/agileBoard.$projId.tsx",
      "parent": "/__mainLayout"
    },
    "/__mainLayout/project/boards/sprints/$projId": {
      "filePath": "__mainLayout/project/boards/sprints.$projId.tsx",
      "parent": "/__mainLayout"
    },
    "/__mainLayout/project/boards/workItem/$workItemId": {
      "filePath": "__mainLayout/project/boards/workItem.$workItemId.tsx",
      "parent": "/__mainLayout"
    },
    "/__mainLayout/project/boards/workItems/$projId": {
      "filePath": "__mainLayout/project/boards/workItems.$projId.tsx",
      "parent": "/__mainLayout"
    },
    "/__mainLayout/project/testPlans/detail/$tpId": {
      "filePath": "__mainLayout/project/testPlans/detail.$tpId.tsx",
      "parent": "/__mainLayout"
    },
    "/__mainLayout/settings/work-flow/$workflowId/design": {
      "filePath": "__mainLayout/settings/work-flow/$workflowId.design.tsx",
      "parent": "/__mainLayout/settings/work-flow"
    },
    "/__mainLayout/settings/work-flow/$workflowId/edit": {
      "filePath": "__mainLayout/settings/work-flow/$workflowId.edit.tsx",
      "parent": "/__mainLayout/settings/work-flow"
    }
  }
}
ROUTE_MANIFEST_END */
