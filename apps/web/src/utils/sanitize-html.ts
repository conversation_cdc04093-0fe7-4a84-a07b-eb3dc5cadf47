/**
 * Sanitize HTML content to prevent XSS attacks
 * This is a simple sanitizer that removes script tags and event handlers
 * For production use, consider using a library like DOMPurify
 */
export function sanitizeHtml(html: string | number | boolean | string[]): string {
  if (!html) {
    return '';
  }

  // Convert to string if needed
  const htmlString = Array.isArray(html) ? html.join(', ') : String(html);

  // Create a temporary div to parse the HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlString;

  // Remove script tags
  const scripts = tempDiv.querySelectorAll('script');
  scripts.forEach((script) => script.remove());

  // Remove event handlers
  const allElements = tempDiv.querySelectorAll('*');
  allElements.forEach((element) => {
    // Remove all event handler attributes
    Array.from(element.attributes).forEach((attr) => {
      if (attr.name.startsWith('on')) {
        element.removeAttribute(attr.name);
      }
    });

    // Remove javascript: hrefs
    if (element.tagName === 'A') {
      const href = element.getAttribute('href');
      if (href?.trim().toLowerCase().startsWith('javascript:')) {
        element.removeAttribute('href');
      }
    }
  });

  return tempDiv.innerHTML;
}

/**
 * Strip HTML tags and return plain text
 */
export function stripHtml(html: string): string {
  if (!html) {
    return '';
  }

  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;
  return tempDiv.textContent || tempDiv.innerText || '';
}
