import { useRootStore } from '../store/store';

const AUTH_TOKEN_KEY = 'TOKEN';

const setAuthToken = (token: string) => localStorage.setItem(AUTH_TOKEN_KEY, token);

const getAuthToken = () => localStorage.getItem(AUTH_TOKEN_KEY);

const isAuthenticated = () => !!getAuthToken();

const logout = () => {
  // Preserve theme settings before clearing
  const theme = localStorage.getItem('theme');
  const themeVariant = localStorage.getItem('themeVariant');

  // Clear all localStorage
  localStorage.clear();

  // Restore theme settings
  if (theme) {
    localStorage.setItem('theme', theme);
  }
  if (themeVariant) {
    localStorage.setItem('themeVariant', themeVariant);
  }

  window.location.reload();
};

const isEmptyUser = (): boolean => {
  const roleAssignments = useRootStore.getState().roleAssignments;
  if (roleAssignments.length === 0) {
    return true;
  }
  return false;
};

export { getAuthToken, isAuthenticated, logout, setAuthToken, isEmptyUser };
