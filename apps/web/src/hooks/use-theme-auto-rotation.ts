import { useToast } from '@repo/ui/components/use-toast';
import { useEffect, useRef } from 'react';
import { THEME_VARIANTS } from '../constants/theme-variants';
import { useTheme } from './use-theme';

export function useThemeAutoRotation() {
  const { variant, setVariant, isAutoRotating } = useTheme();
  const { toast } = useToast();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!isAutoRotating) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    // Start the rotation
    intervalRef.current = setInterval(() => {
      const currentIndex = THEME_VARIANTS.findIndex((theme) => theme.identifier === variant);
      const nextIndex = (currentIndex + 1) % THEME_VARIANTS.length;
      const nextTheme = THEME_VARIANTS[nextIndex];

      setVariant(nextTheme.identifier);

      // Show toast notification
      toast({
        title: 'Theme Changed',
        description: `Switched to ${nextTheme.label}`,
        duration: 3000,
      });
    }, 2000);

    // Cleanup on unmount or when auto-rotation stops
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [isAutoRotating, variant, setVariant, toast]);
}
