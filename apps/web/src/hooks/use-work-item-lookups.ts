import { useEffect, useState } from 'react';
import type { Priority, Sprint, Status, User, WorkItemType } from '../services/hooks.schemas';
import { useGetApiV1Priorities } from '../services/priorities';
import { useGetApiV1Sprints } from '../services/sprints';
import { useGetApiV1Statuses } from '../services/statuses';
import { useGetApiV1Users } from '../services/users';
import { useGetApiV1WorkItemTypes } from '../services/work-item-types';

interface LookupData {
  statuses: Status[];
  priorities: Priority[];
  users: User[];
  sprints: Sprint[];
  workItemTypes: WorkItemType[];
}

export function useWorkItemLookups() {
  const [lookupData, setLookupData] = useState<LookupData>({
    statuses: [],
    priorities: [],
    users: [],
    sprints: [],
    workItemTypes: [],
  });

  // Fetch all lookup data - matching the APIs used in work-item-form.tsx
  const { data: statusesResponse } = useGetApiV1Statuses({});
  const { data: prioritiesResponse } = useGetApiV1Priorities();
  const { data: usersResponse } = useGetApiV1Users(); // No parameters, just like in work-item-form
  const { data: sprintsResponse } = useGetApiV1Sprints();
  const { data: typesResponse } = useGetApiV1WorkItemTypes();

  // Update lookup data when responses change
  useEffect(() => {
    setLookupData({
      statuses: statusesResponse?.data || [],
      priorities: prioritiesResponse?.data || [],
      users: usersResponse?.data || [],
      sprints: sprintsResponse?.data || [],
      workItemTypes: typesResponse?.data || [],
    });
  }, [statusesResponse, prioritiesResponse, usersResponse, sprintsResponse, typesResponse]);

  // Helper functions to get names by ID
  const getUserName = (userId: string | null | undefined): string => {
    if (!userId) {
      return 'Unassigned';
    }
    const user = lookupData.users.find((u) => u.id === userId);
    // First try displayName (as used in work-items.tsx), then fallback to firstName lastName
    if (user) {
      if (user.displayName) {
        return user.displayName;
      }
      const name = `${user.firstName || ''} ${user.lastName || ''}`.trim();
      return name || user.email || userId;
    }
    return userId;
  };

  const getStatusName = (statusId: string | null | undefined): string => {
    if (!statusId) {
      return 'No status';
    }
    const status = lookupData.statuses.find((s) => s.id === statusId);
    return status?.name || statusId;
  };

  const getPriorityName = (priorityId: string | null | undefined): string => {
    if (!priorityId) {
      return 'No priority';
    }
    const priority = lookupData.priorities.find((p) => p.id === priorityId);
    return priority?.name || priorityId;
  };

  const getSprintName = (sprintId: string | null | undefined): string => {
    if (!sprintId) {
      return 'No sprint';
    }
    const sprint = lookupData.sprints.find((s) => s.id === sprintId);
    return sprint?.name || sprintId;
  };

  const getTypeName = (typeId: string | null | undefined): string => {
    if (!typeId) {
      return 'No type';
    }
    const type = lookupData.workItemTypes.find((t) => t.id === typeId);
    return type?.name || typeId;
  };

  // Main function to format field values
  const formatFieldValue = (fieldName: string, value: unknown): string => {
    if (value === null || value === undefined) {
      return 'None';
    }

    switch (fieldName) {
      case 'assigneeId':
      case 'reporterId':
        return getUserName(value as string);
      case 'statusId':
        return getStatusName(value as string);
      case 'priorityId':
        return getPriorityName(value as string);
      case 'sprintId':
      case 'initialSprintId':
        return getSprintName(value as string);
      case 'typeId':
        return getTypeName(value as string);
      case 'parentId':
        // For parentId, we just show the ID since fetching parent work item titles
        // would require additional API calls and could impact performance
        return value as string;
      case 'tags':
        return Array.isArray(value) ? value.join(', ') || 'No tags' : String(value);
      case 'isActive':
        return value ? 'Active' : 'Inactive';
      default:
        if (typeof value === 'object') {
          return JSON.stringify(value);
        }
        return String(value);
    }
  };

  // Check if all data is loaded
  const isLoading = !(
    statusesResponse &&
    prioritiesResponse &&
    usersResponse &&
    sprintsResponse &&
    typesResponse
  );

  return {
    lookupData,
    getUserName,
    getStatusName,
    getPriorityName,
    getSprintName,
    getTypeName,
    formatFieldValue,
    isLoading,
  };
}
