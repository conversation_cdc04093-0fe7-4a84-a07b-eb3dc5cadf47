import { useQueryClient } from '@tanstack/react-query';
import { useRootStore } from '@/web/store/store';
import { getGetApiV1RoleAssignmentsQueryKey } from '../services/role-assignments';

export function useRefreshSidebarData() {
  const queryClient = useQueryClient();
  const populateDataFromRoleAssignments = useRootStore(
    (state) => state.populateDataFromRoleAssignments,
  );

  const refreshSidebarData = async () => {
    console.log('[RefreshSidebarData] Invalidating role assignments and populating data');

    // Invalidate role assignments query to force refetch
    await queryClient.invalidateQueries({ queryKey: getGetApiV1RoleAssignmentsQueryKey() });

    // Wait a bit for the query to refetch and update the store
    setTimeout(() => {
      // Re-populate organizations, workspaces, and projects from updated role assignments
      populateDataFromRoleAssignments();
    }, 100);
  };

  return { refreshSidebarData };
}
