import { useQueryClient } from '@tanstack/react-query';
import { getGetApiV1OrganizationsQueryKey } from '../services/organizations';
import { getGetApiV1ProjectsQueryKey } from '../services/projects';
import { getGetApiV1RoleAssignmentsQueryKey } from '../services/role-assignments';
import { getGetApiV1WorkspacesQueryKey } from '../services/workspaces';

export function useAppDataRefresh() {
  const queryClient = useQueryClient();

  const refetchWorkspaces = () => {
    queryClient.invalidateQueries({ queryKey: getGetApiV1WorkspacesQueryKey() });
  };

  const refetchProjects = () => {
    queryClient.invalidateQueries({ queryKey: getGetApiV1ProjectsQueryKey() });
  };

  const refetchOrganizations = () => {
    queryClient.invalidateQueries({ queryKey: getGetApiV1OrganizationsQueryKey() });
  };

  const refetchRoleAssignments = () => {
    queryClient.invalidateQueries({ queryKey: getGetApiV1RoleAssignmentsQueryKey() });
  };

  const refetchAll = () => {
    refetchOrganizations();
    refetchWorkspaces();
    refetchProjects();
    refetchRoleAssignments();
  };

  return {
    refetchWorkspaces,
    refetchProjects,
    refetchOrganizations,
    refetchRoleAssignments,
    refetchAll,
  };
}
