{"files": [], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.node.json"}], "compilerOptions": {"baseUrl": ".", "target": "ESNext", "module": "ESNext", "moduleResolution": "node", "esModuleInterop": true, "strict": true, "paths": {"@/*": ["./src/*"], "@repo/ui/*": ["../../packages/ui/src/*"], "@repo/api-client/*": ["../../packages/api-client/src/*"], "@repo/api/*": ["../../apps/api/src/*"], "@repo/db/*": ["../../packages/db/src/*"]}}}