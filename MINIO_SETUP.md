# MinIO Setup for SPARK Attachments

This document explains how to set up and use Min<PERSON> for file attachments in the SPARK platform.

## Overview

MinIO is used as an S3-compatible object storage solution for handling file attachments. It provides:
- High-performance file storage
- S3-compatible API
- Easy local development setup
- Scalable production deployment

## Local Development Setup

### 1. Using Docker Compose (Recommended)

The easiest way to run MinIO locally is using the provided docker-compose configuration:

```bash
# Start all services including MinIO
docker-compose up -d

# MinIO will be available at:
# - API: http://localhost:9000
# - Console: http://localhost:9001
# - Credentials: minioadmin / minioadmin
```

### 2. Manual MinIO Setup

If you prefer to run MinIO separately:

```bash
# Using Docker
docker run -d \
  -p 9000:9000 \
  -p 9001:9001 \
  -e MINIO_ROOT_USER=minioadmin \
  -e MINIO_ROOT_PASSWORD=minioadmin \
  -v minio-data:/data \
  --name minio \
  minio/minio server /data --console-address ":9001"

# Or download MinIO binary from https://min.io/download
```

### 3. Environment Configuration

Copy the example environment file and configure MinIO settings:

```bash
cp .env.example .env
```

Key MinIO environment variables:
```env
MINIO_ENDPOINT=localhost
MINIO_PORT=9000
MINIO_USE_SSL=false
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET_NAME=spark-attachments
```

## API Usage

### Upload File

Upload a file and attach it to an entity:

```bash
POST /api/v1/attachments/upload?entityType=work_item&entityId=<uuid>
Content-Type: multipart/form-data

# Form data:
# file: <binary file data>
```

Response:
```json
{
  "id": "attachment-uuid",
  "fileName": "document.pdf",
  "url": "http://localhost:9000/spark-attachments/work_item/entity-id/timestamp-uuid.pdf",
  "size": 1024000,
  "fileType": "application/pdf"
}
```

### Get Signed URL

Get a temporary signed URL for secure file access:

```bash
GET /api/v1/attachments/:id/signed-url?expiry=3600
```

Response:
```json
{
  "url": "http://localhost:9000/spark-attachments/...",
  "expiresIn": 3600
}
```

### List Entity Attachments

List all attachments for a specific entity:

```bash
GET /api/v1/attachments/by-entity?entityType=work_item&entityId=<uuid>
```

### Delete Attachment

Delete an attachment (removes from both database and MinIO):

```bash
DELETE /api/v1/attachments/:id
```

## File Validation

### Allowed File Types

By default, the following file types are allowed:
- Images: `image/*` (JPEG, PNG, GIF, WebP, etc.)
- Documents: `application/pdf`, `text/*`
- Office files: `.doc`, `.docx`, `.xls`, `.xlsx`, `.ppt`, `.pptx`

Configure via `ALLOWED_FILE_TYPES` environment variable.

### File Size Limits

- Default max file size: 10MB
- Configure via `MAX_FILE_SIZE` environment variable (in bytes)

## Storage Structure

Files are organized in MinIO with the following structure:
```
bucket/
├── work_item/
│   ├── entity-id-1/
│   │   ├── timestamp-uuid-filename.ext
│   │   └── timestamp-uuid-filename2.ext
│   └── entity-id-2/
│       └── timestamp-uuid-filename.ext
├── project/
│   └── entity-id/
│       └── timestamp-uuid-filename.ext
└── workspace/
    └── entity-id/
        └── timestamp-uuid-filename.ext
```

## Production Considerations

### 1. Security

- Use strong access keys in production
- Enable SSL/TLS for MinIO
- Configure proper CORS policies
- Implement virus scanning for uploaded files

### 2. Performance

- Consider using MinIO's distributed mode for high availability
- Configure proper resource limits
- Use CDN for serving static files
- Implement caching strategies

### 3. Backup

- Set up regular backups of MinIO data
- Configure replication if using distributed mode
- Test restore procedures regularly

### 4. Monitoring

- Monitor storage usage
- Track upload/download metrics
- Set up alerts for failures
- Monitor bucket growth

## Troubleshooting

### Common Issues

1. **Bucket doesn't exist**
   - The API automatically creates the bucket on startup
   - Check MinIO logs for permission issues

2. **Upload fails**
   - Check file size limits
   - Verify file type is allowed
   - Check MinIO connectivity

3. **URLs not accessible**
   - Ensure MinIO endpoint is correctly configured
   - Check if bucket policy allows public access (if needed)
   - Verify network connectivity

### Debug Mode

Enable debug logging for MinIO operations:
```env
LOG_LEVEL=debug
```

## Migration from Local Storage

If migrating from local file storage:

1. Export file metadata from existing system
2. Upload files to MinIO maintaining structure
3. Update database records with new URLs
4. Verify all files are accessible
5. Remove old file storage

## Additional Resources

- [MinIO Documentation](https://docs.min.io/)
- [MinIO JavaScript SDK](https://docs.min.io/docs/javascript-client-api-reference.html)
- [S3 API Reference](https://docs.aws.amazon.com/AmazonS3/latest/API/Welcome.html)