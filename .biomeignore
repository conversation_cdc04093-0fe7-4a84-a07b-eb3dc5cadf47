# Dependencies
node_modules/
**/node_modules/**
**/.pnpm/**
**/.pnpm-store/**

# Build outputs
dist/
build/
.next/
**/dist/**
**/build/**
**/.next/**
**/.turbo/**
out/

# Test coverage
coverage/
**/coverage/**
**/.nyc_output/**

# Generated files
generated/
**/*.gen.ts
**/*.generated.*
**/routeTree.gen.ts

# Orval generated API hooks and services - IMPORTANT!
apps/web/src/services/**
**/src/services/*.ts
**/src/services/*.schemas.ts
**/services/hooks.schemas.ts

# Routing files web
apps/web/src/routes/**
**/src/routes/**

# Configuration files
**/*.config.js
**/*.config.ts
**/*.config.mjs
**/*.config.cjs
**/vitest.config.ts
**/postcss.config.mjs
**/tailwind.config.js
**/drizzle.config.ts
**/tsconfig.json
**/tsconfig.*.json
**/.eslintrc.*
**/eslint.config.*
**/biome.json
**/biome.jsonc
**/.prettierrc*
**/prettier.config.*
**/orval.config.ts

# Scripts and utilities
**/firebase-messaging-sw.js
**/open-browser.js
**/verify-seeds.js
**/test-date-serialization.js
**/templates/**
**/scripts/**
**/run-e2e-tests.sh

# Lock files and env
**/pnpm-lock.yaml
**/package-lock.json
**/yarn.lock
**/.env*
**/.env.local
**/.env.*.local

# Public assets
**/public/**
**/static/**
**/assets/**

# Migrations and database
**/migrations/**
**/*.sql
**/db/migrations/**
**/db/src/migrations/**

# Test setup files
**/__tests__/**
**/__test__/**
**/__mocks__/**
**/test/**
**/tests/**
**/*.test.ts
**/*.test.tsx
**/*.spec.ts
**/*.spec.tsx
**/test-results/**
**/playwright-report/**
**/playwright/.cache/**

# JSON schemas and data files
**/data/*.json
**/*.schema.json
**/seeds/data/*.json

# IDE and OS files
.DS_Store
Thumbs.db
.idea/
.vscode/
*.swp
*.swo
*~

# Log files
*.log
**/logs/**
**/*.log

# Temporary files
tmp/
temp/
**/tmp/**
**/temp/**

# Git files
.git/
.gitignore
.gitattributes

# Documentation
docs/
**/docs/**
*.md
README.md
CHANGELOG.md
LICENSE

# Package manager files
.npmrc
.yarnrc
.yarnrc.yml

# CI/CD
.github/
.gitlab-ci.yml
.circleci/
.travis.yml

# Husky
.husky/

# Build tools
.parcel-cache/
.cache/
.rollup.cache/

# Storybook
.storybook/
**/storybook-static/**

# Next.js specific
.vercel/

# E2E tests
**/e2e/**
**/*.e2e.ts
**/*.e2e.tsx

# Claude/MCP files
.claude/
**/mcp_config.json

# Specific generated/utility files
**/resourceGen.js
**/dev.js
**/start-minio.js
**/dev-with-deps.js

# TypeScript declaration files
**/*.d.ts

# CSS files
**/*.css
**/*.scss
**/*.sass
**/*.less

# Minified files
**/*.min.js
**/*.min.css

# Source maps
**/*.map