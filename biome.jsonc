{
  "$schema": "https://biomejs.dev/schemas/2.0.6/schema.json",
  "extends": ["ultracite"],
  "linter": {
    "rules": {
      "a11y": {
        // Keep all accessibility rules enabled - they're important for user experience
        "noLabelWithoutControl": "off",
        "useKeyWithClickEvents": "off",
        "noStaticElementInteractions": "off"
      },
      "security": {
        // Keep all security rules enabled - they're critical for safety
        "noDangerouslySetInnerHtml": "off"
      },
      "correctness": {
        // Keep all correctness rules enabled - they prevent bugs
        "noUnusedVariables": "warn",
        "useExhaustiveDependencies": "off",
        "noChildrenProp": "off",
        "noEmptyPattern": "off"
      },
      "complexity": {
        "noForEach": "off", // forEach is fine for side effects
        "noStaticOnlyClass": "off", // Static utility classes are acceptable
        "noExcessiveCognitiveComplexity": "off", // Make it a warning instead of error
        "noUselessTypeConstraint": "off",
        "noBannedTypes": "off",
        "noVoid": "off"
      },
      "nursery": {
        "noAwaitInLoop": "off", // Sometimes sequential operations are intended
        "useJsonImportAttribute": "off", // Not critical for functionality
        "noShadow": "off", // Variable shadowing as warning only
        "noNestedComponentDefinitions": "off", // Warning for gradual refactoring
        "noUnknownAtRule": "off",
        "useIterableCallbackReturn": "off",
        "noNoninteractiveElementInteractions": "off"
      },
      "performance": {
        "useTopLevelRegex": "off", // Minor performance impact for simple regexes
        "noNamespaceImport": "off", // Namespace imports are sometimes cleaner
        "noImgElement": "off" // Allow <img> element (not using Next.js Image everywhere)
      },
      "suspicious": {
        "noCommentText": "info",
        "noExplicitAny": "off", // Make it a warning to allow gradual typing
        "noEmptyBlockStatements": "off", // Sometimes empty blocks are intentional
        "noEvolvingTypes": "off", // TypeScript handles this well
        "noMisplacedAssertion": "off", // Test structure preference
        "useAwait": "off", // Async without await is sometimes intentional
        "noShadowRestrictedNames": "off",
        "noImplicitAnyLet": "off",
        "noConsole": "off",
        "noAssignInExpressions": "off",
        "noArrayIndexKey": "off",
        "noDocumentCookie": "off"
      },
      "style": {
        "useFilenamingConvention": "off", // PascalCase for React components is standard
        "noNestedTernary": "off", // Sometimes nested ternaries are cleaner
        "noParameterProperties": "off", // TypeScript feature that's useful
        "noNonNullAssertion": "off", // Make it a warning for gradual migration
        "useTemplate": "off",
        "useDefaultSwitchClause": "off",
        "useCollapsedElseIf": "off",
        "useForOf": "off"
      }
    }
  },
  "formatter": {
    "enabled": true,
    "formatWithErrors": false,
    "indentStyle": "space",
    "indentWidth": 2,
    "lineEnding": "lf",
    "lineWidth": 100
  },
  "javascript": {
    "formatter": {
      "quoteStyle": "single",
      "jsxQuoteStyle": "double",
      "trailingCommas": "all",
      "semicolons": "always",
      "arrowParentheses": "always"
    }
  },
  "files": {
    "ignoreUnknown": true
  },
  "vcs": {
    "enabled": true,
    "clientKind": "git",
    "useIgnoreFile": true
  },

  "json": {
    "parser": {
      "allowComments": true
    }
  }
}
