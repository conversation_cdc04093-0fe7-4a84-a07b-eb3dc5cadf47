# Database Configuration
DB_HOST=localhost
DB_USER=spark
DB_PASSWORD=spark
DB_NAME=spark
DB_PORT=5432
DATABASE_URL=postgres://spark:spark@localhost:5432/spark

# MinIO Configuration
MINIO_ENDPOINT=localhost
MINIO_PORT=9000
MINIO_USE_SSL=false
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET_NAME=spark-attachments

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=image/*,application/pdf,text/*,.doc,.docx,.xls,.xlsx,.ppt,.pptx

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-for-development-only
JWT_REFRESH_SECRET=your-super-secret-refresh-token-key-for-development-only
JWT_EXPIRES_IN=86400  # 24 hours
JWT_ISSUER=spark-api
JWT_AUDIENCE=spark-app

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=
SMTP_PASS=
EMAIL_FROM=<EMAIL>

# Application Configuration
NODE_ENV=development
PORT=3000
LOG_LEVEL=info
APP_URL=http://localhost:5173