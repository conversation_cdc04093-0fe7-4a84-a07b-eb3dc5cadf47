#!/usr/bin/env node

const { execSync } = require('node:child_process');
const { platform } = require('node:os');

const _isWindows = platform() === 'win32';

// Colors for console output
const _colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
};

function log(_message, _color = 'reset') {}

// Check if Docker is running
function isDockerRunning() {
  try {
    execSync('docker info', { stdio: 'ignore' });
    return true;
  } catch (_error) {
    return false;
  }
}

// Check if MinIO container exists
function containerExists(name) {
  try {
    const result = execSync(`docker ps -a --filter name=${name} --format "{{.Names}}"`, {
      encoding: 'utf8',
    });
    return result.trim() === name;
  } catch (_error) {
    return false;
  }
}

// Check if MinIO is already running
function isMinioRunning() {
  try {
    const result = execSync('docker ps --filter name=spark-minio-dev --format "{{.Names}}"', {
      encoding: 'utf8',
    });
    return result.trim() === 'spark-minio-dev';
  } catch (_error) {
    return false;
  }
}

// Start MinIO container
async function startMinio() {
  log('🚀 Starting MinIO for development...', 'green');

  if (!isDockerRunning()) {
    log('❌ Docker is not running. Please start Docker first.', 'red');
    process.exit(1);
  }

  // Check if MinIO is already running
  if (isMinioRunning()) {
    log('✓ MinIO is already running', 'yellow');
    return;
  }

  // Remove existing container if it exists but is not running
  if (containerExists('spark-minio-dev')) {
    log('Removing existing MinIO container...', 'yellow');
    execSync('docker rm spark-minio-dev', { stdio: 'inherit' });
  }

  // Start MinIO container
  log('Starting MinIO container...', 'blue');
  try {
    execSync(
      `docker run -d \
      --name spark-minio-dev \
      -p 9000:9000 \
      -p 9001:9001 \
      -e MINIO_ROOT_USER=minioadmin \
      -e MINIO_ROOT_PASSWORD=minioadmin \
      -v spark-minio-data:/data \
      minio/minio server /data --console-address ":9001"`,
      { stdio: 'inherit' },
    );

    // Wait for MinIO to be ready
    log('Waiting for MinIO to be ready...', 'blue');
    let attempts = 0;
    const maxAttempts = 30;

    while (attempts < maxAttempts) {
      try {
        execSync('docker exec spark-minio-dev curl -f http://localhost:9000/minio/health/live', {
          stdio: 'ignore',
        });
        log('✅ MinIO is ready!', 'green');
        log('  📁 API: http://localhost:9000', 'green');
        log('  🖥️  Console: http://localhost:9001', 'green');
        log('  🔑 Credentials: minioadmin / minioadmin', 'green');
        break;
      } catch (_error) {
        attempts++;
        if (attempts === maxAttempts) {
          log('❌ MinIO failed to start properly', 'red');
          process.exit(1);
        }
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    }
  } catch (_error) {
    log('❌ Failed to start MinIO container', 'red');
    process.exit(1);
  }
}

// Stop MinIO container
function stopMinio() {
  if (isMinioRunning()) {
    log('Stopping MinIO container...', 'yellow');
    try {
      execSync('docker stop spark-minio-dev', { stdio: 'inherit' });
      execSync('docker rm spark-minio-dev', { stdio: 'inherit' });
      log('✓ MinIO stopped', 'green');
    } catch (_error) {
      log('Failed to stop MinIO', 'red');
    }
  }
}

// Handle script arguments
const command = process.argv[2];

if (command === 'stop') {
  stopMinio();
} else {
  // Default is to start
  startMinio().catch((_error) => {
    log('Error starting MinIO:', 'red');
    process.exit(1);
  });
}

// Handle process termination
process.on('SIGINT', () => {
  log('\nReceived SIGINT, cleaning up...', 'yellow');
  stopMinio();
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('\nReceived SIGTERM, cleaning up...', 'yellow');
  stopMinio();
  process.exit(0);
});
