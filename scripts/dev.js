#!/usr/bin/env node

const { spawn, execSync } = require('node:child_process');
const path = require('node:path');

// Colors for console output
const _colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
};

function log(_message, _color = 'reset') {}

// Check if Docker is running
function isDockerRunning() {
  try {
    execSync('docker info', { stdio: 'ignore' });
    return true;
  } catch (_error) {
    return false;
  }
}

// Check if MinIO is already running
function isMinioRunning() {
  try {
    const result = execSync('docker ps --filter name=spark-minio-dev --format "{{.Names}}"', {
      encoding: 'utf8',
    });
    return result.trim() === 'spark-minio-dev';
  } catch (_error) {
    return false;
  }
}

async function main() {
  log('🚀 Starting SPARK development environment...', 'green');

  // Check Docker
  if (!isDockerRunning()) {
    log('❌ Docker is not running. Please start Docker Desktop first.', 'red');
    process.exit(1);
  }

  // Start MinIO if not running
  if (isMinioRunning()) {
    log('✅ MinIO is already running', 'green');
  } else {
    log('📦 Starting MinIO...', 'blue');
    try {
      execSync('node scripts/start-minio.js', {
        stdio: 'inherit',
        cwd: path.resolve(__dirname, '..'),
      });
    } catch (_error) {
      log('❌ Failed to start MinIO', 'red');
      process.exit(1);
    }
  }

  // Start the turbo dev command
  log('\n🚀 Starting development servers...', 'green');

  const turbo = spawn('turbo', ['run', 'dev'], {
    stdio: 'inherit',
    cwd: path.resolve(__dirname, '..'),
    shell: true,
  });

  // Handle cleanup on exit
  const cleanup = () => {
    log('\n🛑 Shutting down...', 'yellow');
    log('MinIO will continue running. Use "pnpm minio:stop" to stop it.', 'blue');
    process.exit(0);
  };

  process.on('SIGINT', cleanup);
  process.on('SIGTERM', cleanup);

  turbo.on('error', (error) => {
    log(`Error: ${error.message}`, 'red');
    process.exit(1);
  });

  turbo.on('exit', (code) => {
    if (code !== 0) {
      log(`Process exited with code ${code}`, 'red');
    }
    process.exit(code || 0);
  });
}

main().catch((error) => {
  log(`Error: ${error.message}`, 'red');
  process.exit(1);
});
